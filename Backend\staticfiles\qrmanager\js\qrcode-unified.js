/**
 * 二维码统一管理模块
 * 提供统一的二维码预览、打印和图像生成功能
 */

const QRCodeUnified = (function() {
    
    // 私有变量和方法
    const POSITION_MODE = 'center'; // center 或 topleft
    
    /**
     * 将中心点坐标转换为左上角坐标
     * @param {number} centerX - 中心点X坐标
     * @param {number} centerY - 中心点Y坐标
     * @param {number} size - 二维码尺寸
     * @returns {Object} 左上角坐标对象
     */
    function centerToTopLeft(centerX, centerY, size) {
        return {
            left: centerX - (size / 2),
            top: centerY - (size / 2)
        };
    }
    
    /**
     * 设置HTML元素的二维码位置
     * @param {HTMLElement} element - 要设置位置的元素
     * @param {number} x - X坐标（单位由caller指定，通常为毫米或像素）
     * @param {number} y - Y坐标
     * @param {number} size - 二维码尺寸
     */
    function setElementPosition(element, x, y, size) {
        // 假定所有坐标都是中心点坐标
        element.style.width = `${size}px`;
        element.style.height = `${size}px`;
        element.style.left = `${x}px`;
        element.style.top = `${y}px`;
        element.style.transform = 'translate(-50%, -50%)';
        element.style.position = 'absolute';
    }
    
    /**
     * 在Canvas上绘制二维码
     * @param {CanvasRenderingContext2D} ctx - Canvas上下文
     * @param {HTMLImageElement} qrImage - 二维码图像
     * @param {number} x - X坐标
     * @param {number} y - Y坐标
     * @param {number} size - 二维码尺寸
     */
    function drawQRCodeOnCanvas(ctx, qrImage, x, y, size) {
        // 计算左上角坐标（假定x,y是中心点坐标）
        const position = centerToTopLeft(x, y, size);
        // 绘制二维码
        ctx.drawImage(qrImage, position.left, position.top, size, size);
    }
    
    /**
     * 更新预览容器中的二维码位置
     * @param {Object} options - 配置选项
     * @param {HTMLElement} options.container - 预览容器
     * @param {HTMLElement} options.qrElement - 二维码元素
     * @param {number} options.printWidth - 打印宽度(mm)
     * @param {number} options.printHeight - 打印高度(mm)
     * @param {number} options.qrX - 二维码X坐标(mm)
     * @param {number} options.qrY - 二维码Y坐标(mm)
     * @param {number} options.qrSize - 二维码尺寸(mm)
     * @param {number} options.containerMaxWidth - 容器最大宽度(px)
     * @param {number} options.containerMaxHeight - 容器最大高度(px)
     */
    function updatePreview(options) {
        const {
            container, qrElement, 
            printWidth, printHeight,
            qrX, qrY, qrSize,
            containerMaxWidth = 500, 
            containerMaxHeight = 707
        } = options;
        
        // 计算缩放比例
        const scaleX = containerMaxWidth / printWidth;
        const scaleY = containerMaxHeight / printHeight;
        const scale = Math.min(scaleX, scaleY);
        
        // 计算预览容器尺寸
        const previewWidth = Math.round(printWidth * scale);
        const previewHeight = Math.round(printHeight * scale);
        
        // 更新预览容器尺寸
        container.style.width = `${previewWidth}px`;
        container.style.height = `${previewHeight}px`;
        
        // 更新网格背景尺寸（如果存在）
        const gridBackground = container.querySelector('.grid-background');
        if (gridBackground) {
            const gridSize = Math.round(scale * 10); // 每10毫米一个格子
            gridBackground.style.backgroundSize = `${gridSize}px ${gridSize}px`;
        }
        
        // 计算二维码的位置和尺寸
        const previewX = Math.round(qrX * scale);
        const previewY = Math.round(qrY * scale);
        const previewSize = Math.round(qrSize * scale);
        
        // 设置二维码位置
        setElementPosition(qrElement, previewX, previewY, previewSize);
    }
    
    /**
     * 创建用于打印的iframe
     * @param {Object} data - 打印数据
     * @returns {HTMLIFrameElement} 打印iframe
     */
    function createPrintFrame(data) {
        // 创建一个iframe用于打印
        const printFrame = document.createElement('iframe');
        printFrame.style.position = 'absolute';
        printFrame.style.top = '-9999px';
        printFrame.style.left = '-9999px';
        document.body.appendChild(printFrame);
        
        // 设置iframe内容
        const doc = printFrame.contentDocument || printFrame.contentWindow.document;
        doc.open();
        doc.write(`
            <html>
            <head>
                <style>
                    @page {
                        size: ${data.width}mm ${data.height}mm !important;
                        margin: 0 !important;
                        padding: 0 !important;
                    }
                    body {
                        width: ${data.width}mm !important;
                        height: ${data.height}mm !important;
                        margin: 0 !important;
                        padding: 0 !important;
                        overflow: hidden !important;
                    }
                    .background {
                        position: absolute !important;
                        top: 0 !important;
                        left: 0 !important;
                        width: ${data.width}mm !important;
                        height: ${data.height}mm !important;
                        object-fit: contain !important;
                    }
                    .qr-code {
                        position: absolute !important;
                        left: ${data.qrX}mm !important;
                        top: ${data.qrY}mm !important;
                        width: ${data.qrSize}mm !important;
                        height: ${data.qrSize}mm !important;
                        transform: translate(-50%, -50%) !important;
                    }
                </style>
            </head>
            <body>
                ${data.backgroundImage ? `<img src="${data.backgroundImage}" class="background">` : ''}
                <img src="${data.qrImage}" class="qr-code">
            </body>
            </html>
        `);
        doc.close();
        
        return printFrame;
    }
    
    /**
     * 执行打印
     * @param {Object} data - 打印数据
     * @param {Function} callback - 打印完成后的回调
     */
    function print(data, callback) {
        const printFrame = createPrintFrame(data);
        
        // 等待图片加载完成
        const frameWindow = printFrame.contentWindow;
        const images = frameWindow.document.querySelectorAll('img');
        
        if (images.length === 0) {
            // 没有图片直接打印
            frameWindow.print();
            if (callback) callback();
            return;
        }
        
        let loadedImages = 0;
        const checkAllImagesLoaded = () => {
            loadedImages++;
            if (loadedImages >= images.length) {
                // 所有图片已加载，开始打印
                setTimeout(() => {
                    frameWindow.print();
                    if (callback) callback();
                }, 100);
            }
        };
        
        // 监听所有图片加载
        images.forEach(img => {
            if (img.complete) {
                checkAllImagesLoaded();
            } else {
                img.onload = checkAllImagesLoaded;
                img.onerror = checkAllImagesLoaded; // 即使加载失败也继续
            }
        });
    }
    
    /**
     * 生成带二维码的图像
     * @param {Object} data - 图像数据
     * @returns {Promise<string>} 返回Base64编码的图像
     */
    function generateImage(data) {
        return new Promise((resolve, reject) => {
            try {
                // 创建Canvas
                const canvas = document.createElement('canvas');
                canvas.width = data.width;
                canvas.height = data.height;
                const ctx = canvas.getContext('2d');
                
                // 绘制背景色
                ctx.fillStyle = data.backgroundColor || '#FFFFFF';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 绘制背景图（如果有）
                if (data.backgroundImage) {
                    const bgImg = new Image();
                    bgImg.onload = () => {
                        ctx.drawImage(bgImg, 0, 0, canvas.width, canvas.height);
                        drawQRCode();
                    };
                    bgImg.onerror = () => {
                        console.warn('背景图片加载失败');
                        drawQRCode();
                    };
                    bgImg.src = data.backgroundImage;
                } else {
                    drawQRCode();
                }
                
                // 绘制二维码
                function drawQRCode() {
                    if (data.qrImage) {
                        const qrImg = new Image();
                        qrImg.onload = () => {
                            // 使用统一方法绘制二维码
                            drawQRCodeOnCanvas(
                                ctx, 
                                qrImg, 
                                data.qrX, 
                                data.qrY, 
                                data.qrSize
                            );
                            finish();
                        };
                        qrImg.onerror = () => {
                            console.error('二维码图片加载失败');
                            reject(new Error('二维码图片加载失败'));
                        };
                        qrImg.src = data.qrImage;
                    } else {
                        finish();
                    }
                }
                
                // 完成并返回结果
                function finish() {
                    const imageData = canvas.toDataURL('image/png');
                    resolve(imageData);
                }
            } catch (error) {
                reject(error);
            }
        });
    }
    
    // 公开的API
    return {
        updatePreview: updatePreview,
        print: print,
        generateImage: generateImage,
        drawQRCodeOnCanvas: drawQRCodeOnCanvas,
        setElementPosition: setElementPosition,
        
        // 添加渲染预览方法
        renderPreview: function(data, container) {
            // 清空容器
            container.innerHTML = '';
            
            if (!data || !data.qr_image_base64) {
                container.innerHTML = '<div class="alert alert-danger">无法获取二维码数据</div>';
                return;
            }
            
            // 创建预览容器
            const previewDiv = document.createElement('div');
            previewDiv.className = 'qr-preview-container';
            previewDiv.style.position = 'relative';
            
            // 添加样式
            previewDiv.style.width = '100%';
            previewDiv.style.maxWidth = '500px';
            previewDiv.style.margin = '0 auto';
            previewDiv.style.backgroundColor = '#fff';
            previewDiv.style.borderRadius = '8px';
            previewDiv.style.boxShadow = '0 2px 10px rgba(0, 0, 0, 0.1)';
            previewDiv.style.overflow = 'hidden';
            
            // 如果有模板数据，显示带背景的预览
            if (data.template_data) {
                // 设置预览容器尺寸
                const ratio = data.template_data.print_height / data.template_data.print_width;
                previewDiv.style.paddingTop = (ratio * 100) + '%';
                
                // 创建内容容器
                const contentDiv = document.createElement('div');
                contentDiv.style.position = 'absolute';
                contentDiv.style.top = '0';
                contentDiv.style.left = '0';
                contentDiv.style.width = '100%';
                contentDiv.style.height = '100%';
                
                // 添加背景图片（如果有）
                if (data.template_data.background_image) {
                    const bgImg = document.createElement('img');
                    bgImg.src = data.template_data.background_image;
                    bgImg.style.position = 'absolute';
                    bgImg.style.top = '0';
                    bgImg.style.left = '0';
                    bgImg.style.width = '100%';
                    bgImg.style.height = '100%';
                    bgImg.style.objectFit = 'contain';
                    contentDiv.appendChild(bgImg);
                }
                
                // 添加二维码
                const qrImg = document.createElement('img');
                qrImg.src = 'data:image/png;base64,' + data.qr_image_base64;
                qrImg.style.position = 'absolute';
                
                // 计算二维码位置（相对容器尺寸的百分比）
                const qrX = (data.template_data.qr_position_x / data.template_data.print_width) * 100;
                const qrY = (data.template_data.qr_position_y / data.template_data.print_height) * 100;
                const qrSize = (data.template_data.qr_size / data.template_data.print_width) * 100;
                
                // 设置二维码样式
                qrImg.style.width = qrSize + '%';
                qrImg.style.height = 'auto';
                qrImg.style.left = qrX + '%';
                qrImg.style.top = qrY + '%';
                qrImg.style.transform = 'translate(-50%, -50%)';
                
                contentDiv.appendChild(qrImg);
                previewDiv.appendChild(contentDiv);
            } else {
                // 简单预览（无模板）
                previewDiv.style.padding = '20px';
                previewDiv.style.textAlign = 'center';
                
                // 添加床位信息
                const infoDiv = document.createElement('div');
                infoDiv.className = 'mb-3 text-center';
                
                const deptName = data.bed && data.bed.department ? data.bed.department.name : '未知科室';
                const bedNumber = data.bed ? data.bed.number : '未知床位';
                
                infoDiv.innerHTML = `
                    <h5 class="mb-1">${deptName}</h5>
                    <p class="text-muted mb-3">${bedNumber}号床位</p>
                `;
                previewDiv.appendChild(infoDiv);
                
                // 添加二维码
                const qrImg = document.createElement('img');
                qrImg.src = 'data:image/png;base64,' + data.qr_image_base64;
                qrImg.style.maxWidth = '200px';
                qrImg.style.width = '100%';
                qrImg.className = 'img-fluid';
                previewDiv.appendChild(qrImg);
            }
            
            // 添加操作按钮
            const actionDiv = document.createElement('div');
            actionDiv.className = 'mt-3 d-flex justify-content-center';
            actionDiv.innerHTML = `
                <button class="btn btn-primary me-2" onclick="printCurrentQRCode()">
                    <i class="fas fa-print me-1"></i> 打印
                </button>
                <button class="btn btn-success" onclick="downloadCurrentQRCode()">
                    <i class="fas fa-download me-1"></i> 下载
                </button>
            `;
            
            // 将预览添加到容器
            container.appendChild(previewDiv);
            container.appendChild(actionDiv);
        }
    };
})();

// 兼容性处理 - 如果是在Node.js环境
if (typeof module !== 'undefined' && module.exports) {
    module.exports = QRCodeUnified;
} 