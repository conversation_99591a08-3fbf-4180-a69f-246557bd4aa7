# 医院服务评价系统 - 后端详细设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-27
- **文档类型**: 后端详细设计
- **目标读者**: 后端开发工程师、系统架构师

## 1. 后端架构概述

### 1.1 技术栈
- **框架**: Django 4.2.7
- **API框架**: Django REST Framework
- **数据库**: SQLite3
- **Web服务器**: Gunicorn + Nginx
- **Python版本**: 3.8+

### 1.2 项目结构
```
Backend/
├── HospitalQRCode/          # Django项目配置
│   ├── settings.py          # 项目配置
│   ├── urls.py             # 主路由配置
│   └── wsgi.py             # WSGI入口
├── qrmanager/              # 主应用模块
│   ├── models.py           # 数据模型 (804行)
│   ├── views.py            # 视图逻辑 (12,597行)
│   ├── api.py              # API接口 (1,516行)
│   ├── forms.py            # 表单验证 (346行)
│   ├── urls.py             # 应用路由
│   ├── admin.py            # 管理后台
│   ├── middleware/         # 中间件
│   ├── utils.py            # 工具函数
│   ├── security.py         # 安全模块
│   └── serializers.py      # 序列化器
└── requirements.txt        # 依赖包
```

## 2. 数据模型层设计 (models.py)

### 2.1 核心业务模型

#### 2.1.1 Department (科室模型)
```python
class Department(models.Model):
    code = models.CharField(max_length=20, unique=True)  # 科室编码
    name = models.CharField(max_length=100)              # 科室名称
    remarks = models.TextField(blank=True)               # 备注
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
```

**业务逻辑**:
- 科室编码必须唯一
- 支持软删除机制
- 自动记录创建和更新时间

#### 2.1.2 StaffType (工作人员类型模型)
```python
class StaffType(models.Model):
    code = models.CharField(max_length=20, unique=True)  # 类型编码
    name = models.CharField(max_length=50)               # 类型名称
    icon = models.CharField(max_length=50, blank=True)   # 图标
    display_order = models.IntegerField(default=0)      # 显示顺序
    is_active = models.BooleanField(default=True)       # 是否启用
```

**业务逻辑**:
- 支持图标显示
- 可配置显示顺序
- 支持启用/禁用状态

#### 2.1.3 Staff (工作人员模型)
```python
class Staff(models.Model):
    work_number = models.CharField(max_length=20, unique=True)  # 工号
    name = models.CharField(max_length=50)                      # 姓名
    staff_type = models.ForeignKey(StaffType)                   # 人员类型
    title = models.CharField(max_length=50, blank=True)         # 职称
    department = models.ForeignKey(Department)                  # 所属科室
    photo = models.ImageField(upload_to='staff_photos/')        # 照片
    is_active = models.BooleanField(default=True)              # 是否在职
```

**业务逻辑**:
- 工号全局唯一
- 必须关联科室和人员类型
- 支持照片上传
- 支持在职状态管理

#### 2.1.4 Bed (床位模型)
```python
class Bed(models.Model):
    number = models.CharField(max_length=20)           # 床位号
    department = models.ForeignKey(Department)         # 所属科室
    area = models.CharField(max_length=50, blank=True) # 区域
    staff = models.ForeignKey(Staff, null=True)        # 负责人
    is_active = models.BooleanField(default=True)      # 是否启用
    
    class Meta:
        unique_together = ['number', 'department']     # 床位号在科室内唯一
```

**业务逻辑**:
- 床位号在科室内唯一
- 可指定负责的工作人员
- 支持区域划分
- 支持启用/禁用状态

#### 2.1.5 QRCode (二维码模型)
```python
class QRCode(models.Model):
    code = models.UUIDField(default=uuid.uuid4, unique=True)  # UUID码
    name = models.CharField(max_length=100, blank=True)       # 名称
    description = models.TextField(blank=True)                # 描述
    bed = models.OneToOneField(Bed)                          # 关联床位
    is_active = models.BooleanField(default=True)            # 是否启用
    created_at = models.DateTimeField(auto_now_add=True)
    
    def get_qr_image_url(self):
        """获取二维码图片URL"""
        return f"/qr/image/{self.code}/"
    
    def get_evaluation_url(self):
        """获取评价页面URL"""
        encrypted_param = encrypt_qr_param(str(self.code))
        return f"/q/{encrypted_param}"
```

**业务逻辑**:
- 使用UUID作为唯一标识
- 与床位一对一关联
- 支持加密参数生成
- 自动生成二维码图片和评价链接

#### 2.1.6 Evaluation (评价模型)
```python
class Evaluation(models.Model):
    # 基本信息
    qr_code = models.ForeignKey(QRCode)                    # 关联二维码
    bed = models.ForeignKey(Bed)                           # 关联床位
    is_satisfied = models.BooleanField()                   # 整体满意度
    comment = models.TextField()                           # 评价内容
    
    # 联系信息
    hospital_number = models.CharField(max_length=50, blank=True)  # 住院号
    phone_number = models.CharField(max_length=20, blank=True)     # 联系电话
    
    # 满意工作人员 (最多3个)
    satisfied_staff1_id = models.IntegerField(null=True)
    satisfied_staff1_name = models.CharField(max_length=50, blank=True)
    satisfied_staff1_type = models.IntegerField(null=True)
    satisfied_staff1_type_name = models.CharField(max_length=50, blank=True)
    
    satisfied_staff2_id = models.IntegerField(null=True)
    satisfied_staff2_name = models.CharField(max_length=50, blank=True)
    satisfied_staff2_type = models.IntegerField(null=True)
    satisfied_staff2_type_name = models.CharField(max_length=50, blank=True)
    
    satisfied_staff3_id = models.IntegerField(null=True)
    satisfied_staff3_name = models.CharField(max_length=50, blank=True)
    satisfied_staff3_type = models.IntegerField(null=True)
    satisfied_staff3_type_name = models.CharField(max_length=50, blank=True)
    
    # 不满意工作人员 (最多3个)
    unsatisfied_staff1_id = models.IntegerField(null=True)
    unsatisfied_staff1_name = models.CharField(max_length=50, blank=True)
    unsatisfied_staff1_type = models.IntegerField(null=True)
    unsatisfied_staff1_type_name = models.CharField(max_length=50, blank=True)
    
    unsatisfied_staff2_id = models.IntegerField(null=True)
    unsatisfied_staff2_name = models.CharField(max_length=50, blank=True)
    unsatisfied_staff2_type = models.IntegerField(null=True)
    unsatisfied_staff2_type_name = models.CharField(max_length=50, blank=True)
    
    unsatisfied_staff3_id = models.IntegerField(null=True)
    unsatisfied_staff3_name = models.CharField(max_length=50, blank=True)
    unsatisfied_staff3_type = models.IntegerField(null=True)
    unsatisfied_staff3_type_name = models.CharField(max_length=50, blank=True)
    
    # 系统字段
    sentiment = models.CharField(max_length=20, default='neutral')  # 情感倾向
    created_at = models.DateTimeField(auto_now_add=True)
```

**业务逻辑**:
- 支持整体满意度评价
- 支持最多3个满意和3个不满意工作人员评价
- 冗余存储工作人员信息，避免数据变更影响历史记录
- 自动进行情感分析

### 2.2 系统管理模型

#### 2.2.1 APIKey (API密钥模型)
```python
class APIKey(models.Model):
    name = models.CharField(max_length=100)              # 密钥名称
    key = models.CharField(max_length=64, unique=True)   # 密钥值
    permissions = models.JSONField(default=list)        # 权限列表
    ip_whitelist = models.JSONField(default=list)       # IP白名单
    rate_limit = models.IntegerField(default=1000)      # 速率限制
    is_active = models.BooleanField(default=True)       # 是否启用
    expires_at = models.DateTimeField(null=True)        # 过期时间
    last_used_at = models.DateTimeField(null=True)      # 最后使用时间
    created_at = models.DateTimeField(auto_now_add=True)
    
    def is_valid(self):
        """检查密钥是否有效"""
        if not self.is_active:
            return False
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        return True
```

**业务逻辑**:
- 支持细粒度权限控制
- 支持IP白名单限制
- 支持速率限制配置
- 支持过期时间设置

#### 2.2.2 TempToken (临时令牌模型)
```python
class TempToken(models.Model):
    token = models.CharField(max_length=64, unique=True)  # 令牌值
    qr_param = models.TextField()                         # 原始二维码参数
    qr_code = models.ForeignKey(QRCode)                   # 关联二维码
    ip_address = models.GenericIPAddressField()           # 客户端IP
    expires_at = models.DateTimeField()                   # 过期时间
    is_used = models.BooleanField(default=False)         # 是否已使用
    created_at = models.DateTimeField(auto_now_add=True)
    
    @classmethod
    def generate_token(cls, qr_param, qr_code, ip_address):
        """生成临时令牌"""
        token = secrets.token_urlsafe(32)
        expires_at = timezone.now() + timedelta(minutes=30)
        return cls.objects.create(
            token=token,
            qr_param=qr_param,
            qr_code=qr_code,
            ip_address=ip_address,
            expires_at=expires_at
        )
```

**业务逻辑**:
- 二维码验证后生成一次性令牌
- 30分钟有效期
- 绑定客户端IP地址
- 使用后自动失效

#### 2.2.3 DeviceFingerprint (设备指纹模型)
```python
class DeviceFingerprint(models.Model):
    fingerprint = models.CharField(max_length=64, unique=True)  # 设备指纹
    qr_code = models.ForeignKey(QRCode)                         # 关联二维码
    ip_address = models.GenericIPAddressField()                 # IP地址
    user_agent = models.TextField()                             # 用户代理
    last_evaluation_at = models.DateTimeField()                # 最后评价时间
    evaluation_count = models.IntegerField(default=0)          # 评价次数
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ['fingerprint', 'qr_code']
```

**业务逻辑**:
- 防止同一设备重复评价
- 记录设备访问历史
- 支持评价次数限制

## 3. 视图层设计 (views.py)

### 3.1 管理后台视图

#### 3.1.1 DashboardView (仪表板视图)
```python
class DashboardView(LoginRequiredMixin, TemplateView):
    template_name = 'dashboard.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 统计数据
        context.update({
            'total_evaluations': Evaluation.objects.count(),
            'total_qrcodes': QRCode.objects.filter(is_active=True).count(),
            'total_staff': Staff.objects.filter(is_active=True).count(),
            'satisfaction_rate': self.get_satisfaction_rate(),
            'recent_evaluations': self.get_recent_evaluations(),
            'department_stats': self.get_department_stats(),
        })
        return context
    
    def get_satisfaction_rate(self):
        """计算满意度"""
        total = Evaluation.objects.count()
        if total == 0:
            return 0
        satisfied = Evaluation.objects.filter(is_satisfied=True).count()
        return round((satisfied / total) * 100, 2)
```

**功能说明**:
- 显示系统核心统计数据
- 计算整体满意度
- 展示最近评价记录
- 按科室统计数据

#### 3.1.2 QRCodeListView (二维码列表视图)
```python
class QRCodeListView(LoginRequiredMixin, ListView):
    model = QRCode
    template_name = 'qrcode_list.html'
    paginate_by = 20
    
    def get_queryset(self):
        queryset = QRCode.objects.select_related('bed__department')
        
        # 筛选条件
        department_id = self.request.GET.get('department')
        if department_id:
            queryset = queryset.filter(bed__department_id=department_id)
        
        is_active = self.request.GET.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active == 'true')
        
        return queryset.order_by('-created_at')
```

**功能说明**:
- 分页显示二维码列表
- 支持按科室筛选
- 支持按状态筛选
- 关联查询优化性能

### 3.2 API视图层

#### 3.2.1 基础API视图类
```python
class BasePaginatedAPIView(JSONResponseMixin, TokenAuthMixin, View):
    """基础分页API视图"""
    model = None
    fields = []
    search_fields = []
    default_page_size = 20
    
    def get_queryset(self):
        return self.model.objects.all()
    
    def apply_filters(self, queryset, request):
        """应用筛选条件"""
        return queryset
    
    def apply_search(self, queryset, request):
        """应用搜索条件"""
        search_term = request.GET.get('search', '')
        if search_term and self.search_fields:
            q_objects = Q()
            for field in self.search_fields:
                q_objects |= Q(**{f"{field}__icontains": search_term})
            queryset = queryset.filter(q_objects)
        return queryset
    
    def serialize_object(self, obj):
        """序列化对象"""
        data = {}
        for field in self.fields:
            if hasattr(obj, field):
                data[field] = getattr(obj, field)
        return data
```

**设计特点**:
- 提供通用的分页、搜索、筛选功能
- 支持自定义序列化
- 统一的错误处理
- 性能优化的查询

## 4. API接口层设计 (api.py)

### 4.1 三层API架构

#### 4.1.1 公开API (无需认证)
```python
class QRCodeVerifyAPI(JSONResponseMixin, View):
    """二维码验证API"""
    allow_anonymous = True
    
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            encrypted_param = data.get('qr_param')
            
            # 解密参数
            uuid = secure_qr_access(encrypted_param)
            
            # 获取二维码信息
            qrcode_obj = QRCode.objects.get(code=uuid, is_active=True)
            
            # 生成临时令牌
            temp_token = TempToken.generate_token(
                qr_param=encrypted_param,
                qr_code=qrcode_obj,
                ip_address=self._get_client_ip(request)
            )
            
            # 返回数据
            return self.render_to_json_response({
                "status": "success",
                "data": {
                    "temp_token": temp_token.token,
                    "bed": {"id": qrcode_obj.bed.id, "number": qrcode_obj.bed.number},
                    "department": {"id": qrcode_obj.bed.department.id, "name": qrcode_obj.bed.department.name},
                    "staff": self.get_staff_data(qrcode_obj.bed.department)
                }
            })
        except Exception as e:
            return self.render_to_json_response({
                "status": "error",
                "message": "验证失败"
            }, status=400)
```

**功能说明**:
- 验证加密的二维码参数
- 生成临时访问令牌
- 返回床位、科室、工作人员信息
- 完整的错误处理

#### 4.1.2 评价提交API
```python
class PublicEvaluationSubmitAPI(JSONResponseMixin, View):
    """评价提交API"""
    MAX_SATISFIED = 3      # 最多3个满意
    MAX_UNSATISFIED = 3    # 最多3个不满意
    
    def post(self, request, *args, **kwargs):
        try:
            data = json.loads(request.body)
            
            # 验证必填字段
            required_fields = ['qr_param', 'comment', 'staff_evaluations']
            for field in required_fields:
                if field not in data:
                    return self.render_to_json_response({
                        "status": "error",
                        "message": f"缺少必填字段: {field}"
                    }, status=400)
            
            # 验证工作人员评价数量
            staff_evaluations = data['staff_evaluations']
            satisfied_count = sum(1 for eval_data in staff_evaluations if eval_data['is_satisfied'])
            unsatisfied_count = sum(1 for eval_data in staff_evaluations if not eval_data['is_satisfied'])
            
            if satisfied_count > self.MAX_SATISFIED:
                return self.render_to_json_response({
                    "status": "error",
                    "message": f"满意评价超出限制，最多选择{self.MAX_SATISFIED}个"
                }, status=400)
            
            if unsatisfied_count > self.MAX_UNSATISFIED:
                return self.render_to_json_response({
                    "status": "error",
                    "message": f"不满意评价超出限制，最多选择{self.MAX_UNSATISFIED}个"
                }, status=400)
            
            # 创建评价记录
            evaluation = self.create_evaluation(data)
            
            return self.render_to_json_response({
                "status": "success",
                "message": "评价提交成功",
                "data": {"evaluation_id": evaluation.id}
            })
            
        except Exception as e:
            return self.render_to_json_response({
                "status": "error",
                "message": "提交失败"
            }, status=500)
```

**功能说明**:
- 验证评价数据完整性
- 限制工作人员评价数量
- 创建评价记录
- 支持设备指纹防刷

## 5. 中间件设计

### 5.1 API安全中间件
```python
class APISecurityMiddleware(MiddlewareMixin):
    """API安全中间件"""
    
    def process_request(self, request):
        # API路径判断
        if request.path.startswith('/api/'):
            return self.handle_api_request(request)
        return None
    
    def handle_api_request(self, request):
        # 公开API无需认证
        if request.path.startswith('/api/v1/public/'):
            return self.handle_public_api(request)
        
        # RESTful API需要API密钥
        elif request.path.startswith('/api/v1/'):
            return self.handle_restful_api(request)
        
        # 管理API需要Session认证
        elif request.path.startswith('/admin/api/'):
            return self.handle_admin_api(request)
    
    def handle_public_api(self, request):
        """处理公开API"""
        # IP限速检查
        if not self.check_rate_limit(request):
            return JsonResponse({'error': '请求过于频繁'}, status=429)
        return None
    
    def handle_restful_api(self, request):
        """处理RESTful API"""
        # API密钥验证
        api_key = self.get_api_key(request)
        if not api_key or not api_key.is_valid():
            return JsonResponse({'error': '无效的API密钥'}, status=401)
        
        # 权限检查
        if not self.check_permissions(api_key, request):
            return JsonResponse({'error': '权限不足'}, status=403)
        
        request.api_key = api_key
        return None
```

**功能说明**:
- 三层API认证机制
- IP限速保护
- 权限验证
- 完整的安全日志

## 6. 安全设计

### 6.1 参数加密
```python
def encrypt_qr_param(uuid_str):
    """加密二维码参数"""
    key = settings.SECRET_KEY[:32].encode()
    cipher = Fernet(base64.urlsafe_b64encode(key))
    
    # 添加时间戳
    timestamp = int(time.time())
    data = f"{uuid_str}.{timestamp}"
    
    encrypted = cipher.encrypt(data.encode())
    return base64.urlsafe_b64encode(encrypted).decode()

def secure_qr_access(encrypted_param):
    """安全解密二维码参数"""
    try:
        key = settings.SECRET_KEY[:32].encode()
        cipher = Fernet(base64.urlsafe_b64encode(key))
        
        encrypted_data = base64.urlsafe_b64decode(encrypted_param.encode())
        decrypted = cipher.decrypt(encrypted_data).decode()
        
        uuid_str, timestamp = decrypted.split('.')
        
        # 检查时间戳有效性（24小时）
        if int(time.time()) - int(timestamp) > 86400:
            raise ValueError("参数已过期")
        
        return uuid_str
    except Exception as e:
        raise ValueError(f"参数解密失败: {str(e)}")
```

**安全特性**:
- 使用Fernet对称加密
- 添加时间戳防重放
- 24小时有效期
- 完整的异常处理

### 6.2 设备指纹防刷
```python
def generate_device_fingerprint(request):
    """生成设备指纹"""
    components = [
        request.META.get('HTTP_USER_AGENT', ''),
        request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
        request.META.get('HTTP_ACCEPT_ENCODING', ''),
        request.META.get('REMOTE_ADDR', ''),
    ]
    
    fingerprint_data = '|'.join(components)
    return hashlib.sha256(fingerprint_data.encode()).hexdigest()

def check_evaluation_limit(fingerprint, qr_code):
    """检查评价限制"""
    device_fp, created = DeviceFingerprint.objects.get_or_create(
        fingerprint=fingerprint,
        qr_code=qr_code,
        defaults={
            'ip_address': request.META.get('REMOTE_ADDR'),
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'evaluation_count': 0,
            'last_evaluation_at': timezone.now()
        }
    )
    
    # 检查是否在限制时间内
    if device_fp.last_evaluation_at:
        time_diff = timezone.now() - device_fp.last_evaluation_at
        if time_diff.total_seconds() < 3600:  # 1小时内限制
            return False
    
    return True
```

**防刷机制**:
- 基于多维度设备指纹
- 时间窗口限制
- 评价次数统计
- IP地址记录

---

**文档维护**: 后端开发组  
**审核状态**: 已审核  
**更新频率**: 代码变更时同步更新