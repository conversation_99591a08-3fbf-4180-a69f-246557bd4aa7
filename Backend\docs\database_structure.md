# 医院服务评价系统数据库结构文档

## 概述

本文档描述了医院服务评价系统的数据库结构，基于Django ORM设计，使用SQLite3作为默认数据库。文档内容基于实际的models.py文件，确保与代码实现一致。

## 数据库设计原则

1. **规范化设计**：遵循第三范式，减少数据冗余
2. **外键约束**：确保数据完整性和引用完整性
3. **索引优化**：为常用查询字段添加索引
4. **软删除**：重要数据使用is_active字段实现软删除
5. **审计跟踪**：记录数据变更历史和操作日志
6. **安全设计**：敏感数据加密存储，支持设备指纹防刷

## 核心表结构

### 1. 科室表 (qrmanager_department)

科室信息管理表，存储医院各科室的基本信息。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| name | VARCHAR | 100 | NOT NULL, UNIQUE | - | 科室名称 |
| description | TEXT | - | NULLABLE | - | 科室描述 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW | 更新时间 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (name)
- INDEX (is_active)

### 2. 工作人员类型表 (qrmanager_stafftype)

工作人员类型定义表，用于分类管理不同类型的工作人员。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| name | VARCHAR | 50 | NOT NULL | - | 类型名称 |
| code | VARCHAR | 20 | UNIQUE | - | 类型编码 |
| description | TEXT | - | NULLABLE | - | 类型描述 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (code)

### 3. 工作人员表 (qrmanager_staff)

医院工作人员信息表，存储所有工作人员的基本信息。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| name | VARCHAR | 100 | NOT NULL | - | 姓名 |
| work_number | VARCHAR | 20 | UNIQUE | generate_work_number() | 工号 |
| department_id | INTEGER | - | FOREIGN KEY | - | 所属科室 |
| staff_type_id | INTEGER | - | FOREIGN KEY | - | 工作人员类型 |
| phone | VARCHAR | 20 | NULLABLE | - | 电话 |
| email | VARCHAR | 100 | NULLABLE | - | 邮箱 |
| photo | VARCHAR | 200 | NULLABLE | - | 照片路径 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否在职 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW | 更新时间 |

**外键关系**：
- department_id → qrmanager_department.id (CASCADE)
- staff_type_id → qrmanager_stafftype.id (CASCADE)

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (work_number)
- INDEX (department_id)
- INDEX (staff_type_id)
- INDEX (is_active)

**特殊处理**：
- 工号自动生成：WN + 8位UUID
- 照片上传路径：staff_photos/工号姓名.扩展名
- 照片大小限制：最大2MB，支持JPG/PNG格式

### 4. 床位表 (qrmanager_bed)

医院床位信息表，管理各科室的床位信息。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| number | VARCHAR | 10 | NOT NULL | - | 床位号 |
| department_id | INTEGER | - | FOREIGN KEY | - | 所属科室 |
| area | VARCHAR | 1 | NULLABLE | - | 区域(A/B) |
| staff_id | INTEGER | - | FOREIGN KEY | - | 负责人 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NULLABLE | NOW | 创建时间 |
| updated_at | DATETIME | - | NULLABLE | NOW | 更新时间 |

**外键关系**：
- department_id → qrmanager_department.id (CASCADE)
- staff_id → qrmanager_staff.id (SET_NULL)

**约束**：
- UNIQUE (department_id, number) - 床位号在同一科室内唯一

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (department_id, number)
- INDEX (staff_id)
- INDEX (is_active)

### 5. 二维码表 (qrmanager_qrcode)

二维码信息管理表，每个床位对应一个唯一的二维码。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| name | VARCHAR | 100 | NOT NULL | - | 名称 |
| description | TEXT | - | NULLABLE | - | 描述 |
| code | UUID | - | UNIQUE | uuid4() | 唯一编码 |
| bed_id | INTEGER | - | FOREIGN KEY | - | 关联床位 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW | 更新时间 |

**外键关系**：
- bed_id → qrmanager_bed.id (CASCADE, ONE-TO-ONE)

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (code)
- UNIQUE INDEX (bed_id)

**方法**：
- get_qr_image_url(): 获取二维码图片URL
- get_evaluation_url(): 获取评价页面URL

### 6. 评价表 (qrmanager_evaluation)

患者评价信息表，存储通过二维码提交的评价数据。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| qr_code_id | INTEGER | - | FOREIGN KEY | - | 关联二维码 |
| comment | TEXT | - | NOT NULL | - | 评价内容 |
| hospital_rating | INTEGER | - | CHECK (1-5) | 3 | 医院整体评分 |
| hospital_number | VARCHAR | 50 | NULLABLE | - | 住院号 |
| phone_number | VARCHAR | 20 | NULLABLE | - | 联系方式 |
| device_fingerprint | TEXT | - | NULLABLE | - | 设备指纹 |
| ip_address | VARCHAR | 45 | - | - | IP地址 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |

**外键关系**：
- qr_code_id → qrmanager_qrcode.id (CASCADE)

**索引**：
- PRIMARY KEY (id)
- INDEX (qr_code_id)
- INDEX (hospital_rating)
- INDEX (created_at)
- INDEX (ip_address)

### 7. 工作人员评价表 (qrmanager_staffevaluation)

工作人员具体评价表，记录对每个工作人员的满意度评价。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| evaluation_id | INTEGER | - | FOREIGN KEY | - | 关联评价 |
| staff_id | INTEGER | - | FOREIGN KEY | - | 关联工作人员 |
| is_satisfied | BOOLEAN | - | NOT NULL | - | 是否满意 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |

**外键关系**：
- evaluation_id → qrmanager_evaluation.id (CASCADE)
- staff_id → qrmanager_staff.id (CASCADE)

**约束**：
- UNIQUE (evaluation_id, staff_id) - 每个评价中每个工作人员只能评价一次

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (evaluation_id, staff_id)
- INDEX (staff_id)
- INDEX (is_satisfied)

### 8. API密钥表 (qrmanager_apikey)

API访问密钥管理表，用于控制API访问权限。

| 字段名 | 类型 | 长度 | 约束 | 默认值 | 描述 |
|--------|------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键 |
| name | VARCHAR | 100 | NOT NULL | - | 密钥名称 |
| key | VARCHAR | 64 | UNIQUE | - | 密钥值 |
| created_at | DATETIME | - | NOT NULL | NOW | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW | 更新时间 |
| expires_at | DATETIME | - | NULLABLE | - | 过期时间 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| rate_limit_day | INTEGER | - | NOT NULL | 1000 | 每日请求限制 |
| rate_limit_hour | INTEGER | - | NOT NULL | 100 | 每小时请求限制 |
| allowed_ips | TEXT | - | NULLABLE | - | 允许的IP地址 |
| can_read | BOOLEAN | - | NOT NULL | TRUE | 读取权限 |
| can_write | BOOLEAN | - | NOT NULL | FALSE | 写入权限 |
| can_delete | BOOLEAN | - | NOT NULL | FALSE | 删除权限 |

**索引**：
- PRIMARY KEY (id)
- UNIQUE INDEX (key)
- INDEX (is_active)
- INDEX (expires_at)

## 表关系图

```mermaid
erDiagram
    Department ||--o{ Staff : "belongs to"
    Department ||--o{ Bed : "contains"
    StaffType ||--o{ Staff : "categorizes"
    Staff ||--o{ Bed : "manages"
    Bed ||--|| QRCode : "has"
    QRCode ||--o{ Evaluation : "receives"
    Evaluation ||--o{ StaffEvaluation : "contains"
    Staff ||--o{ StaffEvaluation : "evaluated in"
    APIKey ||--o{ APILog : "logs"
```

## 辅助函数

### 工作人员相关
```python
def generate_work_number():
    """生成工号"""
    return f"WN{str(uuid.uuid4())[:8]}"

def validate_image_size(image):
    """验证图片大小"""
    if image.size > 10 * 1024 * 1024:  # 10MB
        raise ValidationError('图片大小不能超过10MB')

def staff_photo_path(instance, filename):
    """生成工作人员照片路径"""
    ext = filename.split('.')[-1]
    filename = f"{instance.work_number}{instance.name}.{ext}"
    return os.path.join('staff_photos', filename)
```

### 安全相关
```python
def encrypt_qr_param(qr_code):
    """加密二维码参数"""
    # 实现在security.py中
    pass

def decrypt_qr_param(encrypted_param):
    """解密二维码参数"""
    # 实现在security.py中
    pass
```

## 数据完整性约束

1. **外键约束**：确保引用完整性
2. **唯一性约束**：防止重复数据
3. **检查约束**：确保数据有效性（如评分1-5）
4. **非空约束**：确保必要字段不为空

## 索引策略

1. **主键索引**：所有表的id字段
2. **唯一索引**：唯一性约束字段
3. **外键索引**：提高关联查询性能
4. **查询索引**：常用查询字段（如created_at, is_active）

## 安全特性

1. **设备指纹**：防止恶意刷评价
2. **IP限制**：API访问IP白名单
3. **令牌机制**：临时令牌控制评价提交
4. **加密存储**：敏感参数加密存储
5. **访问日志**：完整的API访问记录

## 更新记录

- 2025-03-24: 基于实际models.py文件重写数据库文档
- 2025-03-15: 新增API密钥和日志表
- 2025-03-07: 新增工作人员评价表，支持多工作人员评价
- 2024-02-20: 初始版本