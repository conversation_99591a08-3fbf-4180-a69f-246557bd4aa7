from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed
from django.utils.translation import gettext_lazy as _
from .models import APIKey
from django.utils import timezone
import logging

# 获取日志记录器
logger = logging.getLogger('qrmanager')

class APIKeyAuthentication(BaseAuthentication):
    """
    自定义API密钥认证类

    通过HTTP头部中的X-API-Key进行认证
    """
    keyword = 'Api-Key'

    def authenticate(self, request):
        # 记录API认证请求信息
        logger.debug(f"API认证请求 - 路径: {request.path}, 方法: {request.method}")

        # 检查Token认证
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')

        if auth_header.startswith('Token '):
            token = auth_header.split(' ')[1].strip()
            logger.debug(f"找到Token认证尝试: {token[:8] if len(token) > 8 else '***'}...")

            # 从数据库中查找Token
            try:
                api_key = APIKey.objects.get(key=token, is_active=True)
                if not api_key.is_expired():
                    # 更新最后使用时间
                    api_key.last_used_at = timezone.now()
                    api_key.save(update_fields=['last_used_at'])
                    logger.info(f"Token认证成功: {token[:8] if len(token) > 8 else '***'}...")
                    return (None, api_key)
                else:
                    logger.warning(f"Token已过期: {token[:8] if len(token) > 8 else '***'}...")
                    raise AuthenticationFailed(_('Token已过期'))
            except APIKey.DoesNotExist:
                logger.warning(f"无效的Token: {token[:8] if len(token) > 8 else '***'}...")
                # 不立即抛出异常，继续尝试其他认证方式

        # 从请求头中获取API密钥
        api_key_header = request.META.get('HTTP_X_API_KEY')
        if not api_key_header:
            logger.debug("未找到API密钥头")
            return None  # 没有提供API密钥，交给其他认证方式处理

        # 查找API密钥
        try:
            api_key = APIKey.objects.get(key=api_key_header)
        except APIKey.DoesNotExist:
            logger.warning(f"无效的API密钥: {api_key_header[:8] if len(api_key_header) > 8 else '***'}...")
            raise AuthenticationFailed(_('无效的API密钥'))

        # 检查API密钥是否有效
        if not api_key.is_active:
            logger.warning(f"API密钥已禁用: {api_key_header[:8] if len(api_key_header) > 8 else '***'}...")
            raise AuthenticationFailed(_('API密钥已禁用'))

        if api_key.is_expired():
            logger.warning(f"API密钥已过期: {api_key_header[:8] if len(api_key_header) > 8 else '***'}...")
            raise AuthenticationFailed(_('API密钥已过期'))

        # 检查IP限制
        client_ip = self._get_client_ip(request)
        if not api_key.is_valid(ip_address=client_ip):
            logger.warning(f"IP地址不允许: {client_ip}, API密钥: {api_key_header[:8] if len(api_key_header) > 8 else '***'}...")
            raise AuthenticationFailed(_('此IP地址不允许使用该API密钥'))

        # 更新最后使用时间
        api_key.last_used_at = timezone.now()
        api_key.save(update_fields=['last_used_at'])

        logger.info(f"API密钥认证成功: {api_key_header[:8] if len(api_key_header) > 8 else '***'}...")

        # 返回(None, api_key)表示认证成功，但不关联到特定用户
        # 在需要时可以通过request.auth获取api_key对象
        return (None, api_key)

    def authenticate_header(self, request):
        return self.keyword

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class APIPermissionMixin:
    """
    API权限检查混入类

    用于检查API密钥是否有权限访问特定资源和执行特定操作
    """
    resource_name = None  # 子类需要设置，如'departments', 'beds', 'staff', 'qrcodes', 'evaluations'

    def check_permissions(self, request):
        # 先调用父类的权限检查
        super().check_permissions(request)

        # 如果没有使用API密钥认证，则跳过
        if not hasattr(request, 'auth') or not isinstance(request.auth, APIKey):
            return

        api_key = request.auth

        # 检查操作权限
        if request.method in ['GET', 'HEAD', 'OPTIONS'] and not api_key.can_read:
            self.permission_denied(request, message=_('API密钥没有读取权限'))

        if request.method in ['POST', 'PUT', 'PATCH'] and not api_key.can_write:
            self.permission_denied(request, message=_('API密钥没有写入权限'))

        if request.method == 'DELETE' and not api_key.can_delete:
            self.permission_denied(request, message=_('API密钥没有删除权限'))

        # 检查资源访问权限
        if self.resource_name:
            resource_access_attr = f'can_access_{self.resource_name}'
            if hasattr(api_key, resource_access_attr) and not getattr(api_key, resource_access_attr):
                self.permission_denied(
                    request,
                    message=_('API密钥没有访问{}的权限').format(self.resource_name)
                )