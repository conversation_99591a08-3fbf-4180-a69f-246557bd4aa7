/**
 * 移除版本显示脚本 - 确保清除任何残留的版本信息显示
 */

(function() {
    'use strict';
    
    console.log('🧹 启动版本显示清理脚本');
    
    function removeVersionDisplays() {
        // 查找并移除所有可能的版本显示元素
        const selectors = [
            // 通过位置查找
            'div[style*="position: fixed"][style*="bottom: 5px"][style*="right: 5px"]',
            'div[style*="position:fixed"][style*="bottom:5px"][style*="right:5px"]',
            // 通过内容查找
            'div[style*="z-index: 9999"]',
            'div[style*="z-index:9999"]',
            // 通过字体查找
            'div[style*="font-family: monospace"]',
            'div[style*="font-family:monospace"]',
            // 通过背景色查找
            'div[style*="background: rgba(0,0,0,0.7)"]',
            'div[style*="background:rgba(0,0,0,0.7)"]'
        ];
        
        let removedCount = 0;
        
        selectors.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(element => {
                // 检查元素内容是否包含版本信息
                const text = element.textContent || '';
                if (text.match(/v\d{8}-\d{3}/) || text.includes('构建时间') || text.length < 20) {
                    console.log('🗑️ 移除版本显示元素:', text);
                    element.remove();
                    removedCount++;
                }
            });
        });
        
        // 直接查找包含版本号格式的元素
        const allDivs = document.querySelectorAll('div');
        allDivs.forEach(div => {
            const text = div.textContent || '';
            const style = div.getAttribute('style') || '';
            
            // 检查是否是版本信息元素
            if ((text.match(/v\d{8}-\d{3}/) || text.match(/v\d{4}\d{2}\d{2}-\d{3}/)) && 
                style.includes('position') && 
                style.includes('fixed') &&
                style.includes('bottom') &&
                style.includes('right')) {
                console.log('🗑️ 移除匹配的版本显示元素:', text);
                div.remove();
                removedCount++;
            }
        });
        
        if (removedCount > 0) {
            console.log(`✅ 已移除 ${removedCount} 个版本显示元素`);
        } else {
            console.log('ℹ️ 未发现需要移除的版本显示元素');
        }
    }
    
    // 立即执行一次
    removeVersionDisplays();
    
    // DOM加载完成后再执行一次
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', removeVersionDisplays);
    } else {
        setTimeout(removeVersionDisplays, 100);
    }
    
    // 定期检查并移除（防止其他脚本重新添加）
    setInterval(removeVersionDisplays, 5000);
    
    console.log('✅ 版本显示清理脚本已启动');
    
})();