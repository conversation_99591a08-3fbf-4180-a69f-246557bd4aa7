# Generated by Django 4.2.7 on 2025-03-20 15:48

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0040_remove_rating_field'),
    ]

    operations = [
        migrations.CreateModel(
            name='TempToken',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.CharField(max_length=64, unique=True, verbose_name='令牌')),
                ('qr_param', models.CharField(max_length=512, verbose_name='加密参数')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('expires_at', models.DateTimeField(verbose_name='过期时间')),
                ('is_used', models.BooleanField(default=False, verbose_name='是否已使用')),
                ('used_at', models.DateTimeField(blank=True, null=True, verbose_name='使用时间')),
                ('ip_address', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('qr_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='qrmanager.qrcode', verbose_name='关联二维码')),
            ],
            options={
                'verbose_name': '临时令牌',
                'verbose_name_plural': '临时令牌',
                'ordering': ['-created_at'],
            },
        ),
    ]
