{% extends "qrmanager/base.html" %}

{% block title %}删除工作人员{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center">删除工作人员</h1>
                    <div class="text-center mb-4">
                        {% if object.photo %}
                            <img src="{{ object.photo.url }}" alt="{{ object.name }}" class="rounded-circle mb-3" style="width: 100px; height: 100px; object-fit: cover;">
                        {% endif %}
                        <p class="lead">确定要删除工作人员 "{{ object.name }}" 吗？</p>
                        <p class="text-muted">
                            {{ object.title }} - {{ object.department }}
                        </p>
                    </div>
                    <form method="post" class="text-center">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">确认删除</button>
                        <a href="{% url 'qrmanager:staff_list' %}" class="btn btn-secondary">取消</a>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 