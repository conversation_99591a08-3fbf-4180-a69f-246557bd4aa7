from django.core.management.base import BaseCommand
from qrmanager.models import Evaluation, StaffType
import time

class Command(BaseCommand):
    help = '为已有评价记录填充员工类型名称'

    def handle(self, *args, **options):
        # 获取所有员工类型的映射
        staff_types = {str(t.id): t.name for t in StaffType.objects.all()}
        self.stdout.write(f"已加载 {len(staff_types)} 个员工类型: {staff_types}")
        
        # 获取所有评价记录
        evaluations = Evaluation.objects.all()
        total = evaluations.count()
        updated = 0
        skipped = 0
        
        self.stdout.write(f"开始处理 {total} 条评价记录...")
        start_time = time.time()
        
        # 分批处理，每批1000条
        batch_size = 1000
        for i in range(0, total, batch_size):
            batch = evaluations[i:i+batch_size]
            for eval in batch:
                record_updated = False
                
                # 处理满意工作人员
                for j in range(1, 4):
                    type_id = getattr(eval, f'satisfied_staff{j}_type', None)
                    if type_id and not getattr(eval, f'satisfied_staff{j}_type_name', ''):
                        type_name = staff_types.get(str(type_id), '未知类型')
                        setattr(eval, f'satisfied_staff{j}_type_name', type_name)
                        record_updated = True
                        self.stdout.write(f"设置评价 {eval.id} 的满意员工{j}类型名称: {type_name}")
                
                # 处理不满意工作人员
                for j in range(1, 4):
                    type_id = getattr(eval, f'unsatisfied_staff{j}_type', None)
                    if type_id and not getattr(eval, f'unsatisfied_staff{j}_type_name', ''):
                        type_name = staff_types.get(str(type_id), '未知类型')
                        setattr(eval, f'unsatisfied_staff{j}_type_name', type_name)
                        record_updated = True
                        self.stdout.write(f"设置评价 {eval.id} 的不满意员工{j}类型名称: {type_name}")
                
                # 如果有更新，保存记录
                if record_updated:
                    eval.save()
                    updated += 1
                else:
                    skipped += 1
            
            # 输出批处理进度
            elapsed_time = time.time() - start_time
            progress = min((i + batch_size) / total * 100, 100)
            self.stdout.write(f"进度: {progress:.1f}% - 已更新: {updated}, 已跳过: {skipped}, 耗时: {elapsed_time:.1f}秒")
        
        # 完成处理
        total_time = time.time() - start_time
        self.stdout.write(self.style.SUCCESS(
            f"迁移完成，共更新 {updated}/{total} 条评价记录，跳过 {skipped} 条，总耗时: {total_time:.1f}秒"
        )) 