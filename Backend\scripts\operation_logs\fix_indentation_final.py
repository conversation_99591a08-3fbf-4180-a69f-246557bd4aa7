#!/usr/bin/env python
# 修复views.py文件中的缩进问题

with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找OperationLogListView类
class_start = content.find("class OperationLogListView")
if class_start == -1:
    print("找不到OperationLogListView类")
    exit(1)

# 查找错误缩进的get_queryset方法
method_line = "        def get_queryset(self):"
method_pos = content.find(method_line, class_start)
if method_pos == -1:
    print("找不到错误缩进的get_queryset方法")
    exit(1)

# 修复缩进问题
fixed_content = content[:method_pos] + "    def get_queryset(self):" + content[method_pos + len(method_line):]

# 写回文件
with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
    f.write(fixed_content)

print("✅ 已修复get_queryset方法的缩进问题！") 