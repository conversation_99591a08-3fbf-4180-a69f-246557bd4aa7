from django.db import migrations, models
import django.db.models.deletion


def populate_is_satisfied(apps, schema_editor):
    """将rating字段的值转换为is_satisfied字段"""
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    for evaluation in Evaluation.objects.all():
        # 4和5分视为满意，1-3分视为不满意
        evaluation.is_satisfied = evaluation.rating >= 4
        evaluation.save(update_fields=['is_satisfied'])


def populate_staff(apps, schema_editor):
    """确保每个评价都有关联的工作人员"""
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    Staff = apps.get_model('qrmanager', 'Staff')
    
    # 获取一个默认的工作人员，用于没有关联工作人员的评价
    default_staff = Staff.objects.first()
    
    if not default_staff:
        # 如果没有工作人员，创建一个默认的
        default_staff = Staff.objects.create(
            name="系统默认",
            work_number="DEFAULT001"
        )
    
    # 更新没有工作人员的评价
    for evaluation in Evaluation.objects.filter(staff__isnull=True):
        # 尝试从床位获取工作人员
        if evaluation.bed and evaluation.bed.staff:
            evaluation.staff = evaluation.bed.staff
        else:
            # 使用默认工作人员
            evaluation.staff = default_staff
        evaluation.save(update_fields=['staff'])


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0038_alter_qrcode_bed'),
    ]

    operations = [
        # 1. 添加新的is_satisfied字段，允许为空
        migrations.AddField(
            model_name='evaluation',
            name='is_satisfied',
            field=models.BooleanField(choices=[(True, '满意'), (False, '不满意')], null=True, verbose_name='是否满意'),
        ),
        
        # 2. 运行数据迁移函数，填充is_satisfied字段
        migrations.RunPython(populate_is_satisfied),
        
        # 3. 修改is_satisfied字段，不允许为空
        migrations.AlterField(
            model_name='evaluation',
            name='is_satisfied',
            field=models.BooleanField(choices=[(True, '满意'), (False, '不满意')], verbose_name='是否满意'),
        ),
        
        # 4. 运行数据迁移函数，确保每个评价都有关联的工作人员
        migrations.RunPython(populate_staff),
        
        # 5. 修改bed字段的on_delete行为
        migrations.AlterField(
            model_name='evaluation',
            name='bed',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='evaluations', to='qrmanager.bed', verbose_name='床位'),
        ),
        
        # 6. 修改staff字段，使其不可为空并使用PROTECT
        migrations.AlterField(
            model_name='evaluation',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='qrmanager.staff', verbose_name='评价对象(工作人员)'),
        ),
        
        # 7. 修改comment字段的verbose_name
        migrations.AlterField(
            model_name='evaluation',
            name='comment',
            field=models.TextField(blank=True, verbose_name='评价原因'),
        ),
    ] 