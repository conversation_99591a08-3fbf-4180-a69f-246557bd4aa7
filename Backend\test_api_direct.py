#!/usr/bin/env python3
"""
直接测试API视图功能
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.http import HttpRequest
from django.contrib.auth.models import AnonymousUser
from qrmanager.api import PublicEvaluationSubmitAPI
from qrmanager.models import Evaluation, QRCode
from qrmanager.security import encrypt_qr_param

def create_mock_request(data):
    """创建模拟请求"""
    request = HttpRequest()
    request.method = 'POST'
    request.META['CONTENT_TYPE'] = 'application/json'
    request._body = json.dumps(data).encode('utf-8')
    request.user = AnonymousUser()  # 添加用户对象
    return request

def test_api_view_direct():
    """直接测试API视图"""
    print("🔍 直接测试API视图")
    print("=" * 50)
    
    try:
        # 获取测试二维码
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试二维码")
            return False
        
        # 生成加密参数
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        print(f"测试数据:")
        print(f"  二维码: {qr_code.name}")
        print(f"  床位: {qr_code.bed.number}")
        print(f"  科室: {qr_code.bed.department.name}")
        
        # 构建测试数据
        test_data = {
            "qr_param": encrypted_param,
            "comment": "直接API视图测试 - 医院评分功能",
            "hospital_rating": 4,  # 4星评分
            "hospital_number": "DIRECT_TEST001",
            "phone_number": "13600136000",
            "staff_evaluations": []
        }
        
        print(f"\n提交数据:")
        for key, value in test_data.items():
            print(f"  {key}: {value}")
        
        # 创建模拟请求
        request = create_mock_request(test_data)
        
        # 创建API视图实例
        api_view = PublicEvaluationSubmitAPI()
        
        # 调用API
        response = api_view.post(request)
        
        print(f"\nAPI响应:")
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            # 解析响应
            response_data = json.loads(response.content.decode())
            print(f"  状态: {response_data.get('status')}")
            print(f"  消息: {response_data.get('message')}")
            
            data = response_data.get('data', {})
            evaluation_id = data.get('evaluation_id')
            hospital_rating = data.get('hospital_rating')
            
            print(f"  评价ID: {evaluation_id}")
            print(f"  医院评分: {hospital_rating}")
            
            if evaluation_id:
                # 验证数据库记录
                try:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    print(f"\n数据库验证:")
                    print(f"  评价ID: {evaluation.id}")
                    print(f"  医院评分: {evaluation.hospital_rating}")
                    print(f"  评价内容: {evaluation.comment}")
                    print(f"  住院号: {evaluation.hospital_number}")
                    print(f"  联系电话: {evaluation.phone_number}")
                    
                    if evaluation.hospital_rating == 4:
                        print(f"✅ 医院评分正确保存")
                        return True
                    else:
                        print(f"❌ 医院评分保存错误: 期望4，实际{evaluation.hospital_rating}")
                        return False
                        
                except Evaluation.DoesNotExist:
                    print(f"❌ 数据库中未找到评价记录")
                    return False
            else:
                print(f"❌ API响应中没有evaluation_id")
                return False
        else:
            print(f"❌ API请求失败")
            print(f"  响应内容: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ 直接API测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rating_validation_direct():
    """直接测试评分验证"""
    print(f"\n🔍 直接测试评分验证")
    print("=" * 50)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试无效评分
        invalid_ratings = [0, 6, -1, 10, "abc", None]
        
        for rating in invalid_ratings:
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"测试无效评分: {rating}",
                "hospital_rating": rating,
                "staff_evaluations": []
            }
            
            request = create_mock_request(test_data)
            api_view = PublicEvaluationSubmitAPI()
            response = api_view.post(request)
            
            if response.status_code == 400:
                response_data = json.loads(response.content.decode())
                print(f"✅ 无效评分 {rating} 正确被拒绝: {response_data.get('message')}")
            elif response.status_code == 200:
                response_data = json.loads(response.content.decode())
                if response_data.get('status') == 'error':
                    print(f"✅ 无效评分 {rating} 正确被拒绝: {response_data.get('message')}")
                else:
                    print(f"⚠️  无效评分 {rating} 意外通过验证")
            else:
                print(f"⚠️  无效评分 {rating} 验证结果未知: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 评分验证测试失败: {e}")
        return False

def test_optional_rating_direct():
    """直接测试可选评分"""
    print(f"\n🔍 直接测试可选评分")
    print("=" * 50)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试不提供评分
        test_data = {
            "qr_param": encrypted_param,
            "comment": "测试不提供医院评分",
            "staff_evaluations": []
            # 注意：没有hospital_rating字段
        }
        
        request = create_mock_request(test_data)
        api_view = PublicEvaluationSubmitAPI()
        response = api_view.post(request)
        
        if response.status_code == 200:
            response_data = json.loads(response.content.decode())
            if response_data.get('status') == 'success':
                evaluation_id = response_data.get('data', {}).get('evaluation_id')
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    if evaluation.hospital_rating is None:
                        print(f"✅ 可选评分测试通过：允许不提供评分")
                        return True
                    else:
                        print(f"❌ 可选评分测试失败：评分应为空，实际为{evaluation.hospital_rating}")
                        return False
                else:
                    print(f"❌ 可选评分测试失败：没有返回evaluation_id")
                    return False
            else:
                print(f"❌ 可选评分测试失败：{response_data.get('message')}")
                return False
        else:
            print(f"❌ 可选评分测试失败：状态码{response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 可选评分测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 直接API视图医院评分功能测试")
    print("=" * 60)
    
    tests = [
        ("直接API测试", test_api_view_direct),
        ("评分验证测试", test_rating_validation_direct),
        ("可选评分测试", test_optional_rating_direct),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}执行异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"\n📋 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 API医院评分功能完全可用！")
        print("\n✅ 功能确认:")
        print("  - API可以接收和处理hospital_rating字段")
        print("  - 后端正确验证1-5星评分范围")
        print("  - 数据库正确保存评分信息")
        print("  - 评分是可选的，允许不评分")
        print("  - 无效评分会被正确拒绝")
        print("  - API响应包含提交的评分信息")
    else:
        print("⚠️  部分功能存在问题，需要修复")

if __name__ == "__main__":
    main()
