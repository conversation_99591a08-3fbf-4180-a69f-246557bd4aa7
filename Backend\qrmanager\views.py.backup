﻿from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect, FileResponse, HttpResponseNotFound, HttpResponsePermanentRedirect, HttpResponseServerError, HttpResponseNotFound
from django.views.generic import (
    TemplateView, ListView, CreateView, UpdateView, DeleteView, DetailView, View, FormView
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm
from django.contrib import messages
from django.db.models import Count, Avg, Sum, Q, F, Prefetch, Max
from django.db.models.functions import TruncDate
from django.db import models, transaction
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django import forms

from .forms import DepartmentForm, StaffForm, BedForm, QRCodeForm, EvaluationForm, BulkStaffImportForm, PrintTemplateForm
from .models import (
    Bed, QRCode, Department, Staff, Evaluation, PrintTemplate, 
    OperationLog, Dictionary, DictionaryItem, QRCodeHistory, SystemConfig, APIKey, APILog, StaffType
)
from .services import process_evaluation
from .utils import LoggerHelper
from .security import encrypt_qr_param, decrypt_qr_param
from django.conf import settings

import csv
import json
import qrcode
import io
import os
from datetime import datetime, timedelta
from io import BytesIO
from PIL import Image
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, PatternFill
from openpyxl.worksheet.datavalidation import DataValidation
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.pdfgen import canvas
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, PageBreak
from reportlab.lib.styles import getSampleStyleSheet
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from .qrcode_utils import generate_qrcode  # 从专用工具文件导入二维码函数
import tempfile
import urllib.parse
import logging
# 已移至正确位置的导入
from openpyxl.worksheet.datavalidation import DataValidation
import openpyxl
import uuid
import base64
from django.contrib.contenttypes.models import ContentType
import re
from django.views.static import serve
import xlsxwriter

def is_encrypted_param(param):
    """
    检查参数是否为加密格式
    加密参数通常是base64编码的字符串，长度不固定但通常较长
    """
    try:
        # 尝试进行base64解码，如果成功且解码后的长度合理，可能是加密参数
        decoded = base64.b64decode(param)
        # 加密参数解码后应该至少有一定长度
        return len(decoded) > 10
    except:
        # 如果解码失败，则不是有效的base64字符串，不是加密参数
        return False

class IndexView(TemplateView):
    """主页视图"""
    template_name = "qrmanager/index.html"

class AboutView(LoginRequiredMixin, TemplateView):
    """关于页面视图"""
    template_name = "qrmanager/about.html"
    login_url = '/login/'

class ContactView(LoginRequiredMixin, TemplateView):
    """联系我们页面视图"""
    template_name = "qrmanager/contact.html"
    login_url = '/login/'

class PrivacyView(TemplateView):
    """隐私政策页面视图"""
    template_name = "qrmanager/privacy.html"

class EvaluationSuccessView(View):
    """
    评价成功页面视图
    现在只是一个重定向视图，将请求重定向到前端
    """
    def get(self, request, *args, **kwargs):
        # 从系统配置中获取前端URL
        from .models import SystemConfig
        frontend_url = SystemConfig.get_value('frontend_url', 
                                             getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        # 重定向到前端的感谢页面
        return redirect(f"{frontend_url}/thank-you.html")

class DashboardView(LoginRequiredMixin, TemplateView):
    """管理仪表板视图"""
    template_name = "qrmanager/dashboard.html"
    login_url = '/login/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 基础统计
        context['total_qrcodes'] = QRCode.objects.count()
        context['total_evaluations'] = Evaluation.objects.count()
        context['total_staff'] = Staff.objects.count()
        context['total_beds'] = Bed.objects.count()
        context['total_departments'] = Department.objects.count()
        
        # 情感分析统计
        sentiment_stats = Evaluation.objects.values('sentiment').annotate(count=Count('id'))
        context['sentiment_stats'] = {item['sentiment']: item['count'] for item in sentiment_stats}
        
        # 计算满意度比例
        total_evaluations = context['total_evaluations']
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        context['satisfaction_rate'] = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        
        # 最近评价
        context['recent_evaluations'] = Evaluation.objects.select_related('qr_code')[:5]
        
        # 添加 URL 名称
        context['qrcode_list_url'] = reverse_lazy('qrmanager:qrcode_list')
        
        return context

class QRCodeCreateView(LoginRequiredMixin, CreateView):
    """
    创建二维码视图 (已弃用)
    二维码现在由床位自动生成，此视图仅作为兼容保留
    """
    model = QRCode
    template_name = "qrmanager/qrcode_form.html"
    fields = ['name', 'description']
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def dispatch(self, request, *args, **kwargs):
        """重定向到床位列表，因为二维码现在由床位自动生成"""
        messages.info(request, '二维码由床位自动生成，请先创建床位')
        return redirect('qrmanager:bed_list')

    def form_valid(self, form):
        messages.success(self.request, '二维码创建成功！')
        return super().form_valid(form)

class QRCodeListView(LoginRequiredMixin, ListView):
    """二维码列表视图"""
    model = QRCode
    template_name = 'qrmanager/qrcode_list.html'
    context_object_name = 'qrcodes'
    login_url = '/login/'
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 添加部门数据
        context['departments'] = Department.objects.all()
        # 添加区域数据（从Bed模型的choices获取）
        context['areas'] = [{'value': 'A', 'display': 'A区'}, {'value': 'B', 'display': 'B区'}]
        
        # 添加URL设置相关的上下文变量
        from .models import SystemConfig
        context['frontend_url'] = SystemConfig.get_value('frontend_url', 'http://hospital.local')
        
        return context
    
    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = QRCode.objects.select_related(
            'bed',
            'bed__department',
            'bed__staff'
        ).prefetch_related(
            'evaluations'
        )
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')
        
        # 应用筛选条件
        if department:
            queryset = queryset.filter(bed__department_id=department)
        if area:
            queryset = queryset.filter(bed__area=area)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(bed__number__icontains=search) |
                Q(bed__department__name__icontains=search)
            )
        
        # 获取分页大小
        page_size = self.request.GET.get('page_size')
        if page_size:
            try:
                self.paginate_by = int(page_size)
            except ValueError:
                pass
        
        # 获取排序方式
        sort_by = self.request.GET.get('sort_by', 'bed__department__code')
        sort_order = self.request.GET.get('sort_order', 'asc')
        
        # 获取所有数据进行自然排序
        qrcodes = list(queryset)
        
        # 定义自然排序函数
        import re
        def natural_sort_key(qrcode):
            if not qrcode.bed or not qrcode.bed.number:
                return ['999999', 1, '']  # 没有床位的排在最后
            
            # 获取科室代码作为第一排序键，确保是字符串类型
            dept_code = str(qrcode.bed.department.code) if qrcode.bed and qrcode.bed.department and qrcode.bed.department.code else '999999'
            
            # 检查床位号是否以数字开头
            if qrcode.bed.number and qrcode.bed.number[0].isdigit():
                # 数字开头的床位，排在前面（优先级0）
                prefix = 0
            else:
                # 非数字开头的床位，排在后面（优先级1）
                prefix = 1
            
            # 提取床位号中的数字和非数字部分
            convert = lambda text: int(text) if text.isdigit() else text.lower()
            # 科室代码作为第一个排序键，然后是床位号的优先级，最后按自然排序
            return [dept_code, prefix] + [convert(c) for c in re.split('([0-9]+)', qrcode.bed.number)]
        
        # 应用排序
        qrcodes.sort(key=natural_sort_key, reverse=(sort_order == 'desc'))
        
        # 预计算评价数量和平均评分
        for qrcode in qrcodes:
            evaluations = list(qrcode.evaluations.all())
            qrcode.evaluation_count = len(evaluations)
            satisfied_count = sum(1 for e in evaluations if e.is_satisfied)
            qrcode.satisfaction_rate = (satisfied_count / qrcode.evaluation_count * 100) if qrcode.evaluation_count > 0 else 0
            
        return qrcodes

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取科室列表，用于筛选
        context['departments'] = Department.objects.all().order_by('name')
        
        # 获取当前筛选条件
        department_id = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')
        
        # 添加筛选条件到上下文
        context['selected_department'] = int(department_id) if department_id and department_id.isdigit() else None
        context['selected_area'] = area
        context['search_query'] = search
        
        # 获取所有二维码，用于统计
        all_qrcodes = self.get_queryset()
        
        # 为当前页的二维码添加统计信息
        for qr in context['qrcodes']:
            evaluations = list(qr.evaluations.all())
            qr.evaluation_count = len(evaluations)
            satisfied_count = sum(1 for e in evaluations if e.is_satisfied)
            qr.satisfaction_rate = (satisfied_count / qr.evaluation_count * 100) if qr.evaluation_count > 0 else 0
            qr.latest_evaluation = evaluations[0] if evaluations else None
            # 生成二维码图片URL
            qr.image_url = qr.get_qr_image_url()
        
        # 计算总体统计数据
        total_qrcodes = len(all_qrcodes)
        total_evaluations = Evaluation.objects.filter(qr_code__in=all_qrcodes).count()
        satisfied_count = Evaluation.objects.filter(qr_code__in=all_qrcodes, is_satisfied=True).count()
        satisfaction_rate = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        pending_evaluations = Evaluation.objects.filter(
            qr_code__in=all_qrcodes,
            created_at__gte=timezone.now() - timezone.timedelta(days=1),
            process_status='pending'
        ).count()
        
        # 添加统计数据到上下文
        context.update({
            'total_qrcodes': total_qrcodes,
            'total_evaluations': total_evaluations,
            'satisfaction_rate': satisfaction_rate,
            'pending_evaluations': pending_evaluations
        })
        
        # 获取二维码有效期
        try:
            from .models import SystemConfig
            from .security import get_token_expiry
            context['qrcode_expiry'] = get_token_expiry()
            
            # 获取前端URL
            context['frontend_url'] = SystemConfig.get_value('frontend_url', 
                                                           getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        except:
            context['qrcode_expiry'] = 0
            context['frontend_url'] = getattr(settings, 'FRONTEND_URL', 'http://hospital.local')
            
        return context

    def post(self, request, *args, **kwargs):
        """处理批量操作请求"""
        action = request.POST.get('action')
        selected_qrcodes = request.POST.getlist('selected_qrcodes')
        
        if not selected_qrcodes:
            messages.warning(request, '请至少选择一个二维码')
            return redirect('qrmanager:qrcode_list')
            
        if action == 'export':
            # 批量导出
            return self.export_qrcodes(selected_qrcodes)
        elif action == 'print':
            # 批量打印
            return self.print_qrcodes(selected_qrcodes)
        
        return redirect('qrmanager:qrcode_list')
            
    def export_qrcodes(self, qrcode_ids):
        """导出选中的二维码信息"""
        qrcodes = QRCode.objects.filter(id__in=qrcode_ids).select_related('bed', 'bed__department')
        
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "二维码信息"

        # 写入表头
        headers = ['床位号', '科室名称', '区域', '评价数量', '满意度比例(%)', '创建时间', '更新时间']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # 写入数据
        for row, qr in enumerate(qrcodes, 2):
            ws.cell(row=row, column=1, value=qr.bed.number)
            ws.cell(row=row, column=2, value=qr.bed.department.name)
            ws.cell(row=row, column=3, value=qr.bed.get_area_display())
            ws.cell(row=row, column=4, value=qr.evaluations.count())
            ws.cell(row=row, column=5, value=qr.evaluations.filter(is_satisfied=True).count() / qr.evaluations.count() * 100 if qr.evaluations.count() > 0 else 0)
            ws.cell(row=row, column=6, value=qr.created_at.strftime('%Y-%m-%d %H:%M'))
            ws.cell(row=row, column=7, value=qr.updated_at.strftime('%Y-%m-%d %H:%M'))

        # 调整列宽
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15

        # 创建响应
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=qrcodes_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        
        # 记录导出操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="export_qrcodes",
            description=f"导出二维码信息",
            extra_data={'count': len(qrcode_ids)}
        )
        
        return response

    def print_qrcodes(self, qrcode_ids):
        """批量打印二维码为PDF文件"""
        # 获取所选的二维码
        qrcodes = QRCode.objects.filter(id__in=qrcode_ids).select_related('bed', 'bed__department')
        
        # 创建一个PDF文档
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        import tempfile
        from PIL import Image
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            pdf_path = tmp.name
        
        # 创建PDF
        c = canvas.Canvas(pdf_path, pagesize=A4)
        width, height = A4  # 获取页面尺寸
        
        # 每页放置的二维码数量（根据实际情况调整）
        qrcodes_per_page = 4
        margin = 50  # 页面边距
        qr_size = (width - 2 * margin) / 2  # 二维码尺寸
        
        for i, qr in enumerate(qrcodes):
            # 计算当前页和位置
            page = i // qrcodes_per_page
            position = i % qrcodes_per_page
            
            # 如果是新页面，则添加新页
            if position == 0 and i > 0:
                c.showPage()
            
            # 计算二维码位置
            row = position // 2
            col = position % 2
            x = margin + col * qr_size
            y = height - margin - (row + 1) * qr_size
            
            # 使用安全URL生成二维码
            img, _ = generate_qrcode(qr.get_secure_evaluation_url())
            
            # 保存为临时图片文件
            temp_img_path = f"temp_qr_{qr.id}.png"
            img.save(temp_img_path)
            
            # 在PDF中绘制二维码和标题
            # 绘制标题
            c.setFont("Helvetica", 12)
            title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
            c.drawString(x + 10, y + qr_size - 20, title)
            
            # 绘制二维码
            c.drawImage(temp_img_path, x + 10, y + 10, qr_size - 20, qr_size - 40)
            
            # 删除临时图片
            os.remove(temp_img_path)
        
        c.showPage()
        c.save()
        
        # 创建响应并设置响应头
        with open(pdf_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=qrcodes_print.pdf'
        
        # 删除临时PDF文件
        os.remove(pdf_path)
        
        # 记录操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="print_qrcodes",
            description=f"批量打印 {len(qrcode_ids)} 个二维码为PDF文件",
            extra_data={'count': len(qrcode_ids)}
        )
        
        return response

    def get(self, request, *args, **kwargs):
        """处理GET请求，支持AJAX请求"""
        # 调用父类方法获取响应
        response = super().get(request, *args, **kwargs)
        
        # 检查是否为AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.GET.get('ajax') == '1':
            return response
        
        return response

class QRCodeUpdateView(LoginRequiredMixin, UpdateView):
    """更新二维码视图"""
    model = QRCode
    template_name = "qrmanager/qrcode_form.html"
    fields = ['name', 'description']
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def form_valid(self, form):
        messages.success(self.request, '二维码更新成功！')
        return super().form_valid(form)

class QRCodeDeleteView(LoginRequiredMixin, DeleteView):
    """删除二维码视图"""
    model = QRCode
    template_name = "qrmanager/qrcode_confirm_delete.html"
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, '二维码删除成功！')
        return super().delete(request, *args, **kwargs)

class StaffListView(LoginRequiredMixin, ListView):
    """工作人员列表视图"""
    model = Staff
    template_name = 'qrmanager/staff_list.html'
    context_object_name = 'staff_list'
    paginate_by = 20  # 默认每页显示20条记录
    
    def get_paginate_by(self, queryset):
        """
        根据请求参数动态设置每页显示数量
        """
        # 从请求中获取page_size参数
        page_size = self.request.GET.get('page_size')
        if page_size:
            try:
                # 尝试将page_size转换为整数
                page_size = int(page_size)
                # 限制page_size的范围，避免过大或过小的值
                if 10 <= page_size <= 100:
                    return page_size
            except ValueError:
                pass
        # 如果没有有效的page_size参数，使用默认值
        return self.paginate_by

    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = Staff.objects.select_related(
            'department',
            'staff_type'
        )
        
        # 工号搜索
        work_number = self.request.GET.get('work_number')
        if work_number:
            queryset = queryset.filter(work_number__icontains=work_number)
        
        # 姓名搜索
        name = self.request.GET.get('name')
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 科室筛选
        department = self.request.GET.get('department')
        if department:
            queryset = queryset.filter(department_id=department)
        
        # 人员类型筛选
        staff_type = self.request.GET.get('staff_type')
        if staff_type:
            queryset = queryset.filter(staff_type_id=staff_type)
        
        # 职称筛选
        title = self.request.GET.get('title')
        if title:
            queryset = queryset.filter(title=title)
        
        # 强制执行查询并转换为列表，确保数据一致性
        return list(queryset.order_by('department__code', 'work_number'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 预加载并缓存选项数据
        context['departments'] = list(Department.objects.order_by('code', 'name'))
        context['staff_types'] = list(DictionaryItem.objects.filter(
            dictionary__code='staff_type'
        ).order_by('sort_order'))
        context['staff_titles'] = list(DictionaryItem.objects.filter(
            dictionary__code='staff_title'
        ).order_by('sort_order'))
        return context

class StaffCreateView(LoginRequiredMixin, CreateView):
    """创建工作人员视图"""
    model = Staff
    template_name = 'qrmanager/staff_form.html'
    form_class = StaffForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'工作人员 {self.object.name} 创建成功！')
        return response

class StaffUpdateView(LoginRequiredMixin, UpdateView):
    """更新工作人员信息视图"""
    model = Staff
    template_name = 'qrmanager/staff_form.html'
    form_class = StaffForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'工作人员 {self.object.name} 更新成功！')
        return response

class StaffDeleteView(LoginRequiredMixin, DeleteView):
    """删除工作人员视图"""
    model = Staff
    template_name = 'qrmanager/staff_confirm_delete.html'
    success_url = reverse_lazy('qrmanager:staff_list')
    login_url = '/login/'

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, '工作人员信息删除成功！')
        return super().delete(request, *args, **kwargs)

@login_required
def staff_bulk_delete(request):
    """批量删除工作人员"""
    if request.method == 'POST':
        staff_ids = request.POST.getlist('staff_ids')
        if not staff_ids:
            messages.error(request, '未选择任何工作人员')
            return redirect('qrmanager:staff_list')
        
        try:
            # 记录操作日志前获取要删除的工作人员信息
            staff_to_delete = Staff.objects.filter(id__in=staff_ids)
            staff_names = [f"{staff.work_number}-{staff.name}" for staff in staff_to_delete]
            
            # 执行删除操作
            delete_count = staff_to_delete.delete()[0]
            
            # 记录操作日志
            OperationLog.objects.create(
                user=request.user,
                action='staff_bulk_delete',
                description=f"批量删除了{delete_count}名工作人员: {', '.join(staff_names)}",
                status='success',
                extra_data={
                    'staff_ids': staff_ids,
                    'staff_names': staff_names,
                    'count': delete_count
                }
            )
            
            messages.success(request, f'成功删除{delete_count}名工作人员')
        except Exception as e:
            messages.error(request, f'删除失败: {str(e)}')
    
    return redirect('qrmanager:staff_list')

class BedListView(LoginRequiredMixin, ListView):
    """床位列表视图"""
    model = Bed
    template_name = 'qrmanager/bed_list.html'
    context_object_name = 'bed_list'
    paginate_by = None  # 禁用分页，显示所有数据

    def dispatch(self, request, *args, **kwargs):
        """在视图处理前检查科室参数"""
        # 不再需要自动重定向到第一个科室
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = Bed.objects.select_related(
            'department',
            'qrcode'
        ).prefetch_related(
            'qrcode__evaluations'  # 通过二维码预加载评价数据
        )
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')

        # 应用筛选条件
        if department:
            try:
                department_id = int(department)  # 确保是整数
                # 只有当department_id不为空字符串时才进行筛选
                if department_id:
                    queryset = queryset.filter(department_id=department_id)
            except (ValueError, TypeError) as e:
                print(f"Error converting department_id: {e}")
                pass
            
        if area:
            queryset = queryset.filter(area=area)
        if search:
            queryset = queryset.filter(
                Q(number__icontains=search) |
                Q(department__name__icontains=search) |
                Q(staff_evaluations__staff__name__icontains=search)
            )

        # 根据科室筛选情况调整排序方式
        if department:
            # 如果筛选了科室，尝试使用更兼容的排序逻辑
            queryset = queryset.order_by('number')  # 按床位号字符串排序
        else:
            # 如果没有科室筛选，先按科室名称排序，再按床位号排序
            queryset = queryset.order_by('department__name', 'number')
        
        # 强制执行查询并转换为列表，确保数据一致性
        beds = list(queryset)
        
        # 如果筛选了科室，可以在Python代码中进行更精确的排序
        if department:
            # 通过自然排序算法排序床位号（数字部分按数值排序）
            def natural_sort_key(bed):
                import re
                # 检查床位号是否以数字开头
                if bed.number and bed.number[0].isdigit():
                    # 数字开头的床位，排在前面（优先级0）
                    prefix = 0
                else:
                    # 非数字开头的床位，排在后面（优先级1）
                    prefix = 1
                
                # 提取床位号中的数字和非数字部分
                convert = lambda text: int(text) if text.isdigit() else text.lower()
                # 优先级作为第一个排序键，然后按自然排序
                return [prefix] + [convert(c) for c in re.split('([0-9]+)', bed.number)]
            
            # 对已获取的床位列表进行排序
            beds.sort(key=natural_sort_key)
        
        # 预计算每个床位的评价数量
        for bed in beds:
            if hasattr(bed, 'qrcode') and bed.qrcode:
                bed.evaluation_count = bed.qrcode.evaluations.count()
            else:
                bed.evaluation_count = 0

        return beds

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取当前科室
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                context['department'] = Department.objects.select_related('print_template').get(pk=department_id)
            except Department.DoesNotExist:
                pass
                
        # 预加载并缓存科室列表
        context['departments'] = list(Department.objects.order_by('code', 'name'))
        
        # 为每个床位的二维码添加评价URL
        for bed in context['bed_list']:
            if hasattr(bed, 'qrcode') and bed.qrcode:
                bed.qrcode.evaluation_url = bed.qrcode.get_evaluation_url()
        
        return context

class BedCreateView(LoginRequiredMixin, CreateView):
    model = Bed
    form_class = BedForm
    template_name = 'qrmanager/bed_form.html'
    
    def dispatch(self, request, *args, **kwargs):
        """在视图处理前检查科室参数"""
        department_id = request.GET.get('department')
        if not department_id:
            messages.error(request, '创建床位必须指定科室！')
            return redirect('qrmanager:bed_list')
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            messages.error(request, '指定的科室不存在！')
            return redirect('qrmanager:bed_list')
        return super().dispatch(request, *args, **kwargs)
    
    def get_success_url(self):
        """获取成功后的重定向URL"""
        if self.object and self.object.department:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={self.object.department.id}"
        return reverse_lazy('qrmanager:bed_list')

    def get_form_kwargs(self):
        """传递科室ID到表单"""
        kwargs = super().get_form_kwargs()
        department_id = self.request.GET.get('department')
        if department_id:
            kwargs['department_id'] = department_id
        return kwargs

    def get_context_data(self, **kwargs):
        """添加科室信息到上下文"""
        context = super().get_context_data(**kwargs)
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                context['department'] = department
            except Department.DoesNotExist:
                pass
        return context

    def form_valid(self, form):
        """处理表单提交"""
        try:
            # 1. 保存床位信息
            response = super().form_valid(form)
            
            # 2. 创建关联的二维码
            qrcode = QRCode.objects.create(
                bed=self.object,
                name=f"{self.object.department.name}-{self.object.number}号床位二维码" if self.object.department else f"{self.object.number}号床位二维码"
            )
            
            # 3. 记录操作日志
            LoggerHelper.log_model_operation(
                user=self.request.user,
                instance=self.object,
                operation_type="create",
                description=f"创建床位: {self.object.number}",
                extra_data={
                    'department': self.object.department.name if self.object.department else None,
                    'area': self.object.get_area_display(),
                    'staff': self.object.staff.name if self.object.staff else None,
                    'qrcode_id': qrcode.id
                }
            )
            
            messages.success(self.request, f'床位 {self.object.number} 创建成功！')
            return response
            
        except Exception as e:
            # 记录错误日志
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="create_bed_failed",
                description=f"创建床位失败: {str(e)}",
                status='error',
                extra_data={
                    'form_data': form.cleaned_data,
                    'error': str(e)
                }
            )
            messages.error(self.request, f'创建床位失败：{str(e)}')
            return super().form_invalid(form)

    def form_invalid(self, form):
        """处理表单验证失败"""
        # 记录验证失败的日志
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="create_bed_validation_failed",
            description="床位创建表单验证失败",
            status='error',
            extra_data={
                'form_errors': form.errors,
                'form_data': form.cleaned_data
            }
        )
        
        # 重新获取科室信息
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                form.fields['department'].initial = department
                form.fields['department'].queryset = Department.objects.filter(pk=department_id)
                form.fields['department'].widget.attrs.update({
                    'disabled': 'disabled',
                    'class': 'form-select'
                })
            except Department.DoesNotExist:
                pass

        messages.error(self.request, '请检查输入的信息是否正确。')
        return super().form_invalid(form)

class BedUpdateView(LoginRequiredMixin, UpdateView):
    model = Bed
    template_name = 'qrmanager/bed_form.html'
    fields = ['number', 'department', 'area', 'staff']
    
    def get_success_url(self):
        """获取成功后的重定向URL"""
        if self.object and self.object.department:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={self.object.department.id}"
        return reverse_lazy('qrmanager:bed_list')
    
    def get_context_data(self, **kwargs):
        """添加科室信息到上下文"""
        context = super().get_context_data(**kwargs)
        if self.object and self.object.department:
            context['department'] = self.object.department
        return context

    def form_valid(self, form):
        # 记录更新前的数据
        old_instance = self.get_object()
        old_data = {
            'number': old_instance.number,
            'department': old_instance.department.name if old_instance.department else None,
            'area': old_instance.get_area_display(),
            'staff': old_instance.staff.name if old_instance.staff else None
        }
        
        response = super().form_valid(form)
        
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新床位: {self.object.number}",
            old_data=old_data,
            new_data={
                'number': self.object.number,
                'department': self.object.department.name if self.object.department else None,
                'area': self.object.get_area_display(),
                'staff': self.object.staff.name if self.object.staff else None
            }
        )
        
        messages.success(self.request, f'床位 {self.object.number} 更新成功！')
        return response

class BedDeleteView(LoginRequiredMixin, DeleteView):
    model = Bed
    template_name = 'qrmanager/bed_confirm_delete.html'
    
    def get_success_url(self):
        # 从对象获取科室ID
        department_id = getattr(self.object, 'department_id', None)
        if department_id:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={department_id}"
        return reverse_lazy('qrmanager:bed_list')

    def delete(self, request, *args, **kwargs):
        bed = self.get_object()
        # 保存科室ID用于重定向
        self.object = bed
        # 检查关联数据
        related_data = {
            'qrcode_exists': hasattr(bed, 'qrcode'),
            'evaluation_count': Evaluation.objects.filter(bed=bed).count()  # 修改为直接使用bed关联
        }
        
        # 记录删除操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=bed,
            operation_type="delete",
            description=f"删除床位: {bed.number}",
            related_data=related_data
        )
        
        messages.success(self.request, f'床位 {bed.number} 删除成功！')
        return super().delete(request, *args, **kwargs)

class EvaluationListView(LoginRequiredMixin, ListView):
    """评价列表视图"""
    model = Evaluation
    template_name = 'qrmanager/evaluation_list.html'
    context_object_name = 'evaluations'
    login_url = '/login/'
    paginate_by = 20

    def get_queryset(self):
        # 修改select_related，添加bed关联，并使用prefetch_related预加载工作人员评价
        queryset = Evaluation.objects.select_related(
            'qr_code', 'bed', 'bed__department', 'processed_by'
        ).prefetch_related(
            'staff_evaluations__staff'  # 预加载所有相关的工作人员评价
        ).order_by('-created_at')
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        is_satisfied = self.request.GET.get('is_satisfied')
        sentiment = self.request.GET.get('sentiment')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        search = self.request.GET.get('search')

        # 应用筛选条件，修改为使用bed关联
        if department:
            queryset = queryset.filter(bed__department_id=department)
        if is_satisfied:
            queryset = queryset.filter(is_satisfied=is_satisfied == 'True')
        if sentiment:
            queryset = queryset.filter(sentiment=sentiment)
        if status:
            queryset = queryset.filter(process_status=status)
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)
        if search:
            queryset = queryset.filter(
                Q(comment__icontains=search) |
                Q(hospital_number__icontains=search) |  # 添加住院号搜索
                Q(phone_number__icontains=search) |     # 添加联系电话搜索
                Q(bed__number__icontains=search) |
                Q(staff_evaluations__staff__name__icontains=search)
            )

        # 记录查询操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_evaluation_list",
            description="查看评价列表",
            extra_data={
                'filters': {
                    'department': department,
                    'is_satisfied': is_satisfied,
                    'sentiment': sentiment,
                    'status': status,
                    'date_from': date_from,
                    'date_to': date_to,
                    'search': search
                }
            }
        )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['departments'] = Department.objects.all()
        context['satisfaction_choices'] = [(True, '满意'), (False, '不满意')]
        context['sentiment_choices'] = Evaluation.SENTIMENT_CHOICES
        context['process_status_choices'] = Evaluation.PROCESS_STATUS_CHOICES
        
        # 添加筛选参数到上下文
        context['department'] = self.request.GET.get('department', '')
        context['is_satisfied'] = self.request.GET.get('is_satisfied', '')
        context['sentiment'] = self.request.GET.get('sentiment', '')
        context['status'] = self.request.GET.get('status', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')
        context['search'] = self.request.GET.get('search', '')

        # 计算满意度比例
        total_evaluations = Evaluation.objects.count()
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        satisfaction_rate = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        context['satisfaction_rate'] = satisfaction_rate

        # 准备满意度分布数据
        satisfaction_data = []
        # 满意
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        satisfaction_data.append(satisfied_count)
        # 不满意
        unsatisfied_count = Evaluation.objects.filter(is_satisfied=False).count()
        satisfaction_data.append(unsatisfied_count)
        context['satisfaction_data'] = json.dumps(satisfaction_data)

        # 准备科室评价分布数据
        departments = Department.objects.all()
        department_labels = []
        department_data = []
        
        for dept in departments:
            count = Evaluation.objects.filter(bed__department=dept).count()  # 修改为直接使用bed关联
            if count > 0:  # 只显示有评价的科室
                department_labels.append(dept.name)
                department_data.append(count)
        
        context['department_labels'] = json.dumps(department_labels)
        context['department_data'] = json.dumps(department_data)

        # 准备评价趋势数据（近30天）
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=29)
        
        trend_labels = []
        trend_data = []
        
        current_date = start_date
        while current_date <= end_date:
            count = Evaluation.objects.filter(created_at__date=current_date).count()
            trend_labels.append(current_date.strftime('%m-%d'))
            trend_data.append(count)
            current_date += timedelta(days=1)
        
        context['trend_labels'] = json.dumps(trend_labels)
        context['trend_data'] = json.dumps(trend_data)

        return context

class SentimentAnalysisView(LoginRequiredMixin, TemplateView):
    """情感分析视图"""
    template_name = 'qrmanager/sentiment_analysis.html'
    login_url = '/login/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取筛选参数
        time_range = self.request.GET.get('time_range', '30')  # 默认30天
        department_id = self.request.GET.get('department')
        sentiment_filter = self.request.GET.get('sentiment')

        # 构建基础查询集
        evaluations = Evaluation.objects.all()
        
        # 应用时间筛选
        if time_range != 'all':
            days = int(time_range)
            start_date = timezone.now() - timezone.timedelta(days=days)
            evaluations = evaluations.filter(created_at__gte=start_date)

        # 应用科室筛选
        if department_id:
            evaluations = evaluations.filter(bed__department_id=department_id)  # 修改为直接使用bed关联

        # 应用情感倾向筛选
        if sentiment_filter:
            evaluations = evaluations.filter(sentiment=sentiment_filter)

        # 记录查看情感分析的操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_sentiment_analysis",
            description="查看情感分析报告",
            extra_data={
                'filters': {
                    'time_range': time_range,
                    'department_id': department_id,
                    'sentiment': sentiment_filter
                }
            }
        )

        # 计算基础统计数据
        total_evaluations = evaluations.count()
        
        # 计算满意度比例
        satisfied_count = evaluations.filter(is_satisfied=True).count()
        satisfaction_rate = round((satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        
        # 计算投诉率（不满意的比例）
        complaint_count = evaluations.filter(is_satisfied=False).count()
        complaint_rate = round((complaint_count / total_evaluations * 100) if total_evaluations > 0 else 0, 1)

        # 情感分析统计
        sentiment_stats = evaluations.values('sentiment').annotate(count=Count('id'))
        sentiment_dict = {item['sentiment']: item['count'] for item in sentiment_stats}
        
        # 计算情感百分比
        context['positive_percentage'] = round((sentiment_dict.get('positive', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        context['neutral_percentage'] = round((sentiment_dict.get('neutral', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        context['negative_percentage'] = round((sentiment_dict.get('negative', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)

        # 评分统计
        # 计算满意度统计
        satisfaction_stats = evaluations.values('is_satisfied').annotate(count=Count('id'))
        satisfaction_dict = {item['is_satisfied']: item['count'] for item in satisfaction_stats}
        
        # 计算评分百分比
        context['satisfaction_percentages'] = {
            'satisfied': round((satisfaction_dict.get(True, 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1),
            'unsatisfied': round((satisfaction_dict.get(False, 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        }

        # 获取科室列表（用于筛选）
        context['departments'] = Department.objects.all()

        # 计算趋势数据
        trend_days = 7  # 显示最近7天的趋势
        trend_dates = [(timezone.now() - timezone.timedelta(days=i)).date() for i in range(trend_days-1, -1, -1)]
        
        # 情感趋势数据
        sentiment_trend = {
            'positive': [0] * trend_days,
            'neutral': [0] * trend_days,
            'negative': [0] * trend_days
        }
        
        for i, date in enumerate(trend_dates):
            day_stats = evaluations.filter(
                created_at__date=date
            ).values('sentiment').annotate(count=Count('id'))
            for stat in day_stats:
                sentiment_trend[stat['sentiment']][i] = stat['count']

        # 满意度趋势数据
        satisfaction_trend = []
        for date in trend_dates:
            day_evals = evaluations.filter(created_at__date=date)
            total = day_evals.count()
            satisfied = day_evals.filter(is_satisfied=True).count()
            satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
            satisfaction_trend.append(round(satisfaction_rate, 1))

        # 科室评价对比数据
        departments = Department.objects.all()
        department_stats = []
        for dept in departments:
            dept_evals = evaluations.filter(bed__department=dept)  # 修改为直接使用bed关联
            if dept_evals.exists():
                total = dept_evals.count()
                satisfied = dept_evals.filter(is_satisfied=True).count()
                satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
                positive_count = dept_evals.filter(sentiment='positive').count()
                negative_count = dept_evals.filter(sentiment='negative').count()
                total = dept_evals.count()
                
                department_stats.append({
                    'name': dept.name,
                    'satisfaction_rate': round(satisfaction_rate, 1),
                    'positive_rate': round((positive_count / total * 100) if total > 0 else 0, 1),
                    'negative_rate': round((negative_count / total * 100) if total > 0 else 0, 1)
                })

        # 工作人员评价统计
        staff_stats = []
        for staff in Staff.objects.all():
            staff_evals = evaluations.filter(Q(satisfied_staff1_id=staff.id) | Q(satisfied_staff2_id=staff.id) | Q(satisfied_staff3_id=staff.id) | Q(unsatisfied_staff1_id=staff.id) | Q(unsatisfied_staff2_id=staff.id) | Q(unsatisfied_staff3_id=staff.id))
            if staff_evals.exists():
                total = staff_evals.count()
                satisfied = staff_evals.filter(is_satisfied=True).count()
                satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
                positive_count = staff_evals.filter(sentiment='positive').count()
                negative_count = staff_evals.filter(sentiment='negative').count()
                total = staff_evals.count()
                
                staff_stats.append({
                    'name': staff.name,
                    'department': staff.department.name if staff.department else '-',
                    'satisfaction_rate': round(satisfaction_rate, 1),
                    'positive_rate': round((positive_count / total * 100) if total > 0 else 0, 1),
                    'negative_rate': round((negative_count / total * 100) if total > 0 else 0, 1)
                })

        # 添加所有统计数据到上下文
        context.update({
            'total_evaluations': total_evaluations,
            # 不再使用平均评分
            'satisfaction_rate': satisfaction_rate,
            'complaint_rate': complaint_rate,
            'sentiment_stats': sentiment_dict,
            'satisfaction_stats': satisfaction_dict,
            'sentiment_trend_dates': [date.strftime('%m-%d') for date in trend_dates],
            'sentiment_trend_positive': sentiment_trend['positive'],
            'sentiment_trend_neutral': sentiment_trend['neutral'],
            'sentiment_trend_negative': sentiment_trend['negative'],
            'satisfaction_trend_dates': [date.strftime('%m-%d') for date in trend_dates],
            'satisfaction_trend_values': satisfaction_trend,
            'department_names': [stat['name'] for stat in department_stats],
            'department_satisfaction_rates': [stat['satisfaction_rate'] for stat in department_stats],
            'department_positive_rates': [stat['positive_rate'] for stat in department_stats],
            'department_negative_rates': [stat['negative_rate'] for stat in department_stats],
            'top_staff': sorted(staff_stats, key=lambda x: (-x['satisfaction_rate'], -x['positive_rate']))[:5],
            'bottom_staff': sorted(staff_stats, key=lambda x: (x['satisfaction_rate'], -x['negative_rate']))[:5],
            'recent_evaluations': evaluations.select_related(
                'bed', 'bed__department'  # 修改为直接使用bed关联
            ).order_by('-created_at')[:10]
        })

        return context

class DepartmentListView(LoginRequiredMixin, ListView):
    """科室列表视图"""
    model = Department
    template_name = "qrmanager/department_list.html"
    context_object_name = "departments"
    login_url = '/login/'
    ordering = ['code', 'name']

    def get_queryset(self):
        queryset = Department.objects.all()
        queryset = queryset.prefetch_related(
            'staffs',      # 预加载工作人员
            'beds',        # 预加载床位
            'beds__qrcode',  # 预加载二维码
            'beds__qrcode__evaluations'  # 预加载评价
        ).annotate(
            staff_count=Count('staffs', distinct=True),
            bed_count=Count('beds', distinct=True),
            qrcode_count=Count('beds__qrcode', distinct=True),
            evaluation_count=Count('beds__qrcode__evaluations', distinct=True)
        )
        return queryset.order_by('code', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取所有科室
        departments = self.get_queryset()
        
        # 计算总数（使用聚合函数确保计算准确性）
        totals = departments.aggregate(
            total_staff=Sum('staff_count'),
            total_beds=Sum('bed_count'),
            total_evaluations=Sum('evaluation_count')
        )
        
        context.update({
            'total_staff': totals['total_staff'] or 0,
            'total_beds': totals['total_beds'] or 0,
            'total_evaluations': totals['total_evaluations'] or 0
        })
        
        return context

class DepartmentCreateView(LoginRequiredMixin, CreateView):
    """创建科室视图"""
    model = Department
    template_name = "qrmanager/department_form.html"
    fields = ['name']
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录创建操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建科室: {self.object.name}"
        )
        messages.success(self.request, '科室创建成功！')
        return response

class DepartmentUpdateView(LoginRequiredMixin, UpdateView):
    """更新科室视图"""
    model = Department
    template_name = "qrmanager/department_form.html"
    fields = ['name']
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'

    def form_valid(self, form):
        # 记录更新前的数据
        old_data = {
            'name': self.get_object().name
        }
        response = super().form_valid(form)
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新科室: {self.object.name}",
            old_data=old_data
        )
        messages.success(self.request, '科室信息更新成功！')
        return response

class DepartmentDeleteView(LoginRequiredMixin, DeleteView):
    """删除科室视图"""
    model = Department
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'
    
    def post(self, request, *args, **kwargs):
        department = self.get_object()
        password = request.POST.get('password')
        
        # 检查是否是AJAX请求
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        
        # 验证用户密码
        if not request.user.check_password(password):
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': '密码不正确，请重新输入'})
            else:
                messages.error(request, '密码不正确，请重新输入')
                return redirect('qrmanager:department_list')
        
        # 检查是否有关联的工作人员
        staff_count = Staff.objects.filter(department=department).count()
        if staff_count > 0:
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': f'科室"{department.name}"有 {staff_count} 名关联的工作人员，不允许删除！请先解除工作人员与科室的关联。'})
            else:
                messages.error(request, f'科室"{department.name}"有 {staff_count} 名关联的工作人员，不允许删除！请先解除工作人员与科室的关联。')
                return redirect('qrmanager:department_list')
        
        # 检查关联数据
        related_data = {
            'staff_count': staff_count,
            'bed_count': Bed.objects.filter(department=department).count(),
            'print_template_exists': hasattr(department, 'print_template')
        }
        
        try:
            # 先记录要删除的科室信息
            department_name = department.name
            
            # 获取关联的床位
            beds = Bed.objects.filter(department=department)
            
            # 获取关联的二维码
            qrcodes_to_delete = []
            for bed in beds:
                try:
                    if hasattr(bed, 'qrcode'):
                        qrcodes_to_delete.append(bed.qrcode)
                except QRCode.DoesNotExist:
                    pass
            
            # 获取关联的评价
            evaluations_to_delete = []
            for bed in beds:
                evals = Evaluation.objects.filter(bed=bed)
                evaluations_to_delete.extend(list(evals))
            
            # 记录删除操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=department,
                operation_type="delete",
                description=f"删除科室: {department_name}",
                extra_data={
                    **related_data,
                    'beds_deleted': [str(bed) for bed in beds],
                    'qrcodes_deleted': [str(qrcode) for qrcode in qrcodes_to_delete],
                    'evaluations_deleted': len(evaluations_to_delete)
                }
            )
            
            # 删除关联的评价
            for evaluation in evaluations_to_delete:
                evaluation.delete()
            
            # 删除关联的二维码
            for qrcode in qrcodes_to_delete:
                qrcode.delete()
            
            # 删除关联的床位
            beds.delete()
            
            # 执行删除科室操作（会自动删除关联的打印模板，因为是CASCADE关系）
            department.delete()
            
            success_message = f'科室"{department_name}"及其关联的床位、二维码和评价已成功删除！'
            if is_ajax:
                return JsonResponse({'status': 'success', 'message': success_message})
            else:
                messages.success(request, success_message)
                return redirect(self.success_url)
            
        except Exception as e:
            # 如果删除失败，记录错误日志
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=department,
                operation_type="delete",
                description=f"删除科室失败: {department_name}",
                status="error",
                error=str(e)
            )
            
            error_message = f'科室删除失败：{str(e)}'
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': error_message})
            else:
                messages.error(request, error_message)
                return redirect('qrmanager:department_list')

class BedQRCodeView(LoginRequiredMixin, TemplateView):
    """床位二维码预览、生成及替换视图"""
    template_name = "qrmanager/bed_qrcode_preview.html"
    login_url = '/login/'

    def get(self, request, *args, **kwargs):
        bed = get_object_or_404(Bed, pk=self.kwargs['pk'])
        # 如果床位没有关联二维码，则创建一个
        try:
            qrcode_obj = bed.qrcode
        except QRCode.DoesNotExist:
            qrcode_obj = QRCode.objects.create(bed=bed, name=f"QRCode for bed {bed.number}")
            # 记录二维码创建操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=qrcode_obj,
                operation_type="create",
                description=f"为床位 {bed.number} 创建二维码"
            )
        
        # 记录查看操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="view_qrcode",
            description=f"查看床位 {bed.number} 的二维码",
            extra_data={
                'bed_id': bed.id,
                'qrcode_id': qrcode_obj.id,
                'has_template': bed.department and hasattr(bed.department, 'print_template')
            }
        )
        
        return super().get(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        bed = get_object_or_404(Bed, pk=self.kwargs['pk'])
        context['bed'] = bed
        
        # 获取或创建二维码
        try:
            qrcode_obj = bed.qrcode
        except QRCode.DoesNotExist:
            qrcode_obj = QRCode.objects.create(bed=bed, name=f"QRCode for bed {bed.number}")
        
        context['qrcode'] = qrcode_obj
        
        # 使用安全URL生成二维码
        qr_data = qrcode_obj.get_secure_evaluation_url()
        
        # 获取打印模板
        template = None
        if bed.department and hasattr(bed.department, 'print_template'):
            template = bed.department.print_template
            
            # 如果科室没有专属模板，尝试获取公共模板
            if template is None:
                from .models import PrintTemplate
                public_template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
                if public_template:
                    template = public_template
        
        # 如果找到模板，使用统一的模板预览生成函数
        if template:
            from .qrcode_utils import generate_template_preview
            preview_data = generate_template_preview(template, qr_data=qr_data)
            context['qr_image'] = preview_data['qr_image_base64']
            context['template_data'] = preview_data['template_data']
        else:
            # 如果没有模板，只生成二维码
            _, img_base64 = generate_qrcode(qr_data, box_size=40, border=0)
            context['qr_image'] = img_base64
            context['template_data'] = None
        
        return context

class AdminAccountCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """用于创建管理账户的视图，仅供超级用户操作"""
    form_class = UserCreationForm
    template_name = "qrmanager/admin_account_form.html"
    success_url = reverse_lazy('qrmanager:dashboard')

    def test_func(self):
        return self.request.user.is_superuser

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录创建管理账户操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建管理账户: {self.object.username}",
            extra_data={
                'is_staff': self.object.is_staff,
                'is_superuser': self.object.is_superuser,
                'is_active': self.object.is_active
            }
        )
        messages.success(self.request, "管理账户创建成功！")
        return response

class AdminAccountPermissionView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """用于展示所有管理用户及其权限分配的视图，仅供超级用户操作"""
    model = User
    template_name = "qrmanager/admin_permissions.html"
    context_object_name = "admin_users"

    def test_func(self):
        return self.request.user.is_superuser

    def get_queryset(self):
        # 显示所有用户，不再限制只显示 is_staff=True 的用户
        queryset = User.objects.all().order_by('-date_joined')
        # 记录查看权限列表操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_admin_permissions",
            description="查看管理员权限列表",
            extra_data={
                'admin_count': queryset.count()
            }
        )
        return queryset

class AdminAccountUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """用于更新单个管理用户权限的视图，仅供超级用户操作"""
    model = User
    fields = ['is_active', 'is_staff', 'is_superuser']
    template_name = "qrmanager/admin_account_update.html"
    success_url = reverse_lazy('qrmanager:admin_permissions')

    def test_func(self):
        return self.request.user.is_superuser

    def form_valid(self, form):
        # 记录更新前的权限状态
        old_instance = self.get_object()
        old_permissions = {
            'is_active': old_instance.is_active,
            'is_staff': old_instance.is_staff,
            'is_superuser': old_instance.is_superuser
        }
        
        response = super().form_valid(form)
        
        # 记录权限变更
        LoggerHelper.log_permission_change(
            user=self.request.user,
            target_user=self.object,
            permissions_before=old_permissions,
            permissions_after={
                'is_active': self.object.is_active,
                'is_staff': self.object.is_staff,
                'is_superuser': self.object.is_superuser
            }
        )
        
        messages.success(self.request, f"用户 {self.object.username} 的权限已更新！")
        return response

class AccountSettingsView(LoginRequiredMixin, UpdateView):
    """用于显示和更新当前用户账号设定的视图"""
    model = User
    fields = ['first_name', 'last_name', 'email']
    template_name = "qrmanager/account_settings.html"
    success_url = reverse_lazy('qrmanager:dashboard')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        # 记录更新前的用户信息
        old_data = {
            'first_name': self.object.first_name,
            'last_name': self.object.last_name,
            'email': self.object.email
        }
        
        response = super().form_valid(form)
        
        # 记录个人信息更新
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description="更新个人信息",
            old_data=old_data,
            new_data={
                'first_name': form.cleaned_data['first_name'],
                'last_name': form.cleaned_data['last_name'],
                'email': form.cleaned_data['email']
            }
        )
        
        messages.success(self.request, "个人信息更新成功！")
        return response

from django.contrib.auth import views as auth_views

class CustomLoginView(auth_views.LoginView):
    """自定义登录视图"""
    template_name = 'qrmanager/login.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录登录成功
        LoggerHelper.log_auth_operation(
            user=form.get_user(),
            operation_type="login",
            status="success",
            extra_data={
                'username': form.get_user().username,
                'ip_address': self.request.META.get('REMOTE_ADDR')
            }
        )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # 记录登录失败
        LoggerHelper.log_auth_operation(
            user=None,
            operation_type="login",
            status="error",
            extra_data={
                'username': form.cleaned_data.get('username'),
                'ip_address': self.request.META.get('REMOTE_ADDR'),
                'error': '用户名或密码错误'
            }
        )
        return response

class CustomLogoutView(auth_views.LogoutView):
    """自定义登出视图"""
    next_page = 'qrmanager:login'

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            # 记录登出操作
            LoggerHelper.log_auth_operation(
                user=request.user,
                operation_type="logout",
                status="success",
                extra_data={
                    'ip_address': request.META.get('REMOTE_ADDR')
                }
            )
        return super().dispatch(request, *args, **kwargs)

class CustomPasswordChangeView(auth_views.PasswordChangeView):
    """自定义密码修改视图"""
    template_name = 'qrmanager/password_change.html'
    success_url = reverse_lazy('qrmanager:dashboard')

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录密码修改成功
        LoggerHelper.log_auth_operation(
            user=self.request.user,
            operation_type="password_change",
            status="success"
        )
        messages.success(self.request, "密码修改成功！")
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # 记录密码修改失败
        LoggerHelper.log_auth_operation(
            user=self.request.user,
            operation_type="password_change",
            status="error",
            extra_data={'errors': form.errors}
        )
        return response

@login_required
def export_sentiment_report(request):
    """导出情感分析报告"""
    try:
        # 创建HTTP响应，设置CSV文件头
        response = HttpResponse(content_type='text/csv')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="sentiment_report_{timestamp}.csv"'
        
        # 创建CSV写入器
        writer = csv.writer(response)
        
        # 写入标题行
        writer.writerow(['评价时间', '科室', '床位号', '工作人员', '评分', '情感倾向', '评价内容'])
        
        # 获取所有评价数据
        evaluations = Evaluation.objects.select_related(
            'qr_code',
            'bed',  # 添加bed关联
            'bed__department',  # 修改为直接使用bed关联
            'staff'
        ).all()
        
        # 写入数据行
        for eval in evaluations:
            writer.writerow([
                eval.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                eval.qr_code.bed.department.name if eval.qr_code.bed.department else '-',
                eval.qr_code.bed.number if eval.qr_code.bed else '-',
                eval.staff.name if eval.staff else '-',
                eval.rating,
                eval.get_sentiment_display(),
                eval.comment
            ])

        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            action="export_sentiment_report",
            description="导出情感分析报告",
            extra_data={
                'filename': f"sentiment_report_{timestamp}.csv",
                'record_count': evaluations.count()
            },
            request=request
        )
        
        return response
    except Exception as e:
        # 记录导出失败
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_sentiment_report_failed",
            description=f"导出情感分析报告失败: {str(e)}",
            status='error',
            extra_data={'error': str(e)}
        )
        messages.error(request, '导出报告失败，请稍后重试。')
        return redirect('qrmanager:sentiment_analysis')

@login_required
def export_evaluations(request):
    """导出评价列表数据为Excel格式"""
    try:
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from datetime import datetime, time
        
        # 创建一个Excel工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "评价数据"
        
        # 定义表头 - 添加满意和不满意工作人员列
        headers = [
            '评价时间', '科室', '床位号', '工作人员', '住院号', '联系电话', 
            '是否满意', '情感倾向', '处理状态', '处理人', '处理时间', 
            '满意工作人员1', '满意工作人员2', '满意工作人员3',
            '不满意工作人员1', '不满意工作人员2', '不满意工作人员3',
            '评价内容', '处理备注'
        ]
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="0071E3", end_color="0071E3", fill_type="solid")
        header_alignment = Alignment(horizontal="center", vertical="center", wrap_text=True)
        thin_border = Border(
            left=Side(style='thin'), 
            right=Side(style='thin'),
            top=Side(style='thin'),
            bottom=Side(style='thin')
        )
        
        # 写入表头
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = thin_border
        
        # 设置列宽
        column_widths = {
            1: 18,  # 评价时间
            2: 12,  # 科室
            3: 10,  # 床位号
            4: 10,  # 工作人员
            5: 15,  # 住院号
            6: 15,  # 联系电话
            7: 10,  # 满意度
            8: 10,  # 情感倾向
            9: 10,  # 处理状态
            10: 12,  # 处理人
            11: 18,  # 处理时间
            12: 15,  # 满意工作人员1
            13: 15,  # 满意工作人员2
            14: 15,  # 满意工作人员3
            15: 15,  # 不满意工作人员1
            16: 15,  # 不满意工作人员2
            17: 15,  # 不满意工作人员3
            18: 50,  # 评价内容
            19: 50,  # 处理备注
        }
        
        for col, width in column_widths.items():
            ws.column_dimensions[ws.cell(row=1, column=col).column_letter].width = width
        
        # 设置数据样式
        wrap_alignment = Alignment(wrap_text=True, vertical="top")
        
        # 获取筛选参数，与EvaluationListView保持一致
        department = request.GET.get('department')
        is_satisfied = request.GET.get('is_satisfied')
        sentiment = request.GET.get('sentiment')
        status = request.GET.get('status')
        date_from = request.GET.get('date_from')
        date_to = request.GET.get('date_to')
        time_from = request.GET.get('time_from')
        time_to = request.GET.get('time_to')
        search = request.GET.get('search')
        
        # 获取评价数据，使用相同的筛选条件
        queryset = Evaluation.objects.select_related(
            'qr_code', 'bed', 'bed__department', 'processed_by'
        ).prefetch_related(
            'staff_evaluations__staff'
        ).order_by('-created_at')
        
        # 应用筛选条件
        if department:
            queryset = queryset.filter(bed__department_id=department)
        if is_satisfied:
            queryset = queryset.filter(is_satisfied=is_satisfied == 'True')
        if sentiment:
            queryset = queryset.filter(sentiment=sentiment)
        if status:
            queryset = queryset.filter(process_status=status)
            
        # 处理日期和时间筛选条件
        from django.utils import timezone
        from datetime import datetime, timedelta, time
        
        if date_from:
            # 默认开始时间设为00:00:00
            start_time = time(0, 0, 0)
            if time_from:
                # 如果提供了时间参数，解析时间
                try:
                    time_parts = time_from.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    second = int(time_parts[2]) if len(time_parts) > 2 else 0
                    start_time = time(hour, minute, second)
                except (ValueError, IndexError):
                    pass
            
            # 结合日期和时间
            try:
                date_obj = datetime.strptime(date_from, '%Y-%m-%d').date()
                datetime_from = timezone.make_aware(datetime.combine(date_obj, start_time))
                queryset = queryset.filter(created_at__gte=datetime_from)
            except ValueError:
                pass
        
        if date_to:
            # 默认结束时间设为23:59:59
            end_time = time(23, 59, 59)
            if time_to:
                # 如果提供了时间参数，解析时间
                try:
                    time_parts = time_to.split(':')
                    hour = int(time_parts[0])
                    minute = int(time_parts[1])
                    second = int(time_parts[2]) if len(time_parts) > 2 else 0
                    end_time = time(hour, minute, second)
                except (ValueError, IndexError):
                    pass
            
            # 结合日期和时间
            try:
                date_obj = datetime.strptime(date_to, '%Y-%m-%d').date()
                datetime_to = timezone.make_aware(datetime.combine(date_obj, end_time))
                queryset = queryset.filter(created_at__lte=datetime_to)
            except ValueError:
                pass
                
        if search:
            queryset = queryset.filter(
                Q(comment__icontains=search) |
                Q(hospital_number__icontains=search) |
                Q(phone_number__icontains=search) |
                Q(bed__number__icontains=search) |
                Q(staff_evaluations__staff__name__icontains=search)
            )
        
        # 写入数据行
        for row_idx, eval in enumerate(queryset, 2):
            # 评价时间
            ws.cell(row=row_idx, column=1, value=eval.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            # 科室
            ws.cell(row=row_idx, column=2, value=eval.bed.department.name if eval.bed and eval.bed.department else "-")
            # 床位号
            ws.cell(row=row_idx, column=3, value=eval.bed.number if eval.bed else "-")
            # 工作人员数量
            staff_count = eval.staff_evaluations.count()
            ws.cell(row=row_idx, column=4, value=f"{staff_count}名工作人员" if staff_count > 0 else "-")
            # 住院号
            ws.cell(row=row_idx, column=5, value=eval.hospital_number or "-")
            # 联系电话
            ws.cell(row=row_idx, column=6, value=eval.phone_number or "-")
            # 是否满意
            ws.cell(row=row_idx, column=7, value="满意" if eval.is_satisfied else "不满意")
            # 情感倾向
            ws.cell(row=row_idx, column=8, value=dict(eval.SENTIMENT_CHOICES).get(eval.sentiment, "-"))
            # 处理状态
            ws.cell(row=row_idx, column=9, value=dict(eval.PROCESS_STATUS_CHOICES).get(eval.process_status, "-"))
            # 处理人
            ws.cell(row=row_idx, column=10, value=eval.processed_by.username if eval.processed_by else "-")
            # 处理时间
            ws.cell(row=row_idx, column=11, value=eval.processed_at.strftime('%Y-%m-%d %H:%M:%S') if eval.processed_at else "-")
            
            # 获取满意和不满意的工作人员
            satisfied_staff = eval.satisfied_staff()
            unsatisfied_staff = eval.unsatisfied_staff()
            
            # 填充满意工作人员 (最多3个)
            for idx, staff_eval in enumerate(satisfied_staff[:3], 0):
                col_idx = 12 + idx
                staff_info = f"{staff_eval.staff.name}"
                if staff_eval.staff.title:
                    staff_info += f" ({staff_eval.staff.title})"
                ws.cell(row=row_idx, column=col_idx, value=staff_info)
                
            # 填充未填满的满意工作人员列
            for i in range(len(satisfied_staff[:3]), 3):
                ws.cell(row=row_idx, column=12+i, value="-")
            
            # 填充不满意工作人员 (最多3个)
            for idx, staff_eval in enumerate(unsatisfied_staff[:3], 0):
                col_idx = 15 + idx
                staff_info = f"{staff_eval.staff.name}"
                if staff_eval.staff.title:
                    staff_info += f" ({staff_eval.staff.title})"
                ws.cell(row=row_idx, column=col_idx, value=staff_info)
                
            # 填充未填满的不满意工作人员列
            for i in range(len(unsatisfied_staff[:3]), 3):
                ws.cell(row=row_idx, column=15+i, value="-")
            
            # 评价内容
            comment_cell = ws.cell(row=row_idx, column=18, value=eval.comment or "")
            comment_cell.alignment = wrap_alignment
            # 处理备注
            notes_cell = ws.cell(row=row_idx, column=19, value=eval.process_notes or "")
            notes_cell.alignment = wrap_alignment
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename=evaluations_{timestamp}.xlsx'
        
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_evaluations",
            description="导出评价列表",
            extra_data={
                'filters': {
                    'department': department,
                    'is_satisfied': is_satisfied,
                    'sentiment': sentiment,
                    'status': status,
                    'date_from': date_from,
                    'date_to': date_to,
                    'time_from': time_from,
                    'time_to': time_to,
                    'search': search
                },
                'record_count': queryset.count(),
                'filename': f"evaluations_{timestamp}.xlsx"
            }
        )
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        # 记录导出失败
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_evaluations_failed",
            description=f"导出评价列表失败: {str(e)}",
            status='error',
            extra_data={'error': str(e)}
        )
        messages.error(request, f'导出评价列表失败: {str(e)}')
        return redirect('qrmanager:evaluation_list')

class OperationLogListView(LoginRequiredMixin, ListView):
    """操作日志列表视图"""
    model = OperationLog
    template_name = 'qrmanager/operation_log_list.html'
    context_object_name = 'logs'
    paginate_by = 20

    def get_queryset(self):
        queryset = OperationLog.objects.select_related('user')

        # 过滤条件
        user_id = self.request.GET.get('user')
        action = self.request.GET.get('action')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        time_range = self.request.GET.get('time_range')

        # 应用过滤器
        if user_id and user_id.isdigit():
            queryset = queryset.filter(user_id=user_id)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if status:
            queryset = queryset.filter(status=status)

        # 日期范围过滤
        if date_from:
            try:
                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        if date_to:
            try:
                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass

        # 时间范围快捷筛选
        if time_range:
            now = timezone.now()
            if time_range == 'today':
                queryset = queryset.filter(created_at__date=now.date())
            elif time_range == 'yesterday':
                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))
            elif time_range == 'this_week':
                # 本周的开始（星期一）
                week_start = now.date() - timezone.timedelta(days=now.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif time_range == 'this_month':
                # 本月的开始（1号）
                month_start = now.date().replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)
            elif time_range == 'last_30_days':
                # 过去30天
                days_30 = now.date() - timezone.timedelta(days=30)
                queryset = queryset.filter(created_at__date__gte=days_30)

        return queryset.order_by('-created_at')


    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        now = timezone.now()
        
        # 统计数据
        context['total_logs'] = OperationLog.objects.count()
        context['today_logs'] = OperationLog.objects.filter(created_at__date=now.date()).count()
        context['active_users'] = User.objects.filter(
            operationlog__created_at__gte=now - timezone.timedelta(days=7)
        ).distinct().count()
        context['error_logs'] = OperationLog.objects.filter(
            status='error',
            created_at__gte=now - timezone.timedelta(days=7)
        ).count()

        # 计算变化百分比
        last_week_total = OperationLog.objects.filter(
            created_at__date__range=[
                now.date() - timezone.timedelta(days=14),
                now.date() - timezone.timedelta(days=7)
            ]
        ).count()
        this_week_total = OperationLog.objects.filter(
            created_at__date__range=[
                now.date() - timezone.timedelta(days=7),
                now.date()
            ]
        ).count()
        
        yesterday_total = OperationLog.objects.filter(
            created_at__date=now.date() - timezone.timedelta(days=1)
        ).count()

        context['total_change_percentage'] = (
            ((this_week_total - last_week_total) / last_week_total * 100)
            if last_week_total > 0 else 0
        )
        context['today_change_percentage'] = (
            ((context['today_logs'] - yesterday_total) / yesterday_total * 100)
            if yesterday_total > 0 else 0
        )

        # 操作趋势数据
        trend_data = OperationLog.objects.filter(
            created_at__gte=now - timezone.timedelta(days=7)
        ).annotate(
            date=TruncDate('created_at')
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')

        trend_dates = [item['date'].strftime('%Y-%m-%d') for item in trend_data]
        trend_counts = [item['count'] for item in trend_data]
        
        context['trend_dates'] = json.dumps(trend_dates)
        context['trend_counts'] = json.dumps(trend_counts)

        # 操作类型分布
        type_distribution = OperationLog.objects.filter(
            created_at__gte=now - timezone.timedelta(days=7)
        ).values('action').annotate(
            value=Count('id')
        ).values('action', 'value')

        context['type_distribution'] = json.dumps([
            {'name': item['action'], 'value': item['value']}
            for item in type_distribution
        ])

        # 活跃用户TOP5
        context['top_users'] = User.objects.filter(
            operationlog__created_at__gte=now - timezone.timedelta(days=7)
        ).annotate(
            operation_count=Count('operationlog'),
            last_operation=Max('operationlog__created_at')
        ).order_by('-operation_count')[:5]

        # 为每个用户添加最常用操作
        for user in context['top_users']:
            most_common = OperationLog.objects.filter(
                user=user,
                created_at__gte=now - timezone.timedelta(days=7)
            ).values('action').annotate(
                count=Count('id')
            ).order_by('-count').first()
            user.most_common_action = most_common['action'] if most_common else '-'

        # 筛选选项
        context['users'] = User.objects.filter(
            operationlog__isnull=False
        ).distinct()
        context['action_types'] = OperationLog.objects.values_list(
            'action', flat=True
        ).distinct()
        context['status_choices'] = OperationLog.STATUS_CHOICES
        context['ip_addresses'] = OperationLog.objects.values_list(
            'ip_address', flat=True
        ).distinct()

        # 错误日志分析
        error_logs = OperationLog.objects.filter(
            status='error',
            created_at__gte=now - timezone.timedelta(days=7)
        )
        context['error_analysis'] = {
            'total': error_logs.count(),
            'by_action': error_logs.values('action').annotate(count=Count('id')),
            'by_user': error_logs.values('user__username').annotate(count=Count('id')),
            'recent': error_logs.order_by('-created_at')[:5]
        }

        return context

    def post(self, request, *args, **kwargs):
        """处理导出日志的请求"""
        action = request.POST.get('action')
        if action == 'export_excel':
            return self.export_excel()
        elif action == 'export_pdf':
            return self.export_pdf()
        return redirect('qrmanager:operation_logs')

    def export_excel(self):
        """导出Excel格式的日志"""
        try:
            response = HttpResponse(content_type='application/ms-excel')
            response['Content-Disposition'] = 'attachment; filename="operation_logs.xlsx"'
            
            # TODO: 实现Excel导出逻辑
            
            # 记录导出操作
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="export_operation_logs",
                description="导出操作日志(Excel格式)",
                status="success"
            )
            
            return response
        except Exception as e:
            messages.error(self.request, f"导出失败: {str(e)}")
            return redirect('qrmanager:operation_logs')

    def export_pdf(self):
        """导出PDF格式的日志"""
        try:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename="operation_logs.pdf"'
            
            # TODO: 实现PDF导出逻辑
            
            # 记录导出操作
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="export_operation_logs",
                description="导出操作日志(PDF格式)",
                status="success"
            )
            
            return response
        except Exception as e:
            messages.error(self.request, f"导出失败: {str(e)}")
            return redirect('qrmanager:operation_logs')

class StaffBulkImportView(LoginRequiredMixin, FormView):
    """批量导入工作人员视图"""
    form_class = BulkStaffImportForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        excel_file = form.cleaned_data['excel_file']
        try:
            import openpyxl
            wb = openpyxl.load_workbook(excel_file)
            ws = wb.active
            
            success_count = 0
            error_count = 0
            error_messages = []
            
            # 验证表头
            headers = [cell.value for cell in ws[1]]
            expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
            
            # 检查表头是否符合要求
            if not all(header in headers for header in expected_headers):
                missing_headers = [h for h in expected_headers if h not in headers]
                error_messages.append(f"表头缺少必要字段: {', '.join(missing_headers)}")
                messages.error(self.request, f"表头格式不正确，缺少必要字段: {', '.join(missing_headers)}")
                return self.form_invalid(form)
            
            # 获取字段索引
            work_number_idx = headers.index('工号')
            name_idx = headers.index('姓名')
            staff_type_idx = headers.index('人员类型')
            title_idx = headers.index('职称')
            department_idx = headers.index('科室')
            
            # 从第二行开始读取数据（跳过表头）
            for row_idx, row in enumerate(ws.iter_rows(min_row=2), 2):
                try:
                    # 获取每列的值
                    work_number = row[work_number_idx].value
                    name = row[name_idx].value
                    staff_type_name = row[staff_type_idx].value
                    title_name = row[title_idx].value
                    department_name = row[department_idx].value
                    
                    # 清理数据（去除前后空格）
                    if work_number: work_number = str(work_number).strip()
                    if name: name = str(name).strip()
                    if staff_type_name: staff_type_name = str(staff_type_name).strip()
                    if title_name: title_name = str(title_name).strip()
                    if department_name: department_name = str(department_name).strip()
                    
                    # 跳过空行（所有字段都为空）
                    if not any([work_number, name, staff_type_name, title_name, department_name]):
                        continue
                    
                    # 验证必填字段
                    if not all([work_number, name, staff_type_name, department_name]):
                        missing_fields = []
                        if not work_number: missing_fields.append('工号')
                        if not name: missing_fields.append('姓名')
                        # 职称不是必填字段
                        if not title_name: missing_fields.append('职称')
                        if not department_name: missing_fields.append('科室')
                        
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 缺少必填字段 {', '.join(missing_fields)}")
                        continue
                    
                    # 获取科室（宽松比较）
                    department = None
                    for dept in Department.objects.all():
                        if dept.name.strip().lower() == department_name.strip().lower():
                            department = dept
                            break
                    
                    if not department:
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 科室 '{department_name}' 不存在")
                        continue
                    
                    # 获取人员类型（宽松比较）
                    dict_staff_type = None
                    for item in DictionaryItem.objects.filter(dictionary__code='staff_type', is_active=True):
                        if item.name.strip().lower() == staff_type_name.strip().lower():
                            dict_staff_type = item
                            break
                    
                    if not dict_staff_type:
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 人员类型 '{staff_type_name}' 不存在或未激活")
                        continue
                    
                    # 从字典项获取对应的StaffType对象
                    try:
                        staff_type = StaffType.objects.get(code=dict_staff_type.code)
                    except StaffType.DoesNotExist:
                        # 如果不存在，则创建一个新的StaffType对象
                        staff_type = StaffType.objects.create(
                            code=dict_staff_type.code,
                            name=dict_staff_type.name,
                            display_order=dict_staff_type.sort_order,
                            is_active=dict_staff_type.is_active
                        )
                    
                    # 获取职称（宽松比较）
                    title = None
                    # 获取职称（宽松比较）- 只在提供了职称时验证
                    title = None
                    if title_name:
                        for item in DictionaryItem.objects.filter(dictionary__code='staff_title', is_active=True):
                            if item.name.strip().lower() == title_name.strip().lower():
                                title = item
                                break
                        
                        if not title:
                            error_count += 1
                            error_messages.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            continue
                    existing_staff = Staff.objects.filter(work_number=work_number).first()
                    if existing_staff:
                        # 更新现有记录
                        existing_staff.name = name
                        existing_staff.staff_type = staff_type
                        existing_staff.title = title
                        existing_staff.department = department
                        existing_staff.save()
                        success_count += 1
                    else:
                        # 创建新记录
                        Staff.objects.create(
                            work_number=work_number,
                            name=name,
                            staff_type=staff_type,
                            title=title,
                            department=department
                        )
                        success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    error_messages.append(f"行 {row_idx}: {str(e)}")
            
            # 记录导入操作
            LoggerHelper.log_model_operation(
                self.request,
                None,
                'bulk_import',
                extra_data={
                    'success_count': success_count,
                    'error_count': error_count,
                    'error_messages': error_messages
                }
            )
            
            if success_count > 0:
                messages.success(self.request, f'成功导入 {success_count} 条记录')
            if error_count > 0:
                messages.warning(self.request, f'导入失败 {error_count} 条记录')
                for msg in error_messages[:5]:  # 只显示前5条错误信息，避免页面过长
                    messages.error(self.request, msg)
                if len(error_messages) > 5:
                    messages.error(self.request, f"还有 {len(error_messages) - 5} 条错误信息未显示")
                    
        except Exception as e:
            messages.error(self.request, f'文件处理失败：{str(e)}')
            return self.form_invalid(form)
            
        return super().form_valid(form)
        
    def get(self, request, *args, **kwargs):
        """如果是GET请求，直接重定向到工作人员列表页面"""
        return redirect('qrmanager:staff_list')
        
    def post(self, request, *args, **kwargs):
        """处理POST请求，支持AJAX验证"""
        # 检查是否是验证请求
        if 'validate' in request.POST:
            try:
                if 'excel_file' not in request.FILES:
                    return JsonResponse({
                        'status': 'error',
                        'message': '未找到上传的文件'
                    })
                
                excel_file = request.FILES['excel_file']
                
                # 验证文件类型
                if not excel_file.name.endswith(('.xlsx', '.xls')):
                    return JsonResponse({
                        'status': 'error',
                        'message': '文件格式不正确，请上传.xlsx或.xls格式的Excel文件'
                    })
                
                # 验证文件大小
                if excel_file.size > 5 * 1024 * 1024:  # 5MB
                    return JsonResponse({
                        'status': 'error',
                        'message': '文件过大，请上传小于5MB的文件'
                    })
                
                # 验证文件内容
                import openpyxl
                wb = openpyxl.load_workbook(excel_file)
                ws = wb.active
                
                # 获取表头
                headers = [cell.value for cell in ws[1]]
                expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
                
                # 检查表头是否符合要求
                if not all(header in headers for header in expected_headers):
                    missing_headers = [h for h in expected_headers if h not in headers]
                    return JsonResponse({
                        'status': 'error',
                        'message': f"表头格式不正确，缺少必要字段: {', '.join(missing_headers)}"
                    })
                
                # 获取字段索引
                work_number_idx = headers.index('工号')
                name_idx = headers.index('姓名')
                staff_type_idx = headers.index('人员类型')
                title_idx = headers.index('职称')
                department_idx = headers.index('科室')
                
                # 获取有效的科室列表
                valid_departments = list(Department.objects.all().values_list('name', flat=True))
                valid_departments_clean = [name.strip() if name else "" for name in valid_departments]
                
                # 获取有效的人员类型和职称
                valid_staff_types = list(DictionaryItem.objects.filter(
                    dictionary__code='staff_type',
                    is_active=True
                ).values_list('name', flat=True))
                valid_staff_types_clean = [name.strip() if name else "" for name in valid_staff_types]
                
                valid_titles = list(DictionaryItem.objects.filter(
                    dictionary__code='staff_title',
                    is_active=True
                ).values_list('name', flat=True))
                valid_titles_clean = [name.strip() if name else "" for name in valid_titles]
                
                # 添加调试日志
                print(f"有效科室列表: {valid_departments}")
                print(f"有效人员类型列表: {valid_staff_types}")
                print(f"有效职称列表: {valid_titles}")
                
                # 验证数据行
                errors = []
                empty_rows = 0
                data_rows = 0
                
                for row_idx, row in enumerate(ws.iter_rows(min_row=2), 2):
                    # 获取每列的值
                    work_number = row[work_number_idx].value
                    name = row[name_idx].value
                    staff_type_name = row[staff_type_idx].value
                    title_name = row[title_idx].value
                    department_name = row[department_idx].value
                    
                    # 清理数据（去除前后空格）
                    if work_number: work_number = str(work_number).strip()
                    if name: name = str(name).strip()
                    if staff_type_name: staff_type_name = str(staff_type_name).strip()
                    if title_name: title_name = str(title_name).strip()
                    if department_name: department_name = str(department_name).strip()
                    
                    # 检查是否为空行（所有字段都为空）
                    if not any([work_number, name, staff_type_name, title_name, department_name]):
                        empty_rows += 1
                        continue
                    
                    # 计数有效数据行
                    data_rows += 1
                    
                    # 验证必填字段
                    if not all([work_number, name, staff_type_name, department_name]):
                        missing_fields = []
                        if not work_number: missing_fields.append('工号')
                        if not staff_type_name: missing_fields.append('人员类型')
                        # 职称不是必填字段
                        if not title_name: missing_fields.append('职称')
                        if not department_name: missing_fields.append('科室')
                        
                        errors.append(f"行 {row_idx}: 缺少必填字段 {', '.join(missing_fields)}")
                        continue
                    
                    # 验证科室是否存在（宽松比较）
                    department_found = False
                    for valid_dept, valid_dept_clean in zip(valid_departments, valid_departments_clean):
                        if department_name.strip().lower() == valid_dept_clean.lower():
                            department_found = True
                            break
                    
                    if not department_found:
                        errors.append(f"行 {row_idx}: 科室 '{department_name}' 不存在，请从下拉列表中选择有效科室")
                        print(f"科室验证失败: '{department_name}' 不在有效列表中")
                    
                    # 验证人员类型是否有效（宽松比较）
                    staff_type_found = False
                    for valid_type, valid_type_clean in zip(valid_staff_types, valid_staff_types_clean):
                        if staff_type_name.strip().lower() == valid_type_clean.lower():
                            staff_type_found = True
                            break
                    
                    if not staff_type_found:
                        errors.append(f"行 {row_idx}: 人员类型 '{staff_type_name}' 不存在或未激活")
                        print(f"人员类型验证失败: '{staff_type_name}' 不在有效列表中")
                    # 验证职称是否有效（宽松比较）- 只在提供了职称时验证
                    if title_name:
                        title_found = False
                        for valid_title, valid_title_clean in zip(valid_titles, valid_titles_clean):
                            if title_name.strip().lower() == valid_title_clean.lower():
                                title_found = True
                                break
                        
                        if not title_found:
                            errors.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            print(f"职称验证失败: '{title_name}' 不在有效列表中")
                            errors.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            print(f"职称验证失败: '{title_name}' 不在有效列表中")
                
                # 获取数据行数（不包括空行）
                print(f"总行数: {ws.max_row - 1}, 有效数据行: {data_rows}, 空行: {empty_rows}")
                
                # 如果有错误，返回错误信息
                if errors:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'文件验证失败，请修正以下错误（共{len(errors)}个错误，{data_rows}行有效数据，{empty_rows}行空行）：',
                        'errors': errors
                    })
                
                # 验证通过
                return JsonResponse({
                    'status': 'success',
                    'message': f'文件验证通过，共{data_rows}行有效数据',
                    'data': {
                        'row_count': data_rows
                    }
                })
                
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': f'文件验证失败: {str(e)}'
                })
        
        # 正常的表单提交
        return super().post(request, *args, **kwargs)

class DownloadStaffTemplateView(LoginRequiredMixin, View):
    """下载工作人员导入模板视图"""
    def get(self, request):
        # 导入必要的模块
        import openpyxl
        from openpyxl import Workbook
        from openpyxl.utils import get_column_letter
        from openpyxl.styles import Font, PatternFill
        from openpyxl.worksheet.datavalidation import DataValidation
        
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "工作人员导入模板"
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="2F75B5", end_color="2F75B5", fill_type="solid")
        
        # 设置表头
        headers = ['工号', '姓名', '人员类型', '职称', '科室']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            # 设置列宽
            ws.column_dimensions[get_column_letter(col)].width = 20
        
        # 添加示例数据（确保示例数据使用系统中存在的科室）
        # 获取第一个有效科室作为示例
        example_department = Department.objects.first()
        example_staff_type = DictionaryItem.objects.filter(dictionary__code='staff_type', is_active=True).first()
        example_title = DictionaryItem.objects.filter(dictionary__code='staff_title', is_active=True).first()
        
        example_data = [
            '001', 
            '张三', 
            example_staff_type.name if example_staff_type else '医生', 
            example_title.name if example_title else '主任医师', 
            example_department.name if example_department else '请选择科室'
        ]
        
        for col, value in enumerate(example_data, 1):
            ws.cell(row=2, column=col, value=value)
        
        # 获取人员类型和职称的有效值
        try:
            # 尝试从StaffType模型获取数据
            from .models import StaffType
            staff_types = [(type.code, type.name) for type in StaffType.objects.filter(is_active=True)]
        except:
            # 如果失败，从DictionaryItem获取
            staff_types = [(item.name, item.name) for item in DictionaryItem.objects.filter(
                dictionary__code='staff_type',
                is_active=True
            )]
        
        titles = [(item.name, item.name) for item in DictionaryItem.objects.filter(
            dictionary__code='staff_title',
            is_active=True
        )]
        
        # 获取所有科室 - 去掉is_active过滤条件
        departments = Department.objects.all().order_by('name')
        department_names = list(departments.values_list('name', flat=True))
        # 确保科室名称不包含特殊符号
        clean_department_names = [name.strip() for name in department_names]
        
        # 设置数据验证 - 科室必须从列表中选择
        dv = DataValidation(type="list", formula1=f'"{",".join(clean_department_names)}"', allow_blank=False)
        dv.error = '请选择有效的科室'
        dv.errorTitle = '科室无效'
        dv.prompt = '请从下拉列表中选择科室'
        dv.promptTitle = '科室选择'
        ws.add_data_validation(dv)
        dv.add(f'E2:E1000')  # 应用到科室列，包括示例行
        
        # 在A1单元格上方添加重要说明
        ws.insert_rows(1)
        ws.merge_cells('A1:E1')
        important_note = ws.cell(row=1, column=1, value="重要提示：请勿删除或修改示例行，只需在下方添加您的数据。空行将被自动忽略。")
        important_note.font = Font(bold=True, color="FF0000", size=12)
        important_note.alignment = openpyxl.styles.Alignment(horizontal='center')
        
        # 在右侧添加说明（从第7列开始）
        note_col = 7
        title_font = Font(bold=True, color="2F75B5", size=12)
        subtitle_font = Font(bold=True, color="2F75B5")
        
        # 添加标题
        ws.cell(row=1, column=note_col, value="导入说明").font = title_font
        ws.column_dimensions[get_column_letter(note_col)].width = 30
        
        # 添加说明内容
        row = 2
        ws.cell(row=row, column=note_col, value="1. 所有字段都为必填项").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=note_col, value="2. 空行将被自动忽略").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=note_col, value="3. 请不要修改表头").font = Font(bold=True)
        row += 2
        
        ws.cell(row=row, column=note_col, value="4. 有效的人员类型：").font = subtitle_font
        row += 1
        for _, name in staff_types:
            ws.cell(row=row, column=note_col, value=f"• {name}")
            row += 1
        row += 1
            
        ws.cell(row=row, column=note_col, value="5. 有效的职称：").font = subtitle_font
        row += 1
        for _, name in titles:
            ws.cell(row=row, column=note_col, value=f"• {name}")
            row += 1
        row += 1
            
        ws.cell(row=row, column=note_col, value="6. 科室必须从下拉列表中选择").font = Font(bold=True, color="FF0000")
        row += 1
        ws.cell(row=row, column=note_col, value="   不允许创建新科室").font = Font(bold=True, color="FF0000")
        row += 2
        
        # 添加所有科室的详细信息
        ws.cell(row=row, column=note_col, value="7. 系统中所有可用科室：").font = subtitle_font
        row += 1
        
        # 直接在主工作表中列出所有科室信息
        for dept in departments:
            dept_info = f"• {dept.name} (编码: {dept.code or '无编码'})"
            ws.cell(row=row, column=note_col, value=dept_info)
            row += 1
            
            # 如果有备注，则缩进显示备注信息
            if dept.remarks:
                ws.cell(row=row, column=note_col, value=f"    备注: {dept.remarks}")
                row += 1
        
        row += 1
        ws.cell(row=row, column=note_col, value=f"   共有 {departments.count()} 个科室").font = Font(color="FF0000")
        row += 2
        
        ws.cell(row=row, column=note_col, value="8. 导入步骤：").font = subtitle_font
        row += 1
        ws.cell(row=row, column=note_col, value="   a. 在示例行下方添加数据")
        row += 1
        ws.cell(row=row, column=note_col, value="   b. 科室必须从下拉列表中选择")
        row += 1
        ws.cell(row=row, column=note_col, value="   c. 保存文件")
        row += 1
        ws.cell(row=row, column=note_col, value="   d. 上传文件进行导入")
        
        # 保存到内存中
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        # 设置响应头
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        # 添加Content-Disposition响应头，指定文件名
        response['Content-Disposition'] = 'attachment; filename="工作人员导入模板.xlsx"'
        
        return response

class DictionaryListView(LoginRequiredMixin, ListView):
    """字典类型列表视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_list.html'
    context_object_name = 'dictionaries'

    def get_queryset(self):
        return Dictionary.objects.all().order_by('code')

class DictionaryCreateView(LoginRequiredMixin, CreateView):
    """创建字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_form.html'
    fields = ['code', 'name', 'description', 'is_active']
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'字典类型 {self.object.name} 创建成功！')
        return response

class DictionaryUpdateView(LoginRequiredMixin, UpdateView):
    """更新字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_form.html'
    fields = ['name', 'description', 'is_active']
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'字典类型 {self.object.name} 更新成功！')
        return response

class DictionaryDeleteView(LoginRequiredMixin, DeleteView):
    """删除字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_confirm_delete.html'
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        try:
            LoggerHelper.log_model_operation(request, self.object, 'delete')
            messages.success(request, f'字典类型 {self.object.name} 删除成功！')
            return super().delete(request, *args, **kwargs)
        except ProtectedError:
            messages.error(request, f'无法删除字典类型 {self.object.name}，因为它正在被使用！')
            return redirect('qrmanager:dictionary_list')

class DictionaryItemListView(LoginRequiredMixin, ListView):
    """字典项列表视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_list.html'
    context_object_name = 'items'

    def get_queryset(self):
        dictionary_id = self.kwargs.get('dictionary_id')
        return DictionaryItem.objects.filter(dictionary_id=dictionary_id).order_by('sort_order', 'code')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        dictionary_id = self.kwargs.get('dictionary_id')
        context['dictionary'] = get_object_or_404(Dictionary, pk=dictionary_id)
        return context

class DictionaryItemCreateView(LoginRequiredMixin, CreateView):
    """创建字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_form.html'
    fields = ['code', 'name', 'sort_order', 'is_active']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        dictionary_id = self.kwargs.get('dictionary_id')
        context['dictionary'] = get_object_or_404(Dictionary, pk=dictionary_id)
        return context

    def form_valid(self, form):
        dictionary_id = self.kwargs.get('dictionary_id')
        form.instance.dictionary_id = dictionary_id
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'字典项 {self.object.name} 创建成功！')
        return response

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class DictionaryItemUpdateView(LoginRequiredMixin, UpdateView):
    """更新字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_form.html'
    fields = ['name', 'sort_order', 'is_active']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dictionary'] = self.object.dictionary
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'字典项 {self.object.name} 更新成功！')
        return response

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class DictionaryItemDeleteView(LoginRequiredMixin, DeleteView):
    """删除字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        try:
            LoggerHelper.log_model_operation(request, self.object, 'delete')
            messages.success(request, f'字典项 {self.object.name} 删除成功！')
            return super().delete(request, *args, **kwargs)
        except ProtectedError:
            messages.error(request, f'无法删除字典项 {self.object.name}，因为它正在被使用！')
            return redirect('qrmanager:dictionary_item_list', dictionary_id=self.object.dictionary_id)

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class ResetUserPasswordView(UserPassesTestMixin, View):
    """超级管理员重置用户密码的视图"""
    
    def test_func(self):
        """检查当前用户是否是超级管理员"""
        return self.request.user.is_authenticated and self.request.user.is_superuser
    
    def handle_no_permission(self):
        """处理无权限的情况"""
        if self.request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
            return JsonResponse({"status": "error", "message": "您没有权限执行此操作！"}, status=403)
        messages.error(self.request, '您没有权限执行此操作！')
        return redirect('qrmanager:admin_permissions')
    
    def post(self, request, pk):
        """处理重置密码的POST请求"""
        # 检查是否为 AJAX 请求，统一将 header 转换为小写进行比较
        is_ajax = request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest'
        
        try:
            # 获取目标用户
            target_user = get_object_or_404(User, pk=pk)
            
            # 获取表单数据
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')
            admin_password = request.POST.get('admin_password')
            
            # 验证超级管理员密码
            if not request.user.check_password(admin_password):
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '您的管理员密码验证失败，无法执行此操作！'
                    })
                messages.error(request, '您的管理员密码验证失败，无法执行此操作！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证新密码
            if new_password != confirm_password:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '两次输入的密码不一致！'
                    })
                messages.error(request, '两次输入的密码不一致！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证密码长度
            if len(new_password) < 8:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '密码长度必须至少8位！'
                    })
                messages.error(request, '密码长度必须至少8位！')
                return redirect('qrmanager:admin_permissions')
            
            # 不允许重置自己的密码
            if target_user == request.user:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能通过此方式重置自己的密码！'
                    })
                messages.error(request, '不能通过此方式重置自己的密码！')
                return redirect('qrmanager:admin_permissions')
                
            # 设置新密码
            target_user.set_password(new_password)
            target_user.save()
            
            # 尝试记录操作日志，如果失败则捕获但不阻止密码重置
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action='重置密码',
                    description=f'重置用户 {target_user.username} 的密码'
                )
            except Exception as log_exc:
                print('OperationLog creation error:', log_exc)
            
            if is_ajax:
                return JsonResponse({
                    'status': 'success',
                    'message': f'已成功重置用户 {target_user.username} 的密码！'
                })
            
            messages.success(request, f'已成功重置用户 {target_user.username} 的密码！')
            return redirect('qrmanager:admin_permissions')
            
        except User.DoesNotExist:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到指定用户！'
                }, status=404)
            messages.error(request, '未找到指定用户！')
            return redirect('qrmanager:admin_permissions')
            
        except Exception as e:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': f'重置密码时发生错误：{str(e)}'
                }, status=500)
            messages.error(request, f'重置密码时发生错误：{str(e)}')
            return redirect('qrmanager:admin_permissions')

class DeleteUserView(UserPassesTestMixin, View):
    """超级管理员删除用户的视图"""
    
    def test_func(self):
        """检查当前用户是否是超级管理员"""
        return self.request.user.is_authenticated and self.request.user.is_superuser
    
    def handle_no_permission(self):
        """处理无权限的情况"""
        if self.request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
            return JsonResponse({"status": "error", "message": "您没有权限执行此操作！"}, status=403)
        messages.error(self.request, '您没有权限执行此操作！')
        return redirect('qrmanager:admin_permissions')
    
    def post(self, request, pk):
        """处理删除用户的POST请求"""
        # 检查是否为 AJAX 请求
        is_ajax = request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest'
        
        try:
            # 获取目标用户
            target_user = get_object_or_404(User, pk=pk)
            
            # 不允许删除超级管理员
            if target_user.is_superuser:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能删除超级管理员用户！'
                    })
                messages.error(request, '不能删除超级管理员用户！')
                return redirect('qrmanager:admin_permissions')
            
            # 不允许删除自己
            if target_user == request.user:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能删除自己的账户！'
                    })
                messages.error(request, '不能删除自己的账户！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证超级管理员密码
            admin_password = request.POST.get('admin_password')
            if not request.user.check_password(admin_password):
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '管理员密码验证失败！'
                    })
                messages.error(request, '管理员密码验证失败！')
                return redirect('qrmanager:admin_permissions')
            
            # 记录要删除的用户信息
            user_info = {
                'username': target_user.username,
                'is_staff': target_user.is_staff,
                'is_active': target_user.is_active,
                'last_login': target_user.last_login.isoformat() if target_user.last_login else None
            }
            
            # 删除用户
            target_user.delete()
            
            # 记录删除操作
            LoggerHelper.log_operation(
            user=request.user,
            request=request,
                action="delete_user",
                description=f"删除用户: {user_info['username']}",
                status="success",
                extra_data=user_info
            )
            
            if is_ajax:
                return JsonResponse({
                    'status': 'success',
                    'message': f'用户 {user_info["username"]} 已成功删除！'
                })
            
            messages.success(request, f'用户 {user_info["username"]} 已成功删除！')
            return redirect('qrmanager:admin_permissions')
            
        except User.DoesNotExist:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到指定用户！'
                }, status=404)
            messages.error(request, '未找到指定用户！')
            return redirect('qrmanager:admin_permissions')
            
        except Exception as e:
            # 记录错误
            LoggerHelper.log_operation(
            user=request.user,
            request=request,
                action="delete_user_failed",
                description=f"删除用户失败: {str(e)}",
                status="error",
                extra_data={'error': str(e)}
            )
            
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': f'删除用户时发生错误：{str(e)}'
                }, status=500)
            messages.error(request, f'删除用户时发生错误：{str(e)}')
            return redirect('qrmanager:admin_permissions')

# 添加自定义的 CSRF 失败处理视图

def csrf_failure(request, reason=""):
    """处理 CSRF 验证失败的请求，如果是 AJAX 请求则返回 JSON 否则返回标准错误页面"""
    if request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
        return JsonResponse({"status": "error", "message": "CSRF验证失败，请刷新页面后重试！"}, status=403)
    # 如果不是AJAX请求，可以渲染一个标准的错误页面（需要创建csrf_failure.html模板）
    return render(request, 'csrf_failure.html', {'reason': reason})

class QRRegenerateView(LoginRequiredMixin, View):
    """重新生成二维码视图，用于更换床位的二维码"""
    login_url = '/login/'

    def post(self, request, pk):
        qr_code = get_object_or_404(QRCode, pk=pk)
        try:
            # 获取更换原因和安全标记
            reason = request.POST.get('reason', '')
            is_security_issue = request.POST.get('is_security_issue') == 'on'
            
            # 记录旧二维码信息
            old_code = qr_code.code
            
            # 更新二维码的 code 字段
            qr_code.code = uuid.uuid4()
            qr_code.save()
            
            # 使用统一的二维码生成函数
            from .qrcode_utils import generate_qrcode
            # 生成二维码内容（评价URL）
            data = qr_code.get_evaluation_url()
            # 不需要保存图片，因为图片会在需要时动态生成
            
            # 创建二维码历史记录
            if qr_code.bed:
                QRCodeHistory.objects.create(
                    bed=qr_code.bed,
                    old_code=old_code,
                    new_code=qr_code.code,
                    reason=reason,
                    created_by=request.user,
                    is_security_issue=is_security_issue
                )
            
            # 记录重新生成操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=qr_code,
                operation_type="regenerate",
                description=f"重新生成二维码: {qr_code.name}",
                old_data={'code': str(old_code)},
                new_data={
                    'code': str(qr_code.code),
                    'reason': reason,
                    'is_security_issue': is_security_issue
                }
            )
            
            messages.success(request, "二维码已成功重新生成！")
        except Exception as e:
            # 记录重新生成失败
            LoggerHelper.log_operation(
                user=request.user,
                request=request,
                action="regenerate_qrcode_failed",
                description=f"重新生成二维码失败: {str(e)}",
                status='error',
                extra_data={
                    'qrcode_id': pk,
                    'error': str(e)
                }
            )
            messages.error(request, f"重新生成二维码失败: {str(e)}")
        
        # 重定向回二维码列表页面
        return redirect('qrmanager:qrcode_list')

class PrintTemplateCreateView(LoginRequiredMixin, CreateView):
    """创建打印模板视图"""
    model = PrintTemplate
    form_class = PrintTemplateForm
    template_name = 'qrmanager/print_template_form.html'

    def get_success_url(self):
        return reverse_lazy('qrmanager:print_template_list')

    def get_initial(self):
        initial = super().get_initial()
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                initial['name'] = f"{department.name}打印模板"
            except Department.DoesNotExist:
                pass
        else:
            initial['name'] = "公共打印模板"
            initial['is_public'] = True
        return initial

    def form_valid(self, form):
        # 设置科室（如果有）
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                form.instance.department = department
                form.instance.is_public = False
            except Department.DoesNotExist:
                messages.error(self.request, '未找到指定科室')
                return self.form_invalid(form)
        else:
            # 创建公共模板
            form.instance.is_public = True
            form.instance.department = None
        
        # 检查是否已存在该科室的模板文件，如果存在则清理
        try:
            import os
            from django.conf import settings
            
            # 确定文件名前缀
            department_code = form.instance.department.code if form.instance.department else 'public'
            file_prefix = f"template_{department_code}"
            
            # 检查media/print_templates目录下是否有同名文件
            template_dir = os.path.join(settings.MEDIA_ROOT, 'print_templates')
            if os.path.exists(template_dir):
                for filename in os.listdir(template_dir):
                    # 如果找到同一科室的旧模板文件（以相同前缀开头），则删除
                    if filename.startswith(file_prefix) and filename != os.path.basename(form.instance.background_image.name):
                        file_path = os.path.join(template_dir, filename)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            # 记录删除操作
                            LoggerHelper.log_operation(
                                user=self.request.user,
                                request=self.request,
                                action="delete_template_file",
                                description=f"创建新模板时删除旧模板文件: {filename}"
                            )
        except Exception as e:
            # 记录错误但不中断流程
            LoggerHelper.log_operation(
                user=self.request.user,
                request=self.request,
                action="delete_template_file_error",
                description=f"删除旧模板文件失败: {str(e)}"
            )
        
        # 保存模板
        response = super().form_valid(form)
        
        # 记录创建操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建{'公共' if self.object.is_public else '科室'}打印模板: {self.object.name}"
        )
        
        messages.success(self.request, '打印模板创建成功！')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                context['department'] = Department.objects.get(pk=department_id)
            except Department.DoesNotExist:
                pass
        return context

class PrintTemplateUpdateView(LoginRequiredMixin, UpdateView):
    """更新打印模板视图"""
    model = PrintTemplate
    form_class = PrintTemplateForm
    template_name = 'qrmanager/print_template_form.html'

    def get_success_url(self):
        return reverse_lazy('qrmanager:print_template_list')

    def form_valid(self, form):
        # 记录更新前的数据
        old_data = {
            'name': self.object.name,
            'qr_position_x': self.object.qr_position_x,
            'qr_position_y': self.object.qr_position_y,
            'qr_size': self.object.qr_size,
            'is_active': self.object.is_active
        }
        
        # 如果上传了新的背景图片，清理旧的模板文件
        if 'background_image' in form.changed_data and self.object.background_image:
            try:
                # 获取旧文件路径
                old_file_path = self.object.background_image.path
                # 保存旧文件名，用于日志记录
                old_file_name = os.path.basename(old_file_path)
                
                # 删除旧文件
                import os
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)
                    
                # 记录删除操作
                LoggerHelper.log_operation(
                    user=self.request.user,
                    request=self.request,
                    action="delete_template_file",
                    description=f"删除旧模板文件: {old_file_name}"
                )
            except Exception as e:
                # 记录错误但不中断流程
                LoggerHelper.log_operation(
                    user=self.request.user,
                    request=self.request,
                    action="delete_template_file_error",
                    description=f"删除旧模板文件失败: {str(e)}"
                )
        
        response = super().form_valid(form)
        
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新打印模板: {self.object.name}",
            old_data=old_data
        )
        
        messages.success(self.request, '打印模板更新成功！')
        return response

class PrintTemplatePreviewView(LoginRequiredMixin, DetailView):
    """打印模板预览视图"""
    model = PrintTemplate
    template_name = 'qrmanager/print_template_preview.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        template = self.get_object()
        
        # 使用统一的模板预览生成函数
        from .qrcode_utils import generate_template_preview
        preview_data = generate_template_preview(template)
        
        # 将二维码图像添加到上下文
        context['qr_image'] = preview_data['qr_image_base64']
        
        return context

@login_required
def api_departments(request):
    """科室 API 接口"""
    departments = Department.objects.all().order_by('code')
    data = [{
        'id': dept.id,
        'code': dept.code,
        'name': dept.name,
        'staff_count': dept.staff.count(),
        'bed_count': dept.bed_set.count()
    } for dept in departments]
    return JsonResponse({'departments': data})

@login_required
def api_beds(request):
    """床位 API 接口"""
    department_id = request.GET.get('department')
    # 只选择活跃的床位
    beds = Bed.objects.select_related('department').filter(is_active=True)
    if department_id:
        beds = beds.filter(department_id=department_id)
    data = [{
        'id': bed.id,
        'number': bed.number,
        'department': {
            'id': bed.department.id,
            'name': bed.department.name
        } if bed.department else None,
        'area': bed.get_area_display(),
        'staff': {
            'id': bed.staff.id,
            'name': bed.staff.name
        } if bed.staff else None
    } for bed in beds]
    return JsonResponse({'beds': data})

@login_required
def api_staff(request):
    """工作人员 API 接口"""
    department_id = request.GET.get('department')
    staff = Staff.objects.select_related('department', 'staff_type', 'title')
    if department_id:
        staff = staff.filter(department_id=department_id)
    data = [{
        'id': s.id,
        'work_number': s.work_number,
        'name': s.name,
        'department': {
            'id': s.department.id,
            'name': s.department.name
        } if s.department else None,
        'staff_type': {
            'id': s.staff_type.id,
            'name': s.staff_type.name
        } if s.staff_type else None,
        'title': {
            'id': s.title.id,
            'name': s.title.name
        } if s.title else None
    } for s in staff]
    return JsonResponse({'staff': data})

@login_required
def api_qrcodes(request):
    """二维码 API 接口"""
    department_id = request.GET.get('department')
    qrcodes = QRCode.objects.select_related(
        'bed',
        'bed__department',
        'bed__staff'
    ).prefetch_related('evaluations')
    if department_id:
        qrcodes = qrcodes.filter(bed__department_id=department_id)
    data = [{
        'id': qr.id,
        'code': str(qr.code),
        'bed': {
            'id': qr.bed.id,
            'number': qr.bed.number,
            'department': {
                'id': qr.bed.department.id,
                'name': qr.bed.department.name
            } if qr.bed.department else None
        } if qr.bed else None,
        'evaluation_count': qr.evaluations.count(),
        'satisfaction_rate': qr.evaluations.filter(is_satisfied=True).count() / qr.evaluations.count() * 100 if qr.evaluations.count() > 0 else 0
    } for qr in qrcodes]
    return JsonResponse({'qrcodes': data})

@login_required
def api_evaluations(request):
    """评价 API 接口"""
    department_id = request.GET.get('department')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    evaluations = Evaluation.objects.select_related(
        'qr_code',
        'bed',
        'bed__department'
    )
    
    if department_id:
        evaluations = evaluations.filter(bed__department_id=department_id)
    if date_from:
        evaluations = evaluations.filter(created_at__date__gte=date_from)
    if date_to:
        evaluations = evaluations.filter(created_at__date__lte=date_to)
        
    data = [{
        'id': e.id,
        'rating': e.rating,
        'comment': e.comment,
        'sentiment': e.sentiment,
        'created_at': e.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'qr_code': {
            'id': e.qr_code.id if e.qr_code else None,
            'code': str(e.qr_code.code) if e.qr_code else None,
        },
        'bed': {
            'id': e.bed.id,
            'number': e.bed.number,
            'department': {
                'id': e.bed.department.id if e.bed.department else None,
                'name': e.bed.department.name if e.bed.department else None
            } if e.bed.department else None
        } if e.bed else None,
        'staff': {
            'id': e.staff.id,
            'name': e.staff.name
        } if e.staff else None
    } for e in evaluations]
    
    return JsonResponse({'evaluations': data})

@login_required
def import_departments(request):
    """导入科室数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('科室编码') or not row.get('科室名称'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码和科室名称为必填项')
                    continue
                    
                # 检查科室编码是否已存在
                if Department.objects.filter(code=row['科室编码']).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 已存在')
                    continue
                    
                # 创建科室
                department = Department.objects.create(
                    code=row['科室编码'],
                    name=row['科室名称'],
                    remarks=row.get('备注', '')
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=department,
                    operation_type="create",
                    description=f"导入创建科室: {department.name}",
                    extra_data={
                        'code': department.code,
                        'name': department.name,
                        'remarks': department.remarks,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_departments(request):
    """导出科室数据"""
    try:
        # 创建一个 Excel 工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "科室信息"
        
        # 写入表头
        headers = ['科室编码', '科室名称', '工作人员数', '床位数', '二维码数', '评价数', '创建时间', '更新时间', '备注']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 获取所有科室数据
        departments = Department.objects.annotate(
            staff_count=Count('staff', distinct=True),
            bed_count=Count('bed', distinct=True),
            qrcode_count=Count('bed__qrcode', distinct=True),
            evaluation_count=Count('bed__qrcode__evaluations', distinct=True)
        ).order_by('code', 'name')
        
        # 写入数据
        for row, dept in enumerate(departments, 2):
            ws.cell(row=row, column=1, value=dept.code)
            ws.cell(row=row, column=2, value=dept.name)
            ws.cell(row=row, column=3, value=dept.staff_count)
            ws.cell(row=row, column=4, value=dept.bed_count)
            ws.cell(row=row, column=5, value=dept.qrcode_count)
            ws.cell(row=row, column=6, value=dept.evaluation_count)
            ws.cell(row=row, column=7, value=dept.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=8, value=dept.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=9, value=dept.remarks)
        
        # 设置列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=departments.xlsx'
        
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_departments",
            description=f"导出科室数据，共 {departments.count()} 条记录",
            extra_data={
                'total_count': departments.count(),
                'with_staff': sum(1 for dept in departments if dept.staff_count > 0),
                'with_beds': sum(1 for dept in departments if dept.bed_count > 0)
            }
        )
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:department_list')

@login_required
def import_beds(request):
    """导入床位数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 检查床位号是否已存在
                if Bed.objects.filter(number=row['床位号'], department=department).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号 {row["床位号"]} 在科室 {department.name} 中已存在')
                    continue
                    
                # 获取工作人员（如果有）
                staff = None
                if row.get('工作人员工号'):
                    try:
                        staff = Staff.objects.get(work_number=row['工作人员工号'])
                    except Staff.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 工作人员工号 {row["工作人员工号"]} 不存在')
                        continue
                    
                # 创建床位
                bed = Bed.objects.create(
                    number=row['床位号'],
                    department=department,
                    area=row.get('区域', ''),
                    staff=staff
                )
                
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=bed,
                    operation_type="create",
                    description=f"导入创建床位: {bed.number}",
                    extra_data={
                        'department': department.name,
                        'area': bed.area,
                        'staff': staff.name if staff else None,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_beds(request):
    """导出床位数据"""
    try:
        # 创建一个 Excel 工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "床位信息"
        
        # 写入表头
        headers = [
            '床位号', '科室编码', '科室名称', '区域', 
            '工作人员工号', '工作人员姓名', '二维码ID', '评价数', 
            '床位创建时间', '床位更新时间', 
            '二维码创建时间', '二维码更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            # 设置表头样式
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
        
        # 获取所有床位数据
        beds_query = Bed.objects.select_related(
            'department',
            'qrcode'
        ).annotate(
            evaluation_count=Count('qrcode__evaluations', distinct=True)
        )
        
        # 检查是否有科室参数
        department_id = request.GET.get('department')
        if department_id:
            try:
                department_id = int(department_id)
                beds_query = beds_query.filter(department_id=department_id)
                
                # 获取科室名称添加到文件名
                department_name = ""
                try:
                    department = Department.objects.get(pk=department_id)
                    department_name = f"_{department.name}"
                except:
                    pass
                
                # 创建响应
                response = HttpResponse(
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename=beds{department_name}.xlsx'
            except ValueError:
                # 如果科室ID不是有效整数，忽略筛选
                pass
        else:
            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename=beds_all.xlsx'
        
        # 在排序前先获取总数
        beds_count = beds_query.count()
        
        # 获取最终排序的床位列表
        if department_id:
            # 如果是特定科室，先按简单排序获取数据
            beds = beds_query.order_by('number')
            
            # 使用与BedListView相同的自然排序逻辑
            import re
            def natural_sort_key(bed):
                # 检查床位号是否以数字开头
                if bed.number and bed.number[0].isdigit():
                    # 数字开头的床位，排在前面（优先级0）
                    prefix = 0
                else:
                    # 非数字开头的床位，排在后面（优先级1）
                    prefix = 1
                
                # 提取床位号中的数字和非数字部分
                convert = lambda text: int(text) if text.isdigit() else text.lower()
                # 优先级作为第一个排序键，然后按自然排序
                return [prefix] + [convert(c) for c in re.split('([0-9]+)', bed.number)]
            
            # 应用自然排序
            beds = sorted(beds, key=natural_sort_key)
        else:
            # 如果是所有科室，先按科室再按床位号排序
            beds = beds_query.order_by('department__name', 'number')
        
        # 写入数据
        for row, bed in enumerate(beds, 2):
            # 基本信息
            ws.cell(row=row, column=1, value=bed.number)
            ws.cell(row=row, column=2, value=bed.department.code if bed.department else '')
            ws.cell(row=row, column=3, value=bed.department.name if bed.department else '')
            ws.cell(row=row, column=4, value=bed.get_area_display())
            ws.cell(row=row, column=5, value=bed.staff.work_number if bed.staff else '')
            ws.cell(row=row, column=6, value=bed.staff.name if bed.staff else '')
            ws.cell(row=row, column=7, value=str(bed.qrcode.code) if hasattr(bed, 'qrcode') else '')
            ws.cell(row=row, column=8, value=bed.evaluation_count)
            
            # 床位创建和更新时间（直接从床位模型获取）
            if bed.created_at:
                ws.cell(row=row, column=9, value=bed.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=9, value='未知')
                
            if bed.updated_at:
                ws.cell(row=row, column=10, value=bed.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=10, value='未更新')
            
            # 二维码创建和更新时间
            if hasattr(bed, 'qrcode') and bed.qrcode:
                ws.cell(row=row, column=11, value=bed.qrcode.created_at.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row, column=12, value=bed.qrcode.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=11, value='无二维码')
                ws.cell(row=row, column=12, value='无二维码')
        
        # 设置列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        # 设置条件格式（便于区分）
        for row in range(2, len(beds) + 2):
            # 设置床位信息部分和二维码信息部分的颜色区分
            for col in range(9, 11):  # 床位时间列
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color="E6F2FF", end_color="E6F2FF", fill_type="solid")
            
            for col in range(11, 13):  # 二维码时间列
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color="F2E6FF", end_color="F2E6FF", fill_type="solid")
        
        # 记录导出操作
        description = f"导出床位数据，共 {beds_count} 条记录"
        if department_id:
            description += f"（科室ID: {department_id}）"
            
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_beds",
            description=description,
            extra_data={
                'total_count': beds_count,
                'department_id': department_id if department_id else None,
                'with_staff': sum(1 for bed in beds if bed.staff),
                'with_qrcode': sum(1 for bed in beds if hasattr(bed, 'qrcode')),
                'with_evaluations': sum(1 for bed in beds if bed.evaluation_count > 0)
            }
        )
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        # 如果有科室参数，返回到相应科室的床位列表
        department_id = request.GET.get('department')
        if department_id:
            return redirect(f"{reverse('qrmanager:bed_list')}?department={department_id}")
        return redirect('qrmanager:bed_list')

@login_required
def import_staff(request):
    """导入工作人员数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('工号') or not row.get('姓名') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 工号、姓名和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 检查工号是否已存在
                if Staff.objects.filter(work_number=row['工号']).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 工号 {row["工号"]} 已存在')
                    continue
                    
                # 获取人员类型（如果有）
                staff_type = None
                if row.get('人员类型'):
                    try:
                        staff_type = DictionaryItem.objects.get(
                            dictionary__code='staff_type',
                            name=row['人员类型']
                        )
                    except DictionaryItem.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 人员类型 {row["人员类型"]} 不存在')
                        continue
                        
                # 获取职称（如果有）
                title = None
                if row.get('职称'):
                    try:
                        title = DictionaryItem.objects.get(
                            dictionary__code='staff_title',
                            name=row['职称']
                        )
                    except DictionaryItem.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 职称 {row["职称"]} 不存在')
                        continue
                    
                # 创建工作人员
                staff = Staff.objects.create(
                    work_number=row['工号'],
                    name=row['姓名'],
                    department=department,
                    staff_type=staff_type,
                    title=title
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=staff,
                    operation_type="create",
                    description=f"导入创建工作人员: {staff.name}",
                    extra_data={
                        'work_number': staff.work_number,
                        'department': department.name,
                        'staff_type': staff_type.name if staff_type else None,
                        'title': title.name if title else None,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_staff(request):
    """导出工作人员数据"""
    try:
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "工作人员数据"
        
        # 设置表头
        headers = [
            '工号', '姓名', '科室编码', '科室名称', '人员类型', '职称',
            '创建时间', '更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            
        # 获取所有工作人员数据
        staff_list = Staff.objects.select_related(
            'department', 'staff_type', 'title'
        ).all()
        
        # 写入数据
        for row, staff in enumerate(staff_list, 2):
            ws.cell(row=row, column=1, value=staff.work_number)
            ws.cell(row=row, column=2, value=staff.name)
            ws.cell(row=row, column=3, value=staff.department.code if staff.department else '')
            ws.cell(row=row, column=4, value=staff.department.name if staff.department else '')
            ws.cell(row=row, column=5, value=staff.staff_type.name if staff.staff_type else '')
            ws.cell(row=row, column=6, value=staff.title.name if staff.title else '')
            ws.cell(row=row, column=7, value=staff.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=8, value=staff.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            
        # 调整列宽
        for col in range(1, len(headers) + 1):
            max_length = 0
            column = get_column_letter(col)
            
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
                    
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
            
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            operation_type="export",
            target_type="Staff",
            description=f"导出工作人员数据：{staff_list.count()} 条记录",
            extra_data={
                'count': staff_list.count(),
                'format': 'xlsx'
            }
        )
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=staff_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:staff_list')

@login_required
def import_qrcodes(request):
    """导入二维码数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 获取或创建床位
                bed, created = Bed.objects.get_or_create(
                    number=row['床位号'],
                    department=department,
                    defaults={
                        'area': row.get('区域', '')
                    }
                )
                
                # 检查床位是否已有二维码
                if hasattr(bed, 'qrcode'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位 {bed.number} 已有二维码')
                    continue
                    
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=qrcode,
                    operation_type="create",
                    description=f"导入创建二维码: {qrcode.name}",
                    extra_data={
                        'bed': bed.number,
                        'department': department.name,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_qrcodes(request):
    """导出二维码数据"""
    try:
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "二维码数据"
        
        # 设置表头
        headers = [
            '二维码编号', '床位号', '科室编码', '科室名称', '区域',
            '评价数量', '平均评分', '创建时间', '更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            
        # 获取所有二维码数据
        qrcodes = QRCode.objects.select_related(
            'bed',
            'bed__department'
        ).prefetch_related(
            'evaluations'
        ).all()
        
        # 写入数据
        for row, qr in enumerate(qrcodes, 2):
            evaluations = list(qr.evaluations.all())
            evaluation_count = len(evaluations)
            avg_rating = sum(e.rating for e in evaluations) / evaluation_count if evaluation_count > 0 else 0
            
            ws.cell(row=row, column=1, value=str(qr.code))
            ws.cell(row=row, column=2, value=qr.bed.number if qr.bed else '')
            ws.cell(row=row, column=3, value=qr.bed.department.code if qr.bed and qr.bed.department else '')
            ws.cell(row=row, column=4, value=qr.bed.department.name if qr.bed and qr.bed.department else '')
            ws.cell(row=row, column=5, value=qr.bed.get_area_display() if qr.bed else '')
            ws.cell(row=row, column=6, value=evaluation_count)
            ws.cell(row=row, column=7, value=round(avg_rating, 2))
            ws.cell(row=row, column=8, value=qr.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=9, value=qr.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            
        # 调整列宽
        for col in range(1, len(headers) + 1):
            max_length = 0
            column = get_column_letter(col)
            
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
                    
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
            
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_qrcodes",
            description=f"导出二维码数据：{qrcodes.count()} 条记录",
            extra_data={
                'count': qrcodes.count(),
                'with_evaluations': sum(1 for qr in qrcodes if qr.evaluations.exists())
            }
        )
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=qrcodes_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:qrcode_list')

@login_required
def print_department_qrcodes(request, department_id):
    """按科室批量打印二维码"""
    try:
        # 获取科室
        department = get_object_or_404(Department, id=department_id)
        
        # 获取打印模板ID
        template_id = request.GET.get('template_id')
        if not template_id:
            messages.warning(request, '请选择打印模板')
            return redirect('qrmanager:qrcode_list')
        
        # 获取打印份数
        copies = request.GET.get('copies', '1')
        try:
            copies = int(copies)
            if copies < 1:
                copies = 1
        except ValueError:
            copies = 1
        
        # 获取区域过滤条件
        area_filter = request.GET.get('area')
        
        # 获取科室下的所有床位
        beds_query = Bed.objects.filter(department=department, is_active=True)
        if area_filter:
            beds_query = beds_query.filter(area=area_filter)
        
        beds = beds_query.select_related('staff').all()
        
        # 获取床位对应的二维码
        qrcodes = []
        for bed in beds:
            qrcode = QRCode.objects.filter(bed=bed).first()
            if qrcode:
                qrcodes.append(qrcode)
        
        if not qrcodes:
            messages.warning(request, f'未找到{department.name}科室的二维码')
            return redirect('qrmanager:qrcode_list')
        
        # 获取打印模板
        print_template = get_object_or_404(PrintTemplate, id=template_id)
        
        # 创建PDF文档
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename={department.name}_qrcodes.pdf'
        
        # 创建PDF文档
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        import tempfile
        from PIL import Image
        import io
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            pdf_path = tmp.name
        
        # 创建PDF
        c = canvas.Canvas(pdf_path, pagesize=A4)
        width, height = A4  # 获取页面尺寸
        
        # 加载模板背景图片
        if print_template.background_image:
            try:
                # 获取背景图片的完整路径
                background_image_path = print_template.background_image.path
                
                # 打开背景图片，保持原始质量
                background_img = Image.open(background_image_path)
                
                # 记录日志
                logger.info(f"加载模板背景图片: {background_image_path}, 尺寸: {background_img.size}, 格式: {background_img.format}")
                
                # 计算每页放置的二维码数量
                qrcodes_per_page = 1  # 使用模板时，每页只放一个二维码
                
                for i, qr in enumerate(qrcodes):
                    # 如果是新页面，则添加新页
                    if i > 0:
                        c.showPage()
                    
                    # 绘制背景图片，保持原始尺寸和质量
                    # 将背景图片保存为临时文件
                    temp_bg_path = f"temp_bg_{i}.{background_img.format.lower()}"
                    background_img.save(temp_bg_path, format=background_img.format, quality=100)
                    
                    # 在PDF中绘制背景图片，使用原始尺寸
                    c.drawImage(temp_bg_path, 0, 0, width, height, preserveAspectRatio=True)
                    
                    # 使用安全URL生成二维码
                    qr_img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                    
                    # 计算二维码在模板中的位置（毫米转换为点）
                    mm_to_pt = 72 / 25.4  # 1毫米 = 72/25.4点
                    qr_x = print_template.qr_position_x * mm_to_pt
                    qr_y = height - (print_template.qr_position_y * mm_to_pt) - (print_template.qr_size * mm_to_pt)
                    qr_size_pt = print_template.qr_size * mm_to_pt
                    
                    # 保存二维码为临时图片文件
                    temp_qr_path = f"temp_qr_{qr.id}.png"
                    qr_img.save(temp_qr_path, format="PNG", quality=100)
                    
                    # 在PDF中绘制二维码
                    c.drawImage(temp_qr_path, qr_x, qr_y, qr_size_pt, qr_size_pt)
                    
                    # 添加床位信息（可选）
                    if qr.bed:
                        c.setFont("Helvetica", 8)
                        info_text = f"床位: {qr.bed.number} | 科室: {department.name}"
                        if qr.bed.staff:
                            info_text += f" | 负责人: {qr.bed.staff.name}"
                        if hasattr(qr.bed, 'get_area_display') and qr.bed.get_area_display():
                            info_text += f" | 区域: {qr.bed.get_area_display()}"
                        
                        # 在二维码下方添加信息
                        c.drawString(qr_x, qr_y - 10, info_text)
                    
                    # 删除临时图片
                    os.remove(temp_qr_path)
                    os.remove(temp_bg_path)
                
                # 为每份复制创建额外的页面
                if copies > 1:
                    # 保存当前页面
                    c.showPage()
                    
                    # 复制所有页面
                    for _ in range(copies - 1):
                        for i, qr in enumerate(qrcodes):
                            # 如果是新页面，则添加新页
                            if i > 0 or _ > 0:
                                c.showPage()
                            
                            # 重新绘制背景图片
                            temp_bg_path = f"temp_bg_copy_{i}.{background_img.format.lower()}"
                            background_img.save(temp_bg_path, format=background_img.format, quality=100)
                            c.drawImage(temp_bg_path, 0, 0, width, height, preserveAspectRatio=True)
                            
                            # 重新生成二维码
                            qr_img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                            temp_qr_path = f"temp_qr_copy_{qr.id}.png"
                            qr_img.save(temp_qr_path, format="PNG", quality=100)
                            
                            # 在PDF中绘制二维码
                            c.drawImage(temp_qr_path, qr_x, qr_y, qr_size_pt, qr_size_pt)
                            
                            # 添加床位信息（可选）
                            if qr.bed:
                                c.setFont("Helvetica", 8)
                                info_text = f"床位: {qr.bed.number} | 科室: {department.name}"
                                if qr.bed.staff:
                                    info_text += f" | 负责人: {qr.bed.staff.name}"
                                if hasattr(qr.bed, 'get_area_display') and qr.bed.get_area_display():
                                    info_text += f" | 区域: {qr.bed.get_area_display()}"
                                
                                # 在二维码下方添加信息
                                c.drawString(qr_x, qr_y - 10, info_text)
                            
                            # 删除临时图片
                            os.remove(temp_qr_path)
                            os.remove(temp_bg_path)
            
            except Exception as e:
                logger.exception(f"处理模板背景图片时出错: {str(e)}")
                # 如果处理模板背景图片出错，回退到原始的表格方式
                messages.warning(request, f'处理模板背景图片时出错，使用默认表格方式: {str(e)}')
                
                # 使用原始的表格方式生成PDF
                c.showPage()  # 清除之前的页面
                
                # 添加科室标题
                c.setFont("Helvetica", 16)
                c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                
                # 每页放置的二维码数量
                qrcodes_per_page = 4
                margin = 50  # 页面边距
                qr_size = (width - 2 * margin) / 2
                
                for i, qr in enumerate(qrcodes):
                    # 计算当前页和位置
                    page = i // qrcodes_per_page
                    position = i % qrcodes_per_page
                    
                    # 如果是新页面，则添加新页
                    if position == 0 and i > 0:
                        c.showPage()
                        c.setFont("Helvetica", 16)
                        c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                    
                    # 计算二维码位置
                    row = position // 2
                    col = position % 2
                    x = margin + col * qr_size
                    y = height - margin - 50 - (row * qr_size)
                    
                    # 使用安全URL生成二维码
                    img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                    
                    # 保存为临时图片文件
                    temp_img_path = f"temp_qr_{qr.id}.png"
                    img.save(temp_img_path, format="PNG", quality=100)
                    
                    # 绘制标题
                    c.setFont("Helvetica", 12)
                    title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                    c.drawString(x + 10, y + qr_size - 20, title)
                    
                    # 绘制二维码
                    c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                    
                    # 删除临时图片
                    os.remove(temp_img_path)
                
                # 为每份复制创建额外的页面
                if copies > 1:
                    original_pages = (len(qrcodes) + qrcodes_per_page - 1) // qrcodes_per_page
                    
                    for _ in range(copies - 1):
                        for i, qr in enumerate(qrcodes):
                            # 计算当前页和位置
                            page = i // qrcodes_per_page
                            position = i % qrcodes_per_page
                            
                            # 如果是新页面，则添加新页
                            if position == 0:
                                c.showPage()
                                c.setFont("Helvetica", 16)
                                c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                            
                            # 计算二维码位置
                            row = position // 2
                            col = position % 2
                            x = margin + col * qr_size
                            y = height - margin - 50 - (row * qr_size)
                            
                            # 使用安全URL生成二维码
                            img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                            
                            # 保存为临时图片文件
                            temp_img_path = f"temp_qr_copy_{qr.id}.png"
                            img.save(temp_img_path, format="PNG", quality=100)
                            
                            # 绘制标题
                            c.setFont("Helvetica", 12)
                            title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                            c.drawString(x + 10, y + qr_size - 20, title)
                            
                            # 绘制二维码
                            c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                            
                            # 删除临时图片
                            os.remove(temp_img_path)
        else:
            # 如果没有模板背景图片，使用原始的表格方式
            # 添加科室标题
            c.setFont("Helvetica", 16)
            c.drawString(50, height - 50, f"{department.name} - 二维码打印")
            
            # 每页放置的二维码数量
            qrcodes_per_page = 4
            margin = 50  # 页面边距
            qr_size = (width - 2 * margin) / 2
            
            for i, qr in enumerate(qrcodes):
                # 计算当前页和位置
                page = i // qrcodes_per_page
                position = i % qrcodes_per_page
                
                # 如果是新页面，则添加新页
                if position == 0 and i > 0:
                    c.showPage()
                    c.setFont("Helvetica", 16)
                    c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                
                # 计算二维码位置
                row = position // 2
                col = position % 2
                x = margin + col * qr_size
                y = height - margin - 50 - (row * qr_size)
                
                # 使用安全URL生成二维码
                img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                
                # 保存为临时图片文件
                temp_img_path = f"temp_qr_{qr.id}.png"
                img.save(temp_img_path, format="PNG", quality=100)
                
                # 绘制标题
                c.setFont("Helvetica", 12)
                title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                c.drawString(x + 10, y + qr_size - 20, title)
                
                # 绘制二维码
                c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                
                # 删除临时图片
                os.remove(temp_img_path)
            
            # 为每份复制创建额外的页面
            if copies > 1:
                original_pages = (len(qrcodes) + qrcodes_per_page - 1) // qrcodes_per_page
                
                for _ in range(copies - 1):
                    for i, qr in enumerate(qrcodes):
                        # 计算当前页和位置
                        page = i // qrcodes_per_page
                        position = i % qrcodes_per_page
                        
                        # 如果是新页面，则添加新页
                        if position == 0:
                            c.showPage()
                            c.setFont("Helvetica", 16)
                            c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                        
                        # 计算二维码位置
                        row = position // 2
                        col = position % 2
                        x = margin + col * qr_size
                        y = height - margin - 50 - (row * qr_size)
                        
                        # 使用安全URL生成二维码
                        img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                        
                        # 保存为临时图片文件
                        temp_img_path = f"temp_qr_copy_{qr.id}.png"
                        img.save(temp_img_path, format="PNG", quality=100)
                        
                        # 绘制标题
                        c.setFont("Helvetica", 12)
                        title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                        c.drawString(x + 10, y + qr_size - 20, title)
                        
                        # 绘制二维码
                        c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                        
                        # 删除临时图片
                        os.remove(temp_img_path)
        
        # 保存PDF
        c.save()
        
        # 创建响应并设置响应头
        with open(pdf_path, 'rb') as f:
            response_content = f.read()
        
        # 删除临时PDF文件
        os.remove(pdf_path)
        
        # 记录打印操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="print_department_qrcodes",
            description=f"按科室批量打印二维码：{department.name}，共 {len(qrcodes)} 个，{copies} 份",
            extra_data={
                'department_id': department.id,
                'department_name': department.name,
                'qrcode_count': len(qrcodes),
                'copies': copies,
                'area_filter': area_filter,
                'template_id': template_id,
                'template_name': print_template.name if print_template else 'None'
            }
        )
        
        # 返回响应
        response = HttpResponse(response_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename={department.name}_qrcodes.pdf'
        return response
        
    except Exception as e:
        logger.exception(f"打印科室二维码时出错: {str(e)}")
        messages.error(request, f'打印失败：{str(e)}')
        return redirect('qrmanager:qrcode_list')

class PrintTemplateListView(LoginRequiredMixin, ListView):
    """打印模板列表视图"""
    template_name = 'qrmanager/print_template_list.html'
    context_object_name = 'departments'
    login_url = '/login/'

    def get_queryset(self):
        return Department.objects.prefetch_related('print_template').all().order_by('code', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 获取公共模板
        context['public_template'] = PrintTemplate.objects.filter(is_public=True).first()
        context['total_templates'] = PrintTemplate.objects.count()
        context['active_templates'] = PrintTemplate.objects.filter(is_active=True).count()
        return context

class PrintTemplateDeleteView(LoginRequiredMixin, DeleteView):
    """删除打印模板视图"""
    model = PrintTemplate
    success_url = reverse_lazy('qrmanager:print_template_list')
    login_url = '/login/'

    def form_valid(self, form):
        """
        重写form_valid方法，在删除模板记录前删除对应的文件
        注意：Django推荐在DeleteView中使用form_valid而不是delete方法
        """
        template = self.get_object()
        success_message = '打印模板删除成功！'
        
        try:
            # 删除模板文件
            if template.background_image:
                try:
                    # 获取文件路径
                    file_path = template.background_image.path
                    
                    # 记录文件信息用于日志
                    file_info = {
                        'file_name': os.path.basename(file_path),
                        'file_path': file_path,
                        'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    }
                    
                    # 删除文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        success_message += ' 模板文件已删除。'
                except Exception as e:
                    # 记录文件删除错误，但继续删除数据库记录
                    print(f"删除模板文件时出错: {str(e)}")
                    success_message += f' 但模板文件删除失败: {str(e)}'
            
            # 记录删除操作
            LoggerHelper.log_model_operation(
                user=self.request.user,
                instance=template,
                operation_type="delete",
                description=f"删除打印模板: {template.name}",
                extra_data={
                    'department': template.department.name if template.department else None,
                    'file_info': file_info if 'file_info' in locals() else None
                }
            )
            
            messages.success(self.request, success_message)
            return super().form_valid(form)
        except Exception as e:
            messages.error(self.request, f'删除失败：{str(e)}')
            return redirect('qrmanager:print_template_list')
    
    def delete(self, request, *args, **kwargs):
        """
        保留delete方法以兼容旧代码，但实际逻辑已移至form_valid
        """
        return super().delete(request, *args, **kwargs)

@login_required
def get_print_template(request, template_id):
    """获取打印模板信息的API端点"""
    try:
        # 尝试获取指定ID的模板
        try:
            template = PrintTemplate.objects.get(pk=template_id)
        except PrintTemplate.DoesNotExist:
            # 如果指定ID的模板不存在，尝试获取公共模板
            template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
            if not template:
                return JsonResponse({'error': '未找到打印模板，也没有可用的公共模板'}, status=404)
        
        data = {
            'id': template.id,
            'name': template.name,
            'print_width': template.print_width,
            'print_height': template.print_height,
            'qr_position_x': template.qr_position_x,
            'qr_position_y': template.qr_position_y,
            'qr_size': template.qr_size,
            'background_image': template.background_image.url if template.background_image else None,
            'is_public': template.is_public
        }
        return JsonResponse(data)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def api_beds(request):
    """床位 API 接口"""
    department_id = request.GET.get('department')
    # 只选择活跃的床位
    beds = Bed.objects.select_related('department').filter(is_active=True)
    if department_id:
        beds = beds.filter(department_id=department_id)
    data = [{
        'id': bed.id,
        'number': bed.number,
        'department': {
            'id': bed.department.id,
            'name': bed.department.name
        } if bed.department else None,
        'area': bed.get_area_display(),
        'staff': {
            'id': bed.staff.id,
            'name': bed.staff.name
        } if bed.staff else None
    } for bed in beds]
    return JsonResponse({'beds': data})

@login_required
def api_department(request, pk):
    """获取科室信息"""
    try:
        department = Department.objects.get(pk=pk)
        data = {
            'id': department.id,
            'name': department.name,
            'code': department.code
        }
        # 检查是否有remarks字段，有些Department模型可能有这个字段
        if hasattr(department, 'remarks'):
            data['remarks'] = department.remarks
        return JsonResponse(data)
    except Department.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '科室不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
def api_import_beds(request):
    """API: 导入床位数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        department_id = request.POST.get('department_id')
        
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        if not department_id:
            return JsonResponse({'status': 'error', 'message': '未指定科室'}, status=400)
            
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '指定的科室不存在'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号为必填项')
                    continue
                
                # 验证科室编码和名称
                if row.get('科室编码') and row.get('科室编码') != department.code:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row.get("科室编码")} 与当前科室编码 {department.code} 不匹配')
                    continue
                
                if row.get('科室名称') and row.get('科室名称') != department.name:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室名称 {row.get("科室名称")} 与当前科室名称 {department.name} 不匹配')
                    continue
                    
                # 检查床位号是否已存在
                if Bed.objects.filter(number=row['床位号'], department=department).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号 {row["床位号"]} 在科室 {department.name} 中已存在')
                    continue
                    
                # 获取工作人员（如果有）
                staff = None
                if row.get('工作人员工号'):
                    try:
                        staff = Staff.objects.get(work_number=row['工作人员工号'])
                    except Staff.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 工作人员工号 {row["工作人员工号"]} 不存在')
                        continue
                    
                # 创建床位
                bed = Bed.objects.create(
                    number=row['床位号'],
                    department=department,
                    area=row.get('区域', ''),
                    staff=staff
                )
                
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=bed,
                    operation_type="create",
                    description=f"导入创建床位: {bed.number}",
                    extra_data={
                        'department': department.name,
                        'area': bed.area,
                        'staff': staff.name if staff else None
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回结果
        return JsonResponse({
            'status': 'success' if success_count > 0 else 'error',
            'message': f'成功导入 {success_count} 条记录' if success_count > 0 else '导入失败',
            'success': success_count > 0,
            'imported_count': success_count,
            'error_count': error_count,
            'errors': error_messages
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'导入失败: {str(e)}',
            'error_messages': [str(e)]
        }, status=500)

@login_required
def api_bed_template(request):
    """API: 下载床位导入模板"""
    try:
        # 确保导入必要的模块
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from datetime import datetime
        import urllib.parse
        
        department_id = request.GET.get('department_id')
        if not department_id:
            return JsonResponse({'status': 'error', 'message': '未指定科室'}, status=400)
            
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '指定的科室不存在'}, status=400)
            
        # 创建Excel文件
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "床位导入模板"
        
        # 设置表头样式
        header_font = Font(name='Arial', bold=True, color='FFFFFF', size=12)
        header_fill = PatternFill(start_color='0071E3', end_color='0071E3', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        header_border = Border(
            left=Side(style='thin', color='DDDDDD'),
            right=Side(style='thin', color='DDDDDD'),
            top=Side(style='thin', color='DDDDDD'),
            bottom=Side(style='thin', color='DDDDDD')
        )
        
        # 设置数据行样式
        data_font = Font(name='Arial', size=11)
        data_alignment = Alignment(horizontal='center', vertical='center')
        data_border = Border(
            left=Side(style='thin', color='DDDDDD'),
            right=Side(style='thin', color='DDDDDD'),
            top=Side(style='thin', color='DDDDDD'),
            bottom=Side(style='thin', color='DDDDDD')
        )
        
        # 获取所有科室列表
        all_departments = Department.objects.all().order_by('name')
        departments_info = [f"- {dept.name} (编码: {dept.code or '无'})" for dept in all_departments]
        
        # 准备说明文本
        instructions = [
            "导入说明",
            f"1. 科室编码必须为 {department.code}",
            f"2. 科室名称必须为 {department.name}",
            f"3. 科室说明: {department.remarks if department.remarks else '无'}",
            "4. 床位号不能重复，且必须填写",
            "5. 区域可填写 A 或 B，代表不同的病区",
            "6. 工作人员工号为可选项",
            "7. 如填写工号，必须是系统中已存在的工号",
            "8. 请勿修改表头和格式",
            "9. 填写完成后保存，然后在系统中导入"
        ]
        
        # 将所有科室信息添加到说明中
        # 删除这行，不再添加所有科室列表
        # instructions.extend(departments_info)
        
        # 设置表头
        headers = ['科室编码', '科室名称', '床位号', '区域', '工作人员工号']
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = header_border
            
        # 添加示例数据
        example_data = [
            [department.code, department.name, '101', '', ''],
            [department.code, department.name, '102', '', ''],
            [department.code, department.name, '103', '', ''],
            [department.code, department.name, '104', '', ''],
            [department.code, department.name, '105', '', ''],
        ]
        
        # 设置浅蓝色和浅绿色交替行
        row_colors = ['E6F0FF', 'E6FFF0', 'F5F5F7', 'F5F5F7', 'F5F5F7']
        
        for row_num, row_data in enumerate(example_data, 2):
            row_fill = PatternFill(
                start_color=row_colors[row_num-2], 
                end_color=row_colors[row_num-2], 
                fill_type='solid'
            )
            
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.value = value
                cell.font = data_font
                cell.alignment = data_alignment
                cell.border = data_border
                cell.fill = row_fill
                    
        # 添加说明信息到最右侧的列 (G 列)
        # 在 F 列添加空白分隔列
        ws.column_dimensions['F'].width = 5
        
        # 为说明标题设置样式
        instruction_title_font = Font(name='Arial', bold=True, size=12, color='0071E3')
        instruction_title_fill = PatternFill(start_color='F8F9FC', end_color='F8F9FC', fill_type='solid')
        
        # 为说明内容设置样式
        instruction_font = Font(name='Arial', size=11, color='555555')
        instruction_fill = PatternFill(start_color='F8F9FC', end_color='F8F9FC', fill_type='solid')
        
        # 添加说明到 G 列
        for i, instruction in enumerate(instructions):
            cell = ws.cell(row=i+1, column=7)  # G 列
            cell.value = instruction
            
            if i == 0:  # 标题行
                cell.font = instruction_title_font
            else:  # 内容行
                cell.font = instruction_font
                
            cell.fill = instruction_fill
            cell.alignment = Alignment(horizontal='left', vertical='center')
                
        # 设置列宽
        ws.column_dimensions['A'].width = 15  # 科室编码
        ws.column_dimensions['B'].width = 20  # 科室名称
        ws.column_dimensions['C'].width = 15  # 床位号
        ws.column_dimensions['D'].width = 10  # 区域
        ws.column_dimensions['E'].width = 20  # 工作人员工号
        ws.column_dimensions['G'].width = 40  # 说明列
        
        # 设置行高
        ws.row_dimensions[1].height = 25  # 表头行高
        for i in range(2, 7):
            ws.row_dimensions[i].height = 20  # 数据行高
            
        # 冻结表头
        ws.freeze_panes = 'A2'
            
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        # 确保文件名包含科室名称
        filename = f"床位导入模板_{department.name}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        
        # 对文件名进行URL编码，解决中文文件名问题
        encoded_filename = urllib.parse.quote(filename)
        
        # 设置Content-Disposition头，使用ASCII文件名和UTF-8编码的文件名
        ascii_filename = f"bed_import_template_{department.code}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'status': 'error', 'message': f'生成模板失败: {str(e)}'}, status=500)

def departments(request):
    """获取所有科室列表"""
    departments_list = Department.objects.all().order_by('code', 'name')
    result = []
    
    # 将科室数据转换为字典
    for department in departments_list:
        result.append({
            'id': department.id,
            'name': department.name,
            'code': department.code or '',
            'remarks': department.remarks or '',
            'created_at': department.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': department.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 添加CORS头
    response = JsonResponse(result, safe=False)
    response["Access-Control-Allow-Origin"] = "*"  # 允许所有域名访问
    response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    
    # 添加调试信息
    print(f"返回{len(result)}个科室数据")
    
    return response

@login_required
def process_evaluation(request, evaluation_id):
    """处理评价状态更新"""
    evaluation = get_object_or_404(Evaluation, id=evaluation_id)
    status = request.GET.get('status', 'processed')
    next_url = request.GET.get('next', reverse('qrmanager:evaluation_list'))
    
    # 更新状态
    evaluation.process(request.user, status)
    
    # 记录操作
    LoggerHelper.log_operation(
        user=request.user,
        request=request,
        action="process_evaluation",
        description=f"将评价#{evaluation_id}标记为{dict(Evaluation.PROCESS_STATUS_CHOICES)[status]}",
        content_object=evaluation
    )
    
    messages.success(request, f'评价已成功标记为"{dict(Evaluation.PROCESS_STATUS_CHOICES)[status]}"')
    return redirect(next_url)

@login_required
def account_settings(request):
    """账号设置视图"""
    # 账号设置视图逻辑
    if request.method == 'POST':
        # 处理表单提交
        pass
    else:
        # 显示表单
        pass
    return render(request, 'qrmanager/account_settings.html')

@login_required
def api_management(request):
    """API管理中心视图"""
    from .models import APIKey, APILog
    from django.db.models import Count, Avg, Q
    from django.db.models.functions import TruncDate, TruncHour
    import datetime
    
    # 获取基本统计数据
    today = timezone.now().date()
    yesterday = today - datetime.timedelta(days=1)
    
    # 计算API接口数量 (RESTful API端点)
    from .urls import rest_api_urlpatterns
    api_count = len(rest_api_urlpatterns)
    
    # 获取API密钥数量
    api_keys_count = APIKey.objects.filter(is_active=True).count()
    
    # 获取今日API请求数量
    today_requests = APILog.objects.filter(created_at__date=today).count()
    
    # 计算API错误率 (今日)
    if today_requests > 0:
        error_count = APILog.objects.filter(created_at__date=today, status='error').count()
        error_rate = round((error_count / today_requests) * 100, 1)
    else:
        error_rate = 0
        
    # 获取近30天每日API调用统计
    thirty_days_ago = today - datetime.timedelta(days=30)
    daily_stats = APILog.objects.filter(created_at__date__gte=thirty_days_ago)\
        .annotate(date=TruncDate('created_at'))\
        .values('date')\
        .annotate(count=Count('id'), 
                 avg_response_time=Avg('response_time'),
                 error_count=Count('id', filter=Q(status='error')))\
        .order_by('date')
    
    # 获取今日每小时的API调用统计
    hourly_stats = APILog.objects.filter(created_at__date=today)\
        .annotate(hour=TruncHour('created_at'))\
        .values('hour')\
        .annotate(count=Count('id'))\
        .order_by('hour')
    
    # 获取热门API端点
    top_endpoints = APILog.objects.values('endpoint')\
        .annotate(count=Count('id'))\
        .order_by('-count')[:10]
        
    # 获取最近的API日志
    recent_logs = APILog.objects.select_related('api_key').order_by('-created_at')[:20]
    
    # 活跃的API密钥列表
    active_api_keys = APIKey.objects.filter(is_active=True).order_by('-last_used_at')
    
    # 计算昨日请求量增长率
    yesterday_requests = APILog.objects.filter(created_at__date=yesterday).count()
    if yesterday_requests > 0:
        growth_rate = round(((today_requests - yesterday_requests) / yesterday_requests) * 100, 1)
    else:
        growth_rate = 100 if today_requests > 0 else 0
    
    # 响应时间平均值
    avg_response_time = APILog.objects.filter(created_at__date=today).aggregate(avg=Avg('response_time'))['avg'] or 0
    
    # 构建统计数据
    api_stats = {
        'api_count': api_count,
        'api_keys': api_keys_count,
        'api_requests': today_requests,
        'api_errors': error_rate,
        'growth_rate': growth_rate,
        'avg_response_time': round(avg_response_time, 2)
    }
    
    # 构建图表数据
    chart_data = {
        'daily_stats': list(daily_stats),
        'hourly_stats': list(hourly_stats),
        'top_endpoints': list(top_endpoints)
    }
    
    context = {
        'api_stats': api_stats,
        'chart_data': chart_data,
        'recent_logs': recent_logs,
        'active_api_keys': active_api_keys
    }
    
    # 记录查看日志操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_management",
        description="查看API管理中心",
    )
    
    return render(request, 'qrmanager/api_management.html', context)

@login_required
def create_api_key(request):
    """创建新的API密钥"""
    from .models import APIKey
    import uuid
    import hashlib
    
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        rate_limit_day = request.POST.get('rate_limit_day', 1000)
        rate_limit_hour = request.POST.get('rate_limit_hour', 100)
        rate_limit_minute = request.POST.get('rate_limit_minute', 10)
        expires_at = request.POST.get('expires_at', None)
        
        if not name:
            messages.error(request, "API密钥名称不能为空")
            return redirect('qrmanager:api_management')
        
        # 生成唯一的API密钥
        key = hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()
        
        # 创建API密钥
        api_key = APIKey.objects.create(
            name=name,
            key=key,
            created_by=request.user,
            rate_limit_day=rate_limit_day,
            rate_limit_hour=rate_limit_hour,
            rate_limit_minute=rate_limit_minute,
            expires_at=expires_at if expires_at else None
        )
        
        # 记录创建操作
        LoggerHelper.log_operation(
            user=request.user,
            action="create_api_key",
            description=f"创建API密钥：{name}",
            content_object=api_key
        )
        
        messages.success(request, f"API密钥已创建：{key}")
        return redirect('qrmanager:api_management')
    
    return redirect('qrmanager:api_management')


@login_required
def toggle_api_key_status(request, pk):
    """切换API密钥的启用状态"""
    from .models import APIKey
    
    api_key = get_object_or_404(APIKey, pk=pk)
    
    # 切换状态
    api_key.is_active = not api_key.is_active
    api_key.save(update_fields=['is_active'])
    
    # 记录操作
    status = "启用" if api_key.is_active else "禁用"
    LoggerHelper.log_operation(
        user=request.user,
        action="toggle_api_key",
        description=f"{status}API密钥：{api_key.name}",
        content_object=api_key
    )
    
    messages.success(request, f"API密钥已{status}")
    return redirect('qrmanager:api_management')


@login_required
def delete_api_key(request, pk):
    """删除API密钥"""
    from .models import APIKey
    
    api_key = get_object_or_404(APIKey, pk=pk)
    name = api_key.name
    
    # 记录操作
    LoggerHelper.log_operation(
        user=request.user,
        action="delete_api_key",
        description=f"删除API密钥：{name}"
    )
    
    api_key.delete()
    messages.success(request, f"API密钥 {name} 已删除")
    return redirect('qrmanager:api_management')


@login_required
def api_logs(request):
    """API调用日志视图"""
    from .models import APILog, APIKey
    from django.db.models import Q
    
    # 获取筛选参数
    api_key_id = request.GET.get('api_key')
    status = request.GET.get('status')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    endpoint = request.GET.get('endpoint')
    
    # 构建查询
    logs = APILog.objects.select_related('api_key').order_by('-created_at')
    
    # 应用筛选
    if api_key_id:
        logs = logs.filter(api_key_id=api_key_id)
    
    if status:
        logs = logs.filter(status=status)
    
    if date_from:
        logs = logs.filter(created_at__date__gte=date_from)
    
    if date_to:
        logs = logs.filter(created_at__date__lte=date_to)
    
    if endpoint:
        logs = logs.filter(endpoint__icontains=endpoint)
    
    # 分页
    paginator = Paginator(logs, 50)
    page = request.GET.get('page')
    logs = paginator.get_page(page)
    
    # 获取API密钥列表（用于筛选）
    api_keys = APIKey.objects.all()
    
    context = {
        'logs': logs,
        'api_keys': api_keys,
        'filters': {
            'api_key_id': api_key_id,
            'status': status,
            'date_from': date_from,
            'date_to': date_to,
            'endpoint': endpoint
        }
    }
    
    # 记录查看日志操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_logs",
        description="查看API调用日志"
    )
    
    return render(request, 'qrmanager/api_logs.html', context)


@login_required
def api_docs(request):
    """API文档视图"""
    from .urls import admin_api_urlpatterns, internal_api_urlpatterns, public_api_urlpatterns, rest_api_urlpatterns
    
    # 统计各类API数量
    api_counts = {
        'admin_api': len(admin_api_urlpatterns),
        'internal_api': len(internal_api_urlpatterns),
        'public_api': len(public_api_urlpatterns),
        'rest_api': len(rest_api_urlpatterns),
        'total': len(admin_api_urlpatterns) + len(internal_api_urlpatterns) + len(public_api_urlpatterns) + len(rest_api_urlpatterns)
    }
    
    context = {
        'api_counts': api_counts
    }
    
    # 记录查看API文档操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_docs",
        description="查看API文档"
    )
    
    return render(request, 'qrmanager/api_docs.html', context)

class QRCodeHistoryListView(LoginRequiredMixin, ListView):
    """二维码历史记录列表视图"""
    model = QRCodeHistory
    template_name = 'qrmanager/qrcode_history_list.html'
    context_object_name = 'histories'
    login_url = '/login/'
    paginate_by = 20

    def get_queryset(self):
        # 基础查询集
        queryset = QRCodeHistory.objects.select_related('bed', 'bed__department', 'created_by').all()
        
        # 筛选条件
        department_id = self.request.GET.get('department')
        bed_id = self.request.GET.get('bed')
        is_security_issue = self.request.GET.get('is_security_issue')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        # 应用筛选
        if department_id:
            queryset = queryset.filter(bed__department_id=department_id)
        
        if bed_id:
            queryset = queryset.filter(bed_id=bed_id)
        
        if is_security_issue == 'true':
            queryset = queryset.filter(is_security_issue=True)
        elif is_security_issue == 'false':
            queryset = queryset.filter(is_security_issue=False)
            
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
            
        if date_to:
            # 添加一天，使日期范围包含结束日期的全天
            end_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            queryset = queryset.filter(created_at__lt=end_date)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 添加所有科室到上下文
        context['departments'] = Department.objects.all().order_by('name')
        
        # 获取当前选中的科室ID
        department_id = self.request.GET.get('department', '')
from django.shortcuts import render, redirect, get_object_or_404
from django.http import HttpResponse, JsonResponse, HttpResponseRedirect, FileResponse, HttpResponseNotFound, HttpResponsePermanentRedirect, HttpResponseServerError, HttpResponseNotFound
from django.views.generic import (
    TemplateView, ListView, CreateView, UpdateView, DeleteView, DetailView, View, FormView
)
from django.contrib.auth.mixins import LoginRequiredMixin, UserPassesTestMixin
from django.contrib.auth.decorators import login_required
from django.contrib.auth.models import User
from django.contrib.auth.forms import UserCreationForm, PasswordChangeForm
from django.contrib import messages
from django.db.models import Count, Avg, Sum, Q, F, Prefetch, Max
from django.db.models.functions import TruncDate
from django.db import models, transaction
from django.urls import reverse, reverse_lazy
from django.utils import timezone
from django import forms

from .forms import DepartmentForm, StaffForm, BedForm, QRCodeForm, EvaluationForm, BulkStaffImportForm, PrintTemplateForm
from .models import (
    Bed, QRCode, Department, Staff, Evaluation, PrintTemplate, 
    OperationLog, Dictionary, DictionaryItem, QRCodeHistory, SystemConfig, APIKey, APILog, StaffType
)
from .services import process_evaluation
from .utils import LoggerHelper
from .security import encrypt_qr_param, decrypt_qr_param
from django.conf import settings

import csv
import json
import qrcode
import io
import os
from datetime import datetime, timedelta
from io import BytesIO
from PIL import Image
from openpyxl import Workbook
from openpyxl.utils import get_column_letter
from openpyxl.styles import Font, PatternFill
from openpyxl.worksheet.datavalidation import DataValidation
from reportlab.lib import colors
from reportlab.lib.pagesizes import A4
from reportlab.lib.units import mm
from reportlab.pdfgen import canvas
from reportlab.platypus import SimpleDocTemplate, Table, TableStyle, Paragraph, PageBreak
from reportlab.lib.styles import getSampleStyleSheet
from django.core.files.base import ContentFile
from django.core.paginator import Paginator
from .qrcode_utils import generate_qrcode  # 从专用工具文件导入二维码函数
import tempfile
import urllib.parse
import logging
# 已移至正确位置的导入
from openpyxl.worksheet.datavalidation import DataValidation
import openpyxl
import uuid
import base64
from django.contrib.contenttypes.models import ContentType
import re
from django.views.static import serve
import xlsxwriter

def is_encrypted_param(param):
    """
    检查参数是否为加密格式
    加密参数通常是base64编码的字符串，长度不固定但通常较长
    """
    try:
        # 尝试进行base64解码，如果成功且解码后的长度合理，可能是加密参数
        decoded = base64.b64decode(param)
        # 加密参数解码后应该至少有一定长度
        return len(decoded) > 10
    except:
        # 如果解码失败，则不是有效的base64字符串，不是加密参数
        return False

class IndexView(TemplateView):
    """主页视图"""
    template_name = "qrmanager/index.html"

class AboutView(LoginRequiredMixin, TemplateView):
    """关于页面视图"""
    template_name = "qrmanager/about.html"
    login_url = '/login/'

class ContactView(LoginRequiredMixin, TemplateView):
    """联系我们页面视图"""
    template_name = "qrmanager/contact.html"
    login_url = '/login/'

class PrivacyView(TemplateView):
    """隐私政策页面视图"""
    template_name = "qrmanager/privacy.html"

class EvaluationSuccessView(View):
    """
    评价成功页面视图
    现在只是一个重定向视图，将请求重定向到前端
    """
    def get(self, request, *args, **kwargs):
        # 从系统配置中获取前端URL
        from .models import SystemConfig
        frontend_url = SystemConfig.get_value('frontend_url', 
                                             getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        # 重定向到前端的感谢页面
        return redirect(f"{frontend_url}/thank-you.html")

class DashboardView(LoginRequiredMixin, TemplateView):
    """管理仪表板视图"""
    template_name = "qrmanager/dashboard.html"
    login_url = '/login/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 基础统计
        context['total_qrcodes'] = QRCode.objects.count()
        context['total_evaluations'] = Evaluation.objects.count()
        context['total_staff'] = Staff.objects.count()
        context['total_beds'] = Bed.objects.count()
        context['total_departments'] = Department.objects.count()
        
        # 情感分析统计
        sentiment_stats = Evaluation.objects.values('sentiment').annotate(count=Count('id'))
        context['sentiment_stats'] = {item['sentiment']: item['count'] for item in sentiment_stats}
        
        # 计算满意度比例
        total_evaluations = context['total_evaluations']
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        context['satisfaction_rate'] = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        
        # 最近评价
        context['recent_evaluations'] = Evaluation.objects.select_related('qr_code')[:5]
        
        # 添加 URL 名称
        context['qrcode_list_url'] = reverse_lazy('qrmanager:qrcode_list')
        
        return context

class QRCodeCreateView(LoginRequiredMixin, CreateView):
    """
    创建二维码视图 (已弃用)
    二维码现在由床位自动生成，此视图仅作为兼容保留
    """
    model = QRCode
    template_name = "qrmanager/qrcode_form.html"
    fields = ['name', 'description']
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def dispatch(self, request, *args, **kwargs):
        """重定向到床位列表，因为二维码现在由床位自动生成"""
        messages.info(request, '二维码由床位自动生成，请先创建床位')
        return redirect('qrmanager:bed_list')

    def form_valid(self, form):
        messages.success(self.request, '二维码创建成功！')
        return super().form_valid(form)

class QRCodeListView(LoginRequiredMixin, ListView):
    """二维码列表视图"""
    model = QRCode
    template_name = 'qrmanager/qrcode_list.html'
    context_object_name = 'qrcodes'
    login_url = '/login/'
    paginate_by = 20

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 添加部门数据
        context['departments'] = Department.objects.all()
        # 添加区域数据（从Bed模型的choices获取）
        context['areas'] = [{'value': 'A', 'display': 'A区'}, {'value': 'B', 'display': 'B区'}]
        
        # 添加URL设置相关的上下文变量
        from .models import SystemConfig
        context['frontend_url'] = SystemConfig.get_value('frontend_url', 'http://hospital.local')
        
        return context
    
    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = QRCode.objects.select_related(
            'bed',
            'bed__department',
            'bed__staff'
        ).prefetch_related(
            'evaluations'
        )
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')
        
        # 应用筛选条件
        if department:
            queryset = queryset.filter(bed__department_id=department)
        if area:
            queryset = queryset.filter(bed__area=area)
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(bed__number__icontains=search) |
                Q(bed__department__name__icontains=search)
            )
        
        # 获取分页大小
        page_size = self.request.GET.get('page_size')
        if page_size:
            try:
                self.paginate_by = int(page_size)
            except ValueError:
                pass
        
        # 获取排序方式
        sort_by = self.request.GET.get('sort_by', 'bed__department__code')
        sort_order = self.request.GET.get('sort_order', 'asc')
        
        # 获取所有数据进行自然排序
        qrcodes = list(queryset)
        
        # 定义自然排序函数
        import re
        def natural_sort_key(qrcode):
            if not qrcode.bed or not qrcode.bed.number:
                return ['999999', 1, '']  # 没有床位的排在最后
            
            # 获取科室代码作为第一排序键，确保是字符串类型
            dept_code = str(qrcode.bed.department.code) if qrcode.bed and qrcode.bed.department and qrcode.bed.department.code else '999999'
            
            # 检查床位号是否以数字开头
            if qrcode.bed.number and qrcode.bed.number[0].isdigit():
                # 数字开头的床位，排在前面（优先级0）
                prefix = 0
            else:
                # 非数字开头的床位，排在后面（优先级1）
                prefix = 1
            
            # 提取床位号中的数字和非数字部分
            convert = lambda text: int(text) if text.isdigit() else text.lower()
            # 科室代码作为第一个排序键，然后是床位号的优先级，最后按自然排序
            return [dept_code, prefix] + [convert(c) for c in re.split('([0-9]+)', qrcode.bed.number)]
        
        # 应用排序
        qrcodes.sort(key=natural_sort_key, reverse=(sort_order == 'desc'))
        
        # 预计算评价数量和平均评分
        for qrcode in qrcodes:
            evaluations = list(qrcode.evaluations.all())
            qrcode.evaluation_count = len(evaluations)
            satisfied_count = sum(1 for e in evaluations if e.is_satisfied)
            qrcode.satisfaction_rate = (satisfied_count / qrcode.evaluation_count * 100) if qrcode.evaluation_count > 0 else 0
            
        return qrcodes

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取科室列表，用于筛选
        context['departments'] = Department.objects.all().order_by('name')
        
        # 获取当前筛选条件
        department_id = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')
        
        # 添加筛选条件到上下文
        context['selected_department'] = int(department_id) if department_id and department_id.isdigit() else None
        context['selected_area'] = area
        context['search_query'] = search
        
        # 获取所有二维码，用于统计
        all_qrcodes = self.get_queryset()
        
        # 为当前页的二维码添加统计信息
        for qr in context['qrcodes']:
            evaluations = list(qr.evaluations.all())
            qr.evaluation_count = len(evaluations)
            satisfied_count = sum(1 for e in evaluations if e.is_satisfied)
            qr.satisfaction_rate = (satisfied_count / qr.evaluation_count * 100) if qr.evaluation_count > 0 else 0
            qr.latest_evaluation = evaluations[0] if evaluations else None
            # 生成二维码图片URL
            qr.image_url = qr.get_qr_image_url()
        
        # 计算总体统计数据
        total_qrcodes = len(all_qrcodes)
        total_evaluations = Evaluation.objects.filter(qr_code__in=all_qrcodes).count()
        satisfied_count = Evaluation.objects.filter(qr_code__in=all_qrcodes, is_satisfied=True).count()
        satisfaction_rate = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        pending_evaluations = Evaluation.objects.filter(
            qr_code__in=all_qrcodes,
            created_at__gte=timezone.now() - timezone.timedelta(days=1),
            process_status='pending'
        ).count()
        
        # 添加统计数据到上下文
        context.update({
            'total_qrcodes': total_qrcodes,
            'total_evaluations': total_evaluations,
            'satisfaction_rate': satisfaction_rate,
            'pending_evaluations': pending_evaluations
        })
        
        # 获取二维码有效期
        try:
            from .models import SystemConfig
            from .security import get_token_expiry
            context['qrcode_expiry'] = get_token_expiry()
            
            # 获取前端URL
            context['frontend_url'] = SystemConfig.get_value('frontend_url', 
                                                           getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        except:
            context['qrcode_expiry'] = 0
            context['frontend_url'] = getattr(settings, 'FRONTEND_URL', 'http://hospital.local')
            
        return context

    def post(self, request, *args, **kwargs):
        """处理批量操作请求"""
        action = request.POST.get('action')
        selected_qrcodes = request.POST.getlist('selected_qrcodes')
        
        if not selected_qrcodes:
            messages.warning(request, '请至少选择一个二维码')
            return redirect('qrmanager:qrcode_list')
            
        if action == 'export':
            # 批量导出
            return self.export_qrcodes(selected_qrcodes)
        elif action == 'print':
            # 批量打印
            return self.print_qrcodes(selected_qrcodes)
        
        return redirect('qrmanager:qrcode_list')
            
    def export_qrcodes(self, qrcode_ids):
        """导出选中的二维码信息"""
        qrcodes = QRCode.objects.filter(id__in=qrcode_ids).select_related('bed', 'bed__department')
        
        # 创建工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "二维码信息"

        # 写入表头
        headers = ['床位号', '科室名称', '区域', '评价数量', '满意度比例(%)', '创建时间', '更新时间']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)

        # 写入数据
        for row, qr in enumerate(qrcodes, 2):
            ws.cell(row=row, column=1, value=qr.bed.number)
            ws.cell(row=row, column=2, value=qr.bed.department.name)
            ws.cell(row=row, column=3, value=qr.bed.get_area_display())
            ws.cell(row=row, column=4, value=qr.evaluations.count())
            ws.cell(row=row, column=5, value=qr.evaluations.filter(is_satisfied=True).count() / qr.evaluations.count() * 100 if qr.evaluations.count() > 0 else 0)
            ws.cell(row=row, column=6, value=qr.created_at.strftime('%Y-%m-%d %H:%M'))
            ws.cell(row=row, column=7, value=qr.updated_at.strftime('%Y-%m-%d %H:%M'))

        # 调整列宽
        for col in range(1, len(headers) + 1):
            ws.column_dimensions[get_column_letter(col)].width = 15

        # 创建响应
        response = HttpResponse(content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
        response['Content-Disposition'] = 'attachment; filename=qrcodes_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        
        # 记录导出操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="export_qrcodes",
            description=f"导出二维码信息",
            extra_data={'count': len(qrcode_ids)}
        )
        
        return response

    def print_qrcodes(self, qrcode_ids):
        """批量打印二维码为PDF文件"""
        # 获取所选的二维码
        qrcodes = QRCode.objects.filter(id__in=qrcode_ids).select_related('bed', 'bed__department')
        
        # 创建一个PDF文档
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        import tempfile
        from PIL import Image
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            pdf_path = tmp.name
        
        # 创建PDF
        c = canvas.Canvas(pdf_path, pagesize=A4)
        width, height = A4  # 获取页面尺寸
        
        # 每页放置的二维码数量（根据实际情况调整）
        qrcodes_per_page = 4
        margin = 50  # 页面边距
        qr_size = (width - 2 * margin) / 2  # 二维码尺寸
        
        for i, qr in enumerate(qrcodes):
            # 计算当前页和位置
            page = i // qrcodes_per_page
            position = i % qrcodes_per_page
            
            # 如果是新页面，则添加新页
            if position == 0 and i > 0:
                c.showPage()
            
            # 计算二维码位置
            row = position // 2
            col = position % 2
            x = margin + col * qr_size
            y = height - margin - (row + 1) * qr_size
            
            # 使用安全URL生成二维码
            img, _ = generate_qrcode(qr.get_secure_evaluation_url())
            
            # 保存为临时图片文件
            temp_img_path = f"temp_qr_{qr.id}.png"
            img.save(temp_img_path)
            
            # 在PDF中绘制二维码和标题
            # 绘制标题
            c.setFont("Helvetica", 12)
            title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
            c.drawString(x + 10, y + qr_size - 20, title)
            
            # 绘制二维码
            c.drawImage(temp_img_path, x + 10, y + 10, qr_size - 20, qr_size - 40)
            
            # 删除临时图片
            os.remove(temp_img_path)
        
        c.showPage()
        c.save()
        
        # 创建响应并设置响应头
        with open(pdf_path, 'rb') as f:
            response = HttpResponse(f.read(), content_type='application/pdf')
        response['Content-Disposition'] = 'attachment; filename=qrcodes_print.pdf'
        
        # 删除临时PDF文件
        os.remove(pdf_path)
        
        # 记录操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="print_qrcodes",
            description=f"批量打印 {len(qrcode_ids)} 个二维码为PDF文件",
            extra_data={'count': len(qrcode_ids)}
        )
        
        return response

    def get(self, request, *args, **kwargs):
        """处理GET请求，支持AJAX请求"""
        # 调用父类方法获取响应
        response = super().get(request, *args, **kwargs)
        
        # 检查是否为AJAX请求
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest' or request.GET.get('ajax') == '1':
            return response
        
        return response

class QRCodeUpdateView(LoginRequiredMixin, UpdateView):
    """更新二维码视图"""
    model = QRCode
    template_name = "qrmanager/qrcode_form.html"
    fields = ['name', 'description']
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def form_valid(self, form):
        messages.success(self.request, '二维码更新成功！')
        return super().form_valid(form)

class QRCodeDeleteView(LoginRequiredMixin, DeleteView):
    """删除二维码视图"""
    model = QRCode
    template_name = "qrmanager/qrcode_confirm_delete.html"
    success_url = reverse_lazy('qrmanager:qrcode_list')
    login_url = '/login/'

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, '二维码删除成功！')
        return super().delete(request, *args, **kwargs)

class StaffListView(LoginRequiredMixin, ListView):
    """工作人员列表视图"""
    model = Staff
    template_name = 'qrmanager/staff_list.html'
    context_object_name = 'staff_list'
    paginate_by = 20  # 默认每页显示20条记录
    
    def get_paginate_by(self, queryset):
        """
        根据请求参数动态设置每页显示数量
        """
        # 从请求中获取page_size参数
        page_size = self.request.GET.get('page_size')
        if page_size:
            try:
                # 尝试将page_size转换为整数
                page_size = int(page_size)
                # 限制page_size的范围，避免过大或过小的值
                if 10 <= page_size <= 100:
                    return page_size
            except ValueError:
                pass
        # 如果没有有效的page_size参数，使用默认值
        return self.paginate_by

    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = Staff.objects.select_related(
            'department',
            'staff_type'
        )
        
        # 工号搜索
        work_number = self.request.GET.get('work_number')
        if work_number:
            queryset = queryset.filter(work_number__icontains=work_number)
        
        # 姓名搜索
        name = self.request.GET.get('name')
        if name:
            queryset = queryset.filter(name__icontains=name)
        
        # 科室筛选
        department = self.request.GET.get('department')
        if department:
            queryset = queryset.filter(department_id=department)
        
        # 人员类型筛选
        staff_type = self.request.GET.get('staff_type')
        if staff_type:
            queryset = queryset.filter(staff_type_id=staff_type)
        
        # 职称筛选
        title = self.request.GET.get('title')
        if title:
            queryset = queryset.filter(title=title)
        
        # 强制执行查询并转换为列表，确保数据一致性
        return list(queryset.order_by('department__code', 'work_number'))

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 预加载并缓存选项数据
        context['departments'] = list(Department.objects.order_by('code', 'name'))
        context['staff_types'] = list(DictionaryItem.objects.filter(
            dictionary__code='staff_type'
        ).order_by('sort_order'))
        context['staff_titles'] = list(DictionaryItem.objects.filter(
            dictionary__code='staff_title'
        ).order_by('sort_order'))
        return context

class StaffCreateView(LoginRequiredMixin, CreateView):
    """创建工作人员视图"""
    model = Staff
    template_name = 'qrmanager/staff_form.html'
    form_class = StaffForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'工作人员 {self.object.name} 创建成功！')
        return response

class StaffUpdateView(LoginRequiredMixin, UpdateView):
    """更新工作人员信息视图"""
    model = Staff
    template_name = 'qrmanager/staff_form.html'
    form_class = StaffForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'工作人员 {self.object.name} 更新成功！')
        return response

class StaffDeleteView(LoginRequiredMixin, DeleteView):
    """删除工作人员视图"""
    model = Staff
    template_name = 'qrmanager/staff_confirm_delete.html'
    success_url = reverse_lazy('qrmanager:staff_list')
    login_url = '/login/'

    def delete(self, request, *args, **kwargs):
        messages.success(self.request, '工作人员信息删除成功！')
        return super().delete(request, *args, **kwargs)

@login_required
def staff_bulk_delete(request):
    """批量删除工作人员"""
    if request.method == 'POST':
        staff_ids = request.POST.getlist('staff_ids')
        if not staff_ids:
            messages.error(request, '未选择任何工作人员')
            return redirect('qrmanager:staff_list')
        
        try:
            # 记录操作日志前获取要删除的工作人员信息
            staff_to_delete = Staff.objects.filter(id__in=staff_ids)
            staff_names = [f"{staff.work_number}-{staff.name}" for staff in staff_to_delete]
            
            # 执行删除操作
            delete_count = staff_to_delete.delete()[0]
            
            # 记录操作日志
            OperationLog.objects.create(
                user=request.user,
                action='staff_bulk_delete',
                description=f"批量删除了{delete_count}名工作人员: {', '.join(staff_names)}",
                status='success',
                extra_data={
                    'staff_ids': staff_ids,
                    'staff_names': staff_names,
                    'count': delete_count
                }
            )
            
            messages.success(request, f'成功删除{delete_count}名工作人员')
        except Exception as e:
            messages.error(request, f'删除失败: {str(e)}')
    
    return redirect('qrmanager:staff_list')

class BedListView(LoginRequiredMixin, ListView):
    """床位列表视图"""
    model = Bed
    template_name = 'qrmanager/bed_list.html'
    context_object_name = 'bed_list'
    paginate_by = None  # 禁用分页，显示所有数据

    def dispatch(self, request, *args, **kwargs):
        """在视图处理前检查科室参数"""
        # 不再需要自动重定向到第一个科室
        return super().dispatch(request, *args, **kwargs)

    def get_queryset(self):
        # 使用 select_related 预加载关联数据
        queryset = Bed.objects.select_related(
            'department',
            'qrcode'
        ).prefetch_related(
            'qrcode__evaluations'  # 通过二维码预加载评价数据
        )
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        area = self.request.GET.get('area')
        search = self.request.GET.get('search')

        # 应用筛选条件
        if department:
            try:
                department_id = int(department)  # 确保是整数
                # 只有当department_id不为空字符串时才进行筛选
                if department_id:
                    queryset = queryset.filter(department_id=department_id)
            except (ValueError, TypeError) as e:
                print(f"Error converting department_id: {e}")
                pass
            
        if area:
            queryset = queryset.filter(area=area)
        if search:
            queryset = queryset.filter(
                Q(number__icontains=search) |
                Q(department__name__icontains=search) |
                Q(staff_evaluations__staff__name__icontains=search)
            )

        # 根据科室筛选情况调整排序方式
        if department:
            # 如果筛选了科室，尝试使用更兼容的排序逻辑
            queryset = queryset.order_by('number')  # 按床位号字符串排序
        else:
            # 如果没有科室筛选，先按科室名称排序，再按床位号排序
            queryset = queryset.order_by('department__name', 'number')
        
        # 强制执行查询并转换为列表，确保数据一致性
        beds = list(queryset)
        
        # 如果筛选了科室，可以在Python代码中进行更精确的排序
        if department:
            # 通过自然排序算法排序床位号（数字部分按数值排序）
            def natural_sort_key(bed):
                import re
                # 检查床位号是否以数字开头
                if bed.number and bed.number[0].isdigit():
                    # 数字开头的床位，排在前面（优先级0）
                    prefix = 0
                else:
                    # 非数字开头的床位，排在后面（优先级1）
                    prefix = 1
                
                # 提取床位号中的数字和非数字部分
                convert = lambda text: int(text) if text.isdigit() else text.lower()
                # 优先级作为第一个排序键，然后按自然排序
                return [prefix] + [convert(c) for c in re.split('([0-9]+)', bed.number)]
            
            # 对已获取的床位列表进行排序
            beds.sort(key=natural_sort_key)
        
        # 预计算每个床位的评价数量
        for bed in beds:
            if hasattr(bed, 'qrcode') and bed.qrcode:
                bed.evaluation_count = bed.qrcode.evaluations.count()
            else:
                bed.evaluation_count = 0

        return beds

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取当前科室
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                context['department'] = Department.objects.select_related('print_template').get(pk=department_id)
            except Department.DoesNotExist:
                pass
                
        # 预加载并缓存科室列表
        context['departments'] = list(Department.objects.order_by('code', 'name'))
        
        # 为每个床位的二维码添加评价URL
        for bed in context['bed_list']:
            if hasattr(bed, 'qrcode') and bed.qrcode:
                bed.qrcode.evaluation_url = bed.qrcode.get_evaluation_url()
        
        return context

class BedCreateView(LoginRequiredMixin, CreateView):
    model = Bed
    form_class = BedForm
    template_name = 'qrmanager/bed_form.html'
    
    def dispatch(self, request, *args, **kwargs):
        """在视图处理前检查科室参数"""
        department_id = request.GET.get('department')
        if not department_id:
            messages.error(request, '创建床位必须指定科室！')
            return redirect('qrmanager:bed_list')
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            messages.error(request, '指定的科室不存在！')
            return redirect('qrmanager:bed_list')
        return super().dispatch(request, *args, **kwargs)
    
    def get_success_url(self):
        """获取成功后的重定向URL"""
        if self.object and self.object.department:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={self.object.department.id}"
        return reverse_lazy('qrmanager:bed_list')

    def get_form_kwargs(self):
        """传递科室ID到表单"""
        kwargs = super().get_form_kwargs()
        department_id = self.request.GET.get('department')
        if department_id:
            kwargs['department_id'] = department_id
        return kwargs

    def get_context_data(self, **kwargs):
        """添加科室信息到上下文"""
        context = super().get_context_data(**kwargs)
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                context['department'] = department
            except Department.DoesNotExist:
                pass
        return context

    def form_valid(self, form):
        """处理表单提交"""
        try:
            # 1. 保存床位信息
            response = super().form_valid(form)
            
            # 2. 创建关联的二维码
            qrcode = QRCode.objects.create(
                bed=self.object,
                name=f"{self.object.department.name}-{self.object.number}号床位二维码" if self.object.department else f"{self.object.number}号床位二维码"
            )
            
            # 3. 记录操作日志
            LoggerHelper.log_model_operation(
                user=self.request.user,
                instance=self.object,
                operation_type="create",
                description=f"创建床位: {self.object.number}",
                extra_data={
                    'department': self.object.department.name if self.object.department else None,
                    'area': self.object.get_area_display(),
                    'staff': self.object.staff.name if self.object.staff else None,
                    'qrcode_id': qrcode.id
                }
            )
            
            messages.success(self.request, f'床位 {self.object.number} 创建成功！')
            return response
            
        except Exception as e:
            # 记录错误日志
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="create_bed_failed",
                description=f"创建床位失败: {str(e)}",
                status='error',
                extra_data={
                    'form_data': form.cleaned_data,
                    'error': str(e)
                }
            )
            messages.error(self.request, f'创建床位失败：{str(e)}')
            return super().form_invalid(form)

    def form_invalid(self, form):
        """处理表单验证失败"""
        # 记录验证失败的日志
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="create_bed_validation_failed",
            description="床位创建表单验证失败",
            status='error',
            extra_data={
                'form_errors': form.errors,
                'form_data': form.cleaned_data
            }
        )
        
        # 重新获取科室信息
        department_id = self.request.GET.get('department')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                form.fields['department'].initial = department
                form.fields['department'].queryset = Department.objects.filter(pk=department_id)
                form.fields['department'].widget.attrs.update({
                    'disabled': 'disabled',
                    'class': 'form-select'
                })
            except Department.DoesNotExist:
                pass

        messages.error(self.request, '请检查输入的信息是否正确。')
        return super().form_invalid(form)

class BedUpdateView(LoginRequiredMixin, UpdateView):
    model = Bed
    template_name = 'qrmanager/bed_form.html'
    fields = ['number', 'department', 'area', 'staff']
    
    def get_success_url(self):
        """获取成功后的重定向URL"""
        if self.object and self.object.department:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={self.object.department.id}"
        return reverse_lazy('qrmanager:bed_list')
    
    def get_context_data(self, **kwargs):
        """添加科室信息到上下文"""
        context = super().get_context_data(**kwargs)
        if self.object and self.object.department:
            context['department'] = self.object.department
        return context

    def form_valid(self, form):
        # 记录更新前的数据
        old_instance = self.get_object()
        old_data = {
            'number': old_instance.number,
            'department': old_instance.department.name if old_instance.department else None,
            'area': old_instance.get_area_display(),
            'staff': old_instance.staff.name if old_instance.staff else None
        }
        
        response = super().form_valid(form)
        
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新床位: {self.object.number}",
            old_data=old_data,
            new_data={
                'number': self.object.number,
                'department': self.object.department.name if self.object.department else None,
                'area': self.object.get_area_display(),
                'staff': self.object.staff.name if self.object.staff else None
            }
        )
        
        messages.success(self.request, f'床位 {self.object.number} 更新成功！')
        return response

class BedDeleteView(LoginRequiredMixin, DeleteView):
    model = Bed
    template_name = 'qrmanager/bed_confirm_delete.html'
    
    def get_success_url(self):
        # 从对象获取科室ID
        department_id = getattr(self.object, 'department_id', None)
        if department_id:
            return f"{reverse_lazy('qrmanager:bed_list')}?department={department_id}"
        return reverse_lazy('qrmanager:bed_list')

    def delete(self, request, *args, **kwargs):
        bed = self.get_object()
        # 保存科室ID用于重定向
        self.object = bed
        # 检查关联数据
        related_data = {
            'qrcode_exists': hasattr(bed, 'qrcode'),
            'evaluation_count': Evaluation.objects.filter(bed=bed).count()  # 修改为直接使用bed关联
        }
        
        # 记录删除操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=bed,
            operation_type="delete",
            description=f"删除床位: {bed.number}",
            related_data=related_data
        )
        
        messages.success(self.request, f'床位 {bed.number} 删除成功！')
        return super().delete(request, *args, **kwargs)

class EvaluationListView(LoginRequiredMixin, ListView):
    """评价列表视图"""
    model = Evaluation
    template_name = 'qrmanager/evaluation_list.html'
    context_object_name = 'evaluations'
    login_url = '/login/'
    paginate_by = 20

    def get_queryset(self):
        # 修改select_related，添加bed关联，并使用prefetch_related预加载工作人员评价
        queryset = Evaluation.objects.select_related(
            'qr_code', 'bed', 'bed__department', 'processed_by'
        ).prefetch_related(
            'staff_evaluations__staff'  # 预加载所有相关的工作人员评价
        ).order_by('-created_at')
        
        # 获取筛选参数
        department = self.request.GET.get('department')
        is_satisfied = self.request.GET.get('is_satisfied')
        sentiment = self.request.GET.get('sentiment')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        search = self.request.GET.get('search')

        # 应用筛选条件，修改为使用bed关联
        if department:
            queryset = queryset.filter(bed__department_id=department)
        if is_satisfied:
            queryset = queryset.filter(is_satisfied=is_satisfied == 'True')
        if sentiment:
            queryset = queryset.filter(sentiment=sentiment)
        if status:
            queryset = queryset.filter(process_status=status)
        if date_from:
            queryset = queryset.filter(created_at__date__gte=date_from)
        if date_to:
            queryset = queryset.filter(created_at__date__lte=date_to)
        if search:
            queryset = queryset.filter(
                Q(comment__icontains=search) |
                Q(hospital_number__icontains=search) |  # 添加住院号搜索
                Q(phone_number__icontains=search) |     # 添加联系电话搜索
                Q(bed__number__icontains=search) |
                Q(staff_evaluations__staff__name__icontains=search)
            )

        # 记录查询操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_evaluation_list",
            description="查看评价列表",
            extra_data={
                'filters': {
                    'department': department,
                    'is_satisfied': is_satisfied,
                    'sentiment': sentiment,
                    'status': status,
                    'date_from': date_from,
                    'date_to': date_to,
                    'search': search
                }
            }
        )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['departments'] = Department.objects.all()
        context['satisfaction_choices'] = [(True, '满意'), (False, '不满意')]
        context['sentiment_choices'] = Evaluation.SENTIMENT_CHOICES
        context['process_status_choices'] = Evaluation.PROCESS_STATUS_CHOICES
        
        # 添加筛选参数到上下文
        context['department'] = self.request.GET.get('department', '')
        context['is_satisfied'] = self.request.GET.get('is_satisfied', '')
        context['sentiment'] = self.request.GET.get('sentiment', '')
        context['status'] = self.request.GET.get('status', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')
        context['search'] = self.request.GET.get('search', '')

        # 计算满意度比例
        total_evaluations = Evaluation.objects.count()
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        satisfaction_rate = (satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0
        context['satisfaction_rate'] = satisfaction_rate

        # 准备满意度分布数据
        satisfaction_data = []
        # 满意
        satisfied_count = Evaluation.objects.filter(is_satisfied=True).count()
        satisfaction_data.append(satisfied_count)
        # 不满意
        unsatisfied_count = Evaluation.objects.filter(is_satisfied=False).count()
        satisfaction_data.append(unsatisfied_count)
        context['satisfaction_data'] = json.dumps(satisfaction_data)

        # 准备科室评价分布数据
        departments = Department.objects.all()
        department_labels = []
        department_data = []
        
        for dept in departments:
            count = Evaluation.objects.filter(bed__department=dept).count()  # 修改为直接使用bed关联
            if count > 0:  # 只显示有评价的科室
                department_labels.append(dept.name)
                department_data.append(count)
        
        context['department_labels'] = json.dumps(department_labels)
        context['department_data'] = json.dumps(department_data)

        # 准备评价趋势数据（近30天）
        end_date = datetime.now().date()
        start_date = end_date - timedelta(days=29)
        
        trend_labels = []
        trend_data = []
        
        current_date = start_date
        while current_date <= end_date:
            count = Evaluation.objects.filter(created_at__date=current_date).count()
            trend_labels.append(current_date.strftime('%m-%d'))
            trend_data.append(count)
            current_date += timedelta(days=1)
        
        context['trend_labels'] = json.dumps(trend_labels)
        context['trend_data'] = json.dumps(trend_data)

        return context

class SentimentAnalysisView(LoginRequiredMixin, TemplateView):
    """情感分析视图"""
    template_name = 'qrmanager/sentiment_analysis.html'
    login_url = '/login/'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取筛选参数
        time_range = self.request.GET.get('time_range', '30')  # 默认30天
        department_id = self.request.GET.get('department')
        sentiment_filter = self.request.GET.get('sentiment')

        # 构建基础查询集
        evaluations = Evaluation.objects.all()
        
        # 应用时间筛选
        if time_range != 'all':
            days = int(time_range)
            start_date = timezone.now() - timezone.timedelta(days=days)
            evaluations = evaluations.filter(created_at__gte=start_date)

        # 应用科室筛选
        if department_id:
            evaluations = evaluations.filter(bed__department_id=department_id)  # 修改为直接使用bed关联

        # 应用情感倾向筛选
        if sentiment_filter:
            evaluations = evaluations.filter(sentiment=sentiment_filter)

        # 记录查看情感分析的操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_sentiment_analysis",
            description="查看情感分析报告",
            extra_data={
                'filters': {
                    'time_range': time_range,
                    'department_id': department_id,
                    'sentiment': sentiment_filter
                }
            }
        )

        # 计算基础统计数据
        total_evaluations = evaluations.count()
        
        # 计算满意度比例
        satisfied_count = evaluations.filter(is_satisfied=True).count()
        satisfaction_rate = round((satisfied_count / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        
        # 计算投诉率（不满意的比例）
        complaint_count = evaluations.filter(is_satisfied=False).count()
        complaint_rate = round((complaint_count / total_evaluations * 100) if total_evaluations > 0 else 0, 1)

        # 情感分析统计
        sentiment_stats = evaluations.values('sentiment').annotate(count=Count('id'))
        sentiment_dict = {item['sentiment']: item['count'] for item in sentiment_stats}
        
        # 计算情感百分比
        context['positive_percentage'] = round((sentiment_dict.get('positive', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        context['neutral_percentage'] = round((sentiment_dict.get('neutral', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        context['negative_percentage'] = round((sentiment_dict.get('negative', 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)

        # 评分统计
        # 计算满意度统计
        satisfaction_stats = evaluations.values('is_satisfied').annotate(count=Count('id'))
        satisfaction_dict = {item['is_satisfied']: item['count'] for item in satisfaction_stats}
        
        # 计算评分百分比
        context['satisfaction_percentages'] = {
            'satisfied': round((satisfaction_dict.get(True, 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1),
            'unsatisfied': round((satisfaction_dict.get(False, 0) / total_evaluations * 100) if total_evaluations > 0 else 0, 1)
        }

        # 获取科室列表（用于筛选）
        context['departments'] = Department.objects.all()

        # 计算趋势数据
        trend_days = 7  # 显示最近7天的趋势
        trend_dates = [(timezone.now() - timezone.timedelta(days=i)).date() for i in range(trend_days-1, -1, -1)]
        
        # 情感趋势数据
        sentiment_trend = {
            'positive': [0] * trend_days,
            'neutral': [0] * trend_days,
            'negative': [0] * trend_days
        }
        
        for i, date in enumerate(trend_dates):
            day_stats = evaluations.filter(
                created_at__date=date
            ).values('sentiment').annotate(count=Count('id'))
            for stat in day_stats:
                sentiment_trend[stat['sentiment']][i] = stat['count']

        # 满意度趋势数据
        satisfaction_trend = []
        for date in trend_dates:
            day_evals = evaluations.filter(created_at__date=date)
            total = day_evals.count()
            satisfied = day_evals.filter(is_satisfied=True).count()
            satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
            satisfaction_trend.append(round(satisfaction_rate, 1))

        # 科室评价对比数据
        departments = Department.objects.all()
        department_stats = []
        for dept in departments:
            dept_evals = evaluations.filter(bed__department=dept)  # 修改为直接使用bed关联
            if dept_evals.exists():
                total = dept_evals.count()
                satisfied = dept_evals.filter(is_satisfied=True).count()
                satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
                positive_count = dept_evals.filter(sentiment='positive').count()
                negative_count = dept_evals.filter(sentiment='negative').count()
                total = dept_evals.count()
                
                department_stats.append({
                    'name': dept.name,
                    'satisfaction_rate': round(satisfaction_rate, 1),
                    'positive_rate': round((positive_count / total * 100) if total > 0 else 0, 1),
                    'negative_rate': round((negative_count / total * 100) if total > 0 else 0, 1)
                })

        # 工作人员评价统计
        staff_stats = []
        for staff in Staff.objects.all():
            staff_evals = evaluations.filter(Q(satisfied_staff1_id=staff.id) | Q(satisfied_staff2_id=staff.id) | Q(satisfied_staff3_id=staff.id) | Q(unsatisfied_staff1_id=staff.id) | Q(unsatisfied_staff2_id=staff.id) | Q(unsatisfied_staff3_id=staff.id))
            if staff_evals.exists():
                total = staff_evals.count()
                satisfied = staff_evals.filter(is_satisfied=True).count()
                satisfaction_rate = (satisfied / total * 100) if total > 0 else 0
                positive_count = staff_evals.filter(sentiment='positive').count()
                negative_count = staff_evals.filter(sentiment='negative').count()
                total = staff_evals.count()
                
                staff_stats.append({
                    'name': staff.name,
                    'department': staff.department.name if staff.department else '-',
                    'satisfaction_rate': round(satisfaction_rate, 1),
                    'positive_rate': round((positive_count / total * 100) if total > 0 else 0, 1),
                    'negative_rate': round((negative_count / total * 100) if total > 0 else 0, 1)
                })

        # 添加所有统计数据到上下文
        context.update({
            'total_evaluations': total_evaluations,
            # 不再使用平均评分
            'satisfaction_rate': satisfaction_rate,
            'complaint_rate': complaint_rate,
            'sentiment_stats': sentiment_dict,
            'satisfaction_stats': satisfaction_dict,
            'sentiment_trend_dates': [date.strftime('%m-%d') for date in trend_dates],
            'sentiment_trend_positive': sentiment_trend['positive'],
            'sentiment_trend_neutral': sentiment_trend['neutral'],
            'sentiment_trend_negative': sentiment_trend['negative'],
            'satisfaction_trend_dates': [date.strftime('%m-%d') for date in trend_dates],
            'satisfaction_trend_values': satisfaction_trend,
            'department_names': [stat['name'] for stat in department_stats],
            'department_satisfaction_rates': [stat['satisfaction_rate'] for stat in department_stats],
            'department_positive_rates': [stat['positive_rate'] for stat in department_stats],
            'department_negative_rates': [stat['negative_rate'] for stat in department_stats],
            'top_staff': sorted(staff_stats, key=lambda x: (-x['satisfaction_rate'], -x['positive_rate']))[:5],
            'bottom_staff': sorted(staff_stats, key=lambda x: (x['satisfaction_rate'], -x['negative_rate']))[:5],
            'recent_evaluations': evaluations.select_related(
                'bed', 'bed__department'  # 修改为直接使用bed关联
            ).order_by('-created_at')[:10]
        })

        return context

class DepartmentListView(LoginRequiredMixin, ListView):
    """科室列表视图"""
    model = Department
    template_name = "qrmanager/department_list.html"
    context_object_name = "departments"
    login_url = '/login/'
    ordering = ['code', 'name']

    def get_queryset(self):
        queryset = Department.objects.all()
        queryset = queryset.prefetch_related(
            'staffs',      # 预加载工作人员
            'beds',        # 预加载床位
            'beds__qrcode',  # 预加载二维码
            'beds__qrcode__evaluations'  # 预加载评价
        ).annotate(
            staff_count=Count('staffs', distinct=True),
            bed_count=Count('beds', distinct=True),
            qrcode_count=Count('beds__qrcode', distinct=True),
            evaluation_count=Count('beds__qrcode__evaluations', distinct=True)
        )
        return queryset.order_by('code', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取所有科室
        departments = self.get_queryset()
        
        # 计算总数（使用聚合函数确保计算准确性）
        totals = departments.aggregate(
            total_staff=Sum('staff_count'),
            total_beds=Sum('bed_count'),
            total_evaluations=Sum('evaluation_count')
        )
        
        context.update({
            'total_staff': totals['total_staff'] or 0,
            'total_beds': totals['total_beds'] or 0,
            'total_evaluations': totals['total_evaluations'] or 0
        })
        
        return context

class DepartmentCreateView(LoginRequiredMixin, CreateView):
    """创建科室视图"""
    model = Department
    template_name = "qrmanager/department_form.html"
    fields = ['name']
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录创建操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建科室: {self.object.name}"
        )
        messages.success(self.request, '科室创建成功！')
        return response

class DepartmentUpdateView(LoginRequiredMixin, UpdateView):
    """更新科室视图"""
    model = Department
    template_name = "qrmanager/department_form.html"
    fields = ['name']
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'

    def form_valid(self, form):
        # 记录更新前的数据
        old_data = {
            'name': self.get_object().name
        }
        response = super().form_valid(form)
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新科室: {self.object.name}",
            old_data=old_data
        )
        messages.success(self.request, '科室信息更新成功！')
        return response

class DepartmentDeleteView(LoginRequiredMixin, DeleteView):
    """删除科室视图"""
    model = Department
    success_url = reverse_lazy('qrmanager:department_list')
    login_url = '/login/'
    
    def post(self, request, *args, **kwargs):
        department = self.get_object()
        password = request.POST.get('password')
        
        # 检查是否是AJAX请求
        is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
        
        # 验证用户密码
        if not request.user.check_password(password):
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': '密码不正确，请重新输入'})
            else:
                messages.error(request, '密码不正确，请重新输入')
                return redirect('qrmanager:department_list')
        
        # 检查是否有关联的工作人员
        staff_count = Staff.objects.filter(department=department).count()
        if staff_count > 0:
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': f'科室"{department.name}"有 {staff_count} 名关联的工作人员，不允许删除！请先解除工作人员与科室的关联。'})
            else:
                messages.error(request, f'科室"{department.name}"有 {staff_count} 名关联的工作人员，不允许删除！请先解除工作人员与科室的关联。')
                return redirect('qrmanager:department_list')
        
        # 检查关联数据
        related_data = {
            'staff_count': staff_count,
            'bed_count': Bed.objects.filter(department=department).count(),
            'print_template_exists': hasattr(department, 'print_template')
        }
        
        try:
            # 先记录要删除的科室信息
            department_name = department.name
            
            # 获取关联的床位
            beds = Bed.objects.filter(department=department)
            
            # 获取关联的二维码
            qrcodes_to_delete = []
            for bed in beds:
                try:
                    if hasattr(bed, 'qrcode'):
                        qrcodes_to_delete.append(bed.qrcode)
                except QRCode.DoesNotExist:
                    pass
            
            # 获取关联的评价
            evaluations_to_delete = []
            for bed in beds:
                evals = Evaluation.objects.filter(bed=bed)
                evaluations_to_delete.extend(list(evals))
            
            # 记录删除操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=department,
                operation_type="delete",
                description=f"删除科室: {department_name}",
                extra_data={
                    **related_data,
                    'beds_deleted': [str(bed) for bed in beds],
                    'qrcodes_deleted': [str(qrcode) for qrcode in qrcodes_to_delete],
                    'evaluations_deleted': len(evaluations_to_delete)
                }
            )
            
            # 删除关联的评价
            for evaluation in evaluations_to_delete:
                evaluation.delete()
            
            # 删除关联的二维码
            for qrcode in qrcodes_to_delete:
                qrcode.delete()
            
            # 删除关联的床位
            beds.delete()
            
            # 执行删除科室操作（会自动删除关联的打印模板，因为是CASCADE关系）
            department.delete()
            
            success_message = f'科室"{department_name}"及其关联的床位、二维码和评价已成功删除！'
            if is_ajax:
                return JsonResponse({'status': 'success', 'message': success_message})
            else:
                messages.success(request, success_message)
                return redirect(self.success_url)
            
        except Exception as e:
            # 如果删除失败，记录错误日志
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=department,
                operation_type="delete",
                description=f"删除科室失败: {department_name}",
                status="error",
                error=str(e)
            )
            
            error_message = f'科室删除失败：{str(e)}'
            if is_ajax:
                return JsonResponse({'status': 'error', 'message': error_message})
            else:
                messages.error(request, error_message)
                return redirect('qrmanager:department_list')

class BedQRCodeView(LoginRequiredMixin, TemplateView):
    """床位二维码预览、生成及替换视图"""
    template_name = "qrmanager/bed_qrcode_preview.html"
    login_url = '/login/'

    def get(self, request, *args, **kwargs):
        bed = get_object_or_404(Bed, pk=self.kwargs['pk'])
        # 如果床位没有关联二维码，则创建一个
        try:
            qrcode_obj = bed.qrcode
        except QRCode.DoesNotExist:
            qrcode_obj = QRCode.objects.create(bed=bed, name=f"QRCode for bed {bed.number}")
            # 记录二维码创建操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=qrcode_obj,
                operation_type="create",
                description=f"为床位 {bed.number} 创建二维码"
            )
        
        # 记录查看操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="view_qrcode",
            description=f"查看床位 {bed.number} 的二维码",
            extra_data={
                'bed_id': bed.id,
                'qrcode_id': qrcode_obj.id,
                'has_template': bed.department and hasattr(bed.department, 'print_template')
            }
        )
        
        return super().get(request, *args, **kwargs)
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        bed = get_object_or_404(Bed, pk=self.kwargs['pk'])
        context['bed'] = bed
        
        # 获取或创建二维码
        try:
            qrcode_obj = bed.qrcode
        except QRCode.DoesNotExist:
            qrcode_obj = QRCode.objects.create(bed=bed, name=f"QRCode for bed {bed.number}")
        
        context['qrcode'] = qrcode_obj
        
        # 使用安全URL生成二维码
        qr_data = qrcode_obj.get_secure_evaluation_url()
        
        # 获取打印模板
        template = None
        if bed.department and hasattr(bed.department, 'print_template'):
            template = bed.department.print_template
            
            # 如果科室没有专属模板，尝试获取公共模板
            if template is None:
                from .models import PrintTemplate
                public_template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
                if public_template:
                    template = public_template
        
        # 如果找到模板，使用统一的模板预览生成函数
        if template:
            from .qrcode_utils import generate_template_preview
            preview_data = generate_template_preview(template, qr_data=qr_data)
            context['qr_image'] = preview_data['qr_image_base64']
            context['template_data'] = preview_data['template_data']
        else:
            # 如果没有模板，只生成二维码
            _, img_base64 = generate_qrcode(qr_data, box_size=40, border=0)
            context['qr_image'] = img_base64
            context['template_data'] = None
        
        return context

class AdminAccountCreateView(LoginRequiredMixin, UserPassesTestMixin, CreateView):
    """用于创建管理账户的视图，仅供超级用户操作"""
    form_class = UserCreationForm
    template_name = "qrmanager/admin_account_form.html"
    success_url = reverse_lazy('qrmanager:dashboard')

    def test_func(self):
        return self.request.user.is_superuser

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录创建管理账户操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建管理账户: {self.object.username}",
            extra_data={
                'is_staff': self.object.is_staff,
                'is_superuser': self.object.is_superuser,
                'is_active': self.object.is_active
            }
        )
        messages.success(self.request, "管理账户创建成功！")
        return response

class AdminAccountPermissionView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """用于展示所有管理用户及其权限分配的视图，仅供超级用户操作"""
    model = User
    template_name = "qrmanager/admin_permissions.html"
    context_object_name = "admin_users"

    def test_func(self):
        return self.request.user.is_superuser

    def get_queryset(self):
        # 显示所有用户，不再限制只显示 is_staff=True 的用户
        queryset = User.objects.all().order_by('-date_joined')
        # 记录查看权限列表操作
        LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
            action="view_admin_permissions",
            description="查看管理员权限列表",
            extra_data={
                'admin_count': queryset.count()
            }
        )
        return queryset

class AdminAccountUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """用于更新单个管理用户权限的视图，仅供超级用户操作"""
    model = User
    fields = ['is_active', 'is_staff', 'is_superuser']
    template_name = "qrmanager/admin_account_update.html"
    success_url = reverse_lazy('qrmanager:admin_permissions')

    def test_func(self):
        return self.request.user.is_superuser

    def form_valid(self, form):
        # 记录更新前的权限状态
        old_instance = self.get_object()
        old_permissions = {
            'is_active': old_instance.is_active,
            'is_staff': old_instance.is_staff,
            'is_superuser': old_instance.is_superuser
        }
        
        response = super().form_valid(form)
        
        # 记录权限变更
        LoggerHelper.log_permission_change(
            user=self.request.user,
            target_user=self.object,
            permissions_before=old_permissions,
            permissions_after={
                'is_active': self.object.is_active,
                'is_staff': self.object.is_staff,
                'is_superuser': self.object.is_superuser
            }
        )
        
        messages.success(self.request, f"用户 {self.object.username} 的权限已更新！")
        return response

class AccountSettingsView(LoginRequiredMixin, UpdateView):
    """用于显示和更新当前用户账号设定的视图"""
    model = User
    fields = ['first_name', 'last_name', 'email']
    template_name = "qrmanager/account_settings.html"
    success_url = reverse_lazy('qrmanager:dashboard')

    def get_object(self):
        return self.request.user

    def form_valid(self, form):
        # 记录更新前的用户信息
        old_data = {
            'first_name': self.object.first_name,
            'last_name': self.object.last_name,
            'email': self.object.email
        }
        
        response = super().form_valid(form)
        
        # 记录个人信息更新
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description="更新个人信息",
            old_data=old_data,
            new_data={
                'first_name': form.cleaned_data['first_name'],
                'last_name': form.cleaned_data['last_name'],
                'email': form.cleaned_data['email']
            }
        )
        
        messages.success(self.request, "个人信息更新成功！")
        return response

from django.contrib.auth import views as auth_views

class CustomLoginView(auth_views.LoginView):
    """自定义登录视图"""
    template_name = 'qrmanager/login.html'

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录登录成功
        LoggerHelper.log_auth_operation(
            user=form.get_user(),
            operation_type="login",
            status="success",
            extra_data={
                'username': form.get_user().username,
                'ip_address': self.request.META.get('REMOTE_ADDR')
            }
        )
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # 记录登录失败
        LoggerHelper.log_auth_operation(
            user=None,
            operation_type="login",
            status="error",
            extra_data={
                'username': form.cleaned_data.get('username'),
                'ip_address': self.request.META.get('REMOTE_ADDR'),
                'error': '用户名或密码错误'
            }
        )
        return response

class CustomLogoutView(auth_views.LogoutView):
    """自定义登出视图"""
    next_page = 'qrmanager:login'

    def dispatch(self, request, *args, **kwargs):
        if request.user.is_authenticated:
            # 记录登出操作
            LoggerHelper.log_auth_operation(
                user=request.user,
                operation_type="logout",
                status="success",
                extra_data={
                    'ip_address': request.META.get('REMOTE_ADDR')
                }
            )
        return super().dispatch(request, *args, **kwargs)

class CustomPasswordChangeView(auth_views.PasswordChangeView):
    """自定义密码修改视图"""
    template_name = 'qrmanager/password_change.html'
    success_url = reverse_lazy('qrmanager:dashboard')

    def form_valid(self, form):
        response = super().form_valid(form)
        # 记录密码修改成功
        LoggerHelper.log_auth_operation(
            user=self.request.user,
            operation_type="password_change",
            status="success"
        )
        messages.success(self.request, "密码修改成功！")
        return response

    def form_invalid(self, form):
        response = super().form_invalid(form)
        # 记录密码修改失败
        LoggerHelper.log_auth_operation(
            user=self.request.user,
            operation_type="password_change",
            status="error",
            extra_data={'errors': form.errors}
        )
        return response

@login_required
def export_sentiment_report(request):
    """导出情感分析报告"""
    try:
        # 创建HTTP响应，设置CSV文件头
        response = HttpResponse(content_type='text/csv')
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        response['Content-Disposition'] = f'attachment; filename="sentiment_report_{timestamp}.csv"'
        
        # 创建CSV写入器
        writer = csv.writer(response)
        
        # 写入标题行
        writer.writerow(['评价时间', '科室', '床位号', '工作人员', '评分', '情感倾向', '评价内容'])
        
        # 获取所有评价数据
        evaluations = Evaluation.objects.select_related(
            'qr_code',
            'bed',  # 添加bed关联
            'bed__department',  # 修改为直接使用bed关联
            'staff'
        ).all()
        
        # 写入数据行
        for eval in evaluations:
            writer.writerow([
                eval.created_at.strftime('%Y-%m-%d %H:%M:%S'),
                eval.qr_code.bed.department.name if eval.qr_code.bed.department else '-',
                eval.qr_code.bed.number if eval.qr_code.bed else '-',
                eval.staff.name if eval.staff else '-',
                eval.rating,
                eval.get_sentiment_display(),
                eval.comment
            ])

        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            action="export_sentiment_report",
            description="导出情感分析报告",
            extra_data={
                'filename': f"sentiment_report_{timestamp}.csv",
                'record_count': evaluations.count()
            },
            request=request
        )
        
        return response
    except Exception as e:
        # 记录导出失败
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_sentiment_report_failed",
            description=f"导出情感分析报告失败: {str(e)}",
            status='error',
            extra_data={'error': str(e)}
        )
        messages.error(request, '导出报告失败，请稍后重试。')
        return redirect('qrmanager:sentiment_analysis')

class OperationLogListView(LoginRequiredMixin, ListView):
    """操作日志列表视图"""
    model = OperationLog
    template_name = 'qrmanager/operation_log_list.html'
    context_object_name = 'logs'
    paginate_by = 20

    def get_queryset(self):
        queryset = OperationLog.objects.select_related('user')

        # 过滤条件
        user_id = self.request.GET.get('user')
        action = self.request.GET.get('action')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        time_range = self.request.GET.get('time_range')

        # 应用过滤器
        if user_id and user_id.isdigit():
            queryset = queryset.filter(user_id=user_id)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if status:
            queryset = queryset.filter(status=status)

        # 日期范围过滤
        if date_from:
            try:
                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        if date_to:
            try:
                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass

        # 时间范围快捷筛选
        if time_range:
            now = timezone.now()
            if time_range == 'today':
                queryset = queryset.filter(created_at__date=now.date())
            elif time_range == 'yesterday':
                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))
            elif time_range == 'this_week':
                # 本周的开始（星期一）
                week_start = now.date() - timezone.timedelta(days=now.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif time_range == 'this_month':
                # 本月的开始（1号）
                month_start = now.date().replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)
            elif time_range == 'last_30_days':
                # 过去30天
                days_30 = now.date() - timezone.timedelta(days=30)
                queryset = queryset.filter(created_at__date__gte=days_30)

        return queryset.order_by('-created_at')


    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        now = timezone.now()
        
        # 统计数据
        context['total_logs'] = OperationLog.objects.count()
        context['today_logs'] = OperationLog.objects.filter(created_at__date=now.date()).count()
        context['active_users'] = User.objects.filter(
            operationlog__created_at__gte=now - timezone.timedelta(days=7)
        ).distinct().count()
        context['error_logs'] = OperationLog.objects.filter(
            status='error',
            created_at__gte=now - timezone.timedelta(days=7)
        ).count()

        # 计算变化百分比
        last_week_total = OperationLog.objects.filter(
            created_at__date__range=[
                now.date() - timezone.timedelta(days=14),
                now.date() - timezone.timedelta(days=7)
            ]
        ).count()
        this_week_total = OperationLog.objects.filter(
            created_at__date__range=[
                now.date() - timezone.timedelta(days=7),
                now.date()
            ]
        ).count()
        
        yesterday_total = OperationLog.objects.filter(
            created_at__date=now.date() - timezone.timedelta(days=1)
        ).count()

        context['total_change_percentage'] = (
            ((this_week_total - last_week_total) / last_week_total * 100)
            if last_week_total > 0 else 0
        )
        context['today_change_percentage'] = (
            ((context['today_logs'] - yesterday_total) / yesterday_total * 100)
            if yesterday_total > 0 else 0
        )

        # 操作趋势数据
        trend_data = OperationLog.objects.filter(
            created_at__gte=now - timezone.timedelta(days=7)
        ).annotate(
            date=TruncDate('created_at')
        ).values('date').annotate(
            count=Count('id')
        ).order_by('date')

        trend_dates = [item['date'].strftime('%Y-%m-%d') for item in trend_data]
        trend_counts = [item['count'] for item in trend_data]
        
        context['trend_dates'] = json.dumps(trend_dates)
        context['trend_counts'] = json.dumps(trend_counts)

        # 操作类型分布
        type_distribution = OperationLog.objects.filter(
            created_at__gte=now - timezone.timedelta(days=7)
        ).values('action').annotate(
            value=Count('id')
        ).values('action', 'value')

        context['type_distribution'] = json.dumps([
            {'name': item['action'], 'value': item['value']}
            for item in type_distribution
        ])

        # 活跃用户TOP5
        context['top_users'] = User.objects.filter(
            operationlog__created_at__gte=now - timezone.timedelta(days=7)
        ).annotate(
            operation_count=Count('operationlog'),
            last_operation=Max('operationlog__created_at')
        ).order_by('-operation_count')[:5]

        # 为每个用户添加最常用操作
        for user in context['top_users']:
            most_common = OperationLog.objects.filter(
                user=user,
                created_at__gte=now - timezone.timedelta(days=7)
            ).values('action').annotate(
                count=Count('id')
            ).order_by('-count').first()
            user.most_common_action = most_common['action'] if most_common else '-'

        # 筛选选项
        context['users'] = User.objects.filter(
            operationlog__isnull=False
        ).distinct()
        context['action_types'] = OperationLog.objects.values_list(
            'action', flat=True
        ).distinct()
        context['status_choices'] = OperationLog.STATUS_CHOICES
        context['ip_addresses'] = OperationLog.objects.values_list(
            'ip_address', flat=True
        ).distinct()

        # 错误日志分析
        error_logs = OperationLog.objects.filter(
            status='error',
            created_at__gte=now - timezone.timedelta(days=7)
        )
        context['error_analysis'] = {
            'total': error_logs.count(),
            'by_action': error_logs.values('action').annotate(count=Count('id')),
            'by_user': error_logs.values('user__username').annotate(count=Count('id')),
            'recent': error_logs.order_by('-created_at')[:5]
        }

        return context

    def post(self, request, *args, **kwargs):
        """处理导出日志的请求"""
        action = request.POST.get('action')
        if action == 'export_excel':
            return self.export_excel()
        elif action == 'export_pdf':
            return self.export_pdf()
        return redirect('qrmanager:operation_logs')

    def export_excel(self):
        """导出Excel格式的日志"""
        try:
            response = HttpResponse(content_type='application/ms-excel')
            response['Content-Disposition'] = 'attachment; filename="operation_logs.xlsx"'
            
            # TODO: 实现Excel导出逻辑
            
            # 记录导出操作
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="export_operation_logs",
                description="导出操作日志(Excel格式)",
                status="success"
            )
            
            return response
        except Exception as e:
            messages.error(self.request, f"导出失败: {str(e)}")
            return redirect('qrmanager:operation_logs')

    def export_pdf(self):
        """导出PDF格式的日志"""
        try:
            response = HttpResponse(content_type='application/pdf')
            response['Content-Disposition'] = 'attachment; filename="operation_logs.pdf"'
            
            # TODO: 实现PDF导出逻辑
            
            # 记录导出操作
            LoggerHelper.log_operation(
            user=self.request.user,
            request=self.request,
                action="export_operation_logs",
                description="导出操作日志(PDF格式)",
                status="success"
            )
            
            return response
        except Exception as e:
            messages.error(self.request, f"导出失败: {str(e)}")
            return redirect('qrmanager:operation_logs')

class StaffBulkImportView(LoginRequiredMixin, FormView):
    """批量导入工作人员视图"""
    form_class = BulkStaffImportForm
    success_url = reverse_lazy('qrmanager:staff_list')

    def form_valid(self, form):
        excel_file = form.cleaned_data['excel_file']
        try:
            import openpyxl
            wb = openpyxl.load_workbook(excel_file)
            ws = wb.active
            
            success_count = 0
            error_count = 0
            error_messages = []
            
            # 验证表头
            headers = [cell.value for cell in ws[1]]
            expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
            
            # 检查表头是否符合要求
            if not all(header in headers for header in expected_headers):
                missing_headers = [h for h in expected_headers if h not in headers]
                error_messages.append(f"表头缺少必要字段: {', '.join(missing_headers)}")
                messages.error(self.request, f"表头格式不正确，缺少必要字段: {', '.join(missing_headers)}")
                return self.form_invalid(form)
            
            # 获取字段索引
            work_number_idx = headers.index('工号')
            name_idx = headers.index('姓名')
            staff_type_idx = headers.index('人员类型')
            title_idx = headers.index('职称')
            department_idx = headers.index('科室')
            
            # 从第二行开始读取数据（跳过表头）
            for row_idx, row in enumerate(ws.iter_rows(min_row=2), 2):
                try:
                    # 获取每列的值
                    work_number = row[work_number_idx].value
                    name = row[name_idx].value
                    staff_type_name = row[staff_type_idx].value
                    title_name = row[title_idx].value
                    department_name = row[department_idx].value
                    
                    # 清理数据（去除前后空格）
                    if work_number: work_number = str(work_number).strip()
                    if name: name = str(name).strip()
                    if staff_type_name: staff_type_name = str(staff_type_name).strip()
                    if title_name: title_name = str(title_name).strip()
                    if department_name: department_name = str(department_name).strip()
                    
                    # 跳过空行（所有字段都为空）
                    if not any([work_number, name, staff_type_name, title_name, department_name]):
                        continue
                    
                    # 验证必填字段
                    if not all([work_number, name, staff_type_name, department_name]):
                        missing_fields = []
                        if not work_number: missing_fields.append('工号')
                        if not name: missing_fields.append('姓名')
                        # 职称不是必填字段
                        if not title_name: missing_fields.append('职称')
                        if not department_name: missing_fields.append('科室')
                        
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 缺少必填字段 {', '.join(missing_fields)}")
                        continue
                    
                    # 获取科室（宽松比较）
                    department = None
                    for dept in Department.objects.all():
                        if dept.name.strip().lower() == department_name.strip().lower():
                            department = dept
                            break
                    
                    if not department:
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 科室 '{department_name}' 不存在")
                        continue
                    
                    # 获取人员类型（宽松比较）
                    dict_staff_type = None
                    for item in DictionaryItem.objects.filter(dictionary__code='staff_type', is_active=True):
                        if item.name.strip().lower() == staff_type_name.strip().lower():
                            dict_staff_type = item
                            break
                    
                    if not dict_staff_type:
                        error_count += 1
                        error_messages.append(f"行 {row_idx}: 人员类型 '{staff_type_name}' 不存在或未激活")
                        continue
                    
                    # 从字典项获取对应的StaffType对象
                    try:
                        staff_type = StaffType.objects.get(code=dict_staff_type.code)
                    except StaffType.DoesNotExist:
                        # 如果不存在，则创建一个新的StaffType对象
                        staff_type = StaffType.objects.create(
                            code=dict_staff_type.code,
                            name=dict_staff_type.name,
                            display_order=dict_staff_type.sort_order,
                            is_active=dict_staff_type.is_active
                        )
                    
                    # 获取职称（宽松比较）
                    title = None
                    # 获取职称（宽松比较）- 只在提供了职称时验证
                    title = None
                    if title_name:
                        for item in DictionaryItem.objects.filter(dictionary__code='staff_title', is_active=True):
                            if item.name.strip().lower() == title_name.strip().lower():
                                title = item
                                break
                        
                        if not title:
                            error_count += 1
                            error_messages.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            continue
                    existing_staff = Staff.objects.filter(work_number=work_number).first()
                    if existing_staff:
                        # 更新现有记录
                        existing_staff.name = name
                        existing_staff.staff_type = staff_type
                        existing_staff.title = title
                        existing_staff.department = department
                        existing_staff.save()
                        success_count += 1
                    else:
                        # 创建新记录
                        Staff.objects.create(
                            work_number=work_number,
                            name=name,
                            staff_type=staff_type,
                            title=title,
                            department=department
                        )
                        success_count += 1
                    
                except Exception as e:
                    error_count += 1
                    error_messages.append(f"行 {row_idx}: {str(e)}")
            
            # 记录导入操作
            LoggerHelper.log_model_operation(
                self.request,
                None,
                'bulk_import',
                extra_data={
                    'success_count': success_count,
                    'error_count': error_count,
                    'error_messages': error_messages
                }
            )
            
            if success_count > 0:
                messages.success(self.request, f'成功导入 {success_count} 条记录')
            if error_count > 0:
                messages.warning(self.request, f'导入失败 {error_count} 条记录')
                for msg in error_messages[:5]:  # 只显示前5条错误信息，避免页面过长
                    messages.error(self.request, msg)
                if len(error_messages) > 5:
                    messages.error(self.request, f"还有 {len(error_messages) - 5} 条错误信息未显示")
                    
        except Exception as e:
            messages.error(self.request, f'文件处理失败：{str(e)}')
            return self.form_invalid(form)
            
        return super().form_valid(form)
        
    def get(self, request, *args, **kwargs):
        """如果是GET请求，直接重定向到工作人员列表页面"""
        return redirect('qrmanager:staff_list')
        
    def post(self, request, *args, **kwargs):
        """处理POST请求，支持AJAX验证"""
        # 检查是否是验证请求
        if 'validate' in request.POST:
            try:
                if 'excel_file' not in request.FILES:
                    return JsonResponse({
                        'status': 'error',
                        'message': '未找到上传的文件'
                    })
                
                excel_file = request.FILES['excel_file']
                
                # 验证文件类型
                if not excel_file.name.endswith(('.xlsx', '.xls')):
                    return JsonResponse({
                        'status': 'error',
                        'message': '文件格式不正确，请上传.xlsx或.xls格式的Excel文件'
                    })
                
                # 验证文件大小
                if excel_file.size > 5 * 1024 * 1024:  # 5MB
                    return JsonResponse({
                        'status': 'error',
                        'message': '文件过大，请上传小于5MB的文件'
                    })
                
                # 验证文件内容
                import openpyxl
                wb = openpyxl.load_workbook(excel_file)
                ws = wb.active
                
                # 获取表头
                headers = [cell.value for cell in ws[1]]
                expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
                
                # 检查表头是否符合要求
                if not all(header in headers for header in expected_headers):
                    missing_headers = [h for h in expected_headers if h not in headers]
                    return JsonResponse({
                        'status': 'error',
                        'message': f"表头格式不正确，缺少必要字段: {', '.join(missing_headers)}"
                    })
                
                # 获取字段索引
                work_number_idx = headers.index('工号')
                name_idx = headers.index('姓名')
                staff_type_idx = headers.index('人员类型')
                title_idx = headers.index('职称')
                department_idx = headers.index('科室')
                
                # 获取有效的科室列表
                valid_departments = list(Department.objects.all().values_list('name', flat=True))
                valid_departments_clean = [name.strip() if name else "" for name in valid_departments]
                
                # 获取有效的人员类型和职称
                valid_staff_types = list(DictionaryItem.objects.filter(
                    dictionary__code='staff_type',
                    is_active=True
                ).values_list('name', flat=True))
                valid_staff_types_clean = [name.strip() if name else "" for name in valid_staff_types]
                
                valid_titles = list(DictionaryItem.objects.filter(
                    dictionary__code='staff_title',
                    is_active=True
                ).values_list('name', flat=True))
                valid_titles_clean = [name.strip() if name else "" for name in valid_titles]
                
                # 添加调试日志
                print(f"有效科室列表: {valid_departments}")
                print(f"有效人员类型列表: {valid_staff_types}")
                print(f"有效职称列表: {valid_titles}")
                
                # 验证数据行
                errors = []
                empty_rows = 0
                data_rows = 0
                
                for row_idx, row in enumerate(ws.iter_rows(min_row=2), 2):
                    # 获取每列的值
                    work_number = row[work_number_idx].value
                    name = row[name_idx].value
                    staff_type_name = row[staff_type_idx].value
                    title_name = row[title_idx].value
                    department_name = row[department_idx].value
                    
                    # 清理数据（去除前后空格）
                    if work_number: work_number = str(work_number).strip()
                    if name: name = str(name).strip()
                    if staff_type_name: staff_type_name = str(staff_type_name).strip()
                    if title_name: title_name = str(title_name).strip()
                    if department_name: department_name = str(department_name).strip()
                    
                    # 检查是否为空行（所有字段都为空）
                    if not any([work_number, name, staff_type_name, title_name, department_name]):
                        empty_rows += 1
                        continue
                    
                    # 计数有效数据行
                    data_rows += 1
                    
                    # 验证必填字段
                    if not all([work_number, name, staff_type_name, department_name]):
                        missing_fields = []
                        if not work_number: missing_fields.append('工号')
                        if not staff_type_name: missing_fields.append('人员类型')
                        # 职称不是必填字段
                        if not title_name: missing_fields.append('职称')
                        if not department_name: missing_fields.append('科室')
                        
                        errors.append(f"行 {row_idx}: 缺少必填字段 {', '.join(missing_fields)}")
                        continue
                    
                    # 验证科室是否存在（宽松比较）
                    department_found = False
                    for valid_dept, valid_dept_clean in zip(valid_departments, valid_departments_clean):
                        if department_name.strip().lower() == valid_dept_clean.lower():
                            department_found = True
                            break
                    
                    if not department_found:
                        errors.append(f"行 {row_idx}: 科室 '{department_name}' 不存在，请从下拉列表中选择有效科室")
                        print(f"科室验证失败: '{department_name}' 不在有效列表中")
                    
                    # 验证人员类型是否有效（宽松比较）
                    staff_type_found = False
                    for valid_type, valid_type_clean in zip(valid_staff_types, valid_staff_types_clean):
                        if staff_type_name.strip().lower() == valid_type_clean.lower():
                            staff_type_found = True
                            break
                    
                    if not staff_type_found:
                        errors.append(f"行 {row_idx}: 人员类型 '{staff_type_name}' 不存在或未激活")
                        print(f"人员类型验证失败: '{staff_type_name}' 不在有效列表中")
                    # 验证职称是否有效（宽松比较）- 只在提供了职称时验证
                    if title_name:
                        title_found = False
                        for valid_title, valid_title_clean in zip(valid_titles, valid_titles_clean):
                            if title_name.strip().lower() == valid_title_clean.lower():
                                title_found = True
                                break
                        
                        if not title_found:
                            errors.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            print(f"职称验证失败: '{title_name}' 不在有效列表中")
                            errors.append(f"行 {row_idx}: 职称 '{title_name}' 不存在或未激活")
                            print(f"职称验证失败: '{title_name}' 不在有效列表中")
                
                # 获取数据行数（不包括空行）
                print(f"总行数: {ws.max_row - 1}, 有效数据行: {data_rows}, 空行: {empty_rows}")
                
                # 如果有错误，返回错误信息
                if errors:
                    return JsonResponse({
                        'status': 'error',
                        'message': f'文件验证失败，请修正以下错误（共{len(errors)}个错误，{data_rows}行有效数据，{empty_rows}行空行）：',
                        'errors': errors
                    })
                
                # 验证通过
                return JsonResponse({
                    'status': 'success',
                    'message': f'文件验证通过，共{data_rows}行有效数据',
                    'data': {
                        'row_count': data_rows
                    }
                })
                
            except Exception as e:
                return JsonResponse({
                    'status': 'error',
                    'message': f'文件验证失败: {str(e)}'
                })
        
        # 正常的表单提交
        return super().post(request, *args, **kwargs)

class DownloadStaffTemplateView(LoginRequiredMixin, View):
    """下载工作人员导入模板视图"""
    def get(self, request):
        # 导入必要的模块
        import openpyxl
        from openpyxl import Workbook
        from openpyxl.utils import get_column_letter
        from openpyxl.styles import Font, PatternFill
        from openpyxl.worksheet.datavalidation import DataValidation
        
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "工作人员导入模板"
        
        # 设置表头样式
        header_font = Font(bold=True, color="FFFFFF")
        header_fill = PatternFill(start_color="2F75B5", end_color="2F75B5", fill_type="solid")
        
        # 设置表头
        headers = ['工号', '姓名', '人员类型', '职称', '科室']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = header_font
            cell.fill = header_fill
            # 设置列宽
            ws.column_dimensions[get_column_letter(col)].width = 20
        
        # 添加示例数据（确保示例数据使用系统中存在的科室）
        # 获取第一个有效科室作为示例
        example_department = Department.objects.first()
        example_staff_type = DictionaryItem.objects.filter(dictionary__code='staff_type', is_active=True).first()
        example_title = DictionaryItem.objects.filter(dictionary__code='staff_title', is_active=True).first()
        
        example_data = [
            '001', 
            '张三', 
            example_staff_type.name if example_staff_type else '医生', 
            example_title.name if example_title else '主任医师', 
            example_department.name if example_department else '请选择科室'
        ]
        
        for col, value in enumerate(example_data, 1):
            ws.cell(row=2, column=col, value=value)
        
        # 获取人员类型和职称的有效值
        try:
            # 尝试从StaffType模型获取数据
            from .models import StaffType
            staff_types = [(type.code, type.name) for type in StaffType.objects.filter(is_active=True)]
        except:
            # 如果失败，从DictionaryItem获取
            staff_types = [(item.name, item.name) for item in DictionaryItem.objects.filter(
                dictionary__code='staff_type',
                is_active=True
            )]
        
        titles = [(item.name, item.name) for item in DictionaryItem.objects.filter(
            dictionary__code='staff_title',
            is_active=True
        )]
        
        # 获取所有科室 - 去掉is_active过滤条件
        departments = Department.objects.all().order_by('name')
        department_names = list(departments.values_list('name', flat=True))
        # 确保科室名称不包含特殊符号
        clean_department_names = [name.strip() for name in department_names]
        
        # 设置数据验证 - 科室必须从列表中选择
        dv = DataValidation(type="list", formula1=f'"{",".join(clean_department_names)}"', allow_blank=False)
        dv.error = '请选择有效的科室'
        dv.errorTitle = '科室无效'
        dv.prompt = '请从下拉列表中选择科室'
        dv.promptTitle = '科室选择'
        ws.add_data_validation(dv)
        dv.add(f'E2:E1000')  # 应用到科室列，包括示例行
        
        # 在A1单元格上方添加重要说明
        ws.insert_rows(1)
        ws.merge_cells('A1:E1')
        important_note = ws.cell(row=1, column=1, value="重要提示：请勿删除或修改示例行，只需在下方添加您的数据。空行将被自动忽略。")
        important_note.font = Font(bold=True, color="FF0000", size=12)
        important_note.alignment = openpyxl.styles.Alignment(horizontal='center')
        
        # 在右侧添加说明（从第7列开始）
        note_col = 7
        title_font = Font(bold=True, color="2F75B5", size=12)
        subtitle_font = Font(bold=True, color="2F75B5")
        
        # 添加标题
        ws.cell(row=1, column=note_col, value="导入说明").font = title_font
        ws.column_dimensions[get_column_letter(note_col)].width = 30
        
        # 添加说明内容
        row = 2
        ws.cell(row=row, column=note_col, value="1. 所有字段都为必填项").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=note_col, value="2. 空行将被自动忽略").font = Font(bold=True)
        row += 1
        ws.cell(row=row, column=note_col, value="3. 请不要修改表头").font = Font(bold=True)
        row += 2
        
        ws.cell(row=row, column=note_col, value="4. 有效的人员类型：").font = subtitle_font
        row += 1
        for _, name in staff_types:
            ws.cell(row=row, column=note_col, value=f"• {name}")
            row += 1
        row += 1
            
        ws.cell(row=row, column=note_col, value="5. 有效的职称：").font = subtitle_font
        row += 1
        for _, name in titles:
            ws.cell(row=row, column=note_col, value=f"• {name}")
            row += 1
        row += 1
            
        ws.cell(row=row, column=note_col, value="6. 科室必须从下拉列表中选择").font = Font(bold=True, color="FF0000")
        row += 1
        ws.cell(row=row, column=note_col, value="   不允许创建新科室").font = Font(bold=True, color="FF0000")
        row += 2
        
        # 添加所有科室的详细信息
        ws.cell(row=row, column=note_col, value="7. 系统中所有可用科室：").font = subtitle_font
        row += 1
        
        # 直接在主工作表中列出所有科室信息
        for dept in departments:
            dept_info = f"• {dept.name} (编码: {dept.code or '无编码'})"
            ws.cell(row=row, column=note_col, value=dept_info)
            row += 1
            
            # 如果有备注，则缩进显示备注信息
            if dept.remarks:
                ws.cell(row=row, column=note_col, value=f"    备注: {dept.remarks}")
                row += 1
        
        row += 1
        ws.cell(row=row, column=note_col, value=f"   共有 {departments.count()} 个科室").font = Font(color="FF0000")
        row += 2
        
        ws.cell(row=row, column=note_col, value="8. 导入步骤：").font = subtitle_font
        row += 1
        ws.cell(row=row, column=note_col, value="   a. 在示例行下方添加数据")
        row += 1
        ws.cell(row=row, column=note_col, value="   b. 科室必须从下拉列表中选择")
        row += 1
        ws.cell(row=row, column=note_col, value="   c. 保存文件")
        row += 1
        ws.cell(row=row, column=note_col, value="   d. 上传文件进行导入")
        
        # 保存到内存中
        output = BytesIO()
        wb.save(output)
        output.seek(0)
        
        # 设置响应头
        response = HttpResponse(
            output.read(),
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        # 添加Content-Disposition响应头，指定文件名
        response['Content-Disposition'] = 'attachment; filename="工作人员导入模板.xlsx"'
        
        return response

class DictionaryListView(LoginRequiredMixin, ListView):
    """字典类型列表视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_list.html'
    context_object_name = 'dictionaries'

    def get_queryset(self):
        return Dictionary.objects.all().order_by('code')

class DictionaryCreateView(LoginRequiredMixin, CreateView):
    """创建字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_form.html'
    fields = ['code', 'name', 'description', 'is_active']
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'字典类型 {self.object.name} 创建成功！')
        return response

class DictionaryUpdateView(LoginRequiredMixin, UpdateView):
    """更新字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_form.html'
    fields = ['name', 'description', 'is_active']
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'字典类型 {self.object.name} 更新成功！')
        return response

class DictionaryDeleteView(LoginRequiredMixin, DeleteView):
    """删除字典类型视图"""
    model = Dictionary
    template_name = 'qrmanager/dictionary_confirm_delete.html'
    success_url = reverse_lazy('qrmanager:dictionary_list')

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        try:
            LoggerHelper.log_model_operation(request, self.object, 'delete')
            messages.success(request, f'字典类型 {self.object.name} 删除成功！')
            return super().delete(request, *args, **kwargs)
        except ProtectedError:
            messages.error(request, f'无法删除字典类型 {self.object.name}，因为它正在被使用！')
            return redirect('qrmanager:dictionary_list')

class DictionaryItemListView(LoginRequiredMixin, ListView):
    """字典项列表视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_list.html'
    context_object_name = 'items'

    def get_queryset(self):
        dictionary_id = self.kwargs.get('dictionary_id')
        return DictionaryItem.objects.filter(dictionary_id=dictionary_id).order_by('sort_order', 'code')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        dictionary_id = self.kwargs.get('dictionary_id')
        context['dictionary'] = get_object_or_404(Dictionary, pk=dictionary_id)
        return context

class DictionaryItemCreateView(LoginRequiredMixin, CreateView):
    """创建字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_form.html'
    fields = ['code', 'name', 'sort_order', 'is_active']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        dictionary_id = self.kwargs.get('dictionary_id')
        context['dictionary'] = get_object_or_404(Dictionary, pk=dictionary_id)
        return context

    def form_valid(self, form):
        dictionary_id = self.kwargs.get('dictionary_id')
        form.instance.dictionary_id = dictionary_id
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'create')
        messages.success(self.request, f'字典项 {self.object.name} 创建成功！')
        return response

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class DictionaryItemUpdateView(LoginRequiredMixin, UpdateView):
    """更新字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_form.html'
    fields = ['name', 'sort_order', 'is_active']

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['dictionary'] = self.object.dictionary
        return context

    def form_valid(self, form):
        response = super().form_valid(form)
        LoggerHelper.log_model_operation(self.request, self.object, 'update')
        messages.success(self.request, f'字典项 {self.object.name} 更新成功！')
        return response

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class DictionaryItemDeleteView(LoginRequiredMixin, DeleteView):
    """删除字典项视图"""
    model = DictionaryItem
    template_name = 'qrmanager/dictionary_item_confirm_delete.html'

    def delete(self, request, *args, **kwargs):
        self.object = self.get_object()
        try:
            LoggerHelper.log_model_operation(request, self.object, 'delete')
            messages.success(request, f'字典项 {self.object.name} 删除成功！')
            return super().delete(request, *args, **kwargs)
        except ProtectedError:
            messages.error(request, f'无法删除字典项 {self.object.name}，因为它正在被使用！')
            return redirect('qrmanager:dictionary_item_list', dictionary_id=self.object.dictionary_id)

    def get_success_url(self):
        return reverse_lazy('qrmanager:dictionary_item_list', kwargs={'dictionary_id': self.object.dictionary_id})

class ResetUserPasswordView(UserPassesTestMixin, View):
    """超级管理员重置用户密码的视图"""
    
    def test_func(self):
        """检查当前用户是否是超级管理员"""
        return self.request.user.is_authenticated and self.request.user.is_superuser
    
    def handle_no_permission(self):
        """处理无权限的情况"""
        if self.request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
            return JsonResponse({"status": "error", "message": "您没有权限执行此操作！"}, status=403)
        messages.error(self.request, '您没有权限执行此操作！')
        return redirect('qrmanager:admin_permissions')
    
    def post(self, request, pk):
        """处理重置密码的POST请求"""
        # 检查是否为 AJAX 请求，统一将 header 转换为小写进行比较
        is_ajax = request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest'
        
        try:
            # 获取目标用户
            target_user = get_object_or_404(User, pk=pk)
            
            # 获取表单数据
            new_password = request.POST.get('new_password')
            confirm_password = request.POST.get('confirm_password')
            admin_password = request.POST.get('admin_password')
            
            # 验证超级管理员密码
            if not request.user.check_password(admin_password):
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '您的管理员密码验证失败，无法执行此操作！'
                    })
                messages.error(request, '您的管理员密码验证失败，无法执行此操作！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证新密码
            if new_password != confirm_password:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '两次输入的密码不一致！'
                    })
                messages.error(request, '两次输入的密码不一致！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证密码长度
            if len(new_password) < 8:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '密码长度必须至少8位！'
                    })
                messages.error(request, '密码长度必须至少8位！')
                return redirect('qrmanager:admin_permissions')
            
            # 不允许重置自己的密码
            if target_user == request.user:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能通过此方式重置自己的密码！'
                    })
                messages.error(request, '不能通过此方式重置自己的密码！')
                return redirect('qrmanager:admin_permissions')
                
            # 设置新密码
            target_user.set_password(new_password)
            target_user.save()
            
            # 尝试记录操作日志，如果失败则捕获但不阻止密码重置
            try:
                OperationLog.objects.create(
                    user=request.user,
                    action='重置密码',
                    description=f'重置用户 {target_user.username} 的密码'
                )
            except Exception as log_exc:
                print('OperationLog creation error:', log_exc)
            
            if is_ajax:
                return JsonResponse({
                    'status': 'success',
                    'message': f'已成功重置用户 {target_user.username} 的密码！'
                })
            
            messages.success(request, f'已成功重置用户 {target_user.username} 的密码！')
            return redirect('qrmanager:admin_permissions')
            
        except User.DoesNotExist:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到指定用户！'
                }, status=404)
            messages.error(request, '未找到指定用户！')
            return redirect('qrmanager:admin_permissions')
            
        except Exception as e:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': f'重置密码时发生错误：{str(e)}'
                }, status=500)
            messages.error(request, f'重置密码时发生错误：{str(e)}')
            return redirect('qrmanager:admin_permissions')

class DeleteUserView(UserPassesTestMixin, View):
    """超级管理员删除用户的视图"""
    
    def test_func(self):
        """检查当前用户是否是超级管理员"""
        return self.request.user.is_authenticated and self.request.user.is_superuser
    
    def handle_no_permission(self):
        """处理无权限的情况"""
        if self.request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
            return JsonResponse({"status": "error", "message": "您没有权限执行此操作！"}, status=403)
        messages.error(self.request, '您没有权限执行此操作！')
        return redirect('qrmanager:admin_permissions')
    
    def post(self, request, pk):
        """处理删除用户的POST请求"""
        # 检查是否为 AJAX 请求
        is_ajax = request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest'
        
        try:
            # 获取目标用户
            target_user = get_object_or_404(User, pk=pk)
            
            # 不允许删除超级管理员
            if target_user.is_superuser:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能删除超级管理员用户！'
                    })
                messages.error(request, '不能删除超级管理员用户！')
                return redirect('qrmanager:admin_permissions')
            
            # 不允许删除自己
            if target_user == request.user:
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '不能删除自己的账户！'
                    })
                messages.error(request, '不能删除自己的账户！')
                return redirect('qrmanager:admin_permissions')
            
            # 验证超级管理员密码
            admin_password = request.POST.get('admin_password')
            if not request.user.check_password(admin_password):
                if is_ajax:
                    return JsonResponse({
                        'status': 'error',
                        'message': '管理员密码验证失败！'
                    })
                messages.error(request, '管理员密码验证失败！')
                return redirect('qrmanager:admin_permissions')
            
            # 记录要删除的用户信息
            user_info = {
                'username': target_user.username,
                'is_staff': target_user.is_staff,
                'is_active': target_user.is_active,
                'last_login': target_user.last_login.isoformat() if target_user.last_login else None
            }
            
            # 删除用户
            target_user.delete()
            
            # 记录删除操作
            LoggerHelper.log_operation(
            user=request.user,
            request=request,
                action="delete_user",
                description=f"删除用户: {user_info['username']}",
                status="success",
                extra_data=user_info
            )
            
            if is_ajax:
                return JsonResponse({
                    'status': 'success',
                    'message': f'用户 {user_info["username"]} 已成功删除！'
                })
            
            messages.success(request, f'用户 {user_info["username"]} 已成功删除！')
            return redirect('qrmanager:admin_permissions')
            
        except User.DoesNotExist:
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': '未找到指定用户！'
                }, status=404)
            messages.error(request, '未找到指定用户！')
            return redirect('qrmanager:admin_permissions')
            
        except Exception as e:
            # 记录错误
            LoggerHelper.log_operation(
            user=request.user,
            request=request,
                action="delete_user_failed",
                description=f"删除用户失败: {str(e)}",
                status="error",
                extra_data={'error': str(e)}
            )
            
            if is_ajax:
                return JsonResponse({
                    'status': 'error',
                    'message': f'删除用户时发生错误：{str(e)}'
                }, status=500)
            messages.error(request, f'删除用户时发生错误：{str(e)}')
            return redirect('qrmanager:admin_permissions')

# 添加自定义的 CSRF 失败处理视图

def csrf_failure(request, reason=""):
    """处理 CSRF 验证失败的请求，如果是 AJAX 请求则返回 JSON 否则返回标准错误页面"""
    if request.headers.get('x-requested-with', '').lower() == 'xmlhttprequest':
        return JsonResponse({"status": "error", "message": "CSRF验证失败，请刷新页面后重试！"}, status=403)
    # 如果不是AJAX请求，可以渲染一个标准的错误页面（需要创建csrf_failure.html模板）
    return render(request, 'csrf_failure.html', {'reason': reason})

class QRRegenerateView(LoginRequiredMixin, View):
    """重新生成二维码视图，用于更换床位的二维码"""
    login_url = '/login/'

    def post(self, request, pk):
        qr_code = get_object_or_404(QRCode, pk=pk)
        try:
            # 获取更换原因和安全标记
            reason = request.POST.get('reason', '')
            is_security_issue = request.POST.get('is_security_issue') == 'on'
            
            # 记录旧二维码信息
            old_code = qr_code.code
            
            # 更新二维码的 code 字段
            qr_code.code = uuid.uuid4()
            qr_code.save()
            
            # 使用统一的二维码生成函数
            from .qrcode_utils import generate_qrcode
            # 生成二维码内容（评价URL）
            data = qr_code.get_evaluation_url()
            # 不需要保存图片，因为图片会在需要时动态生成
            
            # 创建二维码历史记录
            if qr_code.bed:
                QRCodeHistory.objects.create(
                    bed=qr_code.bed,
                    old_code=old_code,
                    new_code=qr_code.code,
                    reason=reason,
                    created_by=request.user,
                    is_security_issue=is_security_issue
                )
            
            # 记录重新生成操作
            LoggerHelper.log_model_operation(
                user=request.user,
                instance=qr_code,
                operation_type="regenerate",
                description=f"重新生成二维码: {qr_code.name}",
                old_data={'code': str(old_code)},
                new_data={
                    'code': str(qr_code.code),
                    'reason': reason,
                    'is_security_issue': is_security_issue
                }
            )
            
            messages.success(request, "二维码已成功重新生成！")
        except Exception as e:
            # 记录重新生成失败
            LoggerHelper.log_operation(
                user=request.user,
                request=request,
                action="regenerate_qrcode_failed",
                description=f"重新生成二维码失败: {str(e)}",
                status='error',
                extra_data={
                    'qrcode_id': pk,
                    'error': str(e)
                }
            )
            messages.error(request, f"重新生成二维码失败: {str(e)}")
        
        # 重定向回二维码列表页面
        return redirect('qrmanager:qrcode_list')

class PrintTemplateCreateView(LoginRequiredMixin, CreateView):
    """创建打印模板视图"""
    model = PrintTemplate
    form_class = PrintTemplateForm
    template_name = 'qrmanager/print_template_form.html'

    def get_success_url(self):
        return reverse_lazy('qrmanager:print_template_list')

    def get_initial(self):
        initial = super().get_initial()
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                initial['name'] = f"{department.name}打印模板"
            except Department.DoesNotExist:
                pass
        else:
            initial['name'] = "公共打印模板"
            initial['is_public'] = True
        return initial

    def form_valid(self, form):
        # 设置科室（如果有）
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                form.instance.department = department
                form.instance.is_public = False
            except Department.DoesNotExist:
                messages.error(self.request, '未找到指定科室')
                return self.form_invalid(form)
        else:
            # 创建公共模板
            form.instance.is_public = True
            form.instance.department = None
        
        # 检查是否已存在该科室的模板文件，如果存在则清理
        try:
            import os
            from django.conf import settings
            
            # 确定文件名前缀
            department_code = form.instance.department.code if form.instance.department else 'public'
            file_prefix = f"template_{department_code}"
            
            # 检查media/print_templates目录下是否有同名文件
            template_dir = os.path.join(settings.MEDIA_ROOT, 'print_templates')
            if os.path.exists(template_dir):
                for filename in os.listdir(template_dir):
                    # 如果找到同一科室的旧模板文件（以相同前缀开头），则删除
                    if filename.startswith(file_prefix) and filename != os.path.basename(form.instance.background_image.name):
                        file_path = os.path.join(template_dir, filename)
                        if os.path.exists(file_path):
                            os.remove(file_path)
                            # 记录删除操作
                            LoggerHelper.log_operation(
                                user=self.request.user,
                                request=self.request,
                                action="delete_template_file",
                                description=f"创建新模板时删除旧模板文件: {filename}"
                            )
        except Exception as e:
            # 记录错误但不中断流程
            LoggerHelper.log_operation(
                user=self.request.user,
                request=self.request,
                action="delete_template_file_error",
                description=f"删除旧模板文件失败: {str(e)}"
            )
        
        # 保存模板
        response = super().form_valid(form)
        
        # 记录创建操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="create",
            description=f"创建{'公共' if self.object.is_public else '科室'}打印模板: {self.object.name}"
        )
        
        messages.success(self.request, '打印模板创建成功！')
        return response

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        department_id = self.kwargs.get('department_id')
        if department_id:
            try:
                context['department'] = Department.objects.get(pk=department_id)
            except Department.DoesNotExist:
                pass
        return context

class PrintTemplateUpdateView(LoginRequiredMixin, UpdateView):
    """更新打印模板视图"""
    model = PrintTemplate
    form_class = PrintTemplateForm
    template_name = 'qrmanager/print_template_form.html'

    def get_success_url(self):
        return reverse_lazy('qrmanager:print_template_list')

    def form_valid(self, form):
        # 记录更新前的数据
        old_data = {
            'name': self.object.name,
            'qr_position_x': self.object.qr_position_x,
            'qr_position_y': self.object.qr_position_y,
            'qr_size': self.object.qr_size,
            'is_active': self.object.is_active
        }
        
        # 如果上传了新的背景图片，清理旧的模板文件
        if 'background_image' in form.changed_data and self.object.background_image:
            try:
                # 获取旧文件路径
                old_file_path = self.object.background_image.path
                # 保存旧文件名，用于日志记录
                old_file_name = os.path.basename(old_file_path)
                
                # 删除旧文件
                import os
                if os.path.exists(old_file_path):
                    os.remove(old_file_path)
                    
                # 记录删除操作
                LoggerHelper.log_operation(
                    user=self.request.user,
                    request=self.request,
                    action="delete_template_file",
                    description=f"删除旧模板文件: {old_file_name}"
                )
            except Exception as e:
                # 记录错误但不中断流程
                LoggerHelper.log_operation(
                    user=self.request.user,
                    request=self.request,
                    action="delete_template_file_error",
                    description=f"删除旧模板文件失败: {str(e)}"
                )
        
        response = super().form_valid(form)
        
        # 记录更新操作
        LoggerHelper.log_model_operation(
            user=self.request.user,
            instance=self.object,
            operation_type="update",
            description=f"更新打印模板: {self.object.name}",
            old_data=old_data
        )
        
        messages.success(self.request, '打印模板更新成功！')
        return response

class PrintTemplatePreviewView(LoginRequiredMixin, DetailView):
    """打印模板预览视图"""
    model = PrintTemplate
    template_name = 'qrmanager/print_template_preview.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        template = self.get_object()
        
        # 使用统一的模板预览生成函数
        from .qrcode_utils import generate_template_preview
        preview_data = generate_template_preview(template)
        
        # 将二维码图像添加到上下文
        context['qr_image'] = preview_data['qr_image_base64']
        
        return context

@login_required
def api_departments(request):
    """科室 API 接口"""
    departments = Department.objects.all().order_by('code')
    data = [{
        'id': dept.id,
        'code': dept.code,
        'name': dept.name,
        'staff_count': dept.staff.count(),
        'bed_count': dept.bed_set.count()
    } for dept in departments]
    return JsonResponse({'departments': data})

@login_required
def api_beds(request):
    """床位 API 接口"""
    department_id = request.GET.get('department')
    # 只选择活跃的床位
    beds = Bed.objects.select_related('department').filter(is_active=True)
    if department_id:
        beds = beds.filter(department_id=department_id)
    data = [{
        'id': bed.id,
        'number': bed.number,
        'department': {
            'id': bed.department.id,
            'name': bed.department.name
        } if bed.department else None,
        'area': bed.get_area_display(),
        'staff': {
            'id': bed.staff.id,
            'name': bed.staff.name
        } if bed.staff else None
    } for bed in beds]
    return JsonResponse({'beds': data})

@login_required
def api_staff(request):
    """工作人员 API 接口"""
    department_id = request.GET.get('department')
    staff = Staff.objects.select_related('department', 'staff_type', 'title')
    if department_id:
        staff = staff.filter(department_id=department_id)
    data = [{
        'id': s.id,
        'work_number': s.work_number,
        'name': s.name,
        'department': {
            'id': s.department.id,
            'name': s.department.name
        } if s.department else None,
        'staff_type': {
            'id': s.staff_type.id,
            'name': s.staff_type.name
        } if s.staff_type else None,
        'title': {
            'id': s.title.id,
            'name': s.title.name
        } if s.title else None
    } for s in staff]
    return JsonResponse({'staff': data})

@login_required
def api_qrcodes(request):
    """二维码 API 接口"""
    department_id = request.GET.get('department')
    qrcodes = QRCode.objects.select_related(
        'bed',
        'bed__department',
        'bed__staff'
    ).prefetch_related('evaluations')
    if department_id:
        qrcodes = qrcodes.filter(bed__department_id=department_id)
    data = [{
        'id': qr.id,
        'code': str(qr.code),
        'bed': {
            'id': qr.bed.id,
            'number': qr.bed.number,
            'department': {
                'id': qr.bed.department.id,
                'name': qr.bed.department.name
            } if qr.bed.department else None
        } if qr.bed else None,
        'evaluation_count': qr.evaluations.count(),
        'satisfaction_rate': qr.evaluations.filter(is_satisfied=True).count() / qr.evaluations.count() * 100 if qr.evaluations.count() > 0 else 0
    } for qr in qrcodes]
    return JsonResponse({'qrcodes': data})

@login_required
def api_evaluations(request):
    """评价 API 接口"""
    department_id = request.GET.get('department')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    
    evaluations = Evaluation.objects.select_related(
        'qr_code',
        'bed',
        'bed__department'
    )
    
    if department_id:
        evaluations = evaluations.filter(bed__department_id=department_id)
    if date_from:
        evaluations = evaluations.filter(created_at__date__gte=date_from)
    if date_to:
        evaluations = evaluations.filter(created_at__date__lte=date_to)
        
    data = [{
        'id': e.id,
        'rating': e.rating,
        'comment': e.comment,
        'sentiment': e.sentiment,
        'created_at': e.created_at.strftime('%Y-%m-%d %H:%M:%S'),
        'qr_code': {
            'id': e.qr_code.id if e.qr_code else None,
            'code': str(e.qr_code.code) if e.qr_code else None,
        },
        'bed': {
            'id': e.bed.id,
            'number': e.bed.number,
            'department': {
                'id': e.bed.department.id if e.bed.department else None,
                'name': e.bed.department.name if e.bed.department else None
            } if e.bed.department else None
        } if e.bed else None,
        'staff': {
            'id': e.staff.id,
            'name': e.staff.name
        } if e.staff else None
    } for e in evaluations]
    
    return JsonResponse({'evaluations': data})

@login_required
def import_departments(request):
    """导入科室数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('科室编码') or not row.get('科室名称'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码和科室名称为必填项')
                    continue
                    
                # 检查科室编码是否已存在
                if Department.objects.filter(code=row['科室编码']).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 已存在')
                    continue
                    
                # 创建科室
                department = Department.objects.create(
                    code=row['科室编码'],
                    name=row['科室名称'],
                    remarks=row.get('备注', '')
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=department,
                    operation_type="create",
                    description=f"导入创建科室: {department.name}",
                    extra_data={
                        'code': department.code,
                        'name': department.name,
                        'remarks': department.remarks,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_departments(request):
    """导出科室数据"""
    try:
        # 创建一个 Excel 工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "科室信息"
        
        # 写入表头
        headers = ['科室编码', '科室名称', '工作人员数', '床位数', '二维码数', '评价数', '创建时间', '更新时间', '备注']
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
        
        # 获取所有科室数据
        departments = Department.objects.annotate(
            staff_count=Count('staff', distinct=True),
            bed_count=Count('bed', distinct=True),
            qrcode_count=Count('bed__qrcode', distinct=True),
            evaluation_count=Count('bed__qrcode__evaluations', distinct=True)
        ).order_by('code', 'name')
        
        # 写入数据
        for row, dept in enumerate(departments, 2):
            ws.cell(row=row, column=1, value=dept.code)
            ws.cell(row=row, column=2, value=dept.name)
            ws.cell(row=row, column=3, value=dept.staff_count)
            ws.cell(row=row, column=4, value=dept.bed_count)
            ws.cell(row=row, column=5, value=dept.qrcode_count)
            ws.cell(row=row, column=6, value=dept.evaluation_count)
            ws.cell(row=row, column=7, value=dept.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=8, value=dept.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=9, value=dept.remarks)
        
        # 设置列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=departments.xlsx'
        
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_departments",
            description=f"导出科室数据，共 {departments.count()} 条记录",
            extra_data={
                'total_count': departments.count(),
                'with_staff': sum(1 for dept in departments if dept.staff_count > 0),
                'with_beds': sum(1 for dept in departments if dept.bed_count > 0)
            }
        )
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:department_list')

@login_required
def import_beds(request):
    """导入床位数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 检查床位号是否已存在
                if Bed.objects.filter(number=row['床位号'], department=department).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号 {row["床位号"]} 在科室 {department.name} 中已存在')
                    continue
                    
                # 获取工作人员（如果有）
                staff = None
                if row.get('工作人员工号'):
                    try:
                        staff = Staff.objects.get(work_number=row['工作人员工号'])
                    except Staff.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 工作人员工号 {row["工作人员工号"]} 不存在')
                        continue
                    
                # 创建床位
                bed = Bed.objects.create(
                    number=row['床位号'],
                    department=department,
                    area=row.get('区域', ''),
                    staff=staff
                )
                
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=bed,
                    operation_type="create",
                    description=f"导入创建床位: {bed.number}",
                    extra_data={
                        'department': department.name,
                        'area': bed.area,
                        'staff': staff.name if staff else None,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_beds(request):
    """导出床位数据"""
    try:
        # 创建一个 Excel 工作簿
        wb = Workbook()
        ws = wb.active
        ws.title = "床位信息"
        
        # 写入表头
        headers = [
            '床位号', '科室编码', '科室名称', '区域', 
            '工作人员工号', '工作人员姓名', '二维码ID', '评价数', 
            '床位创建时间', '床位更新时间', 
            '二维码创建时间', '二维码更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            # 设置表头样式
            cell = ws.cell(row=1, column=col)
            cell.font = Font(bold=True)
            cell.fill = PatternFill(start_color="E0E0E0", end_color="E0E0E0", fill_type="solid")
        
        # 获取所有床位数据
        beds_query = Bed.objects.select_related(
            'department',
            'qrcode'
        ).annotate(
            evaluation_count=Count('qrcode__evaluations', distinct=True)
        )
        
        # 检查是否有科室参数
        department_id = request.GET.get('department')
        if department_id:
            try:
                department_id = int(department_id)
                beds_query = beds_query.filter(department_id=department_id)
                
                # 获取科室名称添加到文件名
                department_name = ""
                try:
                    department = Department.objects.get(pk=department_id)
                    department_name = f"_{department.name}"
                except:
                    pass
                
                # 创建响应
                response = HttpResponse(
                    content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                )
                response['Content-Disposition'] = f'attachment; filename=beds{department_name}.xlsx'
            except ValueError:
                # 如果科室ID不是有效整数，忽略筛选
                pass
        else:
            # 创建响应
            response = HttpResponse(
                content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
            )
            response['Content-Disposition'] = 'attachment; filename=beds_all.xlsx'
        
        # 在排序前先获取总数
        beds_count = beds_query.count()
        
        # 获取最终排序的床位列表
        if department_id:
            # 如果是特定科室，先按简单排序获取数据
            beds = beds_query.order_by('number')
            
            # 使用与BedListView相同的自然排序逻辑
            import re
            def natural_sort_key(bed):
                # 检查床位号是否以数字开头
                if bed.number and bed.number[0].isdigit():
                    # 数字开头的床位，排在前面（优先级0）
                    prefix = 0
                else:
                    # 非数字开头的床位，排在后面（优先级1）
                    prefix = 1
                
                # 提取床位号中的数字和非数字部分
                convert = lambda text: int(text) if text.isdigit() else text.lower()
                # 优先级作为第一个排序键，然后按自然排序
                return [prefix] + [convert(c) for c in re.split('([0-9]+)', bed.number)]
            
            # 应用自然排序
            beds = sorted(beds, key=natural_sort_key)
        else:
            # 如果是所有科室，先按科室再按床位号排序
            beds = beds_query.order_by('department__name', 'number')
        
        # 写入数据
        for row, bed in enumerate(beds, 2):
            # 基本信息
            ws.cell(row=row, column=1, value=bed.number)
            ws.cell(row=row, column=2, value=bed.department.code if bed.department else '')
            ws.cell(row=row, column=3, value=bed.department.name if bed.department else '')
            ws.cell(row=row, column=4, value=bed.get_area_display())
            ws.cell(row=row, column=5, value=bed.staff.work_number if bed.staff else '')
            ws.cell(row=row, column=6, value=bed.staff.name if bed.staff else '')
            ws.cell(row=row, column=7, value=str(bed.qrcode.code) if hasattr(bed, 'qrcode') else '')
            ws.cell(row=row, column=8, value=bed.evaluation_count)
            
            # 床位创建和更新时间（直接从床位模型获取）
            if bed.created_at:
                ws.cell(row=row, column=9, value=bed.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=9, value='未知')
                
            if bed.updated_at:
                ws.cell(row=row, column=10, value=bed.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=10, value='未更新')
            
            # 二维码创建和更新时间
            if hasattr(bed, 'qrcode') and bed.qrcode:
                ws.cell(row=row, column=11, value=bed.qrcode.created_at.strftime('%Y-%m-%d %H:%M:%S'))
                ws.cell(row=row, column=12, value=bed.qrcode.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            else:
                ws.cell(row=row, column=11, value='无二维码')
                ws.cell(row=row, column=12, value='无二维码')
        
        # 设置列宽
        for col in ws.columns:
            max_length = 0
            column = col[0].column_letter
            for cell in col:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
        
        # 设置条件格式（便于区分）
        for row in range(2, len(beds) + 2):
            # 设置床位信息部分和二维码信息部分的颜色区分
            for col in range(9, 11):  # 床位时间列
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color="E6F2FF", end_color="E6F2FF", fill_type="solid")
            
            for col in range(11, 13):  # 二维码时间列
                cell = ws.cell(row=row, column=col)
                cell.fill = PatternFill(start_color="F2E6FF", end_color="F2E6FF", fill_type="solid")
        
        # 记录导出操作
        description = f"导出床位数据，共 {beds_count} 条记录"
        if department_id:
            description += f"（科室ID: {department_id}）"
            
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_beds",
            description=description,
            extra_data={
                'total_count': beds_count,
                'department_id': department_id if department_id else None,
                'with_staff': sum(1 for bed in beds if bed.staff),
                'with_qrcode': sum(1 for bed in beds if hasattr(bed, 'qrcode')),
                'with_evaluations': sum(1 for bed in beds if bed.evaluation_count > 0)
            }
        )
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        # 如果有科室参数，返回到相应科室的床位列表
        department_id = request.GET.get('department')
        if department_id:
            return redirect(f"{reverse('qrmanager:bed_list')}?department={department_id}")
        return redirect('qrmanager:bed_list')

@login_required
def import_staff(request):
    """导入工作人员数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('工号') or not row.get('姓名') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 工号、姓名和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 检查工号是否已存在
                if Staff.objects.filter(work_number=row['工号']).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 工号 {row["工号"]} 已存在')
                    continue
                    
                # 获取人员类型（如果有）
                staff_type = None
                if row.get('人员类型'):
                    try:
                        staff_type = DictionaryItem.objects.get(
                            dictionary__code='staff_type',
                            name=row['人员类型']
                        )
                    except DictionaryItem.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 人员类型 {row["人员类型"]} 不存在')
                        continue
                        
                # 获取职称（如果有）
                title = None
                if row.get('职称'):
                    try:
                        title = DictionaryItem.objects.get(
                            dictionary__code='staff_title',
                            name=row['职称']
                        )
                    except DictionaryItem.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 职称 {row["职称"]} 不存在')
                        continue
                    
                # 创建工作人员
                staff = Staff.objects.create(
                    work_number=row['工号'],
                    name=row['姓名'],
                    department=department,
                    staff_type=staff_type,
                    title=title
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=staff,
                    operation_type="create",
                    description=f"导入创建工作人员: {staff.name}",
                    extra_data={
                        'work_number': staff.work_number,
                        'department': department.name,
                        'staff_type': staff_type.name if staff_type else None,
                        'title': title.name if title else None,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_staff(request):
    """导出工作人员数据"""
    try:
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "工作人员数据"
        
        # 设置表头
        headers = [
            '工号', '姓名', '科室编码', '科室名称', '人员类型', '职称',
            '创建时间', '更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            
        # 获取所有工作人员数据
        staff_list = Staff.objects.select_related(
            'department', 'staff_type', 'title'
        ).all()
        
        # 写入数据
        for row, staff in enumerate(staff_list, 2):
            ws.cell(row=row, column=1, value=staff.work_number)
            ws.cell(row=row, column=2, value=staff.name)
            ws.cell(row=row, column=3, value=staff.department.code if staff.department else '')
            ws.cell(row=row, column=4, value=staff.department.name if staff.department else '')
            ws.cell(row=row, column=5, value=staff.staff_type.name if staff.staff_type else '')
            ws.cell(row=row, column=6, value=staff.title.name if staff.title else '')
            ws.cell(row=row, column=7, value=staff.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=8, value=staff.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            
        # 调整列宽
        for col in range(1, len(headers) + 1):
            max_length = 0
            column = get_column_letter(col)
            
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
                    
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
            
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            operation_type="export",
            target_type="Staff",
            description=f"导出工作人员数据：{staff_list.count()} 条记录",
            extra_data={
                'count': staff_list.count(),
                'format': 'xlsx'
            }
        )
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=staff_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:staff_list')

@login_required
def import_qrcodes(request):
    """导入二维码数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号') or not row.get('科室编码'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号和科室编码为必填项')
                    continue
                    
                # 获取科室
                try:
                    department = Department.objects.get(code=row['科室编码'])
                except Department.DoesNotExist:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row["科室编码"]} 不存在')
                    continue
                    
                # 获取或创建床位
                bed, created = Bed.objects.get_or_create(
                    number=row['床位号'],
                    department=department,
                    defaults={
                        'area': row.get('区域', '')
                    }
                )
                
                # 检查床位是否已有二维码
                if hasattr(bed, 'qrcode'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位 {bed.number} 已有二维码')
                    continue
                    
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=qrcode,
                    operation_type="create",
                    description=f"导入创建二维码: {qrcode.name}",
                    extra_data={
                        'bed': bed.number,
                        'department': department.name,
                        'source': 'import'
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回导入结果
        return JsonResponse({
            'status': 'success',
            'message': f'导入完成：成功 {success_count} 条，失败 {error_count} 条',
            'details': {
                'success_count': success_count,
                'error_count': error_count,
                'error_messages': error_messages
            }
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': str(e)
        }, status=500)

@login_required
def export_qrcodes(request):
    """导出二维码数据"""
    try:
        # 创建工作簿和工作表
        wb = Workbook()
        ws = wb.active
        ws.title = "二维码数据"
        
        # 设置表头
        headers = [
            '二维码编号', '床位号', '科室编码', '科室名称', '区域',
            '评价数量', '平均评分', '创建时间', '更新时间'
        ]
        for col, header in enumerate(headers, 1):
            ws.cell(row=1, column=col, value=header)
            
        # 获取所有二维码数据
        qrcodes = QRCode.objects.select_related(
            'bed',
            'bed__department'
        ).prefetch_related(
            'evaluations'
        ).all()
        
        # 写入数据
        for row, qr in enumerate(qrcodes, 2):
            evaluations = list(qr.evaluations.all())
            evaluation_count = len(evaluations)
            avg_rating = sum(e.rating for e in evaluations) / evaluation_count if evaluation_count > 0 else 0
            
            ws.cell(row=row, column=1, value=str(qr.code))
            ws.cell(row=row, column=2, value=qr.bed.number if qr.bed else '')
            ws.cell(row=row, column=3, value=qr.bed.department.code if qr.bed and qr.bed.department else '')
            ws.cell(row=row, column=4, value=qr.bed.department.name if qr.bed and qr.bed.department else '')
            ws.cell(row=row, column=5, value=qr.bed.get_area_display() if qr.bed else '')
            ws.cell(row=row, column=6, value=evaluation_count)
            ws.cell(row=row, column=7, value=round(avg_rating, 2))
            ws.cell(row=row, column=8, value=qr.created_at.strftime('%Y-%m-%d %H:%M:%S'))
            ws.cell(row=row, column=9, value=qr.updated_at.strftime('%Y-%m-%d %H:%M:%S'))
            
        # 调整列宽
        for col in range(1, len(headers) + 1):
            max_length = 0
            column = get_column_letter(col)
            
            for cell in ws[column]:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
                    
            adjusted_width = (max_length + 2)
            ws.column_dimensions[column].width = adjusted_width
            
        # 记录导出操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="export_qrcodes",
            description=f"导出二维码数据：{qrcodes.count()} 条记录",
            extra_data={
                'count': qrcodes.count(),
                'with_evaluations': sum(1 for qr in qrcodes if qr.evaluations.exists())
            }
        )
        
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        response['Content-Disposition'] = 'attachment; filename=qrcodes_export.xlsx'
        
        # 保存工作簿
        wb.save(response)
        return response
        
    except Exception as e:
        messages.error(request, f'导出失败：{str(e)}')
        return redirect('qrmanager:qrcode_list')

@login_required
def print_department_qrcodes(request, department_id):
    """按科室批量打印二维码"""
    try:
        # 获取科室
        department = get_object_or_404(Department, id=department_id)
        
        # 获取打印模板ID
        template_id = request.GET.get('template_id')
        if not template_id:
            messages.warning(request, '请选择打印模板')
            return redirect('qrmanager:qrcode_list')
        
        # 获取打印份数
        copies = request.GET.get('copies', '1')
        try:
            copies = int(copies)
            if copies < 1:
                copies = 1
        except ValueError:
            copies = 1
        
        # 获取区域过滤条件
        area_filter = request.GET.get('area')
        
        # 获取科室下的所有床位
        beds_query = Bed.objects.filter(department=department, is_active=True)
        if area_filter:
            beds_query = beds_query.filter(area=area_filter)
        
        beds = beds_query.select_related('staff').all()
        
        # 获取床位对应的二维码
        qrcodes = []
        for bed in beds:
            qrcode = QRCode.objects.filter(bed=bed).first()
            if qrcode:
                qrcodes.append(qrcode)
        
        if not qrcodes:
            messages.warning(request, f'未找到{department.name}科室的二维码')
            return redirect('qrmanager:qrcode_list')
        
        # 获取打印模板
        print_template = get_object_or_404(PrintTemplate, id=template_id)
        
        # 创建PDF文档
        response = HttpResponse(content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename={department.name}_qrcodes.pdf'
        
        # 创建PDF文档
        from reportlab.lib.pagesizes import A4
        from reportlab.pdfgen import canvas
        import tempfile
        from PIL import Image
        import io
        import os
        
        # 创建临时文件
        with tempfile.NamedTemporaryFile(suffix='.pdf', delete=False) as tmp:
            pdf_path = tmp.name
        
        # 创建PDF
        c = canvas.Canvas(pdf_path, pagesize=A4)
        width, height = A4  # 获取页面尺寸
        
        # 加载模板背景图片
        if print_template.background_image:
            try:
                # 获取背景图片的完整路径
                background_image_path = print_template.background_image.path
                
                # 打开背景图片，保持原始质量
                background_img = Image.open(background_image_path)
                
                # 记录日志
                logger.info(f"加载模板背景图片: {background_image_path}, 尺寸: {background_img.size}, 格式: {background_img.format}")
                
                # 计算每页放置的二维码数量
                qrcodes_per_page = 1  # 使用模板时，每页只放一个二维码
                
                for i, qr in enumerate(qrcodes):
                    # 如果是新页面，则添加新页
                    if i > 0:
                        c.showPage()
                    
                    # 绘制背景图片，保持原始尺寸和质量
                    # 将背景图片保存为临时文件
                    temp_bg_path = f"temp_bg_{i}.{background_img.format.lower()}"
                    background_img.save(temp_bg_path, format=background_img.format, quality=100)
                    
                    # 在PDF中绘制背景图片，使用原始尺寸
                    c.drawImage(temp_bg_path, 0, 0, width, height, preserveAspectRatio=True)
                    
                    # 使用安全URL生成二维码
                    qr_img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                    
                    # 计算二维码在模板中的位置（毫米转换为点）
                    mm_to_pt = 72 / 25.4  # 1毫米 = 72/25.4点
                    qr_x = print_template.qr_position_x * mm_to_pt
                    qr_y = height - (print_template.qr_position_y * mm_to_pt) - (print_template.qr_size * mm_to_pt)
                    qr_size_pt = print_template.qr_size * mm_to_pt
                    
                    # 保存二维码为临时图片文件
                    temp_qr_path = f"temp_qr_{qr.id}.png"
                    qr_img.save(temp_qr_path, format="PNG", quality=100)
                    
                    # 在PDF中绘制二维码
                    c.drawImage(temp_qr_path, qr_x, qr_y, qr_size_pt, qr_size_pt)
                    
                    # 添加床位信息（可选）
                    if qr.bed:
                        c.setFont("Helvetica", 8)
                        info_text = f"床位: {qr.bed.number} | 科室: {department.name}"
                        if qr.bed.staff:
                            info_text += f" | 负责人: {qr.bed.staff.name}"
                        if hasattr(qr.bed, 'get_area_display') and qr.bed.get_area_display():
                            info_text += f" | 区域: {qr.bed.get_area_display()}"
                        
                        # 在二维码下方添加信息
                        c.drawString(qr_x, qr_y - 10, info_text)
                    
                    # 删除临时图片
                    os.remove(temp_qr_path)
                    os.remove(temp_bg_path)
                
                # 为每份复制创建额外的页面
                if copies > 1:
                    # 保存当前页面
                    c.showPage()
                    
                    # 复制所有页面
                    for _ in range(copies - 1):
                        for i, qr in enumerate(qrcodes):
                            # 如果是新页面，则添加新页
                            if i > 0 or _ > 0:
                                c.showPage()
                            
                            # 重新绘制背景图片
                            temp_bg_path = f"temp_bg_copy_{i}.{background_img.format.lower()}"
                            background_img.save(temp_bg_path, format=background_img.format, quality=100)
                            c.drawImage(temp_bg_path, 0, 0, width, height, preserveAspectRatio=True)
                            
                            # 重新生成二维码
                            qr_img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                            temp_qr_path = f"temp_qr_copy_{qr.id}.png"
                            qr_img.save(temp_qr_path, format="PNG", quality=100)
                            
                            # 在PDF中绘制二维码
                            c.drawImage(temp_qr_path, qr_x, qr_y, qr_size_pt, qr_size_pt)
                            
                            # 添加床位信息（可选）
                            if qr.bed:
                                c.setFont("Helvetica", 8)
                                info_text = f"床位: {qr.bed.number} | 科室: {department.name}"
                                if qr.bed.staff:
                                    info_text += f" | 负责人: {qr.bed.staff.name}"
                                if hasattr(qr.bed, 'get_area_display') and qr.bed.get_area_display():
                                    info_text += f" | 区域: {qr.bed.get_area_display()}"
                                
                                # 在二维码下方添加信息
                                c.drawString(qr_x, qr_y - 10, info_text)
                            
                            # 删除临时图片
                            os.remove(temp_qr_path)
                            os.remove(temp_bg_path)
            
            except Exception as e:
                logger.exception(f"处理模板背景图片时出错: {str(e)}")
                # 如果处理模板背景图片出错，回退到原始的表格方式
                messages.warning(request, f'处理模板背景图片时出错，使用默认表格方式: {str(e)}')
                
                # 使用原始的表格方式生成PDF
                c.showPage()  # 清除之前的页面
                
                # 添加科室标题
                c.setFont("Helvetica", 16)
                c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                
                # 每页放置的二维码数量
                qrcodes_per_page = 4
                margin = 50  # 页面边距
                qr_size = (width - 2 * margin) / 2
                
                for i, qr in enumerate(qrcodes):
                    # 计算当前页和位置
                    page = i // qrcodes_per_page
                    position = i % qrcodes_per_page
                    
                    # 如果是新页面，则添加新页
                    if position == 0 and i > 0:
                        c.showPage()
                        c.setFont("Helvetica", 16)
                        c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                    
                    # 计算二维码位置
                    row = position // 2
                    col = position % 2
                    x = margin + col * qr_size
                    y = height - margin - 50 - (row * qr_size)
                    
                    # 使用安全URL生成二维码
                    img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                    
                    # 保存为临时图片文件
                    temp_img_path = f"temp_qr_{qr.id}.png"
                    img.save(temp_img_path, format="PNG", quality=100)
                    
                    # 绘制标题
                    c.setFont("Helvetica", 12)
                    title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                    c.drawString(x + 10, y + qr_size - 20, title)
                    
                    # 绘制二维码
                    c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                    
                    # 删除临时图片
                    os.remove(temp_img_path)
                
                # 为每份复制创建额外的页面
                if copies > 1:
                    original_pages = (len(qrcodes) + qrcodes_per_page - 1) // qrcodes_per_page
                    
                    for _ in range(copies - 1):
                        for i, qr in enumerate(qrcodes):
                            # 计算当前页和位置
                            page = i // qrcodes_per_page
                            position = i % qrcodes_per_page
                            
                            # 如果是新页面，则添加新页
                            if position == 0:
                                c.showPage()
                                c.setFont("Helvetica", 16)
                                c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                            
                            # 计算二维码位置
                            row = position // 2
                            col = position % 2
                            x = margin + col * qr_size
                            y = height - margin - 50 - (row * qr_size)
                            
                            # 使用安全URL生成二维码
                            img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                            
                            # 保存为临时图片文件
                            temp_img_path = f"temp_qr_copy_{qr.id}.png"
                            img.save(temp_img_path, format="PNG", quality=100)
                            
                            # 绘制标题
                            c.setFont("Helvetica", 12)
                            title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                            c.drawString(x + 10, y + qr_size - 20, title)
                            
                            # 绘制二维码
                            c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                            
                            # 删除临时图片
                            os.remove(temp_img_path)
        else:
            # 如果没有模板背景图片，使用原始的表格方式
            # 添加科室标题
            c.setFont("Helvetica", 16)
            c.drawString(50, height - 50, f"{department.name} - 二维码打印")
            
            # 每页放置的二维码数量
            qrcodes_per_page = 4
            margin = 50  # 页面边距
            qr_size = (width - 2 * margin) / 2
            
            for i, qr in enumerate(qrcodes):
                # 计算当前页和位置
                page = i // qrcodes_per_page
                position = i % qrcodes_per_page
                
                # 如果是新页面，则添加新页
                if position == 0 and i > 0:
                    c.showPage()
                    c.setFont("Helvetica", 16)
                    c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                
                # 计算二维码位置
                row = position // 2
                col = position % 2
                x = margin + col * qr_size
                y = height - margin - 50 - (row * qr_size)
                
                # 使用安全URL生成二维码
                img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                
                # 保存为临时图片文件
                temp_img_path = f"temp_qr_{qr.id}.png"
                img.save(temp_img_path, format="PNG", quality=100)
                
                # 绘制标题
                c.setFont("Helvetica", 12)
                title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                c.drawString(x + 10, y + qr_size - 20, title)
                
                # 绘制二维码
                c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                
                # 删除临时图片
                os.remove(temp_img_path)
            
            # 为每份复制创建额外的页面
            if copies > 1:
                original_pages = (len(qrcodes) + qrcodes_per_page - 1) // qrcodes_per_page
                
                for _ in range(copies - 1):
                    for i, qr in enumerate(qrcodes):
                        # 计算当前页和位置
                        page = i // qrcodes_per_page
                        position = i % qrcodes_per_page
                        
                        # 如果是新页面，则添加新页
                        if position == 0:
                            c.showPage()
                            c.setFont("Helvetica", 16)
                            c.drawString(50, height - 50, f"{department.name} - 二维码打印")
                        
                        # 计算二维码位置
                        row = position // 2
                        col = position % 2
                        x = margin + col * qr_size
                        y = height - margin - 50 - (row * qr_size)
                        
                        # 使用安全URL生成二维码
                        img, _ = generate_qrcode(qr.get_secure_evaluation_url(), box_size=40, border=0)
                        
                        # 保存为临时图片文件
                        temp_img_path = f"temp_qr_copy_{qr.id}.png"
                        img.save(temp_img_path, format="PNG", quality=100)
                        
                        # 绘制标题
                        c.setFont("Helvetica", 12)
                        title = f"{qr.bed.department.name} - {qr.bed.number}号床位" if qr.bed else qr.name
                        c.drawString(x + 10, y + qr_size - 20, title)
                        
                        # 绘制二维码
                        c.drawImage(temp_img_path, x + 10, y - qr_size + 30, qr_size - 20, qr_size - 20)
                        
                        # 删除临时图片
                        os.remove(temp_img_path)
        
        # 保存PDF
        c.save()
        
        # 创建响应并设置响应头
        with open(pdf_path, 'rb') as f:
            response_content = f.read()
        
        # 删除临时PDF文件
        os.remove(pdf_path)
        
        # 记录打印操作
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="print_department_qrcodes",
            description=f"按科室批量打印二维码：{department.name}，共 {len(qrcodes)} 个，{copies} 份",
            extra_data={
                'department_id': department.id,
                'department_name': department.name,
                'qrcode_count': len(qrcodes),
                'copies': copies,
                'area_filter': area_filter,
                'template_id': template_id,
                'template_name': print_template.name if print_template else 'None'
            }
        )
        
        # 返回响应
        response = HttpResponse(response_content, content_type='application/pdf')
        response['Content-Disposition'] = f'attachment; filename={department.name}_qrcodes.pdf'
        return response
        
    except Exception as e:
        logger.exception(f"打印科室二维码时出错: {str(e)}")
        messages.error(request, f'打印失败：{str(e)}')
        return redirect('qrmanager:qrcode_list')

class PrintTemplateListView(LoginRequiredMixin, ListView):
    """打印模板列表视图"""
    template_name = 'qrmanager/print_template_list.html'
    context_object_name = 'departments'
    login_url = '/login/'

    def get_queryset(self):
        return Department.objects.prefetch_related('print_template').all().order_by('code', 'name')

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 获取公共模板
        context['public_template'] = PrintTemplate.objects.filter(is_public=True).first()
        context['total_templates'] = PrintTemplate.objects.count()
        context['active_templates'] = PrintTemplate.objects.filter(is_active=True).count()
        return context

class PrintTemplateDeleteView(LoginRequiredMixin, DeleteView):
    """删除打印模板视图"""
    model = PrintTemplate
    success_url = reverse_lazy('qrmanager:print_template_list')
    login_url = '/login/'

    def form_valid(self, form):
        """
        重写form_valid方法，在删除模板记录前删除对应的文件
        注意：Django推荐在DeleteView中使用form_valid而不是delete方法
        """
        template = self.get_object()
        success_message = '打印模板删除成功！'
        
        try:
            # 删除模板文件
            if template.background_image:
                try:
                    # 获取文件路径
                    file_path = template.background_image.path
                    
                    # 记录文件信息用于日志
                    file_info = {
                        'file_name': os.path.basename(file_path),
                        'file_path': file_path,
                        'file_size': os.path.getsize(file_path) if os.path.exists(file_path) else 0
                    }
                    
                    # 删除文件
                    if os.path.exists(file_path):
                        os.remove(file_path)
                        success_message += ' 模板文件已删除。'
                except Exception as e:
                    # 记录文件删除错误，但继续删除数据库记录
                    print(f"删除模板文件时出错: {str(e)}")
                    success_message += f' 但模板文件删除失败: {str(e)}'
            
            # 记录删除操作
            LoggerHelper.log_model_operation(
                user=self.request.user,
                instance=template,
                operation_type="delete",
                description=f"删除打印模板: {template.name}",
                extra_data={
                    'department': template.department.name if template.department else None,
                    'file_info': file_info if 'file_info' in locals() else None
                }
            )
            
            messages.success(self.request, success_message)
            return super().form_valid(form)
        except Exception as e:
            messages.error(self.request, f'删除失败：{str(e)}')
            return redirect('qrmanager:print_template_list')
    
    def delete(self, request, *args, **kwargs):
        """
        保留delete方法以兼容旧代码，但实际逻辑已移至form_valid
        """
        return super().delete(request, *args, **kwargs)

@login_required
def get_print_template(request, template_id):
    """获取打印模板信息的API端点"""
    try:
        # 尝试获取指定ID的模板
        try:
            template = PrintTemplate.objects.get(pk=template_id)
        except PrintTemplate.DoesNotExist:
            # 如果指定ID的模板不存在，尝试获取公共模板
            template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
            if not template:
                return JsonResponse({'error': '未找到打印模板，也没有可用的公共模板'}, status=404)
        
        data = {
            'id': template.id,
            'name': template.name,
            'print_width': template.print_width,
            'print_height': template.print_height,
            'qr_position_x': template.qr_position_x,
            'qr_position_y': template.qr_position_y,
            'qr_size': template.qr_size,
            'background_image': template.background_image.url if template.background_image else None,
            'is_public': template.is_public
        }
        return JsonResponse(data)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)

@login_required
def api_beds(request):
    """床位 API 接口"""
    department_id = request.GET.get('department')
    # 只选择活跃的床位
    beds = Bed.objects.select_related('department').filter(is_active=True)
    if department_id:
        beds = beds.filter(department_id=department_id)
    data = [{
        'id': bed.id,
        'number': bed.number,
        'department': {
            'id': bed.department.id,
            'name': bed.department.name
        } if bed.department else None,
        'area': bed.get_area_display(),
        'staff': {
            'id': bed.staff.id,
            'name': bed.staff.name
        } if bed.staff else None
    } for bed in beds]
    return JsonResponse({'beds': data})

@login_required
def api_department(request, pk):
    """获取科室信息"""
    try:
        department = Department.objects.get(pk=pk)
        data = {
            'id': department.id,
            'name': department.name,
            'code': department.code
        }
        # 检查是否有remarks字段，有些Department模型可能有这个字段
        if hasattr(department, 'remarks'):
            data['remarks'] = department.remarks
        return JsonResponse(data)
    except Department.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '科室不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

@login_required
def api_import_beds(request):
    """API: 导入床位数据"""
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
        
    try:
        file = request.FILES.get('file')
        department_id = request.POST.get('department_id')
        
        if not file:
            return JsonResponse({'status': 'error', 'message': '未找到上传的文件'}, status=400)
            
        if not department_id:
            return JsonResponse({'status': 'error', 'message': '未指定科室'}, status=400)
            
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '指定的科室不存在'}, status=400)
            
        # 检查文件类型
        if not file.name.endswith(('.xlsx', '.xls', '.csv')):
            return JsonResponse({'status': 'error', 'message': '不支持的文件格式'}, status=400)
            
        # 读取文件内容
        if file.name.endswith('.csv'):
            # 处理 CSV 文件
            decoded_file = file.read().decode('utf-8-sig').splitlines()
            reader = csv.DictReader(decoded_file)
            rows = list(reader)
        else:
            # 处理 Excel 文件
            wb = openpyxl.load_workbook(file)
            ws = wb.active
            headers = [cell.value for cell in ws[1]]
            rows = []
            for row in ws.iter_rows(min_row=2):
                row_data = {}
                for header, cell in zip(headers, row):
                    row_data[header] = cell.value
                rows.append(row_data)
                
        # 导入数据
        success_count = 0
        error_count = 0
        error_messages = []
        
        for row in rows:
            try:
                # 检查必填字段
                if not row.get('床位号'):
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号为必填项')
                    continue
                
                # 验证科室编码和名称
                if row.get('科室编码') and row.get('科室编码') != department.code:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室编码 {row.get("科室编码")} 与当前科室编码 {department.code} 不匹配')
                    continue
                
                if row.get('科室名称') and row.get('科室名称') != department.name:
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 科室名称 {row.get("科室名称")} 与当前科室名称 {department.name} 不匹配')
                    continue
                    
                # 检查床位号是否已存在
                if Bed.objects.filter(number=row['床位号'], department=department).exists():
                    error_count += 1
                    error_messages.append(f'行 {rows.index(row) + 2}: 床位号 {row["床位号"]} 在科室 {department.name} 中已存在')
                    continue
                    
                # 获取工作人员（如果有）
                staff = None
                if row.get('工作人员工号'):
                    try:
                        staff = Staff.objects.get(work_number=row['工作人员工号'])
                    except Staff.DoesNotExist:
                        error_count += 1
                        error_messages.append(f'行 {rows.index(row) + 2}: 工作人员工号 {row["工作人员工号"]} 不存在')
                        continue
                    
                # 创建床位
                bed = Bed.objects.create(
                    number=row['床位号'],
                    department=department,
                    area=row.get('区域', ''),
                    staff=staff
                )
                
                # 创建二维码
                qrcode = QRCode.objects.create(
                    bed=bed,
                    name=f"{department.name}-{bed.number}号床位二维码"
                )
                
                # 记录创建操作
                LoggerHelper.log_model_operation(
                    user=request.user,
                    instance=bed,
                    operation_type="create",
                    description=f"导入创建床位: {bed.number}",
                    extra_data={
                        'department': department.name,
                        'area': bed.area,
                        'staff': staff.name if staff else None
                    }
                )
                
                success_count += 1
                
            except Exception as e:
                error_count += 1
                error_messages.append(f'行 {rows.index(row) + 2}: {str(e)}')
                
        # 返回结果
        return JsonResponse({
            'status': 'success' if success_count > 0 else 'error',
            'message': f'成功导入 {success_count} 条记录' if success_count > 0 else '导入失败',
            'success': success_count > 0,
            'imported_count': success_count,
            'error_count': error_count,
            'errors': error_messages
        })
        
    except Exception as e:
        return JsonResponse({
            'status': 'error',
            'message': f'导入失败: {str(e)}',
            'error_messages': [str(e)]
        }, status=500)

@login_required
def api_bed_template(request):
    """API: 下载床位导入模板"""
    try:
        # 确保导入必要的模块
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment, Border, Side
        from datetime import datetime
        import urllib.parse
        
        department_id = request.GET.get('department_id')
        if not department_id:
            return JsonResponse({'status': 'error', 'message': '未指定科室'}, status=400)
            
        try:
            department = Department.objects.get(pk=department_id)
        except Department.DoesNotExist:
            return JsonResponse({'status': 'error', 'message': '指定的科室不存在'}, status=400)
            
        # 创建Excel文件
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "床位导入模板"
        
        # 设置表头样式
        header_font = Font(name='Arial', bold=True, color='FFFFFF', size=12)
        header_fill = PatternFill(start_color='0071E3', end_color='0071E3', fill_type='solid')
        header_alignment = Alignment(horizontal='center', vertical='center')
        header_border = Border(
            left=Side(style='thin', color='DDDDDD'),
            right=Side(style='thin', color='DDDDDD'),
            top=Side(style='thin', color='DDDDDD'),
            bottom=Side(style='thin', color='DDDDDD')
        )
        
        # 设置数据行样式
        data_font = Font(name='Arial', size=11)
        data_alignment = Alignment(horizontal='center', vertical='center')
        data_border = Border(
            left=Side(style='thin', color='DDDDDD'),
            right=Side(style='thin', color='DDDDDD'),
            top=Side(style='thin', color='DDDDDD'),
            bottom=Side(style='thin', color='DDDDDD')
        )
        
        # 获取所有科室列表
        all_departments = Department.objects.all().order_by('name')
        departments_info = [f"- {dept.name} (编码: {dept.code or '无'})" for dept in all_departments]
        
        # 准备说明文本
        instructions = [
            "导入说明",
            f"1. 科室编码必须为 {department.code}",
            f"2. 科室名称必须为 {department.name}",
            f"3. 科室说明: {department.remarks if department.remarks else '无'}",
            "4. 床位号不能重复，且必须填写",
            "5. 区域可填写 A 或 B，代表不同的病区",
            "6. 工作人员工号为可选项",
            "7. 如填写工号，必须是系统中已存在的工号",
            "8. 请勿修改表头和格式",
            "9. 填写完成后保存，然后在系统中导入"
        ]
        
        # 将所有科室信息添加到说明中
        # 删除这行，不再添加所有科室列表
        # instructions.extend(departments_info)
        
        # 设置表头
        headers = ['科室编码', '科室名称', '床位号', '区域', '工作人员工号']
        for col_num, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col_num)
            cell.value = header
            cell.font = header_font
            cell.fill = header_fill
            cell.alignment = header_alignment
            cell.border = header_border
            
        # 添加示例数据
        example_data = [
            [department.code, department.name, '101', '', ''],
            [department.code, department.name, '102', '', ''],
            [department.code, department.name, '103', '', ''],
            [department.code, department.name, '104', '', ''],
            [department.code, department.name, '105', '', ''],
        ]
        
        # 设置浅蓝色和浅绿色交替行
        row_colors = ['E6F0FF', 'E6FFF0', 'F5F5F7', 'F5F5F7', 'F5F5F7']
        
        for row_num, row_data in enumerate(example_data, 2):
            row_fill = PatternFill(
                start_color=row_colors[row_num-2], 
                end_color=row_colors[row_num-2], 
                fill_type='solid'
            )
            
            for col_num, value in enumerate(row_data, 1):
                cell = ws.cell(row=row_num, column=col_num)
                cell.value = value
                cell.font = data_font
                cell.alignment = data_alignment
                cell.border = data_border
                cell.fill = row_fill
                    
        # 添加说明信息到最右侧的列 (G 列)
        # 在 F 列添加空白分隔列
        ws.column_dimensions['F'].width = 5
        
        # 为说明标题设置样式
        instruction_title_font = Font(name='Arial', bold=True, size=12, color='0071E3')
        instruction_title_fill = PatternFill(start_color='F8F9FC', end_color='F8F9FC', fill_type='solid')
        
        # 为说明内容设置样式
        instruction_font = Font(name='Arial', size=11, color='555555')
        instruction_fill = PatternFill(start_color='F8F9FC', end_color='F8F9FC', fill_type='solid')
        
        # 添加说明到 G 列
        for i, instruction in enumerate(instructions):
            cell = ws.cell(row=i+1, column=7)  # G 列
            cell.value = instruction
            
            if i == 0:  # 标题行
                cell.font = instruction_title_font
            else:  # 内容行
                cell.font = instruction_font
                
            cell.fill = instruction_fill
            cell.alignment = Alignment(horizontal='left', vertical='center')
                
        # 设置列宽
        ws.column_dimensions['A'].width = 15  # 科室编码
        ws.column_dimensions['B'].width = 20  # 科室名称
        ws.column_dimensions['C'].width = 15  # 床位号
        ws.column_dimensions['D'].width = 10  # 区域
        ws.column_dimensions['E'].width = 20  # 工作人员工号
        ws.column_dimensions['G'].width = 40  # 说明列
        
        # 设置行高
        ws.row_dimensions[1].height = 25  # 表头行高
        for i in range(2, 7):
            ws.row_dimensions[i].height = 20  # 数据行高
            
        # 冻结表头
        ws.freeze_panes = 'A2'
            
        # 创建响应
        response = HttpResponse(
            content_type='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
        )
        # 确保文件名包含科室名称
        filename = f"床位导入模板_{department.name}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        
        # 对文件名进行URL编码，解决中文文件名问题
        encoded_filename = urllib.parse.quote(filename)
        
        # 设置Content-Disposition头，使用ASCII文件名和UTF-8编码的文件名
        ascii_filename = f"bed_import_template_{department.code}_{datetime.now().strftime('%Y%m%d')}.xlsx"
        response['Content-Disposition'] = f'attachment; filename="{ascii_filename}"; filename*=UTF-8\'\'{encoded_filename}'
        
        # 保存到响应
        wb.save(response)
        return response
        
    except Exception as e:
        import traceback
        traceback.print_exc()
        return JsonResponse({'status': 'error', 'message': f'生成模板失败: {str(e)}'}, status=500)

def departments(request):
    """获取所有科室列表"""
    departments_list = Department.objects.all().order_by('code', 'name')
    result = []
    
    # 将科室数据转换为字典
    for department in departments_list:
        result.append({
            'id': department.id,
            'name': department.name,
            'code': department.code or '',
            'remarks': department.remarks or '',
            'created_at': department.created_at.strftime('%Y-%m-%d %H:%M:%S'),
            'updated_at': department.updated_at.strftime('%Y-%m-%d %H:%M:%S')
        })
    
    # 添加CORS头
    response = JsonResponse(result, safe=False)
    response["Access-Control-Allow-Origin"] = "*"  # 允许所有域名访问
    response["Access-Control-Allow-Methods"] = "GET, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    
    # 添加调试信息
    print(f"返回{len(result)}个科室数据")
    
    return response

@login_required
def process_evaluation(request, evaluation_id):
    """处理评价状态更新"""
    evaluation = get_object_or_404(Evaluation, id=evaluation_id)
    status = request.GET.get('status', 'processed')
    next_url = request.GET.get('next', reverse('qrmanager:evaluation_list'))
    
    # 更新状态
    evaluation.process(request.user, status)
    
    # 记录操作
    LoggerHelper.log_operation(
        user=request.user,
        request=request,
        action="process_evaluation",
        description=f"将评价#{evaluation_id}标记为{dict(Evaluation.PROCESS_STATUS_CHOICES)[status]}",
        content_object=evaluation
    )
    
    messages.success(request, f'评价已成功标记为"{dict(Evaluation.PROCESS_STATUS_CHOICES)[status]}"')
    return redirect(next_url)

@login_required
def account_settings(request):
    """账号设置视图"""
    # 账号设置视图逻辑
    if request.method == 'POST':
        # 处理表单提交
        pass
    else:
        # 显示表单
        pass
    return render(request, 'qrmanager/account_settings.html')

@login_required
def api_management(request):
    """API管理中心视图"""
    from .models import APIKey, APILog
    from django.db.models import Count, Avg, Q
    from django.db.models.functions import TruncDate, TruncHour
    import datetime
    
    # 获取基本统计数据
    today = timezone.now().date()
    yesterday = today - datetime.timedelta(days=1)
    
    # 计算API接口数量 (RESTful API端点)
    from .urls import rest_api_urlpatterns
    api_count = len(rest_api_urlpatterns)
    
    # 获取API密钥数量
    api_keys_count = APIKey.objects.filter(is_active=True).count()
    
    # 获取今日API请求数量
    today_requests = APILog.objects.filter(created_at__date=today).count()
    
    # 计算API错误率 (今日)
    if today_requests > 0:
        error_count = APILog.objects.filter(created_at__date=today, status='error').count()
        error_rate = round((error_count / today_requests) * 100, 1)
    else:
        error_rate = 0
        
    # 获取近30天每日API调用统计
    thirty_days_ago = today - datetime.timedelta(days=30)
    daily_stats = APILog.objects.filter(created_at__date__gte=thirty_days_ago)\
        .annotate(date=TruncDate('created_at'))\
        .values('date')\
        .annotate(count=Count('id'), 
                 avg_response_time=Avg('response_time'),
                 error_count=Count('id', filter=Q(status='error')))\
        .order_by('date')
    
    # 获取今日每小时的API调用统计
    hourly_stats = APILog.objects.filter(created_at__date=today)\
        .annotate(hour=TruncHour('created_at'))\
        .values('hour')\
        .annotate(count=Count('id'))\
        .order_by('hour')
    
    # 获取热门API端点
    top_endpoints = APILog.objects.values('endpoint')\
        .annotate(count=Count('id'))\
        .order_by('-count')[:10]
        
    # 获取最近的API日志
    recent_logs = APILog.objects.select_related('api_key').order_by('-created_at')[:20]
    
    # 活跃的API密钥列表
    active_api_keys = APIKey.objects.filter(is_active=True).order_by('-last_used_at')
    
    # 计算昨日请求量增长率
    yesterday_requests = APILog.objects.filter(created_at__date=yesterday).count()
    if yesterday_requests > 0:
        growth_rate = round(((today_requests - yesterday_requests) / yesterday_requests) * 100, 1)
    else:
        growth_rate = 100 if today_requests > 0 else 0
    
    # 响应时间平均值
    avg_response_time = APILog.objects.filter(created_at__date=today).aggregate(avg=Avg('response_time'))['avg'] or 0
    
    # 构建统计数据
    api_stats = {
        'api_count': api_count,
        'api_keys': api_keys_count,
        'api_requests': today_requests,
        'api_errors': error_rate,
        'growth_rate': growth_rate,
        'avg_response_time': round(avg_response_time, 2)
    }
    
    # 构建图表数据
    chart_data = {
        'daily_stats': list(daily_stats),
        'hourly_stats': list(hourly_stats),
        'top_endpoints': list(top_endpoints)
    }
    
    context = {
        'api_stats': api_stats,
        'chart_data': chart_data,
        'recent_logs': recent_logs,
        'active_api_keys': active_api_keys
    }
    
    # 记录查看日志操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_management",
        description="查看API管理中心",
    )
    
    return render(request, 'qrmanager/api_management.html', context)

@login_required
def create_api_key(request):
    """创建新的API密钥"""
    from .models import APIKey
    import uuid
    import hashlib
    
    if request.method == 'POST':
        name = request.POST.get('name', '').strip()
        rate_limit_day = request.POST.get('rate_limit_day', 1000)
        rate_limit_hour = request.POST.get('rate_limit_hour', 100)
        rate_limit_minute = request.POST.get('rate_limit_minute', 10)
        expires_at = request.POST.get('expires_at', None)
        
        if not name:
            messages.error(request, "API密钥名称不能为空")
            return redirect('qrmanager:api_management')
        
        # 生成唯一的API密钥
        key = hashlib.sha256(str(uuid.uuid4()).encode()).hexdigest()
        
        # 创建API密钥
        api_key = APIKey.objects.create(
            name=name,
            key=key,
            created_by=request.user,
            rate_limit_day=rate_limit_day,
            rate_limit_hour=rate_limit_hour,
            rate_limit_minute=rate_limit_minute,
            expires_at=expires_at if expires_at else None
        )
        
        # 记录创建操作
        LoggerHelper.log_operation(
            user=request.user,
            action="create_api_key",
            description=f"创建API密钥：{name}",
            content_object=api_key
        )
        
        messages.success(request, f"API密钥已创建：{key}")
        return redirect('qrmanager:api_management')
    
    return redirect('qrmanager:api_management')


@login_required
def toggle_api_key_status(request, pk):
    """切换API密钥的启用状态"""
    from .models import APIKey
    
    api_key = get_object_or_404(APIKey, pk=pk)
    
    # 切换状态
    api_key.is_active = not api_key.is_active
    api_key.save(update_fields=['is_active'])
    
    # 记录操作
    status = "启用" if api_key.is_active else "禁用"
    LoggerHelper.log_operation(
        user=request.user,
        action="toggle_api_key",
        description=f"{status}API密钥：{api_key.name}",
        content_object=api_key
    )
    
    messages.success(request, f"API密钥已{status}")
    return redirect('qrmanager:api_management')


@login_required
def delete_api_key(request, pk):
    """删除API密钥"""
    from .models import APIKey
    
    api_key = get_object_or_404(APIKey, pk=pk)
    name = api_key.name
    
    # 记录操作
    LoggerHelper.log_operation(
        user=request.user,
        action="delete_api_key",
        description=f"删除API密钥：{name}"
    )
    
    api_key.delete()
    messages.success(request, f"API密钥 {name} 已删除")
    return redirect('qrmanager:api_management')


@login_required
def api_logs(request):
    """API调用日志视图"""
    from .models import APILog, APIKey
    from django.db.models import Q
    
    # 获取筛选参数
    api_key_id = request.GET.get('api_key')
    status = request.GET.get('status')
    date_from = request.GET.get('date_from')
    date_to = request.GET.get('date_to')
    endpoint = request.GET.get('endpoint')
    
    # 构建查询
    logs = APILog.objects.select_related('api_key').order_by('-created_at')
    
    # 应用筛选
    if api_key_id:
        logs = logs.filter(api_key_id=api_key_id)
    
    if status:
        logs = logs.filter(status=status)
    
    if date_from:
        logs = logs.filter(created_at__date__gte=date_from)
    
    if date_to:
        logs = logs.filter(created_at__date__lte=date_to)
    
    if endpoint:
        logs = logs.filter(endpoint__icontains=endpoint)
    
    # 分页
    paginator = Paginator(logs, 50)
    page = request.GET.get('page')
    logs = paginator.get_page(page)
    
    # 获取API密钥列表（用于筛选）
    api_keys = APIKey.objects.all()
    
    context = {
        'logs': logs,
        'api_keys': api_keys,
        'filters': {
            'api_key_id': api_key_id,
            'status': status,
            'date_from': date_from,
            'date_to': date_to,
            'endpoint': endpoint
        }
    }
    
    # 记录查看日志操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_logs",
        description="查看API调用日志"
    )
    
    return render(request, 'qrmanager/api_logs.html', context)


@login_required
def api_docs(request):
    """API文档视图"""
    from .urls import admin_api_urlpatterns, internal_api_urlpatterns, public_api_urlpatterns, rest_api_urlpatterns
    
    # 统计各类API数量
    api_counts = {
        'admin_api': len(admin_api_urlpatterns),
        'internal_api': len(internal_api_urlpatterns),
        'public_api': len(public_api_urlpatterns),
        'rest_api': len(rest_api_urlpatterns),
        'total': len(admin_api_urlpatterns) + len(internal_api_urlpatterns) + len(public_api_urlpatterns) + len(rest_api_urlpatterns)
    }
    
    context = {
        'api_counts': api_counts
    }
    
    # 记录查看API文档操作
    LoggerHelper.log_operation(
        user=request.user,
        action="view_api_docs",
        description="查看API文档"
    )
    
    return render(request, 'qrmanager/api_docs.html', context)

class QRCodeHistoryListView(LoginRequiredMixin, ListView):
    """二维码历史记录列表视图"""
    model = QRCodeHistory
    template_name = 'qrmanager/qrcode_history_list.html'
    context_object_name = 'histories'
    login_url = '/login/'
    paginate_by = 20

    def get_queryset(self):
        # 基础查询集
        queryset = QRCodeHistory.objects.select_related('bed', 'bed__department', 'created_by').all()
        
        # 筛选条件
        department_id = self.request.GET.get('department')
        bed_id = self.request.GET.get('bed')
        is_security_issue = self.request.GET.get('is_security_issue')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        # 应用筛选
        if department_id:
            queryset = queryset.filter(bed__department_id=department_id)
        
        if bed_id:
            queryset = queryset.filter(bed_id=bed_id)
        
        if is_security_issue == 'true':
            queryset = queryset.filter(is_security_issue=True)
        elif is_security_issue == 'false':
            queryset = queryset.filter(is_security_issue=False)
            
        if date_from:
            queryset = queryset.filter(created_at__gte=date_from)
            
        if date_to:
            # 添加一天，使日期范围包含结束日期的全天
            end_date = datetime.strptime(date_to, '%Y-%m-%d') + timedelta(days=1)
            queryset = queryset.filter(created_at__lt=end_date)
        
        return queryset.order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        # 添加所有科室到上下文
        context['departments'] = Department.objects.all().order_by('name')
        
        # 获取当前选中的科室ID
        department_id = self.request.GET.get('department', '')
        
        # 添加床位列表到上下文
        if department_id:
            context['beds'] = Bed.objects.filter(department_id=department_id).order_by('number')
        else:
            context['beds'] = []
        
        # 添加筛选参数到上下文
        context['department_id'] = department_id
        context['bed_id'] = self.request.GET.get('bed', '')
        context['is_security_issue'] = self.request.GET.get('is_security_issue', '')
        context['date_from'] = self.request.GET.get('date_from', '')
        context['date_to'] = self.request.GET.get('date_to', '')
        
        return context


class QRCodePreviewView(LoginRequiredMixin, View):
    """二维码预览视图，返回二维码图片和模板数据"""
    login_url = '/login/'

    def get(self, request, *args, **kwargs):
        # 导入logger
        import logging
        logger = logging.getLogger(__name__)
        
        try:
            qrcode_id = self.kwargs.get('pk')
            qrcode_obj = get_object_or_404(QRCode, pk=qrcode_id)
            
            # 使用安全URL生成二维码数据
            qr_data = qrcode_obj.get_secure_evaluation_url()
            
            # 获取打印模板
            template = None
            if qrcode_obj.bed and qrcode_obj.bed.department:
                # 首先尝试获取科室专属模板
                if hasattr(qrcode_obj.bed.department, 'print_template'):
                    template = qrcode_obj.bed.department.print_template
                
                # 如果科室没有专属模板，尝试获取公共模板
                if template is None:
                    from .models import PrintTemplate
                    public_template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
                    if public_template:
                        template = public_template
            
            # 准备响应数据
            response_data = {
                'id': qrcode_obj.id,
                'name': qrcode_obj.name,
                'code': str(qrcode_obj.code),
                'evaluation_url': qrcode_obj.get_evaluation_url(),
                'secure_evaluation_url': qrcode_obj.get_secure_evaluation_url(),
            }
            
            # 如果找到模板，使用统一的模板预览生成函数
            if template:
                from .qrcode_utils import generate_template_preview
                preview_data = generate_template_preview(template, qr_data=qr_data)
                
                # 将预览数据添加到响应
                response_data.update(preview_data)
            else:
                # 如果没有模板，只生成二维码
                logger.info("没有找到模板，只生成二维码")
                from .qrcode_utils import generate_qrcode
                _, img_base64 = generate_qrcode(qr_data, box_size=40, border=0)
                response_data['qr_image_base64'] = img_base64
                response_data['template_data'] = None
            
            # 准备床位信息
            response_data['bed'] = {
                'id': qrcode_obj.bed.id,
                'number': qrcode_obj.bed.number,
                'area': qrcode_obj.bed.area,
                'area_display': qrcode_obj.bed.get_area_display() if qrcode_obj.bed.area else None,
                'department': {
                    'id': qrcode_obj.bed.department.id,
                    'name': qrcode_obj.bed.department.name,
                    'code': qrcode_obj.bed.department.code
                } if qrcode_obj.bed.department else None,
                'staff': {
                    'id': qrcode_obj.bed.staff.id,
                    'name': qrcode_obj.bed.staff.name,
                    'title': qrcode_obj.bed.staff.title.name if hasattr(qrcode_obj.bed.staff, 'title') and qrcode_obj.bed.staff.title else None
                } if qrcode_obj.bed.staff else None
            }
            
            return JsonResponse(response_data)
        except Exception as e:
            logger.exception(f"处理二维码预览请求时出错: {str(e)}")
            return JsonResponse({'error': f'服务器错误: {str(e)}'}, status=500)

@login_required
def generate_qrcode_image(request, qrcode_id):
    """动态生成二维码图片并直接返回，替代预先存储的图片文件"""
    qrcode_obj = get_object_or_404(QRCode, pk=qrcode_id)
    
    # 使用安全URL生成二维码
    img, _ = generate_qrcode(qrcode_obj.get_secure_evaluation_url())
    
    # 创建HTTP响应
    response = HttpResponse(content_type="image/png")
    img.save(response, "PNG")
    
    # 添加缓存控制头，允许浏览器缓存一段时间（1小时）
    response['Cache-Control'] = 'max-age=3600, public'
    
    return response

class QRCodeTestView(LoginRequiredMixin, TemplateView):
    """二维码预览和打印测试页面"""
    template_name = 'qrmanager/qrcode_test.html'
    login_url = '/login/'

# 添加床位二维码预览API函数
@login_required
def bed_qr_preview(request):
    """处理床位二维码预览请求，返回二维码数据给前端。"""
    import logging
    logger = logging.getLogger(__name__)
    
    try:
        # 获取请求参数 - 支持GET和POST请求
        bed_id = request.GET.get('bed_id') or request.POST.get('bed_id')
        if not bed_id:
            return JsonResponse({'error': '缺少床位ID参数'}, status=400)
        
        # 获取床位
        try:
            bed = Bed.objects.get(pk=bed_id)
        except Bed.DoesNotExist:
            return JsonResponse({'error': '床位不存在'}, status=404)
        
        # 获取二维码 - 不自动创建新的二维码，只查询已有的
        qrcode_obj = None
        try:
            qrcode_obj = bed.qrcode
        except QRCode.DoesNotExist:
            # 如果二维码不存在，返回提示而不是自动创建
            return JsonResponse({
                'error': '此床位尚未关联二维码',
                'bed': {
                    'id': bed.id,
                    'number': bed.number,
                    'department': bed.department.name if bed.department else '未知科室'
                }
            }, status=404)
        
        # 记录查看操作
        from .utils import LoggerHelper
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="view_bed_qrcode",
            description=f"查看床位 {bed.number} 的二维码",
            extra_data={'bed_id': bed.id, 'qrcode_id': qrcode_obj.id if qrcode_obj else None}
        )
        
        # 使用安全URL生成二维码
        qr_data = qrcode_obj.get_secure_evaluation_url()
        
        # 准备响应数据
        response_data = {
            'id': qrcode_obj.id,
            'name': qrcode_obj.name,
            'code': str(qrcode_obj.code),
            'evaluation_url': qrcode_obj.get_evaluation_url(),
            'secure_evaluation_url': qrcode_obj.get_secure_evaluation_url(),
        }
        
        # 获取打印模板
        template = None
        if bed.department and hasattr(bed.department, 'print_template'):
            template = bed.department.print_template
        
        # 如果科室没有专属模板，尝试获取公共模板
        if not template:
            from .models import PrintTemplate
            template = PrintTemplate.objects.filter(is_public=True, is_active=True).first()
        
        # 如果找到模板，使用统一的模板预览生成函数
        if template:
            from .qrcode_utils import generate_template_preview
            preview_data = generate_template_preview(template, qr_data=qr_data)
            
            # 将预览数据添加到响应
            response_data.update(preview_data)
        else:
            # 如果没有模板，只生成二维码
            logger.info("没有找到模板，只生成二维码")
            from .qrcode_utils import generate_qrcode
            _, img_base64 = generate_qrcode(qr_data, box_size=40, border=0)
            response_data['qr_image_base64'] = img_base64
            response_data['template_data'] = None
        
        # 准备床位信息
        response_data['bed'] = {
            'id': bed.id,
            'number': bed.number,
            'area': bed.area,
            'area_display': bed.get_area_display() if bed.area else None,
            'department': {
                'id': bed.department.id,
                'name': bed.department.name,
                'code': bed.department.code
            } if bed.department else None,
            'staff': {
                'id': bed.staff.id,
                'name': bed.staff.name,
                'title': bed.staff.title.name if hasattr(bed.staff, 'title') and bed.staff.title else None
            } if bed.staff else None
        }
        
        return JsonResponse(response_data)
    except Exception as e:
        logger.exception(f"处理床位二维码预览请求时出错: {str(e)}")
        return JsonResponse({'error': f'服务器错误: {str(e)}'}, status=500)

@login_required
def api_department_staff(request, pk):
    """获取科室关联的工作人员信息"""
    try:
        department = Department.objects.get(pk=pk)
        staff = Staff.objects.filter(department=department)
        
        data = [{'id': s.id, 'name': s.name, 'title': s.title.name if s.title else ''} for s in staff]
        return JsonResponse({'status': 'success', 'data': data})
    except Department.DoesNotExist:
        return JsonResponse({'status': 'error', 'message': '科室不存在'}, status=404)
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)

def secure_qrcode_info(request, qr_code):
    """安全地获取二维码信息，用于前端展示"""
    try:
        # 使用安全访问函数处理参数
        from .security import secure_qr_access
        try:
            # 尝试解密参数
            uuid = secure_qr_access(qr_code)
        except ValueError as e:
            return JsonResponse({
                'success': False,
                'error': '二维码验证失败',
                'message': str(e)
            }, status=400)
        
        # 查找二维码
        qrcode_obj = get_object_or_404(QRCode, code=uuid)
        
        # 获取关联的床位和科室信息
        bed = qrcode_obj.bed
        department = bed.department if bed else None
        staff = bed.staff if bed else None
        
        # 构建响应数据
        response_data = {
            'success': True,
            'qrcode': {
                'id': qrcode_obj.id,
                'code': qrcode_obj.code,
                'created_at': qrcode_obj.created_at.isoformat(),
            },
            'bed': {
                'id': bed.id,
                'number': bed.number,
                'area': bed.area,
            } if bed else None,
            'department': {
                'id': department.id,
                'name': department.name,
                'code': department.code,
            } if department else None,
            'staff': {
                'id': staff.id,
                'name': staff.name,
                'work_number': staff.work_number,
                'title': staff.title,
                'staff_type': staff.staff_type,
                'photo_url': request.build_absolute_uri(staff.photo.url) if staff and staff.photo else None,
            } if staff else None,
        }
        
        # 记录API访问
        LoggerHelper.log_operation(
            user=None,
            action="secure_api_access",
            description=f"安全API访问: 获取二维码信息 {qr_code}",
            status='success',
            extra_data={
                'qr_code': qr_code,
                'uuid': uuid,
                'ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
            }
        )
        
        return JsonResponse(response_data)
    except Exception as e:
        # 记录错误
        LoggerHelper.log_operation(
            user=None,
            action="secure_api_access_failed",
            description=f"安全API访问失败: {str(e)}",
            status='error',
            extra_data={
                'qr_code': qr_code,
                'error': str(e),
                'ip': request.META.get('REMOTE_ADDR'),
                'user_agent': request.META.get('HTTP_USER_AGENT'),
            }
        )
        
        return JsonResponse({
            'success': False,
            'error': '无法获取二维码信息',
            'message': str(e)
        }, status=404)

class SystemConfigListView(LoginRequiredMixin, UserPassesTestMixin, ListView):
    """系统配置列表视图，仅超级管理员可访问"""
    model = SystemConfig
    template_name = 'qrmanager/system_config_list.html'
    context_object_name = 'configs'
    login_url = '/login/'
    
    def test_func(self):
        """检查用户是否有权限访问"""
        return self.request.user.is_superuser
    
    def get_queryset(self):
        """获取配置列表"""
        return SystemConfig.objects.all().order_by('key')
    
    def get_context_data(self, **kwargs):
        """添加额外上下文数据"""
        context = super().get_context_data(**kwargs)
        context['title'] = '系统配置'
        context['subtitle'] = '管理全局系统配置项'
        return context


class SystemConfigUpdateView(LoginRequiredMixin, UserPassesTestMixin, UpdateView):
    """系统配置更新视图，仅超级管理员可访问"""
    model = SystemConfig
    template_name = 'qrmanager/system_config_form.html'
    fields = ['value', 'description', 'is_public']
    success_url = reverse_lazy('qrmanager:system_config_list')
    login_url = '/login/'
    
    def test_func(self):
        """检查用户是否有权限访问"""
        return self.request.user.is_superuser
    
    def get_context_data(self, **kwargs):
        """添加额外上下文数据"""
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑系统配置'
        context['subtitle'] = f'编辑配置项: {self.object.key}'
        return context
    
    def form_valid(self, form):
        """表单验证成功后的处理"""
        # 记录更新前的数据
        old_value = self.object.value
        
        # 保存表单
        response = super().form_valid(form)
        
        # 记录操作日志
        from .utils import LoggerHelper
        LoggerHelper.log_operation(
            user=self.request.user,
            action="update_system_config",
            description=f"更新系统配置: {self.object.key}，从 {old_value} 更改为 {self.object.value}",
            status='success'
        )
        
        # 添加成功消息
        messages.success(self.request, f'系统配置 {self.object.key} 已更新')
        
        return response


@login_required
def initialize_system_configs(request):
    """初始化系统配置"""
    if not request.user.is_superuser:
        messages.error(request, '您没有权限执行此操作')
        return redirect('qrmanager:dashboard')
    
    # 定义默认配置
    default_configs = [
        {
            'key': 'system_name',
            'value': '医院服务评价系统',
            'description': '系统名称，显示在页面标题和导航栏',
            'is_public': True
        },
        {
            'key': 'hospital_name',
            'value': '示例医院',
            'description': '医院名称，显示在页面底部',
            'is_public': True
        },
        {
            'key': 'contact_email',
            'value': '<EMAIL>',
            'description': '联系邮箱，显示在联系我们页面',
            'is_public': True
        },
        {
            'key': 'contact_phone',
            'value': '010-12345678',
            'description': '联系电话，显示在联系我们页面',
            'is_public': True
        },
        {
            'key': 'enable_sentiment_analysis',
            'value': 'true',
            'description': '是否启用情感分析功能',
            'is_public': False
        },
        {
            'key': 'frontend_url',
            'value': getattr(settings, 'FRONTEND_URL', 'http://hospital.local'),
            'description': '前端URL，用于生成二维码和重定向到前端页面',
            'is_public': False
        }
    ]
    
    # 创建或更新配置
    created_count = 0
    updated_count = 0
    
    for config in default_configs:
        obj, created = SystemConfig.objects.get_or_create(
            key=config['key'],
            defaults={
                'value': config['value'],
                'description': config['description'],
                'is_public': config['is_public']
            }
        )
        
        if created:
            created_count += 1
        else:
            # 只更新描述和公开状态，不更新值
            obj.description = config['description']
            obj.is_public = config['is_public']
            obj.save()
            updated_count += 1
    
    messages.success(request, f'系统配置初始化完成：创建了 {created_count} 个新配置，更新了 {updated_count} 个现有配置')
    return redirect('qrmanager:system_config_list')

@login_required
def update_url_settings(request):
    """更新URL设置"""
    if request.method == 'POST':
        # 获取表单数据
        frontend_url = request.POST.get('frontend_url')
        
        # 验证URL格式
        if not frontend_url.startswith(('http://', 'https://')):
            messages.error(request, '前端URL必须以http://或https://开头')
            return redirect('qrmanager:qrcode_list')
        
        # 更新系统配置
        from .models import SystemConfig
        SystemConfig.set_value('frontend_url', frontend_url, 
                              '前端URL，用于生成二维码和重定向到前端页面', False)
        
        # 记录操作日志
        from .utils import LoggerHelper
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="update_url_settings",
            description="更新URL设置",
            extra_data={
                'frontend_url': frontend_url
            }
        )
        
        messages.success(request, 'URL设置已更新')
    
    return redirect('qrmanager:qrcode_list')

# API密钥管理视图
class APIKeyListView(LoginRequiredMixin, ListView):
    """API密钥列表视图"""
    model = APIKey
    template_name = 'qrmanager/apikey_list.html'
    context_object_name = 'apikeys'
    
    def get_queryset(self):
        return APIKey.objects.all().order_by('-created_at')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'API密钥管理'
        return context


class APIKeyCreateView(LoginRequiredMixin, CreateView):
    """创建API密钥视图"""
    model = APIKey
    template_name = 'qrmanager/apikey_form.html'
    fields = ['name', 'expires_at', 'is_active', 'rate_limit_day', 'rate_limit_hour', 'rate_limit_minute',
              'allowed_ips', 'can_read', 'can_write', 'can_delete',
              'can_access_departments', 'can_access_beds', 'can_access_staff',
              'can_access_qrcodes', 'can_access_evaluations']
    success_url = reverse_lazy('qrmanager:apikey_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '创建API密钥'
        context['is_create'] = True
        return context
    
    def form_valid(self, form):
        form.instance.created_by = self.request.user
        response = super().form_valid(form)
        messages.success(self.request, f'API密钥 {self.object.name} 创建成功！请保存密钥：{self.object.key}')
        return response


class APIKeyUpdateView(LoginRequiredMixin, UpdateView):
    """更新API密钥视图"""
    model = APIKey
    template_name = 'qrmanager/apikey_form.html'
    fields = ['name', 'expires_at', 'is_active', 'rate_limit_day', 'rate_limit_hour', 'rate_limit_minute',
              'allowed_ips', 'can_read', 'can_write', 'can_delete',
              'can_access_departments', 'can_access_beds', 'can_access_staff',
              'can_access_qrcodes', 'can_access_evaluations']
    success_url = reverse_lazy('qrmanager:apikey_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '编辑API密钥'
        context['is_create'] = False
        return context
    
    def form_valid(self, form):
        response = super().form_valid(form)
        messages.success(self.request, f'API密钥 {self.object.name} 更新成功！')
        return response


class APIKeyDeleteView(LoginRequiredMixin, DeleteView):
    """删除API密钥视图"""
    model = APIKey
    template_name = 'qrmanager/apikey_confirm_delete.html'
    success_url = reverse_lazy('qrmanager:apikey_list')
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = '删除API密钥'
        return context
    
    def delete(self, request, *args, **kwargs):
        apikey = self.get_object()
        messages.success(request, f'API密钥 {apikey.name} 已删除！')
        return super().delete(request, *args, **kwargs)


class APILogListView(LoginRequiredMixin, ListView):
    """API日志列表视图"""
    model = APILog
    template_name = 'qrmanager/apilog_list.html'
    context_object_name = 'apilogs'
    paginate_by = 50
    
    def get_queryset(self):
        queryset = APILog.objects.all().order_by('-timestamp')
        
        # 过滤条件
        api_key_id = self.request.GET.get('api_key')
        status_code = self.request.GET.get('status_code')
        endpoint = self.request.GET.get('endpoint')
        ip_address = self.request.GET.get('ip_address')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        
        if api_key_id:
            queryset = queryset.filter(api_key_id=api_key_id)
        if status_code:
            queryset = queryset.filter(response_status=status_code)
        if endpoint:
            queryset = queryset.filter(endpoint__icontains=endpoint)
        if ip_address:
            queryset = queryset.filter(ip_address=ip_address)
        if date_from:
            date_from = datetime.strptime(date_from, '%Y-%m-%d').date()
            queryset = queryset.filter(timestamp__date__gte=date_from)
        if date_to:
            date_to = datetime.strptime(date_to, '%Y-%m-%d').date()
            queryset = queryset.filter(timestamp__date__lte=date_to)
            
        return queryset
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'API访问日志'
        context['api_keys'] = APIKey.objects.all()
        
        # 保留过滤条件
        context['filters'] = {
            'api_key': self.request.GET.get('api_key', ''),
            'status_code': self.request.GET.get('status_code', ''),
            'endpoint': self.request.GET.get('endpoint', ''),
            'ip_address': self.request.GET.get('ip_address', ''),
            'date_from': self.request.GET.get('date_from', ''),
            'date_to': self.request.GET.get('date_to', ''),
        }
        
        return context


class APILogDetailView(LoginRequiredMixin, DetailView):
    """API日志详情视图"""
    model = APILog
    template_name = 'qrmanager/apilog_detail.html'
    context_object_name = 'apilog'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = 'API日志详情'
        return context

class EvaluationRedirectView(View):
    """将旧版URL重定向到新格式的视图"""
    
    def get(self, request, *args, **kwargs):
        # 获取来源，判断是否是旧格式的URL访问
        encrypted_param = request.GET.get('p')
        qr_code = kwargs.get('qr_code')
        encoded_param = kwargs.get('encoded_param')
        
        # 找出使用的参数
        param = encrypted_param or qr_code or encoded_param
        
        if not param:
            # 记录错误访问
            logger = logging.getLogger('api')
            logger.warning(f"无效的URL格式访问: {request.build_absolute_uri()}")
            return HttpResponseNotFound("不支持的URL格式，请使用新的二维码链接 (/q/加密参数)")
        
        # 如果是UUID格式的qr_code，需要先加密
        if qr_code and '-' in qr_code and len(qr_code) >= 32:
            try:
                logger.info(f"旧版二维码格式，需要加密: {qr_code}")
                param = encrypt_qr_param(qr_code)
                logger.info(f"生成加密参数: {param}")
            except Exception as e:
                logger.error(f"加密二维码失败: {str(e)}")
                # 继续使用原始参数
        
        # 移除可能的前缀等号，避免URL解析问题
        if param.startswith('='):
            param = param[1:]
            
        # 构建重定向URL，使用当前域名和/q/格式
        # 获取当前域名部分，避免硬编码
        host = request.get_host()
        protocol = 'https://' if request.is_secure() else 'http://'
        base_url = f"{protocol}{host}"
        
        # 永远重定向到q/格式
        url = f"{base_url}/q/{param}"
        
        # 记录重定向
        logger = logging.getLogger('api')
        logger.info(f"旧URL格式重定向: {request.build_absolute_uri()} -> {url}")
        
        # 使用301永久重定向
        return HttpResponsePermanentRedirect(url)

class EvaluationView(View):
    """二维码评价页面视图，使用加密参数显示评价页面，只支持/q/格式
    修改为返回JSON数据，不再重定向到前端页面"""
    
    def get(self, request, *args, **kwargs):
        # 获取加密参数
        encoded_param = kwargs.get('encoded_param')
        
        if not encoded_param:
            logger = logging.getLogger('api')
            logger.warning(f"访问评价页面没有提供参数: {request.build_absolute_uri()}")
            return JsonResponse({
                "status": "error",
                "message": "未提供有效的二维码参数"
            }, status=400)
            
        # 去除前缀等号
        if encoded_param.startswith('='):
            encoded_param = encoded_param[1:]
        
        # 验证参数是否符合加密格式
        try:
            # 尝试解密参数，如果成功则是有效的加密参数
            qr_code = secure_qr_access(encoded_param)
            if not qr_code:
                raise ValueError("无效的加密参数")
                
            # 记录成功访问
            logger = logging.getLogger('api')
            logger.info(f"有效的二维码访问: {encoded_param[:20]}..., 返回JSON数据")
            
            # 返回JSON数据，不再重定向到前端页面
            return JsonResponse({
                "status": "success",
                "message": "二维码验证成功",
                "data": {
                    "qr_param": encoded_param,
                    "qr_code_id": qr_code.id,
                    "bed_id": qr_code.bed.id if qr_code.bed else None,
                    "bed_number": qr_code.bed.number if qr_code.bed else None,
                    "department_id": qr_code.bed.department.id if qr_code.bed and qr_code.bed.department else None,
                    "department_name": qr_code.bed.department.name if qr_code.bed and qr_code.bed.department else None
                }
            })
        except Exception as e:
            # 记录错误但不暴露详细信息
            logger = logging.getLogger('api')
            logger.warning(f"无效的二维码参数: {encoded_param[:20]}..., 错误: {str(e)}")
            return JsonResponse({
                "status": "error",
                "message": "无效的二维码参数，请扫描有效的二维码"
            }, status=400)

@login_required
def qrcode_list(request):
    """二维码列表页面"""
    qrcodes = QRCode.objects.all().order_by('-created_at')
    departments = Department.objects.all()
    templates = PrintTemplate.objects.all()
    
    # 获取系统配置
    context = {
        'qrcodes': qrcodes,
        'departments': departments,
        'templates': templates,
        'frontend_url': SystemConfig.get_value('frontend_url', 'http://hospital.local')
    }
    return render(request, 'qrmanager/qrcode_list.html', context)

@login_required
def qrcode_detail(request, qrcode_id):
    """二维码详情页面"""
    qrcode = get_object_or_404(QRCode, id=qrcode_id)
    departments = Department.objects.all()
    templates = PrintTemplate.objects.all()
    
    # 获取系统配置
    context = {
        'qrcode': qrcode,
        'departments': departments,
        'templates': templates,
        'frontend_url': SystemConfig.get_value('frontend_url',
                                              getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
    }
    
    # 获取评价数据
    evaluations = Evaluation.objects.filter(qrcode=qrcode).order_by('-created_at')
    context['evaluations'] = evaluations
    
    # 获取二维码访问前缀
    context['frontend_url'] = getattr(settings, 'FRONTEND_URL', 'http://hospital.local')
    
    return render(request, 'qrmanager/qrcode_detail.html', context)

@login_required
def update_system_configs(request):
    """更新系统配置"""
    if request.method == 'POST':
        system_name = request.POST.get('system_name')
        hospital_name = request.POST.get('hospital_name')
        contact_email = request.POST.get('contact_email')
        contact_phone = request.POST.get('contact_phone')
        enable_sentiment_analysis = request.POST.get('enable_sentiment_analysis', 'false')
        frontend_url = request.POST.get('frontend_url')
        
        # 验证URL格式
        if not frontend_url.startswith(('http://', 'https://')):
            frontend_url = 'http://' + frontend_url
        
        # 更新系统配置
        SystemConfig.set_value('system_name', system_name, '系统名称，显示在页面标题和导航栏')
        SystemConfig.set_value('hospital_name', hospital_name, '医院名称，显示在页面底部')
        SystemConfig.set_value('contact_email', contact_email, '联系邮箱，显示在联系我们页面')
        SystemConfig.set_value('contact_phone', contact_phone, '联系电话，显示在联系我们页面')
        SystemConfig.set_value('enable_sentiment_analysis', enable_sentiment_analysis, '是否启用情感分析功能')
        SystemConfig.set_value('frontend_url', frontend_url,
                              '前端URL，用于生成二维码和重定向到前端页面')
        
        messages.success(request, '系统配置已更新')
        
        # 返回JSON响应
        return JsonResponse({
            'status': 'success',
            'message': '系统配置已更新',
            'system_name': system_name,
            'hospital_name': hospital_name,
            'contact_email': contact_email,
            'contact_phone': contact_phone,
            'enable_sentiment_analysis': enable_sentiment_analysis,
            'frontend_url': frontend_url,
        })
    
    return JsonResponse({'status': 'error', 'message': '无效的请求方法'})
    return JsonResponse({'status': 'error', 'message': '无效的请求方法'})

# 添加前端文件服务视图
def serve_frontend_index(request, path=None):
    """
    处理前端路由，将所有前端路由重定向到index.html
    这样可以支持前端的SPA路由
    增加安全限制，阻止直接访问评价页面
    """
    import os
    import re
    from django.http import HttpResponse, HttpResponseServerError, HttpResponseNotFound, HttpResponseForbidden
    from django.conf import settings
    from django.views.static import serve
    
    logger = logging.getLogger('api')
    logger.info(f"提供前端文件, 路径: {path}, IP: {request.META.get('REMOTE_ADDR')}")
    
    # 安全检查：拒绝evaluate.html和thank-you.html直接访问
    if 'evaluate.html' in request.path or 'thank-you.html' in request.path:
        logger.warning(f"阻止直接访问评价页面: {request.path}, IP: {request.META.get('REMOTE_ADDR')}")
        return HttpResponseForbidden("为保障系统安全，不允许直接访问此页面")
    
    # 验证参数格式
    if path:
        # 移除等号前缀(如果有)
        clean_path = path[1:] if path.startswith('=') else path
        
        # 严格验证加密参数格式
        try:
            # 检查参数是否包含且只包含一个点(.)分隔两部分
            parts = clean_path.split('.')
            if len(parts) != 2:
                logger.warning(f"无效的加密参数格式 (格式错误): {path}")
                return HttpResponseNotFound("无效的二维码链接 - 格式不正确")
            
            # 验证第二部分是8位十六进制校验和
            checksum = parts[1]
            if not re.match(r'^[0-9a-f]{8}$', checksum):
                logger.warning(f"无效的加密参数格式 (校验和错误): {path}")
                return HttpResponseNotFound("无效的二维码链接 - 校验和不正确")
            
            # 验证第一部分是base64格式 (只包含合法base64字符)
            encoded_part = parts[0]
            if not re.match(r'^[A-Za-z0-9+/=]+$', encoded_part):
                logger.warning(f"无效的加密参数格式 (不是base64): {path}")
                return HttpResponseNotFound("无效的二维码链接 - 编码不正确")
                
            # 验证base64字符串长度是否合理 (通常至少是20个字符)
            if len(encoded_part) < 20:
                logger.warning(f"无效的加密参数格式 (base64过短): {path}")
                return HttpResponseNotFound("无效的二维码链接 - 参数长度不正确")
                
            # 尝试解码base64字符串，确保其为有效的base64
            try:
                import base64
                base64.b64decode(encoded_part + '=' * (4 - len(encoded_part) % 4))
            except Exception as e:
                logger.warning(f"无效的base64编码: {str(e)}")
                return HttpResponseNotFound("无效的二维码链接 - base64解码失败")
                
        except Exception as e:
            logger.error(f"参数验证出错: {str(e)}")
            return HttpResponseNotFound("无效的二维码链接 - 验证失败")
    
    # 直接提供前端index.html文件
    frontend_dir = os.path.join(os.path.dirname(settings.BASE_DIR), 'Frontend')
    index_file = os.path.join(frontend_dir, 'index.html')
    
    try:
        with open(index_file, 'r', encoding='utf-8') as f:
            html_content = f.read()
            return HttpResponse(html_content, content_type='text/html; charset=utf-8')
    except Exception as e:
        logger.error(f"无法读取前端index.html文件: {str(e)}")
        return HttpResponseServerError("服务器错误，无法加载前端页面")
