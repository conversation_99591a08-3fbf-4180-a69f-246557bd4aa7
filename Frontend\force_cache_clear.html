<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>强制清除缓存 - 医院服务评价系统</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            color: white;
        }
        .container {
            background: rgba(255, 255, 255, 0.95);
            color: #333;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.3);
            text-align: center;
        }
        h1 {
            color: #2c3e50;
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: bold;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
            transition: transform 0.2s;
        }
        button:hover {
            transform: translateY(-2px);
        }
        .countdown {
            font-size: 24px;
            font-weight: bold;
            color: #e74c3c;
        }
        .progress {
            width: 100%;
            height: 20px;
            background-color: #f0f0f0;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px 0;
        }
        .progress-bar {
            height: 100%;
            background: linear-gradient(90deg, #667eea 0%, #764ba2 100%);
            width: 0%;
            transition: width 0.3s ease;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔄 强制清除缓存</h1>
        
        <div class="status info">
            <strong>说明：</strong>如果您看到的页面内容不是最新的，请点击下方按钮清除缓存。
        </div>
        
        <div id="status-display"></div>
        
        <div class="progress">
            <div class="progress-bar" id="progress-bar"></div>
        </div>
        
        <button onclick="forceClearCache()">🧹 立即清除缓存</button>
        <button onclick="goToMainPage()">🏠 返回主页</button>
        
        <div id="countdown-display"></div>
        
        <div class="status warning">
            <strong>注意：</strong>清除缓存后页面将自动刷新，请稍等片刻。
        </div>
    </div>

    <script>
        let progressInterval;
        let countdownInterval;
        
        function updateStatus(message, type = 'info') {
            const statusDiv = document.getElementById('status-display');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
        }
        
        function updateProgress(percent) {
            const progressBar = document.getElementById('progress-bar');
            progressBar.style.width = percent + '%';
        }
        
        function startCountdown(seconds) {
            const countdownDiv = document.getElementById('countdown-display');
            let remaining = seconds;
            
            countdownInterval = setInterval(() => {
                countdownDiv.innerHTML = `<div class="countdown">页面将在 ${remaining} 秒后自动刷新...</div>`;
                remaining--;
                
                if (remaining < 0) {
                    clearInterval(countdownInterval);
                    window.location.href = '/index.html?cache_cleared=' + Date.now();
                }
            }, 1000);
        }
        
        function forceClearCache() {
            updateStatus('🚀 开始清除缓存...', 'info');
            updateProgress(10);
            
            // 步骤1: 清除localStorage
            setTimeout(() => {
                try {
                    localStorage.clear();
                    updateStatus('✅ 本地存储已清除', 'success');
                    updateProgress(25);
                } catch(e) {
                    updateStatus('⚠️ 清除本地存储失败: ' + e.message, 'warning');
                }
                
                // 步骤2: 清除sessionStorage
                setTimeout(() => {
                    try {
                        sessionStorage.clear();
                        updateStatus('✅ 会话存储已清除', 'success');
                        updateProgress(40);
                    } catch(e) {
                        updateStatus('⚠️ 清除会话存储失败: ' + e.message, 'warning');
                    }
                    
                    // 步骤3: 清除Service Worker
                    setTimeout(() => {
                        if ('serviceWorker' in navigator) {
                            navigator.serviceWorker.getRegistrations().then(registrations => {
                                registrations.forEach(registration => {
                                    registration.unregister();
                                });
                                updateStatus('✅ Service Worker已清除', 'success');
                                updateProgress(60);
                            });
                        } else {
                            updateStatus('ℹ️ 无Service Worker需要清除', 'info');
                            updateProgress(60);
                        }
                        
                        // 步骤4: 清除Cache API
                        setTimeout(() => {
                            if ('caches' in window) {
                                caches.keys().then(names => {
                                    return Promise.all(
                                        names.map(name => caches.delete(name))
                                    );
                                }).then(() => {
                                    updateStatus('✅ 浏览器缓存已清除', 'success');
                                    updateProgress(80);
                                });
                            } else {
                                updateStatus('ℹ️ 无浏览器缓存需要清除', 'info');
                                updateProgress(80);
                            }
                            
                            // 步骤5: 完成
                            setTimeout(() => {
                                updateStatus('🎉 缓存清除完成！页面即将刷新...', 'success');
                                updateProgress(100);
                                startCountdown(3);
                            }, 500);
                        }, 500);
                    }, 500);
                }, 500);
            }, 500);
        }
        
        function goToMainPage() {
            window.location.href = '/index.html?from_cache_clear=' + Date.now();
        }
        
        // 页面加载时自动检查
        window.addEventListener('load', () => {
            updateStatus('📋 缓存清理工具已就绪', 'info');
            
            // 检查是否有缓存数据
            const hasLocalStorage = localStorage.length > 0;
            const hasSessionStorage = sessionStorage.length > 0;
            
            if (hasLocalStorage || hasSessionStorage) {
                updateStatus('⚠️ 检测到缓存数据，建议清除', 'warning');
            } else {
                updateStatus('✅ 当前无缓存数据', 'success');
            }
        });
    </script>
</body>
</html>