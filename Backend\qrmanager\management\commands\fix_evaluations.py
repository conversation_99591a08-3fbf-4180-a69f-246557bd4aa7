from django.core.management.base import BaseCommand
from qrmanager.models import Evaluation, StaffEvaluation
from django.db import transaction

class Command(BaseCommand):
    help = '为现有评价创建StaffEvaluation关联记录'

    def handle(self, *args, **options):
        self.stdout.write('开始修复评价记录...')
        
        # 获取所有评价记录
        evaluations = Evaluation.objects.select_related('staff').all()
        self.stdout.write(f'找到{evaluations.count()}条评价记录')
        
        # 已有的StaffEvaluation记录数
        existing_count = StaffEvaluation.objects.count()
        self.stdout.write(f'当前有{existing_count}条工作人员评价关联记录')
        
        created_count = 0
        skipped_count = 0
        error_count = 0
        
        with transaction.atomic():
            for eval_record in evaluations:
                try:
                    # 如果评价没有关联的工作人员，跳过
                    if not eval_record.staff:
                        self.stdout.write(f'评价ID {eval_record.id} 没有关联工作人员，跳过')
                        skipped_count += 1
                        continue
                        
                    # 检查是否已经有对应的StaffEvaluation记录
                    existing = StaffEvaluation.objects.filter(
                        evaluation=eval_record,
                        staff=eval_record.staff
                    ).exists()
                    
                    if existing:
                        self.stdout.write(f'评价ID {eval_record.id} 已有对应的StaffEvaluation记录，跳过')
                        skipped_count += 1
                        continue
                        
                    # 创建StaffEvaluation记录
                    staff_eval = StaffEvaluation(
                        evaluation=eval_record,
                        staff=eval_record.staff,
                        is_satisfied=eval_record.is_satisfied
                    )
                    staff_eval.save()
                    created_count += 1
                    self.stdout.write(f'为评价ID {eval_record.id} 创建了StaffEvaluation记录，'
                                     f'工作人员: {eval_record.staff.name}, '
                                     f'满意: {eval_record.is_satisfied}')
                
                except Exception as e:
                    self.stdout.write(self.style.ERROR(f'处理评价ID {eval_record.id} 时出错: {str(e)}'))
                    error_count += 1
        
        self.stdout.write(self.style.SUCCESS('修复完成！'))
        self.stdout.write(f'已创建 {created_count} 条新的StaffEvaluation记录')
        self.stdout.write(f'跳过 {skipped_count} 条评价记录')
        self.stdout.write(f'处理失败 {error_count} 条评价记录')
        self.stdout.write(f'现在共有 {StaffEvaluation.objects.count()} 条StaffEvaluation记录') 