"""
速率限制中间件
用于限制请求频率，防止DoS攻击
"""

from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.cache import cache
import time

class RateLimitMiddleware(MiddlewareMixin):
    """
    请求速率限制中间件
    限制每个IP的请求频率
    """
    
    # 每分钟最大请求数
    MAX_REQUESTS_PER_MINUTE = 60
    
    # 白名单IP列表
    WHITELIST_IPS = [
        '127.0.0.1',
        '::1',
    ]
    
    def process_request(self, request):
        """
        处理请求，检查是否超过速率限制
        
        参数:
            request: HTTP请求对象
            
        返回:
            如果超过速率限制，返回429响应
            否则返回None，继续处理请求
        """
        # 获取客户端IP
        client_ip = self.get_client_ip(request)
        
        # 白名单IP不受限制
        if client_ip in self.WHITELIST_IPS:
            return None
            
        # 获取当前时间戳（秒）
        current_time = int(time.time())
        
        # 缓存键：IP地址 + 当前分钟
        cache_key = f"rate_limit:{client_ip}:{current_time // 60}"
        
        # 获取当前计数
        count = cache.get(cache_key, 0)
        
        # 如果超过限制，返回429响应
        if count >= self.MAX_REQUESTS_PER_MINUTE:
            return JsonResponse({
                'status': 'error',
                'message': '请求过于频繁，请稍后再试'
            }, status=429)
            
        # 增加计数并设置过期时间（1分钟）
        cache.set(cache_key, count + 1, 60)
        
        # 继续处理请求
        return None
        
    def get_client_ip(self, request):
        """
        获取客户端IP地址
        
        参数:
            request: HTTP请求对象
            
        返回:
            客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip