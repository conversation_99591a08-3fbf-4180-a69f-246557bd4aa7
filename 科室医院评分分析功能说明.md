# 科室医院评分分析功能说明

## 🎯 功能概述

在情感分析页面新增了科室医院评分分析功能，将医院整体评分与科室关联起来，因为患者是通过扫描特定科室的二维码来评价的，所以医院整体评分应该归属到对应的科室。

## 💡 设计理念

### 核心逻辑
- **评分归属**：患者通过扫描科室二维码进行评价，其医院整体评分归属到该科室
- **科室影响**：分析各科室对医院整体形象的影响程度
- **数据关联**：`hospital_rating` 字段通过 `bed.department` 关联到具体科室

### 业务价值
- **科室责任**：明确各科室对医院整体评价的贡献和影响
- **精准改进**：识别影响医院形象的关键科室
- **激励机制**：为科室建立基于医院评分的激励体系

## 📊 功能设计

### 1. 数据统计逻辑

#### 数据来源
```python
# 获取有医院评分的评价，按科室分组
hospital_rated_evaluations = evaluations.filter(hospital_rating__isnull=False)

for evaluation in hospital_rated_evaluations:
    if evaluation.bed and evaluation.bed.department:
        dept_name = evaluation.bed.department.name
        # 按科室收集医院评分数据
```

#### 统计指标
- **平均评分**：科室患者给医院的平均评分
- **评分数量**：该科室的医院评分总数
- **评分分布**：1-5星的具体分布情况
- **极值统计**：5星数量和1星数量

### 2. 排名算法

#### 医院评分最高科室TOP5
```python
'top_hospital_rating_departments': sorted([d for d in department_hospital_stats if d['total_count'] >= 1],
                                         key=lambda x: (-x['avg_rating'], -x['total_count']))[:5]
```

**排名依据**：
1. **主要指标**：平均评分（越高越好）
2. **次要指标**：评分数量（样本量参考）

#### 医院评分最低科室TOP5
```python
'bottom_hospital_rating_departments': sorted([d for d in department_hospital_stats if d['total_count'] >= 1],
                                            key=lambda x: (x['avg_rating'], -x['total_count']))[:5]
```

**排名依据**：
1. **主要指标**：平均评分（越低排名越前）
2. **次要指标**：评分数量（样本量参考）

### 3. 数据结构

#### 科室医院评分统计
```python
department_hospital_ratings = {
    'name': dept_name,                    # 科室名称
    'ratings': [],                        # 所有评分列表
    'total_count': 0,                     # 评分总数
    'avg_rating': 0,                      # 平均评分
    'rating_distribution': {              # 评分分布
        1: 0, 2: 0, 3: 0, 4: 0, 5: 0
    }
}
```

## 🎨 前端显示设计

### 1. 页面布局
```
[工作人员总体统计]
[情感分布图表 | 评分分布图表]
[科室对比图表]
[工作人员TOP5：表扬TOP5 | 批评TOP5]
[科室排名TOP5：最佳科室 | 需要改进科室]
[科室医院评分分析：评分最高科室 | 评分最低科室]  ⭐ 新增
```

### 2. 表格设计

#### 医院评分最高科室TOP5
- **科室名称**：加粗显示
- **平均评分**：绿色数字 + 星级显示
- **评分数量**：蓝色徽章
- **5星数量**：绿色显示
- **1星数量**：红色显示

#### 医院评分最低科室TOP5
- **科室名称**：加粗显示
- **平均评分**：橙色数字 + 星级显示
- **评分数量**：灰色徽章
- **5星数量**：绿色显示
- **1星数量**：红色显示

### 3. 视觉元素

#### 星级显示
```html
{% for i in "12345" %}
    {% if forloop.counter <= dept.avg_rating|floatformat:0 %}
        ★
    {% else %}
        ☆
    {% endif %}
{% endfor %}
```

#### 颜色编码
- **高评分**：绿色系（成功、正面）
- **低评分**：橙色系（警告、需关注）
- **中性数据**：蓝色/灰色系

## 📈 业务应用场景

### 1. 管理决策支持
- **资源配置**：向评分高的科室学习经验，向评分低的科室投入更多资源
- **培训计划**：针对评分低的科室制定专项培训计划
- **激励机制**：基于医院评分建立科室激励体系

### 2. 质量改进指导
- **问题识别**：快速识别影响医院整体形象的科室
- **改进重点**：明确质量改进的重点科室和方向
- **效果监控**：跟踪科室改进措施的效果

### 3. 品牌管理
- **形象分析**：了解各科室对医院品牌形象的贡献
- **口碑管理**：重点关注评分低的科室的口碑改善
- **优势宣传**：利用评分高的科室作为医院优势宣传点

## 🔧 技术实现要点

### 1. 数据关联
- 通过 `evaluation.bed.department` 建立评分与科室的关联
- 确保数据完整性，处理床位或科室为空的情况
- 只统计有医院评分的评价记录

### 2. 性能优化
- 使用 `filter(hospital_rating__isnull=False)` 减少数据处理量
- 在内存中进行统计计算，避免复杂的数据库查询
- 合理设置最小样本量阈值

### 3. 数据准确性
- 严格的数据验证确保评分在1-5范围内
- 处理边界情况（如科室为空、评分为空等）
- 保证统计结果的准确性和一致性

## 📁 修改的文件

### 后端文件
- `Backend/qrmanager/views.py`
  - 新增科室医院评分统计逻辑
  - 新增科室医院评分排名算法
  - 更新context数据传递

### 前端文件
- `Backend/qrmanager/templates/qrmanager/sentiment_analysis.html`
  - 移除最近评价列表
  - 新增科室医院评分分析表格
  - 优化页面布局和样式

## 🧪 测试建议

### 1. 数据验证
- 验证科室医院评分统计的准确性
- 检查排名算法的合理性
- 确认评分分布统计的正确性

### 2. 边界情况测试
- 测试没有医院评分的科室
- 测试床位或科室信息缺失的情况
- 测试评分数据为空的情况

### 3. 用户体验测试
- 验证页面布局的合理性
- 检查数据显示的清晰度
- 确认星级显示的正确性

## 🔮 后续优化建议

### 1. 功能扩展
- 添加科室医院评分的时间趋势分析
- 支持按时间范围筛选科室医院评分
- 增加科室医院评分的详细分析页面

### 2. 数据可视化
- 添加科室医院评分分布的图表展示
- 增加评分趋势的可视化分析
- 提供交互式的数据探索功能

### 3. 报告功能
- 支持科室医院评分报告导出
- 生成科室改进建议报告
- 提供定期的评分分析报告

## 📝 总结

科室医院评分分析功能成功将医院整体评分与科室关联，为医院管理层提供了：

1. **清晰的科室责任划分**：明确各科室对医院整体评价的影响
2. **精准的改进方向**：识别需要重点关注的科室
3. **有效的激励机制**：基于客观数据的科室评价体系
4. **全面的质量监控**：从科室维度监控医院整体服务质量

这个功能将帮助医院更好地管理各科室的服务质量，提升医院整体形象和患者满意度。
