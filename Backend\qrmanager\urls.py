from django.urls import path, include, re_path
from django.contrib.auth import views as auth_views
from django.contrib.auth.decorators import login_required
from . import views
from . import api
from rest_framework.routers import DefaultRouter
from .log_config_views import logging_config, clear_old_logs
from .bulk_delete import evaluation_bulk_delete
from .security_monitoring_views import SecurityMonitoringView, security_event_detail_api, qrcode_security_detail_api, ip_security_detail_api
from .security_management_views import SecurityManagementView, ban_ip_action, unban_ip_action, clear_qrcode_limits_action, update_security_config_action, clear_all_limits_action

app_name = 'qrmanager'

# 基础页面路由
base_urlpatterns = [
    # 基础页面
    path('', views.IndexView.as_view(), name='index'),
    path('about/', views.AboutView.as_view(), name='about'),
    path('contact/', views.ContactView.as_view(), name='contact'),
    path('privacy/', views.PrivacyView.as_view(), name='privacy'),

    # 唯一支持的评价路由格式 - 直接服务前端index.html
    re_path(r'^q/(?P<path>.+)/$', views.serve_frontend_index, name='evaluation'),
    re_path(r'^q/(?P<path>.+)$', views.serve_frontend_index, name='evaluation_no_slash'),

    # 评价成功页面
    path('evaluation/success/', views.EvaluationSuccessView.as_view(), name='evaluation_success'),

    # 旧格式路由 - 全部重定向到 q/ 格式
    path('evaluation/<str:qr_code>/', views.EvaluationRedirectView.as_view(), name='evaluation_legacy'),
    path('evaluation/q/<str:encoded_param>/', views.EvaluationRedirectView.as_view(), name='evaluation_legacy_secure'),
    path('e/<str:encoded_param>/', views.EvaluationRedirectView.as_view(), name='evaluation_legacy_short'),
    re_path(r'^p=(?P<encoded_param>.+)$', views.EvaluationRedirectView.as_view(), name='evaluation_legacy_param'),

    # 管理后台
    path('dashboard/', views.DashboardView.as_view(), name='dashboard'),
]

# 管理API路由 - 需要登录验证
admin_api_urlpatterns = [
    # 科室管理
    path('departments/', views.DepartmentListView.as_view(), name='department_list'),
    path('departments/create/', views.DepartmentCreateView.as_view(), name='department_create'),
    path('departments/<int:pk>/edit/', views.DepartmentUpdateView.as_view(), name='department_edit'),
    path('departments/<int:pk>/delete/', views.DepartmentDeleteView.as_view(), name='department_delete'),
    path('departments/import/', views.import_departments, name='department_import'),
    path('departments/export/', views.export_departments, name='department_export'),

    # 床位管理
    path('beds/', views.BedListView.as_view(), name='bed_list'),
    path('beds/create/', views.BedCreateView.as_view(), name='bed_create'),
    path('beds/<int:pk>/edit/', views.BedUpdateView.as_view(), name='bed_edit'),
    path('beds/<int:pk>/delete/', views.BedDeleteView.as_view(), name='bed_delete'),
    path('beds/import/', views.import_beds, name='bed_import'),
    path('beds/export/', views.export_beds, name='bed_export'),
    path('beds/qr_preview/', views.bed_qr_preview, name='bed_qr_preview_api'),

    # 工作人员管理
    path('staff/', views.StaffListView.as_view(), name='staff_list'),
    path('staff/create/', views.StaffCreateView.as_view(), name='staff_create'),
    path('staff/<int:pk>/edit/', views.StaffUpdateView.as_view(), name='staff_edit'),
    path('staff/<int:pk>/delete/', views.StaffDeleteView.as_view(), name='staff_delete'),
    path('staff/export/', views.export_staff, name='staff_export'),
    path('staff/bulk_import/', views.StaffBulkImportView.as_view(), name='staff_bulk_import'),
    path('staff/template/', views.DownloadStaffTemplateView.as_view(), name='staff_template'),
    path('staff/bulk_delete/', views.staff_bulk_delete, name='staff_bulk_delete'),

    # 二维码管理
    path('qrcodes/', views.QRCodeListView.as_view(), name='qrcode_list'),
    path('qrcodes/create/', views.QRCodeCreateView.as_view(), name='qrcode_create'),
    path('qrcodes/<int:pk>/update/', views.QRCodeUpdateView.as_view(), name='qrcode_update'),
    path('qrcodes/<int:pk>/delete/', views.QRCodeDeleteView.as_view(), name='qrcode_delete'),
    path('qrcodes/<int:pk>/regenerate/', views.QRRegenerateView.as_view(), name='qrcode_regenerate'),
    path('qrcodes/<int:pk>/preview/', views.QRCodePreviewView.as_view(), name='qrcode_preview'),
    path('qrcodes/<int:qrcode_id>/image/', views.generate_qrcode_image, name='generate_qrcode_image'),
    path('qrcodes/history/', views.QRCodeHistoryListView.as_view(), name='qrcode_history'),
    path('qrcodes/import/', views.import_qrcodes, name='qrcode_import'),
    path('qrcodes/export/', views.export_qrcodes, name='qrcode_export'),
    path('qrcodes/print/department/<int:department_id>/zip/', views.print_department_qrcodes_zip, name='print_department_qrcodes_zip'),

    # 评价管理
    path('evaluations/', views.EvaluationListView.as_view(), name='evaluation_list'),
    path('evaluations/process/<int:evaluation_id>/', views.process_evaluation, name='process_evaluation'),
    path('evaluations/export/', views.export_evaluations, name='export_evaluations'),
    path('evaluations/bulk_delete/', evaluation_bulk_delete, name='evaluation_bulk_delete'),
    path('evaluations/add_note/', views.add_evaluation_note, name='add_evaluation_note'),
    path('evaluations/get_note/', views.get_evaluation_note, name='get_evaluation_note'),
    path('sentiment/', views.SentimentAnalysisView.as_view(), name='sentiment_analysis'),
    path('sentiment/export/', views.export_sentiment_report, name='export_sentiment_report'),

    # 字典管理
    path('dictionaries/', views.DictionaryListView.as_view(), name='dictionary_list'),
    path('dictionaries/create/', views.DictionaryCreateView.as_view(), name='dictionary_create'),
    path('dictionaries/<int:pk>/update/', views.DictionaryUpdateView.as_view(), name='dictionary_update'),
    path('dictionaries/<int:pk>/delete/', views.DictionaryDeleteView.as_view(), name='dictionary_delete'),
    path('dictionaries/<int:dictionary_id>/items/', views.DictionaryItemListView.as_view(), name='dictionary_item_list'),
    path('dictionaries/<int:dictionary_id>/items/create/', views.DictionaryItemCreateView.as_view(), name='dictionary_item_create'),
    path('dictionaries/items/<int:pk>/update/', views.DictionaryItemUpdateView.as_view(), name='dictionary_item_update'),
    path('dictionaries/items/<int:pk>/delete/', views.DictionaryItemDeleteView.as_view(), name='dictionary_item_delete'),

    # 用户管理
    path('admin/create/', views.AdminAccountCreateView.as_view(), name='admin_create'),
    path('admin_permissions/', views.AdminAccountPermissionView.as_view(), name='admin_permissions'),
    path('admin_account_update/<int:pk>/', views.AdminAccountUpdateView.as_view(), name='admin_account_update'),
    path('admin_account_create/', views.AdminAccountCreateView.as_view(), name='admin_account_create'),
    path('admin/reset_password/<int:pk>/', views.ResetUserPasswordView.as_view(), name='reset_user_password'),
    path('admin/delete_user/<int:pk>/', views.DeleteUserView.as_view(), name='delete_user'),
    path('account_settings/', views.AccountSettingsView.as_view(), name='account_settings'),
    path('password_change/', views.CustomPasswordChangeView.as_view(), name='password_change'),
    path('password_change/done/', views.CustomPasswordChangeDoneView.as_view(), name='password_change_done'),

    # 日志管理
    path('operation_logs/', views.OperationLogListView.as_view(), name='operation_log_list'),
    path('operation_logs/', views.OperationLogListView.as_view(), name='operation_logs'),

    # 安全监控
    path('security/monitoring/', SecurityMonitoringView.as_view(), name='security_monitoring'),
    path('security/events/<int:event_id>/', security_event_detail_api, name='security_event_detail'),
    path('security/qrcodes/<str:uuid>/', qrcode_security_detail_api, name='qrcode_security_detail'),
    path('security/ips/<str:ip>/', ip_security_detail_api, name='ip_security_detail'),

    # 安全管理
    path('security/management/', SecurityManagementView.as_view(), name='security_management'),
    path('security/actions/ban-ip/', ban_ip_action, name='ban_ip'),
    path('security/actions/unban-ip/', unban_ip_action, name='unban_ip'),
    path('security/actions/clear-limits/', clear_qrcode_limits_action, name='clear_qrcode_limits'),
    path('security/actions/update-config/', update_security_config_action, name='update_security_config'),
    path('security/actions/clear-all-limits/', clear_all_limits_action, name='clear_all_limits'),

    # 导出操作日志
    path('operation_logs/export/excel/', views.OperationLogListView.as_view(), {'action': 'export_excel'}, name='export_operation_logs_excel'),
    path('operation_logs/export/pdf/', views.OperationLogListView.as_view(), {'action': 'export_pdf'}, name='export_operation_logs_pdf'),

    # 日志配置管理
    path('logging-config/', logging_config, name='logging_config'),
    path('logging-config/clear-old-logs/', clear_old_logs, name='clear_old_logs'),

    # 打印模板管理
    path('print-templates/', views.PrintTemplateListView.as_view(), name='print_template_list'),
    path('print-templates/create/', views.PrintTemplateCreateView.as_view(), name='print_template_create'),
    path('departments/<int:department_id>/print-template/create/', views.PrintTemplateCreateView.as_view(), name='department_print_template_create'),
    path('print-templates/<int:pk>/update/', views.PrintTemplateUpdateView.as_view(), name='print_template_update'),
    path('print-templates/<int:pk>/preview/', views.PrintTemplatePreviewView.as_view(), name='print_template_preview'),
    path('print-templates/<int:pk>/delete/', views.PrintTemplateDeleteView.as_view(), name='print_template_delete'),

    # 系统配置
    path('system-configs/', views.SystemConfigListView.as_view(), name='system_config_list'),
    path('system-configs/<int:pk>/update/', views.SystemConfigUpdateView.as_view(), name='system_config_update'),
    path('system-configs/initialize/', views.initialize_system_configs, name='initialize_system_configs'),
    path('update_url_settings/', views.update_url_settings, name='update_url_settings'),

    # API管理中心
    path('api/management/', views.api_management, name='api_management'),
    path('api/keys/create/', views.create_api_key, name='create_api_key'),
    path('api/keys/<int:pk>/toggle/', views.toggle_api_key_status, name='toggle_api_key'),
    path('api/keys/<int:pk>/delete/', views.delete_api_key, name='delete_api_key'),
    path('api/logs/', views.api_logs, name='api_logs'),

    # API文档
    path('api/docs/', views.api_docs, name='api_docs'),

    # API密钥管理
    path('api-keys/', views.APIKeyListView.as_view(), name='apikey_list'),
    path('api-keys/create/', views.APIKeyCreateView.as_view(), name='apikey_create'),
    path('api-keys/<int:pk>/update/', views.APIKeyUpdateView.as_view(), name='apikey_update'),
    path('api-keys/<int:pk>/delete/', views.APIKeyDeleteView.as_view(), name='apikey_delete'),
    path('api-logs/', views.APILogListView.as_view(), name='apilog_list'),
    path('api-logs/<int:pk>/', views.APILogDetailView.as_view(), name='apilog_detail'),
]

# 内部API路由 - 需要登录验证
internal_api_urlpatterns = [
    # 旧式API接口 - 需要登录验证
    path('admin/api/departments/', views.api_departments, name='api_departments'),
    path('admin/api/beds/', views.api_beds, name='api_beds'),
    path('admin/api/staff/', views.api_staff, name='api_staff'),
    path('admin/api/qrcodes/', views.api_qrcodes, name='api_qrcodes'),
    path('admin/api/evaluations/', views.api_evaluations, name='api_evaluations'),
    path('admin/api/print_templates/<int:template_id>/', views.get_print_template, name='get_print_template'),
    path('admin/api/departments/<int:pk>/', views.api_department, name='api_department_detail'),
    path('admin/api/department/<int:pk>/staff/', views.api_department_staff, name='api_department_staff'),
    path('admin/api/beds/import/', views.api_import_beds, name='api_bed_import'),
    path('admin/api/beds/template/', views.api_bed_template, name='api_bed_template'),
    # 进度API
    path('admin/api/reset-progress/', views.reset_progress, name='reset_progress'),
    path('admin/api/progress/', views.get_progress, name='get_progress'),
    path('admin/api/cancel-task/', views.cancel_task, name='cancel_task'),
]



# RESTful API路由 - 需要API令牌认证
rest_api_urlpatterns = [
    # 科室API
    path('api/v1/departments/', api.DepartmentListAPI.as_view(), name='api_department_list'),
    path('api/v1/departments/<int:pk>/', api.DepartmentDetailAPI.as_view(), name='api_v1_department_detail'),
    path('api/v1/staff-types/', api.StaffTypeAPI.as_view(), name='api_staff_types'),

    # 床位API
    path('api/v1/beds/', api.BedListAPI.as_view(), name='api_v1_beds'),
    path('api/v1/beds/<int:pk>/', api.BedDetailAPI.as_view(), name='api_v1_bed_detail'),

    # 工作人员API
    path('api/v1/staff/', api.StaffListAPI.as_view(), name='api_v1_staff'),
    path('api/v1/staff/<int:pk>/', api.StaffDetailAPI.as_view(), name='api_v1_staff_detail'),

    # 二维码API
    path('api/v1/qrcodes/', api.QRCodeListAPI.as_view(), name='api_v1_qrcodes'),
    path('api/v1/qrcodes/<int:pk>/', api.QRCodeDetailAPI.as_view(), name='api_v1_qrcode_detail'),

    # 安全的二维码API - 需要API令牌认证
    path('api/v1/secure/qrcode/<str:qr_code>/', api.SecureQRCodeAPI.as_view(), name='api_v1_secure_qrcode'),

    # 评价API
    path('api/v1/evaluations/', api.EvaluationListAPI.as_view(), name='api_v1_evaluations'),
    path('api/v1/evaluations/<int:pk>/', api.EvaluationDetailAPI.as_view(), name='api_v1_evaluation_detail'),

    # 打印模板API
    path('api/v1/print-templates/<int:pk>/', api.PrintTemplateAPI.as_view(), name='api_v1_print_template'),
]

# 公开RESTful API路由 - 无需认证
public_rest_api_urlpatterns = [
    # 公开API - 无需认证
    path('api/v1/public/submit-evaluation/', api.PublicEvaluationSubmitAPI.as_view(), name='api_v1_public_submit_evaluation'),

    # 添加POST验证API端点到public路径下 - 这是唯一保留的QR码验证接口
    path('api/v1/public/qrcode/verify/', api.QRCodeVerifyAPI.as_view(), name='api_v1_public_qrcode_verify_post'),

    # 仅支持/q/格式的二维码验证API - 已删除
    # path('api/v1/qrcode/verify/q/<str:encoded_param>/', api.QRCodeVerifyAPI.as_view(), name='api_v1_qrcode_verify'),

    # 添加POST验证API端点 - 已删除
    # path('api/v1/qrcode/verify/', api.QRCodeVerifyAPI.as_view(), name='api_v1_qrcode_verify_post'),

    # 新增生成加密参数API - 用于测试 - 已删除
    # path('api/v1/qrcode/generate-param/', api.GenerateParamAPI.as_view(), name='api_v1_generate_param'),
]

# 创建REST框架路由器
router = DefaultRouter()
router.register(r'api/v1/departments', api.DepartmentViewSet)
router.register(r'api/v1/beds', api.BedViewSet)
router.register(r'api/v1/staff', api.StaffViewSet)
router.register(r'api/v1/qrcodes', api.QRCodeViewSet)
router.register(r'api/v1/evaluations', api.EvaluationViewSet)
router.register(r'api/v1/print-templates', api.PrintTemplateViewSet)

# 合并所有路由
urlpatterns = (
    base_urlpatterns +
    admin_api_urlpatterns +
    internal_api_urlpatterns +
    rest_api_urlpatterns +
    public_rest_api_urlpatterns +
    router.urls
)