/*! 
 * 简化版Tailwind CSS - 只包含感谢页面使用的样式
 * 本地化版本，无外部依赖
 */
 
/* 基础样式 */
*,::before,::after{box-sizing:border-box;border-width:0;border-style:solid}
html{line-height:1.5;-webkit-text-size-adjust:100%;-moz-tab-size:4;tab-size:4;font-family:ui-sans-serif,system-ui,-apple-system,BlinkMacSystemFont,"Segoe UI",Roboto,"Helvetica Neue",<PERSON><PERSON>,"Noto Sans",sans-serif,"Apple Color Emoji","Segoe UI Emoji","Segoe UI Symbol","Noto Color Emoji"}
body{margin:0;line-height:inherit}

/* 布局类 */
.min-h-screen{min-height:100vh}
.flex{display:flex}
.flex-col{flex-direction:column}
.items-center{align-items:center}
.justify-center{justify-content:center}
.p-4{padding:1rem}
.p-6{padding:1.5rem}
.p-8{padding:2rem}
.mt-2{margin-top:0.5rem}
.mb-4{margin-bottom:1rem}
.mb-6{margin-bottom:1.5rem}
.mx-auto{margin-left:auto;margin-right:auto}
.space-y-3 > * + *{margin-top:0.75rem}
.w-full{width:100%}
.max-w-md{max-width:28rem}
.w-20{width:5rem}
.h-20{height:5rem}

/* 背景和边框 */
.bg-white{background-color:#ffffff}
.bg-green-100{background-color:#dcfce7}
.bg-blue-500{background-color:#3b82f6}
.bg-gray-50{background-color:#f9fafb}
.hover\:bg-blue-600:hover{background-color:#2563eb}
.border-t{border-top-width:1px}
.border-gray-100{border-color:#f3f4f6}
.rounded-full{border-radius:9999px}
.rounded-md{border-radius:0.375rem}

/* 文本样式 */
.text-white{color:#ffffff}
.text-green-500{color:#22c55e}
.text-gray-500{color:#6b7280}
.text-gray-600{color:#4b5563}
.hover\:text-gray-700:hover{color:#374151}
.text-center{text-align:center}
.text-sm{font-size:0.875rem;line-height:1.25rem}
.text-2xl{font-size:1.5rem;line-height:2rem}
.text-xl{font-size:1.25rem;line-height:1.75rem}
.text-4xl{font-size:2.25rem;line-height:2.5rem}
.font-bold{font-weight:700}
.opacity-80{opacity:0.8}

/* 其他 */
.transition{transition-property:color,background-color,border-color,text-decoration-color,fill,stroke,opacity,box-shadow,transform,filter,backdrop-filter;transition-timing-function:cubic-bezier(0.4,0,0.2,1);transition-duration:150ms}
.duration-300{transition-duration:300ms}
.py-2{padding-top:0.5rem;padding-bottom:0.5rem}
.px-4{padding-left:1rem;padding-right:1rem} 