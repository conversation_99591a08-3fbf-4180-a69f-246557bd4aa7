import re

# 读取文件内容
with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    content = f.readlines()

# 修改验证必填字段的条件
content[2300] = content[2300].replace('all([work_number, name, staff_type_name, title_name, department_name])', 'all([work_number, name, staff_type_name, department_name])')

# 移除职称必填字段的检查
content[2304] = '                        # 职称不是必填字段\n'

# 修改职称验证部分，只在提供了职称时验证
title_validation_start = 2334
new_title_validation = [
    '                    # 验证职称是否有效（宽松比较）- 只在提供了职称时验证\n',
    '                    if title_name:\n',
    '                        title_found = False\n',
    '                        for valid_title, valid_title_clean in zip(valid_titles, valid_titles_clean):\n',
    '                            if title_name.strip().lower() == valid_title_clean.lower():\n',
    '                                title_found = True\n',
    '                                break\n',
    '                        \n',
    '                        if not title_found:\n',
    '                            errors.append(f"行 {row_idx}: 职称 \'{title_name}\' 不存在或未激活")\n',
    '                            print(f"职称验证失败: \'{title_name}\' 不在有效列表中")\n'
]
content[title_validation_start-1:title_validation_start+9] = new_title_validation

# 写入修改后的内容
with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
    f.writelines(content)

print('文件修改成功') 