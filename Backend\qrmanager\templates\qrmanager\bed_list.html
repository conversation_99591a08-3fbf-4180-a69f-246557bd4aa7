{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}床位管理{% endblock %}

{% block extra_css %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<style>
/* 全局样式 */
:root {
    --primary-color: #0071e3;
    --secondary-color: #86b7fe;
    --success-color: #34c759;
    --danger-color: #ff3b30;
    --warning-color: #ff9500;
    --info-color: #5ac8fa;
    --light-color: #f5f5f7;
    --dark-color: #1d1d1f;
    --border-radius: 8px;
    --box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Helvetica, Arial, sans-serif;
    background-color: #f5f5f7;
    color: #1d1d1f;
}

.card {
    border: none;
    border-radius: var(--border-radius);
    box-shadow: var(--box-shadow);
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
}

.btn {
    border-radius: 20px;
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    transition: all 0.2s ease;
}

.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #0062cc;
    border-color: #0062cc;
}

.btn-success {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-success:hover {
    background-color: #2eb04d;
    border-color: #2eb04d;
}

.btn-danger {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background-color: #e02e24;
    border-color: #e02e24;
}

.btn-warning {
    background-color: var(--warning-color);
    border-color: var(--warning-color);
    color: white;
}

.btn-warning:hover {
    background-color: #e68600;
    border-color: #e68600;
    color: white;
}

.btn-info {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-info:hover {
    background-color: #47b3e6;
    border-color: #47b3e6;
}

.btn-outline-primary {
    color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-primary:hover {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-outline-success {
    color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-success:hover {
    background-color: var(--success-color);
    border-color: var(--success-color);
}

.btn-outline-danger {
    color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-danger:hover {
    background-color: var(--danger-color);
    border-color: var(--danger-color);
}

.btn-outline-info {
    color: var(--info-color);
    border-color: var(--info-color);
}

.btn-outline-info:hover {
    background-color: var(--info-color);
    border-color: var(--info-color);
    color: white;
}

.btn-sm {
    padding: 0.25rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 16px;
}

.form-control, .form-select {
    border-radius: var(--border-radius);
    padding: 0.5rem 1rem;
    border: 1px solid #d2d2d7;
    transition: all 0.2s ease;
}

.form-control:focus, .form-select:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.25rem rgba(0, 113, 227, 0.25);
}

.table {
    border-radius: var(--border-radius);
    overflow: hidden;
}

.table th {
    background-color: #f5f5f7;
    font-weight: 600;
    border-top: none;
}

.table-hover tbody tr:hover {
    background-color: rgba(0, 113, 227, 0.05);
}

.pagination .page-link {
    color: var(--primary-color);
    border-radius: 50%;
    margin: 0 3px;
    width: 36px;
    height: 36px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.pagination .page-item.active .page-link {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

/* 拖拽区域样式 */
.import-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
    cursor: pointer;
    margin-bottom: 20px;
    position: relative;
    overflow: hidden;
}

.import-area:hover {
    border-color: var(--primary-color);
    box-shadow: 0 0 15px rgba(0, 113, 227, 0.2);
    transform: translateY(-2px);
}

.import-area:hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    border: 2px dashed var(--primary-color);
    border-radius: 8px;
    animation: pulse 1.5s infinite;
    pointer-events: none;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
        transform: scale(0.98);
    }
    50% {
        opacity: 1;
        transform: scale(1);
    }
    100% {
        opacity: 0.6;
        transform: scale(0.98);
    }
}

.import-area.highlight {
    border-color: var(--primary-color);
    background-color: rgba(0, 113, 227, 0.05);
    transform: translateY(-2px);
    box-shadow: 0 5px 20px rgba(0, 113, 227, 0.25);
}

.import-icon {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 15px;
    transition: all 0.3s ease;
}

.import-area:hover .import-icon {
    color: var(--primary-color);
    transform: scale(1.1);
}

.import-area p {
    color: #6c757d;
    font-size: 16px;
    transition: all 0.3s ease;
}

.import-area:hover p {
    color: var(--primary-color);
}

/* 二维码预览样式 */
.preview-container {
    display: flex;
    height: 600px;
}

.preview-sidebar {
    width: 280px;
    background-color: #f8f9fa;
    border-right: 1px solid #e9ecef;
    overflow-y: auto;
    padding: 0;
}

.preview-main {
    flex: 1;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    overflow: hidden;
    position: relative;
}

.preview-section {
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
}

.preview-section:last-child {
    border-bottom: none;
}

.preview-section-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    color: #212529;
}

.preview-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 8px;
    font-size: 13px;
}

.preview-label {
    color: #6c757d;
    flex-shrink: 0;
    margin-right: 10px;
}

.preview-value {
    font-weight: 500;
    text-align: right;
    word-break: break-all;
}

.preview-value.code {
    font-family: monospace;
    font-size: 12px;
    background-color: #f1f3f5;
    padding: 2px 4px;
    border-radius: 3px;
}

.template-preview-container {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
}

.template-preview {
    width: 100%;
    max-width: 500px;
    height: 0;
    padding-top: 141.4%; /* A4比例 */
    position: relative;
    background-color: #fff;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.template-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.template-no-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #adb5bd;
    font-size: 14px;
    background-color: #f8f9fa;
}

.qrcode-only-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px;
    width: 100%;
    height: 100%;
}

.qrcode-container {
    background-color: transparent;
    padding: 0;
    border-radius: 0;
    box-shadow: none;
    margin-bottom: 20px;
}

.qrcode-large {
    width: 200px;
    height: 200px;
    border: none;
    box-shadow: none;
}

.qrcode-notice {
    color: #6c757d;
    font-size: 14px;
    background-color: #f8f9fa;
    padding: 8px 16px;
    border-radius: 4px;
}

.preview-url-container {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
}

.preview-url-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* Toast容器样式 */
.toast-container {
    z-index: 1090;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <div class="card fade-in">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="card-title">床位管理</h1>
                        <div class="d-flex flex-wrap gap-2">
                            <a href="{% url 'qrmanager:department_list' %}" class="btn btn-outline-primary">
                                <i class="fas fa-hospital me-2 text-primary"></i>返回科室管理
                            </a>
                            {% if department %}
                                {% if department.print_template %}
                                <a href="{% url 'qrmanager:print_template_update' department.print_template.pk %}"
                                   class="btn btn-outline-info" title="编辑打印模板">
                                    <i class="fas fa-edit me-2"></i>编辑打印模板
                                </a>
                                <a href="{% url 'qrmanager:print_template_preview' department.print_template.pk %}"
                                   class="btn btn-outline-success" title="打印预览">
                                    <i class="fas fa-print me-2"></i>打印预览
                                </a>
                                {% else %}
                                <a href="{% url 'qrmanager:department_print_template_create' department.id %}"
                                   class="btn btn-outline-warning" title="设置打印模板">
                                    <i class="fas fa-plus me-2"></i>设置打印模板
                                </a>
                                {% endif %}
                                <button type="button" class="btn btn-outline-primary import-btn"
                                        data-department-id="{{ department.id }}"
                                        onclick="showImportModal('{{ department.id }}')"
                                        title="导入床位">
                                    <i class="fas fa-file-import me-2"></i>导入床位
                                </button>
                                <a href="{% url 'qrmanager:bed_export' %}?department={{ department.id }}"
                                   class="btn btn-outline-secondary" title="导出床位">
                                    <i class="fas fa-file-export me-2"></i>导出床位
                                </a>
                                <a href="{% url 'qrmanager:bed_create' %}?department={{ department.id }}"
                                   class="btn btn-success" title="新增床位">
                                    <i class="fas fa-plus me-2"></i>新增床位
                                </a>
                            {% else %}
                                <div class="alert alert-warning mb-0 py-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>请先选择一个科室再添加床位
                                </div>
                            {% endif %}
                        </div>
                    </div>

                    <!-- 筛选表单 -->
                    <form method="get" class="mb-4" id="filterForm">
                        <div class="row g-3">
                            <div class="col-md-4">
                                <div class="input-group">
                                    <span class="input-group-text bg-light"><i class="fas fa-hospital-alt text-primary"></i></span>
                                <select name="department" class="form-select" id="departmentSelect">
                                    <option value="">全部科室</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if department and department.id == dept.id %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text bg-light"><i class="fas fa-map-marker-alt text-info"></i></span>
                                <select name="area" class="form-select">
                                    <option value="">全部区域</option>
                                    <option value="A" {% if request.GET.area == 'A' %}selected{% endif %}>A区</option>
                                    <option value="B" {% if request.GET.area == 'B' %}selected{% endif %}>B区</option>
                                </select>
                                </div>
                            </div>
                            <div class="col-md-3">
                                <div class="input-group">
                                    <span class="input-group-text bg-light"><i class="fas fa-search text-secondary"></i></span>
                                <input type="text" name="search" class="form-control" placeholder="搜索床位号..." value="{{ request.GET.search }}">
                                </div>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-info w-100">
                                    <i class="fas fa-filter me-2"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>

                    {% if bed_list %}
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>床位号</th>
                                    <th>科室</th>
                                    <th>区域</th>
                                    <th>负责人</th>
                                    <th>二维码</th>
                                    <th>评价数</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for bed in bed_list %}
                                {% with previous_bed=bed_list|slice:forloop.counter0|last %}
                                {% if forloop.first or bed.department_id != previous_bed.department_id %}
                                <tr class="table-light" data-department-id="{{ bed.department_id }}">
                                    <td colspan="7" class="fw-bold bg-light">
                                        <i class="fas fa-hospital-alt me-2 text-primary"></i>{{ bed.department.name }}
                                    </td>
                                </tr>
                                {% endif %}
                                {% endwith %}
                                <tr>
                                    <td><span class="fw-medium">{{ bed.number }}</span></td>
                                    <td>{{ bed.department.name }}</td>
                                    <td>
                                        {% if bed.area %}
                                        <span class="badge bg-info text-white">{{ bed.get_area_display }}</span>
                                        {% else %}
                                        <span class="text-muted">-</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if bed.staff %}
                                        <span class="d-flex align-items-center">
                                            <i class="fas fa-user-md me-1 text-secondary"></i>
                                            {{ bed.staff.name }}
                                        </span>
                                        {% else %}
                                        <span class="text-muted">未分配</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if bed.qrcode %}
                                        <span class="badge bg-success">
                                            <i class="fas fa-qrcode me-1"></i>已生成
                                        </span>
                                        <a href="{% url 'qrmanager:qrcode_list' %}?department={{ bed.department_id }}&search={{ bed.number }}" class="btn btn-sm btn-link text-primary">
                                            <i class="fas fa-external-link-alt me-1"></i>在二维码管理中查看
                                        </a>
                                        {% else %}
                                        <span class="badge bg-secondary">未生成</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if bed.evaluation_count > 0 %}
                                        <span class="badge bg-success">{{ bed.evaluation_count }}</span>
                                        {% else %}
                                        <span class="badge bg-light text-dark">0</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <div class="action-buttons">
                                            <a href="{% url 'qrmanager:bed_edit' bed.pk %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-edit me-1"></i>编辑
                                            </a>
                                            <form action="{% url 'qrmanager:bed_delete' bed.pk %}" method="post" style="display: inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger"
                                                        onclick="return confirm('警告：删除{{ bed.number }}号床位将同时删除关联的二维码和评价数据！确定要继续吗？');">
                                                    <i class="fas fa-trash me-1"></i>删除
                                                </button>
                                            </form>
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="alert alert-info text-center">
                        暂无床位信息，请点击"新增床位"添加。
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加导入床位模态框 -->
<div class="modal" id="importModal" tabindex="-1" role="dialog" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="fas fa-file-import me-2 text-primary"></i>导入床位数据
                    <span class="badge bg-primary ms-2" id="departmentNameBadge"></span>
                </h5>
                <button type="button" class="btn-close" onclick="hideModal('importModal')" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">导入说明</h5>
                            <p class="mb-0">请按照以下格式准备Excel或CSV文件，您可以下载模板后填写数据。</p>
                        </div>
                    </div>
                </div>

                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-table me-2 text-primary"></i>数据格式示例</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>科室编码</th>
                                        <th>科室名称</th>
                                        <th>床位号</th>
                                        <th>区域</th>
                                        <th>工作人员工号</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td id="departmentCodeExample"></td>
                                        <td id="departmentNameExample"></td>
                                        <td>101</td>
                                        <td>A</td>
                                        <td>可选</td>
                                    </tr>
                                    <tr>
                                        <td id="departmentCodeExample2"></td>
                                        <td id="departmentNameExample2"></td>
                                        <td>102</td>
                                        <td>B</td>
                                        <td>可选</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <a href="#" id="downloadTemplateBtn" class="btn btn-outline-primary">
                                <i class="fas fa-download me-2"></i>下载导入模板
                            </a>
                        </div>
                    </div>
                </div>

                <form id="importForm" enctype="multipart/form-data" class="mt-4">
                    {% csrf_token %}
                    <input type="hidden" name="department_id" id="departmentIdInput">
                    <div class="import-area" id="dropZone">
                        <div class="import-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <p class="mb-3">拖拽文件到此处或点击选择文件</p>
                        <input type="file" name="file" id="fileInput" class="d-none" accept=".xlsx,.xls,.csv">
                        <button type="button" class="btn btn-outline-primary" id="browseBtn">
                            <i class="fas fa-folder-open me-2"></i>选择文件
                        </button>
                        <div id="fileInfo" class="mt-3 d-none">
                            <p class="text-success mb-1">已选择文件：<span id="fileName"></span></p>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeFileBtn">
                                <i class="fas fa-times me-1"></i>移除
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" onclick="hideModal('importModal')" id="cancelImportBtn">取消</button>
                <button type="button" class="btn btn-primary" id="importBtn" disabled>
                    <i class="fas fa-upload me-2"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 导入结果模态框 -->
<div class="modal" id="importResultModal" tabindex="-1" role="dialog" aria-labelledby="importResultModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="importResultModalLabel">
                    <i class="fas fa-info-circle me-2 text-primary"></i>导入结果
                </h5>
                <button type="button" class="btn-close" onclick="hideModal('importResultModal')" aria-label="Close"></button>
            </div>
            <div class="modal-body" id="importResultContent">
                <!-- 结果内容将在这里动态生成 -->
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-primary" onclick="hideModal('importResultModal')">确定</button>
                <button type="button" class="btn btn-outline-secondary" onclick="window.location.reload()">
                    <i class="fas fa-sync-alt me-2"></i>刷新页面
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 添加Toast容器 -->
<div class="toast-container position-fixed bottom-0 end-0 p-3"></div>

{% endblock %}

{% block extra_js %}
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdn.jsdelivr.net/npm/qrcodejs@1.0.0/qrcode.min.js"></script>
<script src="{% static 'qrmanager/js/qrcode-unified.js' %}"></script>
<script>
$(document).ready(function() {
    console.log('页面加载完成');

    // 初始化模态框
    var importModal = new bootstrap.Modal(document.getElementById('importModal'));
    var importResultModal = new bootstrap.Modal(document.getElementById('importResultModal'));

    // 将模态框实例保存为全局变量，以便在事件处理函数中使用
    window.importModal = importModal;

    // 监听所有模态框的隐藏事件，确保背景遮罩正确移除
    $('.modal').on('hidden.bs.modal', function() {
        // 移除所有模态框背景和相关样式
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
    });

    // 绑定导入按钮点击事件
    $('.import-btn').click(function() {
        var departmentId = $(this).data('department-id');
        console.log('导入按钮被点击, 部门ID:', departmentId);

        // 清空文件输入
        $('#fileInput').val('');
        $('#fileInfo').addClass('d-none');
        $('#importBtn').prop('disabled', true);

        // 设置部门ID
        $('#departmentIdInput').val(departmentId);

        // 获取科室信息并显示
        $.ajax({
            url: '{% url 'qrmanager:api_department_detail' 0 %}'.replace('0', departmentId),
            method: 'GET',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function(data) {
                console.log('获取到科室信息:', data);

                // 设置科室名称
                $('#departmentNameBadge').text(data.name || '');

                // 设置科室编码和名称示例
                $('#departmentCodeExample, #departmentCodeExample2').text(data.code || '');
                $('#departmentNameExample, #departmentNameExample2').text(data.name || '');

                // 显示模态框
                window.importModal.show();
            },
            error: function(xhr, status, error) {
                console.error('获取科室信息失败:', error, xhr.status, xhr.responseText);

                // 检查是否是未授权错误（会话过期）
                if (xhr.status === 401) {
                    // 显示会话过期提示
                    alert('会话已过期，请重新登录');

                    // 延迟2秒后重定向到登录页面
                    setTimeout(function() {
                        window.location.href = '{% url "login" %}';
                    }, 2000);
                    return;
                }

                // 处理其他错误
                alert('获取科室信息失败，请刷新页面重试: ' + error);
            }
        });
    });

    // 文件选择按钮点击事件
    $('#browseBtn').click(function() {
        $('#fileInput').click();
    });

    // 文件输入变化事件
    $('#fileInput').change(function() {
        if (this.files.length === 0) return;

        var file = this.files[0];

        // 检查文件类型
        var validExtensions = ['.xlsx', '.xls', '.csv'];
        var fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

        if (!validExtensions.includes(fileExtension)) {
            alert('请选择有效的Excel或CSV文件');
            $(this).val('');
            return;
        }

        // 显示文件信息
        $('#fileName').text(file.name);
        $('#fileInfo').removeClass('d-none');
        $('#importBtn').prop('disabled', false);
    });

    // 移除文件按钮点击事件
    $('#removeFileBtn').click(function() {
        $('#fileInput').val('');
        $('#fileInfo').addClass('d-none');
        $('#importBtn').prop('disabled', true);
    });

    // 下载模板按钮点击事件
    $('#downloadTemplateBtn').click(function(e) {
        e.preventDefault();
        var departmentId = $('#departmentIdInput').val();
        if (!departmentId) {
            alert('未找到科室ID，请刷新页面重试');
            return;
        }

        console.log('下载模板按钮被点击，部门ID:', departmentId);

        // 打开新窗口下载模板
        var downloadUrl = '{% url 'qrmanager:api_bed_template' %}?department_id=' + departmentId;
        console.log('下载URL:', downloadUrl);

        // 显示加载提示
        showToast('正在准备下载模板...', 'info');

        // 先检查URL是否可访问
        $.ajax({
            url: downloadUrl,
            method: 'HEAD',
            headers: {
                'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
            },
            success: function() {
                // URL可访问，打开下载
                window.open(downloadUrl, '_blank');
                showToast('模板下载已开始', 'success');
            },
            error: function(xhr, status, error) {
                console.error('模板下载失败:', error, xhr.status, xhr.responseText);

                // 检查是否是未授权错误（会话过期）
                if (xhr.status === 401) {
                    // 显示会话过期提示
                    alert('会话已过期，请重新登录');

                    // 延迟2秒后重定向到登录页面
                    setTimeout(function() {
                        window.location.href = '{% url "login" %}';
                    }, 2000);
                    return;
                }

                showToast('模板下载失败，请稍后重试', 'danger');

                // 尝试直接打开
                window.open(downloadUrl, '_blank');
            }
        });
    });

    // 导入按钮点击事件
    $('#importBtn').click(function() {
        var fileInput = document.getElementById('fileInput');
        var departmentId = $('#departmentIdInput').val();

        if (!fileInput || !fileInput.files.length) {
            alert('请选择要导入的文件');
            return;
        }

        // 创建FormData对象
        var formData = new FormData();
        formData.append('file', fileInput.files[0]);
        formData.append('department_id', departmentId);

        // 获取CSRF令牌
        var csrfToken = $('[name=csrfmiddlewaretoken]').val();

        // 禁用按钮，显示加载状态
        $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>导入中...');

        // 显示加载提示
        showToast('正在处理导入请求...', 'info');

        console.log('发送导入请求，部门ID:', departmentId, '文件:', fileInput.files[0].name);

        // 发送请求
        $.ajax({
            url: '{% url 'qrmanager:api_bed_import' %}',
            method: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            headers: {
                'X-CSRFToken': csrfToken
            },
            success: function(data) {
                console.log('导入结果:', data);

                // 隐藏导入模态框
                importModal.hide();

                // 显示结果
                var html = '';

                if (data.success) {
                    showToast('导入成功: ' + data.imported_count + ' 条记录', 'success');
                    html = '<div class="alert alert-success"><div class="d-flex"><div class="me-3"><i class="fas fa-check-circle fa-3x text-success"></i></div><div><h5 class="alert-heading">导入成功</h5><p class="mb-0">成功导入 <strong>' + data.imported_count + '</strong> 个床位。</p></div></div></div>';

                    // 如果有错误记录
                    if (data.errors && data.errors.length > 0) {
                        html += '<div class="alert alert-warning mt-3"><div class="d-flex"><div class="me-3"><i class="fas fa-exclamation-triangle fa-2x text-warning"></i></div><div><h5 class="alert-heading">部分记录导入失败</h5><p>以下 ' + data.errors.length + ' 条记录导入失败：</p><div class="table-responsive mt-2"><table class="table table-sm table-bordered"><thead class="table-light"><tr><th>序号</th><th>错误原因</th></tr></thead><tbody>';

                        $.each(data.errors, function(index, error) {
                            html += '<tr><td>' + (index + 1) + '</td><td>' + error + '</td></tr>';
                        });

                        html += '</tbody></table></div></div></div></div>';
                    }

                    // 添加成功提示和刷新建议
                    html += '<div class="alert alert-info mt-3"><i class="fas fa-info-circle me-2"></i>导入操作已完成，您可以点击"刷新页面"按钮查看最新数据。</div>';
                } else {
                    showToast('导入失败: ' + (data.message || '未知错误'), 'danger');
                    html = '<div class="alert alert-danger"><div class="d-flex"><div class="me-3"><i class="fas fa-times-circle fa-3x text-danger"></i></div><div><h5 class="alert-heading">导入失败</h5><p class="mb-0">' + (data.message || '未知错误') + '</p></div></div></div>';

                    // 如果有错误记录
                    if (data.errors && data.errors.length > 0) {
                        html += '<div class="mt-3"><h6>错误详情：</h6><div class="table-responsive"><table class="table table-sm table-bordered"><thead class="table-light"><tr><th>序号</th><th>错误原因</th></tr></thead><tbody>';

                        $.each(data.errors, function(index, error) {
                            html += '<tr><td>' + (index + 1) + '</td><td>' + error + '</td></tr>';
                        });

                        html += '</tbody></table></div></div>';
                    }

                    // 添加建议
                    html += '<div class="alert alert-info mt-3"><i class="fas fa-lightbulb me-2"></i><strong>建议：</strong> 请检查您的导入文件格式是否正确，确保所有必填字段已填写，然后重新尝试导入。</div>';
                }

                $('#importResultContent').html(html);

                // 显示结果模态框
                importResultModal.show();

                // 确保模态框背景正确显示
                if ($('.modal-backdrop').length === 0) {
                    $('body').addClass('modal-open');
                    $('<div class="modal-backdrop fade show"></div>').appendTo('body');
                }
            },
            error: function(xhr, status, error) {
                console.error('导入失败:', error, xhr.status, xhr.responseText);

                // 检查是否是未授权错误（会话过期）
                if (xhr.status === 401) {
                    // 隐藏导入模态框
                    importModal.hide();

                    // 显示会话过期提示
                    alert('会话已过期，请重新登录');

                    // 延迟2秒后重定向到登录页面
                    setTimeout(function() {
                        window.location.href = '{% url "login" %}';
                    }, 2000);
                    return;
                }

                // 隐藏导入模态框
                importModal.hide();

                // 尝试解析错误信息
                let errorMsg = '导入失败';
                try {
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg += ': ' + xhr.responseJSON.message;
                    } else if (xhr.responseText) {
                        const response = JSON.parse(xhr.responseText);
                        if (response.message) {
                            errorMsg += ': ' + response.message;
                        }
                    }
                } catch (e) {
                    errorMsg += ': ' + error;
                }

                showToast(errorMsg, 'danger');

                // 显示错误信息
                var html = '<div class="alert alert-danger"><div class="d-flex"><div class="me-3"><i class="fas fa-times-circle fa-3x text-danger"></i></div><div><h5 class="alert-heading">导入失败</h5><p class="mb-0">' + errorMsg + '</p></div></div></div><div class="alert alert-info mt-3"><i class="fas fa-info-circle me-2"></i><strong>提示：</strong> 请确保服务器正常运行，并且您有权限执行此操作。</div>';

                $('#importResultContent').html(html);

                // 显示结果模态框
                importResultModal.show();

                // 确保模态框背景正确显示
                if ($('.modal-backdrop').length === 0) {
                    $('body').addClass('modal-open');
                    $('<div class="modal-backdrop fade show"></div>').appendTo('body');
                }
            },
            complete: function() {
                // 恢复按钮状态
                $('#importBtn').prop('disabled', false).html('<i class="fas fa-upload me-2"></i>开始导入');
            }
        });
    });

    // 拖放区域事件
    var dropZone = document.getElementById('dropZone');
    if (dropZone) {
        // 阻止默认拖放行为
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(function(eventName) {
            dropZone.addEventListener(eventName, function(e) {
                e.preventDefault();
                e.stopPropagation();
            }, false);
        });

        // 高亮拖放区域
        ['dragenter', 'dragover'].forEach(function(eventName) {
            dropZone.addEventListener(eventName, function() {
                dropZone.classList.add('highlight');
            }, false);
        });

        ['dragleave', 'drop'].forEach(function(eventName) {
            dropZone.addEventListener(eventName, function() {
                dropZone.classList.remove('highlight');
            }, false);
        });

        // 处理拖放文件
        dropZone.addEventListener('drop', function(e) {
            var dt = e.dataTransfer;
            var files = dt.files;

            if (files.length === 0) return;

            var file = files[0];

            // 检查文件类型
            var validExtensions = ['.xlsx', '.xls', '.csv'];
            var fileExtension = file.name.substring(file.name.lastIndexOf('.')).toLowerCase();

            if (!validExtensions.includes(fileExtension)) {
                alert('请选择有效的Excel或CSV文件');
                return;
            }

            // 设置文件输入
            var fileInput = document.getElementById('fileInput');

            // 创建一个新的 FileList 对象
            var dataTransfer = new DataTransfer();
            dataTransfer.items.add(file);
            fileInput.files = dataTransfer.files;

            // 显示文件信息
            $('#fileName').text(file.name);
            $('#fileInfo').removeClass('d-none');
            $('#importBtn').prop('disabled', false);
        }, false);
    }

    // 科室选择器自动提交
    $('#departmentSelect').change(function() {
        $('#filterForm').submit();
    });

    // 区域选择变化时提交表单
    $('select[name="area"]').change(function() {
        $('#filterForm').submit();
    });
});

// 显示Toast提示
function showToast(message, type = 'success') {
    // 检查是否已存在Toast容器
    let toastContainer = document.querySelector('.toast-container');

    // 如果不存在，创建一个
    if (!toastContainer) {
        toastContainer = document.createElement('div');
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }

    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toast = document.createElement('div');
    toast.className = `toast align-items-center text-white bg-${type} border-0`;
    toast.id = toastId;
    toast.setAttribute('role', 'alert');
    toast.setAttribute('aria-live', 'assertive');
    toast.setAttribute('aria-atomic', 'true');

    // 设置Toast内容
    toast.innerHTML = `
        <div class="d-flex">
            <div class="toast-body">
                ${message}
            </div>
            <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
    `;

    // 添加到容器
    toastContainer.appendChild(toast);

    // 初始化并显示Toast
    const bsToast = new bootstrap.Toast(toast, {
        animation: true,
        autohide: true,
        delay: 3000
    });
    bsToast.show();

    // 自动移除
    toast.addEventListener('hidden.bs.toast', function() {
        toast.remove();
    });
}

// 隐藏模态框
function hideModal(modalId) {
    console.log('隐藏模态框:', modalId);
    var modalElement = document.getElementById(modalId);
    if (!modalElement) return;

    var modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();

        // 手动移除遮罩层
        setTimeout(function() {
            var backdrops = document.querySelectorAll('.modal-backdrop');
            backdrops.forEach(function(backdrop) {
                backdrop.remove();
            });
            document.body.classList.remove('modal-open');
            document.body.style.overflow = '';
            document.body.style.paddingRight = '';
        }, 300);
    }
}

// 显示导入模态框
function showImportModal(departmentId) {
    console.log('显示导入模态框，部门ID:', departmentId);

    // 清空文件输入
    $('#fileInput').val('');
    $('#fileInfo').addClass('d-none');
    $('#importBtn').prop('disabled', true);

    // 设置部门ID
    $('#departmentIdInput').val(departmentId);

    // 获取科室信息并显示
    $.ajax({
        url: '{% url 'qrmanager:api_department_detail' 0 %}'.replace('0', departmentId),
        method: 'GET',
        headers: {
            'X-CSRFToken': $('[name=csrfmiddlewaretoken]').val()
        },
        success: function(data) {
            console.log('获取到科室信息:', data);

            // 设置科室名称
            $('#departmentNameBadge').text(data.name || '');

            // 设置科室编码和名称示例
            $('#departmentCodeExample, #departmentCodeExample2').text(data.code || '');
            $('#departmentNameExample, #departmentNameExample2').text(data.name || '');

            // 显示模态框
            var importModal = new bootstrap.Modal(document.getElementById('importModal'));
            importModal.show();

            // 确保模态框背景正确显示
            if ($('.modal-backdrop').length === 0) {
                $('body').addClass('modal-open');
                $('<div class="modal-backdrop fade show"></div>').appendTo('body');
            }
        },
        error: function(xhr, status, error) {
            console.error('获取科室信息失败:', error, xhr.status, xhr.responseText);

            // 检查是否是未授权错误（会话过期）
            if (xhr.status === 401) {
                // 显示会话过期提示
                alert('会话已过期，请重新登录');

                // 延迟2秒后重定向到登录页面
                setTimeout(function() {
                    window.location.href = '{% url "login" %}';
                }, 2000);
                return;
            }

            // 尝试解析其他错误信息
            let errorMsg = '获取科室信息失败，请刷新页面重试';
            try {
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMsg += ': ' + xhr.responseJSON.message;
                } else if (xhr.responseText) {
                    const response = JSON.parse(xhr.responseText);
                    if (response.message) {
                        errorMsg += ': ' + response.message;
                    }
                }
            } catch (e) {
                errorMsg += ': ' + error;
            }

            alert(errorMsg);
        }
    });
}

// 修复模态框问题的通用方法
function fixModalBackdropIssue() {
    console.log('修复模态框背景问题');

    // 检查是否有显示的模态框
    if ($('.modal.show').length > 0) {
        // 如果有显示的模态框但没有背景，添加背景
        if ($('.modal-backdrop').length === 0) {
            $('body').addClass('modal-open');
            $('<div class="modal-backdrop fade show"></div>').appendTo('body');
        }
    } else {
        // 如果没有显示的模态框但有背景，移除背景
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');
    }
}

// 监听模态框显示事件
$(document).on('shown.bs.modal', '.modal', function() {
    console.log('模态框显示事件触发');

    // 确保有模态框背景
    if ($('.modal-backdrop').length === 0) {
        $('body').addClass('modal-open');
        $('<div class="modal-backdrop fade show"></div>').appendTo('body');
    }
});

// 监听模态框隐藏事件
$(document).on('hidden.bs.modal', '.modal', function() {
    console.log('模态框隐藏事件触发');

    // 如果没有其他显示的模态框，移除背景
    if ($('.modal.show').length === 0) {
        $('.modal-backdrop').remove();
        $('body').removeClass('modal-open');
        $('body').css('overflow', '');
        $('body').css('padding-right', '');
    }
});

// 监听点击事件，检测是否需要修复灰色背景问题
$(document).on('click', function(e) {
    // 延迟执行，确保模态框状态已更新
    setTimeout(function() {
    // 如果所有模态框都已关闭但仍有模态框背景，则清理
    if ($('.modal.show').length === 0 && $('.modal-backdrop').length > 0) {
        fixModalBackdropIssue();
    }
        // 如果有显示的模态框但没有背景，添加背景
        else if ($('.modal.show').length > 0 && $('.modal-backdrop').length === 0) {
            fixModalBackdropIssue();
        }
    }, 100);
});
</script>
{% endblock %}