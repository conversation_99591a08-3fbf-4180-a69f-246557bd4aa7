# Generated by Django 4.2.7 on 2025-02-22 17:12

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0021_alter_printtemplate_qr_position_x_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='printtemplate',
            name='print_height',
            field=models.DecimalField(decimal_places=1, default=297.0, help_text='单位：毫米', max_digits=5, verbose_name='打印高度'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='print_width',
            field=models.DecimalField(decimal_places=1, default=210.0, help_text='单位：毫米', max_digits=5, verbose_name='打印宽度'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_position_x',
            field=models.IntegerField(default=105, help_text='单位：毫米，从左边开始计算', verbose_name='二维码X坐标'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_position_y',
            field=models.IntegerField(default=148, help_text='单位：毫米，从上边开始计算', verbose_name='二维码Y坐标'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_size',
            field=models.IntegerField(default=20, help_text='单位：毫米，建议20-30毫米，过大会遮挡背景，过小可能难以扫描', verbose_name='二维码尺寸'),
        ),
    ]
