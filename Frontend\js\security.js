/**
 * 安全模块 - 提供基本的安全功能
 * 此模块完全本地化，不依赖任何外部资源
 */

(function() {
    // 安全模块命名空间
    window.security = {
        /**
         * 初始化安全模块
         */
        init: function() {
            console.log('安全模块初始化完成');

            // 强制更新页面标题
            document.title = "自贡市第四人民医院服务评价系统";

            this.setupCSRFProtection();
            this.setupXSSProtection();
            this.setupClickjackingProtection();
            this.monitorFormSubmissions();
        },

        /**
         * 设置CSRF保护
         */
        setupCSRFProtection: function() {
            // 为所有AJAX请求添加CSRF保护
            let originalFetch = window.fetch;
            window.fetch = function(url, options) {
                options = options || {};
                options.credentials = 'same-origin';
                options.headers = options.headers || {};

                // 从cookie中获取CSRF令牌
                const csrfToken = document.cookie.split(';')
                    .find(c => c.trim().startsWith('csrftoken='));

                if (csrfToken) {
                    const token = csrfToken.split('=')[1];
                    options.headers['X-CSRFToken'] = token;
                }

                return originalFetch(url, options);
            };
        },

        /**
         * 设置XSS保护
         */
        setupXSSProtection: function() {
            // 净化输入函数
            window.sanitizeInput = function(input) {
                if (!input) return '';

                // 基本HTML转义
                return input.replace(/&/g, '&amp;')
                    .replace(/</g, '&lt;')
                    .replace(/>/g, '&gt;')
                    .replace(/"/g, '&quot;')
                    .replace(/'/g, '&#039;');
            };

            // 保护文本区域输入
            document.querySelectorAll('textarea').forEach(textarea => {
                textarea.addEventListener('change', function() {
                    // 限制输入长度，防止过度大的输入
                    if (this.value.length > 1000) {
                        this.value = this.value.substring(0, 1000);
                        console.warn('输入已被截断至1000字符');
                    }
                });
            });
        },

        /**
         * 设置点击劫持保护
         */
        setupClickjackingProtection: function() {
            // 检测iframe嵌套
            if (window !== window.top) {
                console.warn('检测到页面被嵌套在iframe中');
                // 可选：跳出iframe
                // window.top.location = window.location;
            }
        },

        /**
         * 监控表单提交
         */
        monitorFormSubmissions: function() {
            document.querySelectorAll('form').forEach(form => {
                form.addEventListener('submit', function(e) {
                    // 验证表单输入
                    const textInputs = this.querySelectorAll('input[type="text"], textarea');
                    let hasInvalidInput = false;

                    textInputs.forEach(input => {
                        // 检查潜在的危险输入模式
                        const value = input.value;
                        if (/<script|javascript:|onerror=|onclick=|alert\(|eval\(/.test(value)) {
                            console.error('检测到潜在危险输入:', value);
                            input.classList.add('error-input');
                            hasInvalidInput = true;
                        }
                    });

                    if (hasInvalidInput) {
                        e.preventDefault();
                        alert('请移除表单中的特殊字符和代码片段');
                        return false;
                    }
                });
            });
        }
    };
})();