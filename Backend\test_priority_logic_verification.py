#!/usr/bin/env python3
"""
验证医院评分优先级逻辑
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.http import HttpRequest
from django.contrib.auth.models import AnonymousUser
from qrmanager.api import PublicEvaluationSubmitAPI
from qrmanager.models import Evaluation, QRCode, Staff
from qrmanager.security import encrypt_qr_param

def create_mock_request(data):
    """创建模拟请求"""
    request = HttpRequest()
    request.method = 'POST'
    request.META['CONTENT_TYPE'] = 'application/json'
    request._body = json.dumps(data).encode('utf-8')
    request.user = AnonymousUser()
    return request

def test_priority_rules():
    """测试优先级规则"""
    print("🎯 验证医院评分优先级逻辑")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试二维码")
            return False
        
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 获取一个工作人员ID
        bed = qr_code.bed
        if bed and bed.department:
            staff = Staff.objects.filter(department=bed.department, is_active=True).first()
            if not staff:
                print("⚠️  没有找到测试用的工作人员")
                return False
        else:
            print("❌ 二维码没有关联床位或科室")
            return False
        
        # 测试用例：验证您提到的4个规则
        test_cases = [
            {
                "name": "医院2星 + 工作人员满意",
                "hospital_rating": 2,
                "staff_satisfied": True,
                "expected_sentiment": "negative",
                "rule": "医院评分优先，2星 → 负面"
            },
            {
                "name": "医院4星 + 工作人员不满意", 
                "hospital_rating": 4,
                "staff_satisfied": False,
                "expected_sentiment": "positive",
                "rule": "医院评分优先，4星 → 正面"
            },
            {
                "name": "无医院评分 + 工作人员满意",
                "hospital_rating": None,
                "staff_satisfied": True,
                "expected_sentiment": "positive",
                "rule": "无医院评分，工作人员满意 → 正面"
            },
            {
                "name": "无医院评分 + 工作人员不满意",
                "hospital_rating": None,
                "staff_satisfied": False,
                "expected_sentiment": "negative", 
                "rule": "无医院评分，工作人员不满意 → 负面"
            }
        ]
        
        print("测试规则:")
        print("-" * 60)
        
        results = []
        
        for i, case in enumerate(test_cases, 1):
            print(f"\n{i}. {case['name']}")
            print(f"   规则: {case['rule']}")
            
            # 构建测试数据
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"优先级测试 - {case['name']}",
                "hospital_number": f"PRIORITY_TEST_{i}",
                "phone_number": "13800138000",
                "staff_evaluations": [{
                    "staff_id": staff.id,
                    "is_satisfied": case['staff_satisfied']
                }]
            }
            
            # 如果有医院评分，添加到数据中
            if case['hospital_rating'] is not None:
                test_data['hospital_rating'] = case['hospital_rating']
            
            # 发送请求
            request = create_mock_request(test_data)
            api_view = PublicEvaluationSubmitAPI()
            response = api_view.post(request)
            
            if response.status_code == 200:
                response_data = json.loads(response.content.decode())
                evaluation_id = response_data.get('data', {}).get('evaluation_id')
                
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    actual_sentiment = evaluation.sentiment
                    
                    # 验证结果
                    if actual_sentiment == case['expected_sentiment']:
                        print(f"   ✅ 结果: {actual_sentiment} (符合预期)")
                        results.append(True)
                    else:
                        print(f"   ❌ 结果: {actual_sentiment} (期望: {case['expected_sentiment']})")
                        results.append(False)
                    
                    # 显示详细信息
                    print(f"   📊 详情:")
                    print(f"      - 医院评分: {evaluation.hospital_rating or '无'}")
                    print(f"      - 工作人员满意: {case['staff_satisfied']}")
                    print(f"      - 最终情感: {actual_sentiment}")
                    print(f"      - 整体满意度: {evaluation.is_satisfied}")
                    
                    # 清理测试数据
                    evaluation.delete()
                else:
                    print(f"   ❌ API没有返回evaluation_id")
                    results.append(False)
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_edge_cases():
    """测试边界情况"""
    print(f"\n🔍 测试边界情况")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 边界测试用例
        edge_cases = [
            {
                "name": "医院1星 (最低评分)",
                "hospital_rating": 1,
                "expected_sentiment": "negative"
            },
            {
                "name": "医院3星 (中性评分)",
                "hospital_rating": 3,
                "expected_sentiment": "neutral"
            },
            {
                "name": "医院5星 (最高评分)",
                "hospital_rating": 5,
                "expected_sentiment": "positive"
            },
            {
                "name": "无评分无工作人员",
                "hospital_rating": None,
                "expected_sentiment": "neutral"
            }
        ]
        
        results = []
        
        for i, case in enumerate(edge_cases, 1):
            print(f"\n{i}. {case['name']}")
            
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"边界测试 - {case['name']}",
                "hospital_number": f"EDGE_TEST_{i}",
                "phone_number": "13800138000",
                "staff_evaluations": []  # 无工作人员评价
            }
            
            if case['hospital_rating'] is not None:
                test_data['hospital_rating'] = case['hospital_rating']
            
            request = create_mock_request(test_data)
            api_view = PublicEvaluationSubmitAPI()
            response = api_view.post(request)
            
            if response.status_code == 200:
                response_data = json.loads(response.content.decode())
                evaluation_id = response_data.get('data', {}).get('evaluation_id')
                
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    actual_sentiment = evaluation.sentiment
                    
                    if actual_sentiment == case['expected_sentiment']:
                        print(f"   ✅ 结果: {actual_sentiment}")
                        results.append(True)
                    else:
                        print(f"   ❌ 结果: {actual_sentiment} (期望: {case['expected_sentiment']})")
                        results.append(False)
                    
                    evaluation.delete()
                else:
                    print(f"   ❌ 没有返回evaluation_id")
                    results.append(False)
            else:
                print(f"   ❌ API请求失败: {response.status_code}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 边界测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 医院评分优先级逻辑验证")
    print("=" * 80)
    print("验证规则:")
    print("1. 医院2星 + 工作人员满意 → 负面（医院评分优先）")
    print("2. 医院4星 + 工作人员不满意 → 正面（医院评分优先）")
    print("3. 无医院评分 + 工作人员满意 → 正面")
    print("4. 无医院评分 + 工作人员不满意 → 负面")
    print()
    
    tests = [
        ("优先级规则测试", test_priority_rules),
        ("边界情况测试", test_edge_cases),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}执行异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"\n📋 验证结果总结")
    print("=" * 80)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有验证通过！后端完全按照您的规则运行！")
        print("\n✅ 确认的规则:")
        print("  1. 医院评分优先于工作人员评价")
        print("  2. 1-2星医院评分 → 负面情感")
        print("  3. 3星医院评分 → 中性情感")
        print("  4. 4-5星医院评分 → 正面情感")
        print("  5. 无医院评分时，根据工作人员评价确定")
        print("  6. 无任何评价时，默认中性")
    else:
        print("⚠️  部分规则验证失败，需要检查")

if __name__ == "__main__":
    main()
