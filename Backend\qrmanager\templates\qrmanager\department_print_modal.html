{% load static %}
<!-- 科室批量打印模态窗口 -->
<div class="modal fade" id="departmentPrintModal" tabindex="-1" aria-labelledby="departmentPrintModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="departmentPrintModalLabel">按科室批量打印二维码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="row mb-3">
                    <div class="col-md-6">
                        <label for="printDepartmentSelect" class="form-label">选择科室</label>
                        <select class="form-select" id="printDepartmentSelect">
                            <option value="">-- 请选择科室 --</option>
                            {% for department in departments %}
                            <option value="{{ department.id }}">{{ department.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label for="outputFormatSelect" class="form-label">输出格式</label>
                        <select class="form-select" id="outputFormatSelect">
                            <option value="png">PNG</option>
                            <option value="pdf">PDF</option>
                        </select>
                    </div>
                </div>
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i>
                    <span>选择科室后，系统将生成该科室下所有床位的二维码打印文件。</span>
                </div>

                <!-- 添加进度条 -->
                <div class="progress mt-3 d-none" id="printProgressContainer">
                    <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" id="printProgressBar" style="width: 0%"></div>
                </div>
                <div class="d-flex justify-content-between mt-2 d-none" id="printProgressText">
                    <span id="progressMessage">正在生成文件，请稍候...</span>
                    <span id="progressPercentage">0%</span>
                </div>

                <!-- 取消按钮 -->
                <div class="text-center mt-3 mb-3 d-none" id="cancelTaskContainer">
                    <button type="button" id="cancelTaskBtn" class="btn btn-sm btn-outline-danger" onclick="cancelCurrentTask()">
                        <i class="fas fa-times-circle me-1"></i>取消任务
                    </button>
                    <div class="small text-muted mt-1" id="taskIdDisplay"></div>
                </div>

                <!-- 成功提示信息 -->
                <div class="alert alert-success mt-3 d-none" id="successMessage">
                    <i class="fas fa-check-circle me-2"></i>
                    <span>二维码文件已成功生成并下载！</span>
                </div>

            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" id="generateDepartmentBtn">
                    <i class="fas fa-cog me-1"></i>生成
                </button>
            </div>
        </div>
    </div>
</div>

<script src="{% static 'qrmanager/js/qrcode-unified.js' %}"></script>
<script>
// 科室批量打印相关JavaScript
function openDepartmentPrintModal() {
    // 显示模态窗口
    const modal = new bootstrap.Modal(document.getElementById('departmentPrintModal'));
    modal.show();

    // 重置进度条和相关元素
    document.getElementById('printProgressContainer').classList.add('d-none');
    document.getElementById('printProgressText').classList.add('d-none');
    document.getElementById('cancelTaskContainer').classList.add('d-none');
    document.getElementById('printProgressBar').style.width = '0%';
    document.getElementById('printProgressBar').classList.remove('bg-danger', 'bg-warning', 'bg-success', 'bg-info');
    document.getElementById('printProgressBar').classList.add('bg-primary');
    document.getElementById('progressPercentage').textContent = '0%';
    document.getElementById('progressMessage').textContent = '正在生成文件，请稍候...';
    document.getElementById('successMessage').classList.add('d-none');
}

// 取消当前任务的函数
function cancelCurrentTask() {
    console.log("开始取消任务，当前任务ID:", window.currentTaskId);

    // 尝试从会话存储中获取任务ID（作为备份）
    let taskId = window.currentTaskId;
    if (!taskId) {
        taskId = sessionStorage.getItem('currentTaskId');
        console.log("从会话存储中获取任务ID:", taskId);
    }

    if (!taskId) {
        console.log("没有任务ID，不执行取消操作");
        // 显示友好的提示
        document.getElementById('progressMessage').textContent = '无法取消任务：未找到任务ID';

        // 隐藏取消按钮
        document.getElementById('cancelTaskContainer').classList.add('d-none');

        // 显示警告提示
        const successMessage = document.getElementById('successMessage');
        successMessage.classList.remove('d-none');
        successMessage.classList.remove('alert-success');
        successMessage.classList.add('alert-warning');
        successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>无法取消任务：未找到任务ID</span>';
        return;
    }

    // 获取当前进度信息
    const currentProgress = document.getElementById('printProgressBar').style.width;
    const currentProgressValue = parseInt(currentProgress) || 0;
    const currentMessage = document.getElementById('progressMessage').textContent;

    console.log("发送取消请求，任务ID:", taskId, "当前进度:", currentProgressValue, "消息:", currentMessage);

    // 构建请求体，包含更多信息
    const formData = new FormData();
    formData.append('task_id', taskId);
    formData.append('current_progress', currentProgressValue);
    formData.append('current_message', currentMessage);
    // 获取科室ID
    const departmentSelect = document.getElementById('printDepartmentSelect');
    formData.append('department_id', departmentSelect ? departmentSelect.value : '');

    // 获取格式
    const formatSelect = document.getElementById('outputFormatSelect');
    formData.append('format', formatSelect ? formatSelect.value : '');
    formData.append('client_timestamp', new Date().toISOString());

    fetch('/admin/api/cancel-task/', {
        method: 'POST',
        headers: {
            'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
        },
        body: formData
    })
    .then(response => {
        console.log("取消请求响应状态:", response.status);
        return response.json();
    })
    .then(data => {
        console.log("取消任务结果:", data);
        if (data.status === 'success') {
            console.log("任务取消成功，清除任务ID");
            // 清除任务ID
            window.currentTaskId = null;
            sessionStorage.removeItem('currentTaskId');

            // 更新UI显示
            document.getElementById('progressMessage').textContent = '任务已取消';
            document.getElementById('printProgressBar').classList.remove('bg-primary', 'bg-info');
            document.getElementById('printProgressBar').classList.add('bg-warning');

            // 隐藏取消按钮
            document.getElementById('cancelTaskContainer').classList.add('d-none');

            // 显示取消提示
            const successMessage = document.getElementById('successMessage');
            successMessage.classList.remove('d-none');
            successMessage.classList.remove('alert-success');
            successMessage.classList.add('alert-warning');
            successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>任务已取消！</span>';
        } else {
            console.error("任务取消失败:", data.message);

            // 显示友好的错误提示
            document.getElementById('progressMessage').textContent = '取消任务失败：' + (data.message || '未知错误');

            // 显示错误提示
            const successMessage = document.getElementById('successMessage');
            successMessage.classList.remove('d-none');
            successMessage.classList.remove('alert-success');
            successMessage.classList.add('alert-danger');
            successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>取消任务失败：' + (data.message || '未知错误') + '</span>';
        }
    })
    .catch(error => {
        console.error('取消任务时出错:', error);

        // 显示友好的错误提示
        document.getElementById('progressMessage').textContent = '取消任务时出错';

        // 显示错误提示
        const successMessage = document.getElementById('successMessage');
        successMessage.classList.remove('d-none');
        successMessage.classList.remove('alert-success');
        successMessage.classList.add('alert-danger');
        successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>取消任务时出错：' + error.message + '</span>';
    });
}

// 当页面加载完成后初始化事件监听
document.addEventListener('DOMContentLoaded', function() {
    // 添加模态框关闭事件，自动取消任务（在开始隐藏时就触发）
    document.getElementById('departmentPrintModal').addEventListener('hide.bs.modal', function() {
        console.log("模态框开始隐藏，当前任务ID:", window.currentTaskId);
        if (window.currentTaskId) {
            // 立即发送取消请求，不等待异步操作
            const taskId = window.currentTaskId;
            console.log("立即发送同步取消请求，任务ID:", taskId);

            // 使用同步 XMLHttpRequest 发送取消请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/api/cancel-task/', false); // 第三个参数 false 表示同步请求
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.setRequestHeader('X-CSRFToken', document.querySelector('input[name="csrfmiddlewaretoken"]').value);
            xhr.send('task_id=' + taskId);

            console.log("同步取消请求已发送，响应状态:", xhr.status);

            // 清除任务ID（全局变量和会话存储）
            window.currentTaskId = null;
            sessionStorage.removeItem('currentTaskId');

            // 更新UI显示
            document.getElementById('progressMessage').textContent = '任务已取消';
            document.getElementById('printProgressBar').classList.remove('bg-primary', 'bg-info');
            document.getElementById('printProgressBar').classList.add('bg-warning');

            // 隐藏取消按钮
            document.getElementById('cancelTaskContainer').classList.add('d-none');

            // 显示取消提示
            const successMessage = document.getElementById('successMessage');
            successMessage.classList.remove('d-none');
            successMessage.classList.remove('alert-success');
            successMessage.classList.add('alert-warning');
            successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>任务已取消！</span>';
        }
    });

    // 添加页面卸载事件，自动取消任务（使用同步请求）
    window.addEventListener('beforeunload', function(event) {
        console.log("页面即将卸载，当前任务ID:", window.currentTaskId);
        if (window.currentTaskId) {
            // 使用同步 XMLHttpRequest 发送取消请求
            const xhr = new XMLHttpRequest();
            xhr.open('POST', '/admin/api/cancel-task/', false); // 第三个参数 false 表示同步请求
            xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
            xhr.setRequestHeader('X-CSRFToken', document.querySelector('input[name="csrfmiddlewaretoken"]').value);
            xhr.send('task_id=' + window.currentTaskId);

            // 清除任务ID（全局变量和会话存储）
            window.currentTaskId = null;
            sessionStorage.removeItem('currentTaskId');

            // 为了兼容性，返回一个提示消息
            event.returnValue = '有任务正在进行，确定要离开吗？';
            return event.returnValue;
        }
    });
    // 封装生成二维码的函数
    function sendGenerateRequest(departmentId, outputFormat, departmentName) {
        console.log("开始生成二维码，科室ID:", departmentId, "格式:", outputFormat);

        // 构建打印URL
        let printUrl = `/qrcodes/print/department/${departmentId}/zip/?format=${outputFormat}`;
        console.log("发送请求到URL:", printUrl);

        // 创建轮询进度的函数
        let progressInterval = setInterval(function() {
            // 构建请求URL，如果有任务ID则添加到查询参数中
            let url = '/admin/api/progress/';
            if (window.currentTaskId) {
                url += `?task_id=${window.currentTaskId}`;
            }

            fetch(url)
                .then(response => response.json())
                .then(data => {
                    console.log("进度更新:", data);
                    // 更新进度条
                    document.getElementById('printProgressBar').style.width = `${data.value}%`;
                    document.getElementById('progressPercentage').textContent = `${data.value}%`;
                    document.getElementById('progressMessage').textContent = data.message;

                    // 根据进度更新进度条颜色
                    if (data.value >= 100) {
                        document.getElementById('printProgressBar').classList.remove('bg-primary');
                        document.getElementById('printProgressBar').classList.add('bg-success');
                    } else if (data.value >= 75) {
                        document.getElementById('printProgressBar').classList.remove('bg-primary');
                        document.getElementById('printProgressBar').classList.add('bg-info');
                    }

                    // 检查任务是否被取消
                    if (data.cancelled) {
                        clearInterval(progressInterval);
                        // 清除任务ID（全局变量和会话存储）
                        window.currentTaskId = null;
                        sessionStorage.removeItem('currentTaskId');

                        document.getElementById('progressMessage').textContent = '任务已取消';
                        document.getElementById('printProgressBar').classList.remove('bg-primary', 'bg-info');
                        document.getElementById('printProgressBar').classList.add('bg-warning');

                        // 隐藏取消按钮
                        document.getElementById('cancelTaskContainer').classList.add('d-none');

                        // 显示取消提示
                        const successMessage = document.getElementById('successMessage');
                        successMessage.classList.remove('d-none');
                        successMessage.classList.remove('alert-success');
                        successMessage.classList.add('alert-warning');
                        successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>任务已取消！</span>';

                        return;
                    }

                    // 🔧 关键修复：进度100%时不立即停止轮询
                    // 继续轮询直到主下载请求完成，避免用户看到100%但还要等待下载
                    if (data.value === 100) {
                        // 更新进度显示，但不停止轮询
                        if (!downloadStarted) {
                            document.getElementById('progressMessage').textContent = '文件生成完成，准备下载...';
                        } else {
                            // 如果下载已开始，显示传输状态
                            document.getElementById('progressMessage').textContent = '文件传输中，请稍候...';
                        }
                        // 轮询将在主请求完成时停止（第393行）
                    }
                })
                .catch(error => {
                    console.error('获取进度时出错:', error);
                });
        }, 500); // 每500毫秒轮询一次

        // 🔧 改进：添加下载开始标记，让进度轮询知道下载已开始
        let downloadStarted = false;

        // 使用fetch API发送请求
        fetch(printUrl)
            .then(response => {
                // 🔧 标记下载已开始
                downloadStarted = true;
                if (!response.ok) {
                    clearInterval(progressInterval);
                    // 清除任务ID（全局变量和会话存储）
                    window.currentTaskId = null;
                    sessionStorage.removeItem('currentTaskId');
                    throw new Error('网络响应不正常');
                }

                // 检查响应的内容类型
                const contentType = response.headers.get('content-type');
                console.log("响应内容类型:", contentType);

                // 如果是JSON，可能是取消响应或错误响应
                if (contentType && contentType.includes('application/json')) {
                    return response.json().then(data => {
                        // 停止轮询
                        clearInterval(progressInterval);

                        // 清除任务ID（全局变量和会话存储）
                        window.currentTaskId = null;
                        sessionStorage.removeItem('currentTaskId');

                        console.log("收到JSON响应:", data);

                        // 检查是否是取消响应
                        if (data.status === 'cancelled') {
                            console.log("任务已被取消，不下载文件");

                            // 更新UI显示
                            document.getElementById('progressMessage').textContent = '任务已取消';
                            document.getElementById('printProgressBar').classList.remove('bg-primary', 'bg-info');
                            document.getElementById('printProgressBar').classList.add('bg-warning');

                            // 隐藏取消按钮
                            document.getElementById('cancelTaskContainer').classList.add('d-none');

                            // 显示取消提示
                            const successMessage = document.getElementById('successMessage');
                            successMessage.classList.remove('d-none');
                            successMessage.classList.remove('alert-success');
                            successMessage.classList.add('alert-warning');
                            successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>任务已取消！</span>';

                            // 抛出错误，中断后续处理
                            throw new Error('任务已取消');
                        } else {
                            // 其他JSON响应，可能是错误
                            throw new Error('服务器返回了非预期的JSON响应');
                        }
                    });
                }

                // 如果是二进制数据，继续下载
                return response.blob();
            })
            .then(blob => {
                // 停止轮询
                clearInterval(progressInterval);

                // 清除任务ID（全局变量和会话存储）
                window.currentTaskId = null;
                sessionStorage.removeItem('currentTaskId');

                // 确保进度条显示100%
                document.getElementById('printProgressBar').style.width = '100%';
                document.getElementById('progressPercentage').textContent = '100%';
                document.getElementById('progressMessage').textContent = '文件生成完成，准备下载...';
                document.getElementById('printProgressBar').classList.remove('bg-primary');
                document.getElementById('printProgressBar').classList.add('bg-success');

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `${departmentName}_qrcodes.zip`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);

                // 显示成功提示信息
                document.getElementById('successMessage').classList.remove('d-none');
                document.getElementById('successMessage').classList.remove('alert-danger');
                document.getElementById('successMessage').classList.add('alert-success');
                document.getElementById('successMessage').innerHTML = '<i class="fas fa-check-circle me-2"></i><span>二维码文件已成功生成并下载！</span>';

                // 3秒后隐藏进度条和取消按钮，但保留成功提示
                setTimeout(() => {
                    document.getElementById('printProgressContainer').classList.add('d-none');
                    document.getElementById('printProgressText').classList.add('d-none');
                    document.getElementById('cancelTaskContainer').classList.add('d-none');
                }, 3000);
            })
            .catch(error => {
                console.error('下载文件时出错:', error);

                // 停止轮询
                clearInterval(progressInterval);

                // 清除任务ID（全局变量和会话存储）
                window.currentTaskId = null;
                sessionStorage.removeItem('currentTaskId');

                document.getElementById('progressMessage').textContent = '生成文件时出错，请重试';
                document.getElementById('progressPercentage').textContent = '0%';
                document.getElementById('printProgressBar').classList.remove('bg-primary');
                document.getElementById('printProgressBar').classList.add('bg-danger');

                // 隐藏取消按钮
                document.getElementById('cancelTaskContainer').classList.add('d-none');

                // 显示错误提示
                const successMessage = document.getElementById('successMessage');
                successMessage.classList.remove('d-none');
                successMessage.classList.remove('alert-success');
                successMessage.classList.add('alert-danger');
                successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>生成文件失败，请重试！</span>';
            });
    }

    // 生成二维码按钮点击事件
    document.getElementById('generateDepartmentBtn').addEventListener('click', function() {
        const departmentId = document.getElementById('printDepartmentSelect').value;

        if (!departmentId) {
            alert('请选择科室');
            return;
        }

        const outputFormat = document.getElementById('outputFormatSelect').value;

        // 获取科室名称（用于文件名）
        const departmentSelect = document.getElementById('printDepartmentSelect');
        const departmentName = departmentSelect.options[departmentSelect.selectedIndex].text;

        // 显示进度条和相关元素
        document.getElementById('printProgressContainer').classList.remove('d-none');
        document.getElementById('printProgressText').classList.remove('d-none');
        document.getElementById('cancelTaskContainer').classList.remove('d-none');
        document.getElementById('successMessage').classList.add('d-none');

        // 设置初始进度
        document.getElementById('printProgressBar').style.width = '0%';
        document.getElementById('progressPercentage').textContent = '0%';
        document.getElementById('progressMessage').textContent = '正在准备数据...';

        // 先重置进度信息，获取任务ID
        fetch('/admin/api/reset-progress/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded',
                'X-CSRFToken': document.querySelector('input[name="csrfmiddlewaretoken"]').value
            }
        })
            .then(response => response.json())
            .then(data => {
                console.log("重置进度信息成功，任务ID:", data.task_id);
                // 保存任务ID到全局变量和会话存储中
                window.currentTaskId = data.task_id;
                // 同时保存到会话存储中作为备份
                if (data.task_id) {
                    sessionStorage.setItem('currentTaskId', data.task_id);
                    console.log("任务ID已保存到会话存储中:", data.task_id);

                    // 显示任务ID（仅显示前8位，方便用户识别）
                    const shortTaskId = data.task_id.substring(0, 8) + '...';
                    document.getElementById('taskIdDisplay').textContent = '任务ID: ' + shortTaskId;

                    // 重要：在获取任务ID后，再发送生成二维码的请求
                    // 添加短暂延迟，确保用户能看到进度条初始状态
                    setTimeout(() => {
                        sendGenerateRequest(departmentId, outputFormat, departmentName);
                    }, 300);
                } else {
                    console.error("未获取到任务ID，无法继续生成二维码");
                    document.getElementById('progressMessage').textContent = '无法获取任务ID，请重试';
                    document.getElementById('printProgressBar').classList.remove('bg-primary');
                    document.getElementById('printProgressBar').classList.add('bg-danger');

                    // 显示错误提示
                    const successMessage = document.getElementById('successMessage');
                    successMessage.classList.remove('d-none');
                    successMessage.classList.remove('alert-success');
                    successMessage.classList.add('alert-danger');
                    successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>无法获取任务ID，请重试！</span>';
                }
            })
            .catch(error => {
                console.error('重置进度信息失败:', error);
                document.getElementById('progressMessage').textContent = '重置进度信息失败，请重试';
                document.getElementById('printProgressBar').classList.remove('bg-primary');
                document.getElementById('printProgressBar').classList.add('bg-danger');

                // 显示错误提示
                const successMessage = document.getElementById('successMessage');
                successMessage.classList.remove('d-none');
                successMessage.classList.remove('alert-success');
                successMessage.classList.add('alert-danger');
                successMessage.innerHTML = '<i class="fas fa-exclamation-circle me-2"></i><span>重置进度信息失败，请重试！</span>';
            });
    });
});
</script>