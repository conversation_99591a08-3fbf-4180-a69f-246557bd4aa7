# Generated by Django 4.2.7 on 2025-03-16 13:20

from django.db import migrations, models
import django.db.models.deletion


def create_default_staff_types(apps, schema_editor):
    """创建默认的工作人员类型"""
    StaffType = apps.get_model('qrmanager', 'StaffType')
    
    # 创建默认类型
    default_types = [
        {'code': 'doctor', 'name': '医生', 'display_order': 1},
        {'code': 'nurse', 'name': '护士', 'display_order': 2},
        {'code': 'technician', 'name': '技师', 'display_order': 3},
        {'code': 'other', 'name': '其他', 'display_order': 99}
    ]
    
    for type_data in default_types:
        StaffType.objects.get_or_create(
            code=type_data['code'],
            defaults={
                'name': type_data['name'],
                'display_order': type_data['display_order'],
                'is_active': True
            }
        )


def update_staff_references(apps, schema_editor):
    """更新工作人员记录的外键引用"""
    try:
        Staff = apps.get_model('qrmanager', 'Staff')
        StaffType = apps.get_model('qrmanager', 'StaffType')
        DictionaryItem = apps.get_model('qrmanager', 'DictionaryItem')
        
        # 创建字典项到StaffType的映射
        type_mapping = {}
        
        # 获取所有字典项
        dict_items = DictionaryItem.objects.filter(dictionary__code='staff_type')
        
        for item in dict_items:
            # 尝试找到匹配的StaffType
            staff_type = None
            if item.code == 'doctor':
                staff_type = StaffType.objects.filter(code='doctor').first()
            elif item.code == 'nurse':
                staff_type = StaffType.objects.filter(code='nurse').first()
            elif item.code == 'technician':
                staff_type = StaffType.objects.filter(code='technician').first()
            else:
                staff_type = StaffType.objects.filter(code='other').first()
            
            if staff_type:
                type_mapping[item.id] = staff_type
        
        # 获取默认工作人员类型
        default_type = StaffType.objects.filter(code='other').first()
        
        # 更新所有工作人员记录
        for staff in Staff.objects.all():
            if hasattr(staff, 'staff_type_id') and staff.staff_type_id:
                if staff.staff_type_id in type_mapping:
                    staff.new_staff_type = type_mapping[staff.staff_type_id]
                else:
                    staff.new_staff_type = default_type
                staff.save()
    except Exception as e:
        # 如果出现任何错误，只记录错误但不中断迁移
        print(f"更新工作人员引用时出错: {str(e)}")


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0031_add_apikey_missing_fields'),
    ]

    operations = [
        # 第一步：创建StaffType模型
        migrations.CreateModel(
            name='StaffType',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='类型编码')),
                ('name', models.CharField(max_length=100, verbose_name='类型名称')),
                ('icon', models.CharField(blank=True, help_text='图标CSS类名，例如：fa-user-md', max_length=50, verbose_name='图标')),
                ('display_order', models.IntegerField(default=0, verbose_name='显示顺序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '工作人员类型',
                'verbose_name_plural': '工作人员类型',
                'ordering': ['display_order', 'code'],
            },
        ),
        
        # 第二步：创建默认的工作人员类型
        migrations.RunPython(create_default_staff_types),
        
        # 第三步：添加新字段，允许为空
        migrations.AddField(
            model_name='staff',
            name='new_staff_type',
            field=models.ForeignKey(null=True, blank=True, on_delete=django.db.models.deletion.SET_NULL, related_name='new_staffs', to='qrmanager.stafftype', verbose_name='新人员类型'),
        ),
        
        # 第四步：尝试更新工作人员记录（如果可能）
        migrations.RunPython(update_staff_references),
    ]
