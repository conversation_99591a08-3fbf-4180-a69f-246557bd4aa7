/**
 * API模块 - 处理与服务器的通信
 * 所有API调用都通过此模块进行，确保安全性和一致性
 * 2025-03-24 重构版
 *
 * 注意：此模块已被apiService.js替代，保留此文件仅为向后兼容
 */

(function() {
    // 设置API基础URL - 使用本地地址
    const API_BASE_URL = 'https://zg120pj.cn';  // 使用域名和HTTPS

    // 定义API端点 - 使用Nginx配置的外部路径
    const API_ENDPOINTS = {
        VERIFY_QRCODE: '/service/resources/',
        SUBMIT_EVALUATION: '/service/evaluation/'
    };

    // 在控制台打印当前使用的API基础URL（仅在开发环境）
    if (!window.location.search.includes('debug=true') && localStorage.getItem('debugMode') !== 'true') {
        // 生产环境不输出日志
    } else {
        console.log(`当前API基础URL: ${API_BASE_URL}`);
        console.log('API模块已被apiService替代，此模块仅为向后兼容');
    }

    // 应用数据存储
    const appData = {
        clientIP: '127.0.0.1'
    };

    // 全局OPTIONS请求锁 - 确保同一URL的OPTIONS请求只发送一次
    window._optionsRequestLocks = window._optionsRequestLocks || {};

    // 请求管理器
    const RequestManager = {
        // 记录进行中的请求
        pendingRequests: {},

        // 请求节流计时器
        throttleTimers: {},

        // 缓存的响应
        responseCache: {},

        /**
         * 生成请求标识符
         * @param {string} url - 请求URL
         * @param {object} data - 请求数据
         * @returns {string} - 请求标识符
         */
        getRequestId: function(url, data) {
            return `${url}:${JSON.stringify(data)}`;
        },

        /**
         * 检查是否有相同请求正在进行
         * @param {string} url - 请求URL
         * @param {object} data - 请求数据
         * @returns {boolean} - 是否有相同请求正在进行
         */
        isRequestPending: function(url, data) {
            const requestId = this.getRequestId(url, data);
            return !!this.pendingRequests[requestId];
        },

        /**
         * 获取缓存的响应
         * @param {string} url - 请求URL
         * @param {object} data - 请求数据
         * @returns {object|null} - 缓存的响应或null
         */
        getCachedResponse: function(url, data) {
            const requestId = this.getRequestId(url, data);
            return this.responseCache[requestId] || null;
        },

        /**
         * 缓存响应
         * @param {string} url - 请求URL
         * @param {object} data - 请求数据
         * @param {object} response - 响应对象
         */
        cacheResponse: function(url, data, response) {
            const requestId = this.getRequestId(url, data);
            this.responseCache[requestId] = response;

            // 设置缓存过期（5分钟后）
            setTimeout(() => {
                delete this.responseCache[requestId];
            }, 5 * 60 * 1000);
        },

        /**
         * 执行请求
         * @param {string} url - 请求URL
         * @param {object} options - 请求选项
         * @param {object} data - 请求数据
         * @returns {Promise} - 请求Promise
         */
        executeRequest: async function(url, options, data) {
            const requestId = this.getRequestId(url, data);

            // 标记请求为进行中
            this.pendingRequests[requestId] = true;

            try {
                // 使用日志模块记录请求（如果已加载）
                if (window.logger && typeof window.logger.debug === 'function') {
                    window.logger.debug(`执行请求: ${url}`);
                } else {
                    console.log(`[RequestManager] 执行请求: ${url}`);
                }
                const response = await fetch(url, options);

                // 读取响应文本
                const responseText = await response.text();

                // 解析JSON
                let responseData;
                try {
                    responseData = JSON.parse(responseText);
                } catch (e) {
                    // 使用日志模块记录错误（如果已加载）
                    if (window.logger && typeof window.logger.error === 'function') {
                        window.logger.error('JSON解析失败:', e);
                    } else {
                        console.error('JSON解析失败:', e);
                    }
                    throw new Error('响应解析失败: ' + responseText.substring(0, 100));
                }

                // 缓存响应
                this.cacheResponse(url, data, responseData);

                return responseData;
            } finally {
                // 请求完成，移除标记
                delete this.pendingRequests[requestId];
            }
        },

        /**
         * 发送请求（带节流和缓存）
         * @param {string} url - 请求URL
         * @param {object} options - 请求选项
         * @param {object} data - 请求数据
         * @param {number} throttleTime - 节流时间（毫秒）
         * @returns {Promise} - 请求Promise
         */
        sendRequest: function(url, options, data, throttleTime = 1000) {
            const requestId = this.getRequestId(url, data);

            // 检查是否有相同请求正在进行
            if (this.isRequestPending(requestId)) {
                console.log(`[RequestManager] 请求已在进行中: ${url}`);
                const cachedResponse = this.getCachedResponse(url, data);
                if (cachedResponse) {
                    console.log(`[RequestManager] 返回缓存响应`);
                    return Promise.resolve(cachedResponse);
                }

                // 等待请求完成
                return new Promise((resolve) => {
                    const checkInterval = setInterval(() => {
                        if (!this.isRequestPending(requestId)) {
                            clearInterval(checkInterval);
                            const response = this.getCachedResponse(url, data);
                            resolve(response);
                        }
                    }, 100);
                });
            }

            // 检查是否需要节流
            if (this.throttleTimers[requestId]) {
                console.log(`[RequestManager] 请求被节流: ${url}`);
                const cachedResponse = this.getCachedResponse(url, data);
                if (cachedResponse) {
                    return Promise.resolve(cachedResponse);
                }
            }

            // 设置节流
            this.throttleTimers[requestId] = true;
            setTimeout(() => {
                delete this.throttleTimers[requestId];
            }, throttleTime);

            // 执行请求
            return this.executeRequest(url, options, data);
        }
    };

    // 初始化获取客户端IP
    try {
        if (window.location.hostname && window.location.hostname !== 'localhost') {
            appData.clientIP = window.location.hostname;
        } else if (window.location.host) {
            appData.clientIP = window.location.host.split(':')[0];
        }
    } catch (e) {
        console.error('获取客户端IP失败:', e);
    }

    // API模块
    window.api = {
        // 请求管理器引用
        requestManager: RequestManager,

        /**
         * 验证二维码
         * @param {string} qrParam - 二维码参数
         * @returns {Promise} - 返回验证结果的Promise
         */
        verifyQRCode: async function(qrParam) {
            if (!qrParam) {
                throw new Error('未提供二维码参数');
            }

            // 检查是否已经提交过评价，如果是，不执行验证流程
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，跳过验证流程');
                return null;
            }

            console.log('开始验证二维码:', qrParam);

            // 立即保存QR参数到appData
            window.appData = window.appData || {};
            window.appData.qrParam = qrParam;
            window.appData.qrCode = qrParam;
            console.log('已将二维码参数保存到appData中');

            // 设置请求超时
            const TIMEOUT_MS = 10000; // 10秒超时

            // 创建超时Promise
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => {
                    reject(new Error('请求超时，请检查网络连接'));
                }, TIMEOUT_MS);
            });

            try {
                // 构建API URL - 使用集中定义的端点
                const url = `${API_BASE_URL}${API_ENDPOINTS.VERIFY_QRCODE}`;

                // 构建请求数据
                const requestData = {
                    qr_param: qrParam,
                    client_ip: appData.clientIP
                };

                console.log(`发送验证请求到: ${url}`);

                // 构建请求选项
                const options = {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    mode: 'cors',
                    cache: 'no-cache',
                    credentials: 'omit',
                    body: JSON.stringify(requestData)
                };

                // 添加认证头
                if (window.appData) {
                    const encryptedString = window.appData.encryptedString;
                    const tempToken = window.appData.tempToken;

                    if (encryptedString) {
                        console.log('添加加密字符串到请求头');
                        options.headers['Authorization'] = 'EncryptedString ' + encryptedString;
                        options.headers['X-Auth-String'] = encryptedString;
                    } else if (tempToken) {
                        console.log('添加临时令牌到请求头');
                        options.headers['Authorization'] = 'Bearer ' + tempToken;
                    }
                }

                console.log('请求配置:', options);

                // 使用标准的fetch处理，避免RequestManager可能引入的延迟
                const requestPromise = fetch(url, options)
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`服务器返回错误状态: ${response.status} ${response.statusText}`);
                        }
                        return response.text();
                    })
                    .then(text => {
                        try {
                            return JSON.parse(text);
                        } catch (e) {
                            console.error('JSON解析失败:', e);
                            throw new Error('响应解析失败: ' + text.substring(0, 100));
                        }
                    });

                // 竞态执行，有任何一个Promise完成就继续
                const data = await Promise.race([requestPromise, timeoutPromise]);

                // 保存原始响应用于调试
                window.appData.lastApiResponse = data;

                // 检查所有可能的认证字段
                const authFields = [
                    'encrypted_string', 'encryptedString', 'encrypt_string',
                    'auth_string', 'authString', 'token', 'temp_token', 'tempToken',
                    'auth_token', 'authToken'
                ];

                // 检查顶层数据
                let foundAuth = false;
                for (const field of authFields) {
                    if (data[field]) {
                        window.appData.encryptedString = data[field];
                        console.log(`从API响应顶层保存加密字符串 (${field})`);
                        foundAuth = true;
                        break;
                    }
                }

                // 如果没找到顶层认证字段，检查data中是否包含
                if (!foundAuth && data.data) {
                    for (const field of authFields) {
                        if (data.data[field]) {
                            window.appData.encryptedString = data.data[field];
                            console.log(`从API响应data对象保存加密字符串 (${field})`);
                            foundAuth = true;
                            break;
                        }
                    }
                }

                // 单独保存临时令牌作为备用
                if (data.temp_token) {
                    window.appData.tempToken = data.temp_token;
                    console.log('保存临时令牌作为备用');
                } else if (data.data && data.data.temp_token) {
                    window.appData.tempToken = data.data.temp_token;
                    console.log('从data对象保存临时令牌作为备用');
                }

                if (!foundAuth) {
                    console.warn('警告: API响应中未找到加密字符串');
                }

                // 验证返回数据
                if (data.status === 'success' && data.data) {
                    // 标记验证成功
                    window.appData.verificationSuccess = true;

                    // 更新应用数据
                    this.updateAppData(data.data);

                    // 初始化其他模块
                    if (window.staffModule) {
                        console.log('开始初始化staffModule...');

                        // 检查DOM元素是否就绪
                        if (typeof window.staffModule.isDomReady === 'function') {
                            const isDomReady = window.staffModule.isDomReady();
                            if (!isDomReady) {
                                console.warn('DOM元素尚未就绪，使用延迟初始化staffModule');

                                // 延迟100ms后重试初始化
                                setTimeout(() => {
                                    if (typeof window.staffModule.init === 'function') {
                                        window.staffModule.init(data.data.staff, data.data.staff_types);
                                    }
                                }, 100);
                            } else {
                                // DOM已就绪，直接初始化
                                if (typeof window.staffModule.init === 'function') {
                                    window.staffModule.init(data.data.staff, data.data.staff_types);
                                }
                            }
                        } else {
                            // 旧版staffModule没有isDomReady方法，直接初始化
                            if (typeof window.staffModule.init === 'function') {
                                window.staffModule.init(data.data.staff, data.data.staff_types);
                            }
                        }
                    } else {
                        console.warn('staffModule未找到，无法初始化');

                        // 保存数据到全局，以便后续初始化
                        window.appData.pendingStaffData = {
                            staff: data.data.staff,
                            staff_types: data.data.staff_types
                        };
                    }

                    return data;
                } else {
                    // 标记验证失败
                    window.appData.verificationSuccess = false;
                    throw new Error(data.message || '验证失败');
                }
            } catch (error) {
                // 使用日志模块记录错误
                if (window.logger && typeof window.logger.error === 'function') {
                    window.logger.error('请求失败:', error);
                } else {
                    console.error('请求失败:', error);
                }

                // 保存错误信息用于调试
                window.appData.lastApiError = {
                    message: error.message,
                    stack: error.stack,
                    timestamp: new Date().toISOString()
                };

                // 标记验证失败
                window.appData.verificationSuccess = false;

                // 使用集中式错误处理
                if (window.logger && typeof window.logger.handleError === 'function') {
                    // 在生产环境中显示用户友好的错误消息
                    const isProduction = !window.location.search.includes('debug=true') &&
                                        localStorage.getItem('debugMode') !== 'true';

                    const userFriendlyMessage = isProduction ?
                        '验证失败，请重新扫描二维码' :
                        `验证失败: ${error.message}`;

                    window.logger.handleError(error, userFriendlyMessage);
                }

                throw error;
            }
        },

        /**
         * 提交评价
         * @param {object} evaluationData - 评价数据
         * @param {boolean} noRedirect - 是否禁止自动跳转
         * @returns {Promise} - 返回提交结果的Promise
         */
        submitEvaluation: async function(evaluationData, noRedirect = false) {
            if (!evaluationData) {
                throw new Error('未提供评价数据');
            }

            // 检查是否已经提交过评价，如果是，不执行提交操作
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，不执行提交操作');
                return { status: 'success', message: '您已经提交过评价' };
            }

            // 显示加载指示器
            if (window.showLoading) {
                window.showLoading();
            }

            try {
                // 验证qrParam参数
                if (!evaluationData.qr_param) {
                    console.warn('evaluationData中缺少qr_param参数');
                    // 尝试从appData获取
                    if (window.appData && window.appData.qrParam) {
                        console.log('从appData获取qrParam:', window.appData.qrParam);
                        evaluationData.qr_param = window.appData.qrParam;
                    } else if (window.appData && window.appData.qrCode) {
                        console.log('从appData获取qrCode:', window.appData.qrCode);
                        evaluationData.qr_param = window.appData.qrCode;
                    } else {
                        // 最后尝试从URL中提取
                        const extractedQr = this.extractQRCodeFromURL();
                        if (extractedQr) {
                            console.log('从URL中提取qrParam:', extractedQr);
                            evaluationData.qr_param = extractedQr;
                        } else {
                            throw new Error('无法获取二维码参数，请重新扫描二维码');
                        }
                    }
                }

                // 确保评价数据格式正确
                if (!evaluationData.staff_evaluations || !Array.isArray(evaluationData.staff_evaluations)) {
                    throw new Error('评价数据格式错误: 缺少staff_evaluations数组');
                }

                if (!evaluationData.comment) {
                    throw new Error('评价数据格式错误: 缺少评价内容');
                }

                // 使用集中定义的API端点
                const FULL_API_URL = `${API_BASE_URL}${API_ENDPOINTS.SUBMIT_EVALUATION}`;

                console.log(`准备调用评价提交接口: ${FULL_API_URL}`);
                console.log('评价提交数据:', evaluationData);

                // 使用RequestManager发送请求，而不是直接使用fetch
                // 这样可以利用RequestManager的缓存、节流等功能
                const data = await this.requestManager.executeRequest(
                    FULL_API_URL,
                    {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(evaluationData)
                    },
                    evaluationData
                );

                // 保存原始响应用于调试
                window.appData.lastEvaluationResponse = data;

                // 验证返回数据
                if (data.status === 'success' || data.code === 0 || data.code === 200) {
                    console.log('评价提交成功:', data);

                    // 记录评价状态到会话存储
                    sessionStorage.setItem('evaluationSubmitted', 'true');
                    sessionStorage.setItem('evaluationSubmittedTime', new Date().toISOString());
                    sessionStorage.setItem('qrParam', evaluationData.qr_param || '');
                    console.log('已记录评价状态到会话存储');

                    // 如果需要跳转且没有禁止
                    if (!noRedirect) {
                        // 如果有重定向URL，跳转到该URL
                        if (data.data && data.data.redirect_url) {
                            window.location.href = data.data.redirect_url;
                        } else {
                            window.location.href = '/thank-you.html';
                        }
                    }

                    return data;
                } else {
                    throw new Error(data.message || data.msg || '评价提交失败');
                }

            } catch (error) {
                // 使用日志模块记录错误
                if (window.logger && typeof window.logger.error === 'function') {
                    window.logger.error('评价提交过程出错:', error);
                } else {
                    console.error('评价提交过程出错:', error);
                }

                // 保存错误信息用于调试
                window.appData.lastEvaluationError = {
                    message: error.message,
                    stack: error.stack,
                    timestamp: new Date().toISOString()
                };

                // 使用集中式错误处理
                if (window.logger && typeof window.logger.handleError === 'function') {
                    // 在生产环境中显示用户友好的错误消息
                    const isProduction = !window.location.search.includes('debug=true') &&
                                        localStorage.getItem('debugMode') !== 'true';

                    const userFriendlyMessage = isProduction ?
                        '提交失败，请稍后重试' :
                        `提交失败: ${error.message}`;

                    window.logger.handleError(error, userFriendlyMessage);
                }

                throw error;
            } finally {
                // 隐藏加载指示器
                if (window.hideLoading) {
                    window.hideLoading();
                }
            }
        },

        /**
         * 测试与API服务器的连接
         * @returns {Promise<boolean>} - 连接是否成功
         */
        testConnection: async function() {
            // 检查是否已经提交过评价，如果是，不执行连接测试
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，跳过连接测试');
                return true;
            }

            // 如果二维码验证已成功，直接返回true
            if (window.appData && window.appData.verificationSuccess) {
                console.log('二维码验证已成功，跳过连接测试');
                return true;
            }

            // 如果已经测试过连接，直接返回缓存结果
            if (window._appInitFlags && window._appInitFlags.apiConnectionTested) {
                console.log('API连接已测试过，返回缓存结果');
                return window.appData.apiConnected === true;
            }

            // 使用验证端点进行连接测试 - 使用集中定义的端点
            const url = `${API_BASE_URL}${API_ENDPOINTS.VERIFY_QRCODE}`;

            // 检查全局OPTIONS锁，避免重复的OPTIONS请求
            if (window._optionsRequestLocks && window._optionsRequestLocks[url]) {
                console.log(`OPTIONS请求锁已存在: ${url}，跳过重复请求`);
                return true;
            }

            // 设置全局OPTIONS锁
            window._optionsRequestLocks = window._optionsRequestLocks || {};
            window._optionsRequestLocks[url] = true;

            // 重试配置
            const maxRetries = 2;
            const retryDelay = 1000; // 1秒

            for (let attempt = 0; attempt <= maxRetries; attempt++) {
                try {
                    // 标记已测试，避免重复测试
                    if (window._appInitFlags) {
                        window._appInitFlags.apiConnectionTested = true;
                    }

                    console.log(`执行API连接测试 (尝试 ${attempt + 1}/${maxRetries + 1}): ${url}`);

                    // 构建请求选项，缩短超时时间
                    const options = {
                        method: 'OPTIONS',
                        headers: {
                            'Accept': 'application/json'
                        },
                        mode: 'cors',
                        cache: 'no-cache',
                        signal: AbortSignal.timeout(3000) // 3秒超时
                    };

                    // 直接使用fetch而不是RequestManager，避免复杂的请求管理逻辑
                    const response = await fetch(url, options);

                    // 设置连接状态
                    const isConnected = response.ok || response.status === 204;
                    window.appData.apiConnected = isConnected;

                    if (isConnected) {
                        console.log('API连接成功');
                        return true;
                    } else {
                        console.warn(`API连接失败，状态码: ${response.status} (尝试 ${attempt + 1})`);
                        if (attempt === maxRetries) {
                            // 最后一次尝试失败，但不影响功能
                            console.log('API连接测试最终失败，但不影响功能使用');
                            return true; // 返回true避免显示错误状态
                        }
                    }
                } catch (error) {
                    console.warn(`API连接测试失败 (尝试 ${attempt + 1}):`, error.message);
                    if (attempt === maxRetries) {
                        console.log('API连接测试最终失败，但不影响功能使用');
                        window.appData.apiConnected = false;
                        return true; // 返回true避免显示错误状态
                    }
                }

                // 如果不是最后一次尝试，等待后重试
                if (attempt < maxRetries) {
                    await new Promise(resolve => setTimeout(resolve, retryDelay));
                }
            }

            // 释放锁
            setTimeout(() => {
                delete window._optionsRequestLocks[url];
            }, 5000);

            return true; // 默认返回true避免显示错误状态
        },

        /**
         * 更新应用数据
         * @param {object} data - API返回的数据
         */
        updateAppData: function(data) {
            try {
                console.log('更新appData数据', data);

                window.appData = window.appData || {};

                // 检查API返回的数据
                if (data) {
                    // 保存加密字符串 - 按各种可能的字段名称查找
                    if (data.encrypted_string) {
                        window.appData.encryptedString = data.encrypted_string;
                        console.log('成功保存加密字符串 (encrypted_string)');
                    } else if (data.encrypt_string) {
                        window.appData.encryptedString = data.encrypt_string;
                        console.log('成功保存加密字符串 (encrypt_string)');
                    } else if (data.auth_string) {
                        window.appData.encryptedString = data.auth_string;
                        console.log('成功保存加密字符串 (auth_string)');
                    } else if (data.token) {
                        window.appData.encryptedString = data.token;
                        console.log('成功保存加密字符串 (token)');
                    } else if (data.temp_token) {
                        // 兼容旧版，如果没有加密字符串但有临时令牌
                        window.appData.tempToken = data.temp_token;
                        console.log('成功保存临时令牌 (temp_token)');
                    }

                    // 保存科室信息
                    if (data.department) {
                        window.appData.department = data.department;
                        console.log('成功保存科室信息', data.department);
                    }

                    // 保存床位信息
                    if (data.bed) {
                        window.appData.bed = data.bed;
                        console.log('成功保存床位信息', data.bed);
                    }

                    // 保存工作人员类型
                    if (data.staff_types && Array.isArray(data.staff_types)) {
                        window.appData.staffTypes = data.staff_types;
                        console.log('成功保存工作人员类型', data.staff_types.length);

                        // 打印每种类型的详细信息
                        data.staff_types.forEach(type => {
                            console.log(`工作人员类型详情: ID=${type.id}, 名称=${type.name}, 代码=${type.code}`);
                        });
                    }

                    // 保存工作人员列表
                    if (data.staff && Array.isArray(data.staff)) {
                        window.appData.staffList = data.staff;
                        console.log('成功保存工作人员列表', data.staff.length);

                        // 打印每个工作人员的类型ID
                        data.staff.forEach(staff => {
                            console.log(`工作人员详情: ID=${staff.id}, 名称=${staff.name}, 类型ID=${staff.type_id || staff.staff_type}`);
                        });
                    }

                    // 立即尝试初始化staffModule
                    if (window.staffModule) {
                        if (window.staffModule.isInitialized) {
                            console.log('强制重置staffModule状态后重新初始化');
                            window.staffModule.isInitialized = false;
                            window.staffModule.isInitializing = false;
                        }

                        console.log('API验证成功后立即初始化staffModule');
                        window.staffModule.init(window.appData.staffList || [], window.appData.staffTypes || []);
                        console.log('初始化完成，等待用户选择工作人员类型');
                    }

                    // 保存QRCode参数 - 确保后续可以使用
                    if (window.appData.qrParam) {
                        window.appData.qrCode = window.appData.qrParam;
                        console.log('保存qrParam到qrCode', window.appData.qrParam);
                    }

                    // 设置提交评价的正确API路径 - 使用集中定义的端点
                    window.appData.evaluationApiPath = API_ENDPOINTS.SUBMIT_EVALUATION;
                    window.appData.fullEvaluationApiUrl = `${API_BASE_URL}${API_ENDPOINTS.SUBMIT_EVALUATION}`;
                    console.log('设置正确的评价提交API路径', window.appData.evaluationApiPath);

                    // 设置验证成功标志
                    window.appData.verificationSuccess = true;
                    console.log('设置验证成功标志');
                }
            } catch (e) {
                console.error('updateAppData错误:', e);
            }
        },

        /**
         * 从URL中提取二维码参数
         * @returns {string|null} - 二维码参数或null
         */
        extractQRCodeFromURL: function() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                let qrParam = urlParams.get('qr') || urlParams.get('qrcode') || urlParams.get('code');

                // 检查是否成功获取
                if (qrParam) {
                    console.log('从URL中提取的qrParam:', qrParam);

                    // 立即保存到appData中供后续使用
                    if (window.appData) {
                        window.appData.qrParam = qrParam;
                        window.appData.qrCode = qrParam; // 同时保存两个名称以确保兼容性
                        console.log('已将qrParam保存到appData中');
                    }

                    return qrParam;
                }

                console.warn('URL中未找到二维码参数');
                return null;
            } catch (error) {
                console.error('提取二维码参数出错:', error);
                return null;
            }
        }
    };
})();