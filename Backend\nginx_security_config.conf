# 🔒 Nginx安全配置增强
# 针对公网API暴露的安全加固配置

# 在http块中添加以下配置
http {
    # 🔒 基础安全设置
    server_tokens off;                    # 隐藏nginx版本信息
    client_max_body_size 1m;             # 限制请求体大小
    client_body_timeout 10s;             # 请求体超时
    client_header_timeout 10s;           # 请求头超时
    keepalive_timeout 65s;               # 保持连接超时
    send_timeout 10s;                    # 发送超时
    
    # 🔒 缓冲区安全设置
    client_body_buffer_size 1k;          # 请求体缓冲区
    client_header_buffer_size 1k;        # 请求头缓冲区
    large_client_header_buffers 2 1k;    # 大请求头缓冲区
    
    # 🔒 速率限制配置
    # 定义限制区域
    limit_req_zone $binary_remote_addr zone=api_limit:10m rate=10r/m;      # API限制：每分钟10次
    limit_req_zone $binary_remote_addr zone=global_limit:10m rate=60r/m;   # 全局限制：每分钟60次
    limit_req_zone $binary_remote_addr zone=burst_limit:10m rate=5r/s;     # 突发限制：每秒5次
    
    # 连接数限制
    limit_conn_zone $binary_remote_addr zone=conn_limit:10m;
    
    # 🔒 地理位置限制（可选）
    # geo $allowed_country {
    #     default 0;
    #     CN 1;  # 只允许中国IP
    # }
    
    # 🔒 恶意IP检测
    map $http_user_agent $blocked_agent {
        default 0;
        ~*sqlmap 1;
        ~*nikto 1;
        ~*nmap 1;
        ~*masscan 1;
        ~*zap 1;
        ~*bot 1;
        ~*crawler 1;
        ~*spider 1;
    }
    
    # 🔒 可疑请求检测
    map $request_uri $blocked_uri {
        default 0;
        ~*\.(php|asp|jsp)$ 1;
        ~*admin 1;
        ~*wp- 1;
        ~*phpmyadmin 1;
        ~*\.env 1;
    }
    
    # 在server块中添加以下配置
    server {
        listen 443 ssl http2;
        server_name zg120pj.cn;
        
        # 🔒 SSL安全配置增强
        ssl_certificate      "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.crt";
        ssl_certificate_key  "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.key";
        
        # SSL协议和密码套件
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305;
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:10m;
        ssl_session_tickets off;
        
        # OCSP装订
        ssl_stapling on;
        ssl_stapling_verify on;
        
        # 🔒 完整安全头部
        add_header Strict-Transport-Security "max-age=31536000; includeSubDomains; preload" always;
        add_header X-Content-Type-Options nosniff always;
        add_header X-Frame-Options DENY always;
        add_header X-XSS-Protection "1; mode=block" always;
        add_header Referrer-Policy "strict-origin-when-cross-origin" always;
        add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self'; connect-src 'self'; frame-ancestors 'none';" always;
        add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;
        add_header X-Permitted-Cross-Domain-Policies none always;
        
        # 🔒 阻止恶意请求
        if ($blocked_agent) {
            return 403 "Access Denied - Malicious User Agent";
        }
        
        if ($blocked_uri) {
            return 403 "Access Denied - Suspicious Request";
        }
        
        # 🔒 地理位置限制（如果启用）
        # if ($allowed_country = 0) {
        #     return 403 "Access Denied - Geographic Restriction";
        # }
        
        # 🔒 连接数限制
        limit_conn conn_limit 10;  # 每个IP最多10个连接
        
        # 🔒 API路径特殊保护
        location /api/v1/public/ {
            # 应用速率限制
            limit_req zone=api_limit burst=3 nodelay;
            limit_req zone=global_limit burst=10 nodelay;
            
            # 只允许POST请求
            if ($request_method !~ ^(POST|OPTIONS)$) {
                return 405 "Method Not Allowed";
            }
            
            # 验证Content-Type
            if ($content_type !~ "application/json") {
                return 400 "Invalid Content-Type";
            }
            
            # 添加API特定头部
            add_header X-API-Version "1.0" always;
            add_header X-Rate-Limit "10/min" always;
            
            # 代理到后端
            proxy_pass http://127.0.0.1:8001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            
            # 代理超时设置
            proxy_connect_timeout 5s;
            proxy_send_timeout 10s;
            proxy_read_timeout 10s;
            
            # 缓冲区设置
            proxy_buffer_size 4k;
            proxy_buffers 8 4k;
            proxy_busy_buffers_size 8k;
        }
        
        # 🔒 静态文件安全
        location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
            expires 30d;
            add_header Cache-Control "public, no-transform";
            add_header X-Content-Type-Options nosniff;
            
            # 防止直接访问敏感文件
            location ~* \.(env|config|log|sql|bak)$ {
                deny all;
            }
        }
        
        # 🔒 阻止常见攻击路径
        location ~* /(wp-admin|wp-login|phpmyadmin|admin|administrator) {
            return 403 "Access Denied";
        }
        
        # 🔒 阻止敏感文件访问
        location ~* \.(env|git|svn|htaccess|htpasswd|ini|log|sql|bak|old)$ {
            deny all;
        }
        
        # 🔒 阻止HTTP方法
        location / {
            limit_except GET POST HEAD OPTIONS {
                deny all;
            }
            
            # 其他配置...
        }
        
        # 🔒 错误页面（不泄露信息）
        error_page 403 /403.html;
        error_page 404 /404.html;
        error_page 500 502 503 504 /50x.html;
        
        location = /403.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /404.html {
            root /usr/share/nginx/html;
            internal;
        }
        
        location = /50x.html {
            root /usr/share/nginx/html;
            internal;
        }
    }
    
    # 🔒 日志格式增强
    log_format security_log '$remote_addr - $remote_user [$time_local] '
                           '"$request" $status $body_bytes_sent '
                           '"$http_referer" "$http_user_agent" '
                           '$request_time $upstream_response_time '
                           '"$http_x_forwarded_for" "$http_x_real_ip"';
    
    # 🔒 访问日志
    access_log /var/log/nginx/security_access.log security_log;
    error_log /var/log/nginx/security_error.log warn;
}

# 🔒 额外的安全模块配置（如果可用）

# ModSecurity WAF配置示例
# load_module modules/ngx_http_modsecurity_module.so;
# 
# http {
#     modsecurity on;
#     modsecurity_rules_file /etc/nginx/modsec/main.conf;
# }

# 🔒 fail2ban配置建议
# 在 /etc/fail2ban/jail.local 中添加：
#
# [nginx-req-limit]
# enabled = true
# filter = nginx-req-limit
# action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
# logpath = /var/log/nginx/security_error.log
# findtime = 600
# bantime = 7200
# maxretry = 10

# 🔒 监控脚本建议
# 创建 /etc/nginx/scripts/security_monitor.sh：
# #!/bin/bash
# # 监控异常访问
# tail -f /var/log/nginx/security_access.log | while read line; do
#     if echo "$line" | grep -E "(403|429|500)"; then
#         echo "$(date): Security Event - $line" >> /var/log/nginx/security_events.log
#     fi
# done
