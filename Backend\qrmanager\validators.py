"""
自定义密码验证器
"""

import re
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _

class PasswordComplexityValidator:
    """
    密码复杂度验证器
    要求密码包含大小写字母、数字和特殊字符
    """
    
    def __init__(self, min_length=8):
        self.min_length = min_length
    
    def validate(self, password, user=None):
        """
        验证密码复杂度
        
        参数:
            password: 密码
            user: 用户对象
            
        异常:
            ValidationError: 密码不符合复杂度要求
        """
        # 检查密码长度
        if len(password) < self.min_length:
            raise ValidationError(
                _("密码长度不能少于%(min_length)d个字符。"),
                code='password_too_short',
                params={'min_length': self.min_length},
            )
        
        # 检查是否包含小写字母
        if not re.search(r'[a-z]', password):
            raise ValidationError(
                _("密码必须包含至少一个小写字母。"),
                code='password_no_lower',
            )
        
        # 检查是否包含大写字母
        if not re.search(r'[A-Z]', password):
            raise ValidationError(
                _("密码必须包含至少一个大写字母。"),
                code='password_no_upper',
            )
        
        # 检查是否包含数字
        if not re.search(r'[0-9]', password):
            raise ValidationError(
                _("密码必须包含至少一个数字。"),
                code='password_no_digit',
            )
        
        # 检查是否包含特殊字符
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                _("密码必须包含至少一个特殊字符（!@#$%^&*(),.?\":{}|<>）。"),
                code='password_no_special',
            )
    
    def get_help_text(self):
        """
        获取帮助文本
        
        返回:
            str: 帮助文本
        """
        return _(
            "您的密码必须包含至少%(min_length)d个字符，"
            "并且必须包含大小写字母、数字和特殊字符。"
        ) % {'min_length': self.min_length}