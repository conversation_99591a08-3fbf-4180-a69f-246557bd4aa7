# Generated by Django 4.2.7 on 2025-02-07 03:39

from django.db import migrations
import uuid

def generate_work_numbers(apps, schema_editor):
    Staff = apps.get_model('qrmanager', 'Staff')
    for staff in Staff.objects.all():
        while True:
            work_number = f"WN{str(uuid.uuid4())[:8]}"
            if not Staff.objects.filter(work_number=work_number).exists():
                staff.work_number = work_number
                staff.save()
                break

class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0006_remove_staff_email_remove_staff_phone_and_more'),
    ]

    operations = [
        migrations.RunPython(generate_work_numbers),
    ]
