from waitress import serve
import os
from flask import Flask, send_from_directory, abort, request
import logging

# 确保logs目录存在
logs_dir = os.path.join(os.path.dirname(__file__), 'logs')
if not os.path.exists(logs_dir):
    os.makedirs(logs_dir)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s [%(levelname)s] %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(logs_dir, "frontend_access.log")),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

app = Flask(__name__, static_folder='.')

# 安全头部中间件
@app.after_request
def add_security_headers(response):
    response.headers['X-Content-Type-Options'] = 'nosniff'
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'
    response.headers['X-XSS-Protection'] = '1; mode=block'
    response.headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
    return response

# 记录每个请求
@app.before_request
def log_request_info():
    logger.info(f"访问: {request.remote_addr} -> {request.method} {request.path}")

# 阻止访问敏感文件
@app.before_request
def block_sensitive_files():
    path = request.path.lower()
    if any(blocked in path for blocked in ['.git', '.env', '.htaccess', '.config', 'server.py']):
        logger.warning(f"阻止敏感文件访问: {request.remote_addr} -> {path}")
        return abort(403)

    # 阻止直接访问管理相关路径
    if any(admin_path in path for admin_path in ['/admin', '/dashboard', '/manage', '/management']):
        logger.warning(f"阻止管理页面直接访问: {request.remote_addr} -> {path}")
        return abort(403)

@app.route('/<path:path>')
def serve_file(path):
    # 如果路径存在且是文件而非目录，直接提供文件
    if os.path.isfile(path):
        return send_from_directory('.', path)

    # 检查目录中是否有index.html
    if os.path.isdir(path):
        index_path = os.path.join(path, 'index.html')
        if os.path.exists(index_path):
            return send_from_directory(path, 'index.html')

    # 默认返回根目录的index.html
    return send_from_directory('.', 'index.html')

@app.route('/')
def serve_index():
    return send_from_directory('.', 'index.html')

# 404错误处理
@app.errorhandler(404)
def page_not_found(e):
    return send_from_directory('.', 'index.html')

# 定义允许访问的IP地址
ALLOWED_IPS = ['**********/24', '127.0.0.1']

def is_ip_allowed(ip):
    # 简单IP检查，生产环境应使用更完善的方法
    for allowed in ALLOWED_IPS:
        if '/' in allowed:  # CIDR表示法
            network = allowed.split('/')[0]
            if ip.startswith(network.rsplit('.', 1)[0]):
                return True
        elif ip == allowed:
            return True
    return False

if __name__ == '__main__':
    # 服务器启动信息
    host = '127.0.0.1'
    port = 80

    logger.info(f"前端测试服务器启动于 http://{host}:{port}")
    logger.info(f"按Ctrl+C停止服务器")

    # 使用waitress启动服务器
    try:
        serve(app, host=host, port=port)
    except KeyboardInterrupt:
        logger.info("服务器已停止")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
        print(f"错误: {e}")
        print("提示: 如果是权限错误，请尝试以管理员身份运行或更换端口(如8080)")