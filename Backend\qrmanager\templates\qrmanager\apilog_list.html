{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item active">API访问日志</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-history me-1"></i>
            API访问日志
        </div>
        <div class="card-body">
            <!-- 过滤器 -->
            <div class="accordion mb-4" id="filterAccordion">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="headingOne">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#collapseOne" aria-expanded="true" aria-controls="collapseOne">
                            <i class="fas fa-filter me-1"></i> 过滤条件
                        </button>
                    </h2>
                    <div id="collapseOne" class="accordion-collapse collapse show" aria-labelledby="headingOne" data-bs-parent="#filterAccordion">
                        <div class="accordion-body">
                            <form method="get" class="row g-3">
                                <div class="col-md-4">
                                    <label for="api_key" class="form-label">API密钥</label>
                                    <select class="form-select" id="api_key" name="api_key">
                                        <option value="">-- 全部 --</option>
                                        {% for key in api_keys %}
                                            <option value="{{ key.id }}" {% if filters.api_key == key.id|stringformat:"s" %}selected{% endif %}>{{ key.name }}</option>
                                        {% endfor %}
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="status_code" class="form-label">状态码</label>
                                    <select class="form-select" id="status_code" name="status_code">
                                        <option value="">-- 全部 --</option>
                                        <option value="200" {% if filters.status_code == '200' %}selected{% endif %}>200 (成功)</option>
                                        <option value="201" {% if filters.status_code == '201' %}selected{% endif %}>201 (已创建)</option>
                                        <option value="400" {% if filters.status_code == '400' %}selected{% endif %}>400 (请求错误)</option>
                                        <option value="401" {% if filters.status_code == '401' %}selected{% endif %}>401 (未授权)</option>
                                        <option value="403" {% if filters.status_code == '403' %}selected{% endif %}>403 (禁止访问)</option>
                                        <option value="404" {% if filters.status_code == '404' %}selected{% endif %}>404 (未找到)</option>
                                        <option value="429" {% if filters.status_code == '429' %}selected{% endif %}>429 (请求过多)</option>
                                        <option value="500" {% if filters.status_code == '500' %}selected{% endif %}>500 (服务器错误)</option>
                                    </select>
                                </div>
                                <div class="col-md-4">
                                    <label for="endpoint" class="form-label">访问端点</label>
                                    <input type="text" class="form-control" id="endpoint" name="endpoint" value="{{ filters.endpoint }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="ip_address" class="form-label">IP地址</label>
                                    <input type="text" class="form-control" id="ip_address" name="ip_address" value="{{ filters.ip_address }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="date_from" class="form-label">开始日期</label>
                                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filters.date_from }}">
                                </div>
                                <div class="col-md-4">
                                    <label for="date_to" class="form-label">结束日期</label>
                                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filters.date_to }}">
                                </div>
                                <div class="col-12">
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-search me-1"></i> 搜索
                                    </button>
                                    <a href="{% url 'qrmanager:apilog_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-redo me-1"></i> 重置
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- 日志表格 -->
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="apilogsTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>时间</th>
                            <th>API密钥</th>
                            <th>端点</th>
                            <th>方法</th>
                            <th>状态码</th>
                            <th>响应时间(秒)</th>
                            <th>IP地址</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for log in apilogs %}
                        <tr>
                            <td>{{ log.timestamp|date:"Y-m-d H:i:s" }}</td>
                            <td>
                                {% if log.api_key %}
                                    {{ log.api_key.name }}
                                {% else %}
                                    <span class="text-muted">未使用密钥</span>
                                {% endif %}
                            </td>
                            <td>{{ log.endpoint }}</td>
                            <td>
                                {% if log.method == 'GET' %}
                                    <span class="badge bg-success">GET</span>
                                {% elif log.method == 'POST' %}
                                    <span class="badge bg-primary">POST</span>
                                {% elif log.method == 'PUT' %}
                                    <span class="badge bg-warning">PUT</span>
                                {% elif log.method == 'DELETE' %}
                                    <span class="badge bg-danger">DELETE</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ log.method }}</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if log.response_status >= 200 and log.response_status < 300 %}
                                    <span class="badge bg-success">{{ log.response_status }}</span>
                                {% elif log.response_status >= 400 and log.response_status < 500 %}
                                    <span class="badge bg-warning">{{ log.response_status }}</span>
                                {% elif log.response_status >= 500 %}
                                    <span class="badge bg-danger">{{ log.response_status }}</span>
                                {% else %}
                                    <span class="badge bg-secondary">{{ log.response_status }}</span>
                                {% endif %}
                            </td>
                            <td>{{ log.response_time|floatformat:3 }}</td>
                            <td>{{ log.ip_address }}</td>
                            <td>
                                <a href="{% url 'qrmanager:apilog_detail' log.id %}" class="btn btn-info btn-sm">
                                    <i class="fas fa-eye"></i>
                                </a>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">暂无API访问日志</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                        {% if page_obj.number == num %}
                            <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item"><a class="page-link" href="?page={{ num }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a></li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %} 