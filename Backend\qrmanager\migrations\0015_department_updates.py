from django.db import migrations, models

def generate_department_codes(apps, schema_editor):
    Department = apps.get_model('qrmanager', 'Department')
    for index, dept in enumerate(Department.objects.all().order_by('created_at'), start=1):
        dept.code = f'DEPT{index:03d}'
        dept.save()

class Migration(migrations.Migration):
    dependencies = [
        ('qrmanager', '0014_alter_bed_number_alter_bed_unique_together'),
    ]

    operations = [
        # 第一步：添加可为空的字段
        migrations.AddField(
            model_name='department',
            name='code',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='科室编码'),
        ),
        migrations.AddField(
            model_name='department',
            name='remarks',
            field=models.TextField(blank=True, verbose_name='备注'),
        ),
        migrations.AlterModelOptions(
            name='department',
            options={'ordering': ['code'], 'verbose_name': '科室', 'verbose_name_plural': '科室'},
        ),
        # 第二步：为现有记录生成编码
        migrations.RunPython(generate_department_codes),
    ] 