#!/usr/bin/env python
"""
调试安全数据记录 - 检查缓存中的实际数据
"""
import os
import sys
import django
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.core.cache import cache
from django.conf import settings

def debug_cache_data():
    """调试缓存数据"""
    print("🔍 调试安全数据记录")
    print("=" * 60)
    
    # 检查缓存配置
    print(f"缓存配置: {settings.CACHES}")
    print(f"缓存后端: {cache.__class__}")
    print()
    
    # 尝试不同的方法获取缓存键
    print("📋 尝试获取缓存键...")
    
    # 方法1：直接访问_cache属性
    try:
        if hasattr(cache, '_cache'):
            print(f"缓存对象类型: {type(cache._cache)}")
            if hasattr(cache._cache, 'keys'):
                keys = list(cache._cache.keys())
                print(f"方法1 - 找到 {len(keys)} 个缓存键")
                for key in keys[:10]:  # 只显示前10个
                    print(f"  {key}")
            else:
                print("方法1 - _cache没有keys方法")
        else:
            print("方法1 - 缓存没有_cache属性")
    except Exception as e:
        print(f"方法1 失败: {e}")
    
    print()
    
    # 方法2：检查已知的安全相关键
    print("🔒 检查已知的安全键...")
    
    known_patterns = [
        'qrcode_limit:',
        'security_event:',
        'access_record:',
        'qrcode_stats:',
        'ip_requests:',
        'ip_qrcodes:'
    ]
    
    current_time = int(datetime.now().timestamp())
    
    for pattern in known_patterns:
        found_keys = []
        
        # 尝试一些可能的键名
        for i in range(10):  # 检查最近10分钟
            minute_ago = current_time - (i * 60)
            
            test_keys = [
                f"{pattern}3306c6d3-bff1-43c4-aa6a-d954ec37b7e1:evaluation",
                f"{pattern}3306c6d3-bff1-43c4-aa6a-d954ec37b7e1:verification",
                f"{pattern}127.0.0.1:{minute_ago // 60}",
                f"{pattern}qrcode_rate_limit:{minute_ago}",
                f"{pattern}{minute_ago}",
            ]
            
            for test_key in test_keys:
                try:
                    value = cache.get(test_key)
                    if value is not None:
                        found_keys.append((test_key, value))
                except:
                    pass
        
        if found_keys:
            print(f"  {pattern} - 找到 {len(found_keys)} 个键:")
            for key, value in found_keys[:3]:  # 只显示前3个
                print(f"    {key}: {str(value)[:100]}...")
        else:
            print(f"  {pattern} - 未找到数据")
    
    print()
    
    # 方法3：手动设置和获取测试数据
    print("🧪 测试缓存读写...")
    
    test_key = "test_security_data"
    test_value = {"test": "data", "timestamp": current_time}
    
    try:
        cache.set(test_key, test_value, 300)
        retrieved_value = cache.get(test_key)
        
        if retrieved_value == test_value:
            print("✅ 缓存读写正常")
        else:
            print(f"❌ 缓存读写异常: 设置 {test_value}, 获取 {retrieved_value}")
    except Exception as e:
        print(f"❌ 缓存读写失败: {e}")
    
    print()
    
    # 方法4：检查Django缓存统计
    print("📊 Django缓存统计...")
    
    try:
        # 尝试获取缓存统计信息
        if hasattr(cache, '_cache'):
            cache_obj = cache._cache
            print(f"缓存对象: {cache_obj}")
            
            # 对于文件缓存，检查缓存目录
            if hasattr(cache_obj, '_dir'):
                cache_dir = cache_obj._dir
                print(f"缓存目录: {cache_dir}")
                
                import os
                if os.path.exists(cache_dir):
                    files = os.listdir(cache_dir)
                    print(f"缓存文件数量: {len(files)}")
                    
                    # 显示最近的几个文件
                    if files:
                        files.sort(key=lambda f: os.path.getmtime(os.path.join(cache_dir, f)), reverse=True)
                        print("最近的缓存文件:")
                        for f in files[:5]:
                            file_path = os.path.join(cache_dir, f)
                            mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                            print(f"  {f} - {mtime}")
                else:
                    print("缓存目录不存在")
    except Exception as e:
        print(f"获取缓存统计失败: {e}")

def test_security_middleware():
    """测试安全中间件是否正在记录数据"""
    print("\n🔒 测试安全中间件数据记录...")
    
    # 手动触发一些安全记录
    from qrcode_based_security import get_qrcode_security_stats, get_ip_security_profile
    
    try:
        # 测试获取二维码统计
        uuid = "3306c6d3-bff1-43c4-aa6a-d954ec37b7e1"
        stats = get_qrcode_security_stats(uuid)
        print(f"二维码统计: {stats}")
        
        # 测试获取IP统计
        ip_profile = get_ip_security_profile("127.0.0.1")
        print(f"IP统计: {ip_profile}")
        
    except Exception as e:
        print(f"获取安全统计失败: {e}")

def manual_add_test_data():
    """手动添加测试数据"""
    print("\n🧪 手动添加测试数据...")
    
    current_time = int(datetime.now().timestamp())
    
    # 添加安全事件
    event_key = f"security_event:test_event:{current_time}"
    event_data = {
        'type': 'qrcode_rate_limit',
        'data': {
            'uuid': '3306c6d3-bff1-43c4-aa6a-d954ec37b7e1',
            'ip': '127.0.0.1',
            'operation': 'verification'
        },
        'timestamp': current_time
    }
    
    try:
        cache.set(event_key, event_data, 3600)
        print(f"✅ 添加安全事件: {event_key}")
        
        # 验证数据
        retrieved = cache.get(event_key)
        if retrieved:
            print(f"✅ 验证成功: {retrieved}")
        else:
            print("❌ 验证失败: 无法获取刚添加的数据")
            
    except Exception as e:
        print(f"❌ 添加测试数据失败: {e}")
    
    # 添加二维码统计
    stats_key = f"qrcode_stats:3306c6d3-bff1-43c4-aa6a-d954ec37b7e1:{current_time // 3600}"
    stats_data = {
        'access_count': 5,
        'unique_ips': {'127.0.0.1', '*************'}
    }
    
    try:
        cache.set(stats_key, stats_data, 3600)
        print(f"✅ 添加二维码统计: {stats_key}")
    except Exception as e:
        print(f"❌ 添加二维码统计失败: {e}")

if __name__ == "__main__":
    debug_cache_data()
    test_security_middleware()
    manual_add_test_data()
    
    print("\n" + "=" * 60)
    print("🔍 调试完成")
    print("请检查安全监控页面是否显示数据")
    print("访问: http://127.0.0.1:8000/security/monitoring/")
