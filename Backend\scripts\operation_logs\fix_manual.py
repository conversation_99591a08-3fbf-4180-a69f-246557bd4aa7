#!/usr/bin/env python
# 手动修复views.py中的语法错误

with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找错误行附近的内容
error_line_text = ").order_by('-created_at')"
error_context = content[content.find("def get_queryset(self):", content.find("class OperationLogListView")):content.find("def get_context_data", content.find("class OperationLogListView"))]

print("错误方法内容：")
print(error_context)

# 替换整个方法
# 查找方法的起始和结束位置
start_pos = content.find("def get_queryset(self):", content.find("class OperationLogListView"))
end_pos = content.find("def get_context_data", content.find("class OperationLogListView"))

if start_pos != -1 and end_pos != -1:
    # 新的正确方法
    new_method = """    def get_queryset(self):
        queryset = OperationLog.objects.select_related('user')

        # 过滤条件
        user_id = self.request.GET.get('user')
        action = self.request.GET.get('action')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        time_range = self.request.GET.get('time_range')

        # 应用过滤器
        if user_id and user_id.isdigit():
            queryset = queryset.filter(user_id=user_id)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if status:
            queryset = queryset.filter(status=status)

        # 日期范围过滤
        if date_from:
            try:
                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        if date_to:
            try:
                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass

        # 时间范围快捷筛选
        if time_range:
            now = timezone.now()
            if time_range == 'today':
                queryset = queryset.filter(created_at__date=now.date())
            elif time_range == 'yesterday':
                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))
            elif time_range == 'this_week':
                # 本周的开始（星期一）
                week_start = now.date() - timezone.timedelta(days=now.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif time_range == 'this_month':
                # 本月的开始（1号）
                month_start = now.date().replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)
            elif time_range == 'last_30_days':
                # 过去30天
                days_30 = now.date() - timezone.timedelta(days=30)
                queryset = queryset.filter(created_at__date__gte=days_30)

        return queryset.order_by('-created_at')
"""

    # 替换方法
    new_content = content[:start_pos] + new_method + content[end_pos:]
    
    # 写回文件
    with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
        f.write(new_content)
    
    print("\n已修复语法错误！")
else:
    print("\n无法找到方法，请手动修复。") 