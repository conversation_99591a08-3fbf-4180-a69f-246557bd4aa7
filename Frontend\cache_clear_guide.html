<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存清理指南 - 自贡市第四人民医院</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }
        .header h1 {
            margin: 0;
            font-size: 28px;
            font-weight: 600;
        }
        .header p {
            margin: 10px 0 0 0;
            opacity: 0.9;
            font-size: 16px;
        }
        .content {
            padding: 30px;
        }
        .problem-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 30px;
        }
        .problem-section h2 {
            color: #856404;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .problem-section h2::before {
            content: "⚠️";
            margin-right: 10px;
            font-size: 24px;
        }
        .solution-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        .solution-card {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        .solution-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }
        .solution-card h3 {
            color: #495057;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .solution-card h3::before {
            content: "📱";
            margin-right: 10px;
            font-size: 20px;
        }
        .steps {
            list-style: none;
            padding: 0;
        }
        .steps li {
            background: white;
            margin: 8px 0;
            padding: 12px 15px;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            position: relative;
            padding-left: 45px;
        }
        .steps li::before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 15px;
            top: 50%;
            transform: translateY(-50%);
            background: #007bff;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .steps {
            counter-reset: step-counter;
        }
        .quick-fix {
            background: #d4edda;
            border: 1px solid #c3e6cb;
            border-radius: 10px;
            padding: 20px;
            margin-bottom: 20px;
        }
        .quick-fix h3 {
            color: #155724;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .quick-fix h3::before {
            content: "⚡";
            margin-right: 10px;
            font-size: 20px;
        }
        .tech-info {
            background: #e2e3e5;
            border: 1px solid #d6d8db;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        .tech-info h3 {
            color: #383d41;
            margin-top: 0;
            display: flex;
            align-items: center;
        }
        .tech-info h3::before {
            content: "🔧";
            margin-right: 10px;
            font-size: 20px;
        }
        .version-info {
            background: #f8f9fa;
            border: 2px solid #007bff;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            text-align: center;
        }
        .version-info strong {
            color: #007bff;
            font-size: 18px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007bff;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-weight: 500;
            transition: background 0.2s;
            margin: 10px 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .btn-success {
            background: #28a745;
        }
        .btn-success:hover {
            background: #1e7e34;
        }
        @media (max-width: 600px) {
            .solution-grid {
                grid-template-columns: 1fr;
            }
            .container {
                margin: 10px;
                border-radius: 10px;
            }
            .header, .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔄 缓存清理指南</h1>
            <p>解决扫码后看不到最新页面的问题</p>
        </div>
        
        <div class="content">
            <div class="problem-section">
                <h2>遇到的问题</h2>
                <p><strong>症状：</strong>扫描二维码后，页面显示的还是旧版本，看不到最新的修改内容。</p>
                <p><strong>原因：</strong>手机浏览器缓存了旧版本的页面文件，需要清理缓存才能看到最新版本。</p>
            </div>

            <div class="version-info">
                <strong>当前最新版本：202506181354</strong><br>
                <small>如果您看到的版本号不是这个，说明需要清理缓存</small>
            </div>

            <div class="quick-fix">
                <h3>快速解决方法</h3>
                <p><strong>最简单的方法：</strong>退出微信，重新打开微信，再次扫描二维码。</p>
                <p><strong>或者：</strong>在页面上长按刷新按钮，选择"硬性重新加载"。</p>
            </div>

            <div class="solution-grid">
                <div class="solution-card">
                    <h3>Android 手机 (Chrome浏览器)</h3>
                    <ol class="steps">
                        <li>打开Chrome浏览器</li>
                        <li>点击右上角三个点的菜单</li>
                        <li>选择"历史记录"</li>
                        <li>点击"清除浏览数据"</li>
                        <li>选择"缓存的图片和文件"</li>
                        <li>点击"清除数据"</li>
                        <li>重新扫描二维码</li>
                    </ol>
                </div>

                <div class="solution-card">
                    <h3>iPhone 手机 (Safari浏览器)</h3>
                    <ol class="steps">
                        <li>打开"设置"应用</li>
                        <li>向下滚动找到"Safari"</li>
                        <li>点击进入Safari设置</li>
                        <li>找到"清除历史记录与网站数据"</li>
                        <li>点击并确认清除</li>
                        <li>重新扫描二维码</li>
                    </ol>
                </div>

                <div class="solution-card">
                    <h3>微信内置浏览器</h3>
                    <ol class="steps">
                        <li>在微信中打开页面</li>
                        <li>点击右上角"..."菜单</li>
                        <li>选择"刷新"</li>
                        <li>如果还是旧版本，完全退出微信</li>
                        <li>重新打开微信</li>
                        <li>再次扫描二维码</li>
                    </ol>
                </div>

                <div class="solution-card">
                    <h3>其他浏览器</h3>
                    <ol class="steps">
                        <li>在页面上长按刷新按钮</li>
                        <li>选择"硬性重新加载"或"强制刷新"</li>
                        <li>或者清除浏览器缓存</li>
                        <li>重新访问页面</li>
                    </ol>
                </div>
            </div>

            <div class="tech-info">
                <h3>技术说明</h3>
                <p><strong>为什么会出现缓存问题？</strong></p>
                <ul>
                    <li>浏览器为了提高访问速度，会缓存网页文件</li>
                    <li>当网页更新后，浏览器可能还在使用旧的缓存文件</li>
                    <li>我们已经优化了缓存策略，但首次更新后需要手动清理一次</li>
                </ul>
                
                <p><strong>我们的技术改进：</strong></p>
                <ul>
                    <li>✅ HTML文件缓存时间缩短为5分钟</li>
                    <li>✅ JS/CSS文件添加版本号自动更新</li>
                    <li>✅ 添加防缓存Meta标签</li>
                    <li>✅ 优化Nginx缓存配置</li>
                </ul>
            </div>

            <div style="text-align: center; margin-top: 30px;">
                <a href="javascript:location.reload(true)" class="btn btn-success">🔄 强制刷新当前页面</a>
                <a href="/" class="btn">🏠 返回首页</a>
            </div>

            <div style="text-align: center; margin-top: 20px; color: #666; font-size: 14px;">
                <p>如果按照以上方法操作后仍然看不到最新版本，请联系技术支持。</p>
                <p>技术支持：自贡市第四人民医院信息科</p>
            </div>
        </div>
    </div>

    <script>
        // 显示当前页面版本信息
        document.addEventListener('DOMContentLoaded', function() {
            const versionMeta = document.querySelector('meta[name="version"]');
            if (versionMeta) {
                const currentVersion = versionMeta.getAttribute('content');
                console.log('当前页面版本:', currentVersion);
            }
            
            // 检查是否是最新版本
            const expectedVersion = '202506181354';
            const actualVersion = versionMeta ? versionMeta.getAttribute('content') : 'unknown';
            
            if (actualVersion !== expectedVersion && actualVersion !== 'unknown') {
                const versionInfo = document.querySelector('.version-info');
                if (versionInfo) {
                    versionInfo.innerHTML = `
                        <strong style="color: #dc3545;">检测到版本不匹配！</strong><br>
                        <small>期望版本: ${expectedVersion}</small><br>
                        <small>当前版本: ${actualVersion}</small><br>
                        <small style="color: #dc3545;">请按照下方指南清理缓存</small>
                    `;
                    versionInfo.style.borderColor = '#dc3545';
                    versionInfo.style.background = '#f8d7da';
                }
            }
        });
    </script>
</body>
</html>
