/**
 * API服务模块 - 统一处理所有API请求和响应
 * 集中管理API调用，确保一致的错误处理和数据转换
 */

(function() {
    // API配置
    const API_CONFIG = {
        // 基础URL - 生产环境使用HTTPS和域名
        BASE_URL: 'https://zg120pj.cn',
        
        // API端点 - 使用Nginx配置的外部路径
        ENDPOINTS: {
            VERIFY_QRCODE: '/service/resources/',
            SUBMIT_EVALUATION: '/service/evaluation/'
        },
        
        // 请求超时时间（毫秒）
        TIMEOUT: 30000,
        
        // 重试配置
        RETRY: {
            MAX_ATTEMPTS: 2,
            DELAY: 1000
        }
    };
    
    // 请求计数器和缓存
    const requestCache = {};
    let pendingRequests = 0;
    
    /**
     * 创建完整的API URL
     * @param {string} endpoint - API端点路径
     * @returns {string} - 完整的API URL
     */
    function createApiUrl(endpoint) {
        return `${API_CONFIG.BASE_URL}${endpoint}`;
    }
    
    /**
     * 显示加载状态
     * @private
     */
    function showLoading() {
        pendingRequests++;
        if (typeof window.showLoading === 'function') {
            window.showLoading();
        }
    }
    
    /**
     * 隐藏加载状态
     * @private
     */
    function hideLoading() {
        pendingRequests = Math.max(0, pendingRequests - 1);
        if (pendingRequests === 0 && typeof window.hideLoading === 'function') {
            window.hideLoading();
        }
    }
    
    /**
     * 处理API错误
     * @param {Error} error - 错误对象
     * @param {string} context - 错误上下文
     * @returns {object} - 标准化的错误对象
     * @private
     */
    function handleApiError(error, context) {
        // 记录错误
        console.error(`API错误 (${context}):`, error);
        
        // 创建标准化的错误对象
        const standardError = {
            message: error.message || '未知错误',
            code: error.code || 'UNKNOWN_ERROR',
            context: context,
            timestamp: new Date().toISOString(),
            originalError: error
        };
        
        // 保存到全局对象以便调试
        window.appData = window.appData || {};
        window.appData.lastApiError = standardError;
        
        // 显示用户友好的错误消息
        if (typeof window.showError === 'function') {
            // 在生产环境中显示用户友好的错误消息
            const isProduction = !window.location.search.includes('debug=true') &&
                                localStorage.getItem('debugMode') !== 'true';
            
            const userFriendlyMessage = isProduction ?
                '操作失败，请稍后重试' :
                `${context}: ${error.message}`;
            
            window.showError(userFriendlyMessage);
        }
        
        return standardError;
    }
    
    /**
     * 发送API请求
     * @param {string} endpoint - API端点
     * @param {object} options - 请求选项
     * @param {boolean} [showLoadingIndicator=true] - 是否显示加载指示器
     * @returns {Promise<object>} - 响应数据
     * @private
     */
    async function sendRequest(endpoint, options, showLoadingIndicator = true) {
        const url = createApiUrl(endpoint);
        const requestId = `${options.method}:${url}:${JSON.stringify(options.body || {})}`;
        
        // 检查是否有缓存的响应（仅对GET请求）
        if (options.method === 'GET' && requestCache[requestId]) {
            return requestCache[requestId];
        }
        
        // 显示加载状态
        if (showLoadingIndicator) {
            showLoading();
        }
        
        try {
            // 添加超时处理
            const timeoutPromise = new Promise((_, reject) => {
                setTimeout(() => reject(new Error('请求超时')), API_CONFIG.TIMEOUT);
            });
            
            // 发送请求
            console.log('发送请求到:', url);
            console.log('请求选项:', options);

            const fetchPromise = fetch(url, options)
                .then(async response => {
                    console.log('收到响应:', response.status, response.statusText);

                    // 检查响应状态
                    if (!response.ok) {
                        // 尝试解析错误响应
                        let errorMessage;
                        try {
                            const errorData = await response.json();
                            console.log('错误响应数据:', errorData);
                            errorMessage = errorData.message || errorData.error || `服务器返回错误: ${response.status}`;
                        } catch (e) {
                            console.log('无法解析错误响应:', e);
                            errorMessage = `服务器返回错误: ${response.status}`;
                        }
                        throw new Error(errorMessage);
                    }

                    // 解析响应数据
                    const text = await response.text();
                    console.log('响应文本:', text);

                    let data;
                    try {
                        data = JSON.parse(text);
                        console.log('解析后的数据:', data);
                    } catch (e) {
                        console.warn('响应不是有效的JSON:', text);
                        data = { raw: text };
                    }

                    // 缓存GET请求的响应
                    if (options.method === 'GET') {
                        requestCache[requestId] = data;
                        // 设置缓存过期（5分钟）
                        setTimeout(() => {
                            delete requestCache[requestId];
                        }, 5 * 60 * 1000);
                    }

                    return data;
                })
                .catch(error => {
                    console.error('Fetch请求失败:', error);
                    throw error;
                });
            
            // 使用Promise.race处理超时
            return await Promise.race([fetchPromise, timeoutPromise]);
        } catch (error) {
            // 处理网络错误、超时等
            throw error;
        } finally {
            // 隐藏加载状态
            if (showLoadingIndicator) {
                hideLoading();
            }
        }
    }
    
    // API服务对象 - 第一部分
    const apiService = {
        /**
         * 验证二维码
         * @param {string} qrParam - 二维码参数
         * @returns {Promise<object>} - 验证结果
         */
        verifyQRCode: async function(qrParam) {
            if (!qrParam) {
                throw new Error('未提供二维码参数');
            }

            // 保存QR参数到全局对象
            window.appData = window.appData || {};
            window.appData.qrParam = qrParam;
            window.appData.qrCode = qrParam;

            try {
                // 构建请求选项
                const options = {
                    method: 'POST',
                    headers: {
                        'Accept': 'application/json',
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        qr_param: qrParam,
                        client_ip: window.appData.clientIP || '127.0.0.1'
                    })
                };

                // 发送请求
                const data = await sendRequest(API_CONFIG.ENDPOINTS.VERIFY_QRCODE, options);

                // 保存响应数据
                window.appData.lastApiResponse = data;

                // 验证响应数据
                if (data.status === 'success' && data.data) {
                    // 标记验证成功
                    window.appData.verificationSuccess = true;

                    // 更新应用数据
                    this.updateAppData(data.data);

                    return data;
                } else {
                    // 标记验证失败
                    window.appData.verificationSuccess = false;
                    throw new Error(data.message || '验证失败');
                }
            } catch (error) {
                // 标记验证失败
                window.appData.verificationSuccess = false;

                // 处理错误
                const standardError = handleApiError(error, '验证二维码');
                throw standardError;
            }
        },

        /**
         * 更新应用数据
         * @param {object} data - API返回的数据
         */
        updateAppData: function(data) {
            window.appData = window.appData || {};

            if (data) {
                // 保存加密字符串
                if (data.encrypted_string) {
                    window.appData.encryptedString = data.encrypted_string;
                } else if (data.encrypt_string) {
                    window.appData.encryptedString = data.encrypt_string;
                } else if (data.auth_string) {
                    window.appData.encryptedString = data.auth_string;
                } else if (data.token) {
                    window.appData.encryptedString = data.token;
                } else if (data.temp_token) {
                    window.appData.tempToken = data.temp_token;
                }

                // 保存科室信息
                if (data.department) {
                    window.appData.department = data.department;
                }

                // 保存床位信息
                if (data.bed) {
                    window.appData.bed = data.bed;
                }

                // 保存工作人员类型
                if (data.staff_types && Array.isArray(data.staff_types)) {
                    window.appData.staffTypes = data.staff_types;
                }

                // 保存工作人员列表
                if (data.staff && Array.isArray(data.staff)) {
                    window.appData.staffList = data.staff;
                }

                // 设置验证成功标志
                window.appData.verificationSuccess = true;
            }
        }
    };

    // 导出全局函数和配置
    window.API_CONFIG = API_CONFIG;
    window.sendRequest = sendRequest;
    window.handleApiError = handleApiError;
    window.apiService = apiService;
})();
