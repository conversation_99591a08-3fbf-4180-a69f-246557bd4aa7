from django.db import migrations, models
import django.db.models.deletion

def set_default_staff_type_and_title(apps, schema_editor):
    DictionaryItem = apps.get_model('qrmanager', 'DictionaryItem')
    Staff = apps.get_model('qrmanager', 'Staff')
    
    # 获取默认的人员类型（医生）
    default_staff_type = DictionaryItem.objects.get(
        dictionary__code='staff_type',
        code='doctor'
    )
    
    # 获取默认的职称（住院医师）
    default_title = DictionaryItem.objects.get(
        dictionary__code='staff_title',
        code='resident'
    )
    
    # 更新所有Staff记录
    for staff in Staff.objects.all():
        staff.staff_type = default_staff_type
        staff.title = default_title
        staff.save()

class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0009_initial_dictionary_data'),
    ]

    operations = [
        migrations.AddField(
            model_name='staff',
            name='staff_type',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_types',
                to='qrmanager.dictionaryitem',
                limit_choices_to={'dictionary__code': 'staff_type', 'is_active': True},
                verbose_name='人员类型'
            ),
        ),
        migrations.AddField(
            model_name='staff',
            name='title',
            field=models.ForeignKey(
                null=True,
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_titles',
                to='qrmanager.dictionaryitem',
                limit_choices_to={'dictionary__code': 'staff_title', 'is_active': True},
                verbose_name='职称'
            ),
        ),
        migrations.RunPython(set_default_staff_type_and_title),
        migrations.AlterField(
            model_name='staff',
            name='staff_type',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_types',
                to='qrmanager.dictionaryitem',
                limit_choices_to={'dictionary__code': 'staff_type', 'is_active': True},
                verbose_name='人员类型'
            ),
        ),
        migrations.AlterField(
            model_name='staff',
            name='title',
            field=models.ForeignKey(
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_titles',
                to='qrmanager.dictionaryitem',
                limit_choices_to={'dictionary__code': 'staff_title', 'is_active': True},
                verbose_name='职称'
            ),
        ),
    ] 