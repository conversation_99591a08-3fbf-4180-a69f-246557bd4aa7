<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>缓存配置验证工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-item {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
            border-left: 4px solid #ccc;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border-left-color: #28a745;
        }
        .warning {
            background-color: #fff3cd;
            color: #856404;
            border-left-color: #ffc107;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border-left-color: #dc3545;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border-left-color: #17a2b8;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .timestamp {
            font-family: monospace;
            font-size: 12px;
            color: #666;
        }
        .headers-display {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow-y: auto;
            margin-top: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 缓存配置验证工具</h1>
        
        <div class="test-item info">
            <strong>验证目标：</strong>确保每次扫码都能获取服务器最新版本
            <br><strong>当前时间：</strong><span class="timestamp" id="current-time"></span>
        </div>

        <div id="test-results"></div>

        <div style="text-align: center; margin: 20px 0;">
            <button onclick="runAllTests()">🚀 开始验证</button>
            <button onclick="testResourceHeaders()">📄 测试资源头部</button>
            <button onclick="testCacheBypass()">🔄 测试缓存绕过</button>
            <button onclick="clearResults()">🧹 清除结果</button>
        </div>

        <div class="test-item warning">
            <strong>使用说明：</strong>
            <br>1. 点击"开始验证"检查所有缓存配置
            <br>2. 如果所有项目都显示✅，说明配置正确
            <br>3. 修改前端文件后，刷新页面验证是否立即生效
        </div>
    </div>

    <script>
        function updateTime() {
            document.getElementById('current-time').textContent = new Date().toLocaleString();
        }
        
        function addResult(title, type, message, details = null) {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-item ${type}`;
            
            let content = `<strong>${title}:</strong> ${message}`;
            if (details) {
                content += `<div class="headers-display">${JSON.stringify(details, null, 2)}</div>`;
            }
            
            resultDiv.innerHTML = content;
            resultsDiv.appendChild(resultDiv);
        }

        function clearResults() {
            document.getElementById('test-results').innerHTML = '';
        }

        async function testResourceHeaders() {
            addResult('资源头部测试', 'info', '正在检查服务器响应头...');
            
            const resources = [
                { url: '/index.html', type: 'HTML' },
                { url: '/styles.css', type: 'CSS' },
                { url: '/js/main.js', type: 'JavaScript' }
            ];

            for (const resource of resources) {
                try {
                    const response = await fetch(resource.url, {
                        method: 'HEAD',
                        cache: 'no-cache'
                    });

                    const headers = {};
                    for (const [key, value] of response.headers.entries()) {
                        headers[key] = value;
                    }

                    const cacheControl = headers['cache-control'] || '';
                    const pragma = headers['pragma'] || '';
                    const expires = headers['expires'] || '';

                    if (cacheControl.includes('no-cache') && 
                        cacheControl.includes('no-store') && 
                        cacheControl.includes('must-revalidate')) {
                        addResult(`✅ ${resource.type}缓存配置`, 'success', '正确禁用缓存', headers);
                    } else {
                        addResult(`❌ ${resource.type}缓存配置`, 'error', '缓存配置不正确', headers);
                    }
                } catch (error) {
                    addResult(`❌ ${resource.type}测试`, 'error', `请求失败: ${error.message}`);
                }
            }
        }

        async function testCacheBypass() {
            addResult('缓存绕过测试', 'info', '测试是否真正绕过缓存...');
            
            const testUrl = '/index.html';
            const timestamps = [];
            
            // 连续请求3次，检查是否每次都是新请求
            for (let i = 0; i < 3; i++) {
                try {
                    const start = Date.now();
                    const response = await fetch(`${testUrl}?t=${Date.now()}`, {
                        cache: 'no-cache',
                        headers: {
                            'Cache-Control': 'no-cache',
                            'Pragma': 'no-cache'
                        }
                    });
                    const end = Date.now();
                    
                    timestamps.push({
                        request: i + 1,
                        duration: end - start,
                        status: response.status,
                        fromCache: response.headers.get('x-cache') === 'HIT'
                    });
                } catch (error) {
                    addResult('缓存绕过测试', 'error', `请求${i + 1}失败: ${error.message}`);
                    return;
                }
            }
            
            // 分析结果
            const avgDuration = timestamps.reduce((sum, t) => sum + t.duration, 0) / timestamps.length;
            const hasCache = timestamps.some(t => t.fromCache);
            
            if (!hasCache && avgDuration > 10) {
                addResult('✅ 缓存绕过测试', 'success', '每次都是新请求，无缓存', timestamps);
            } else if (hasCache) {
                addResult('❌ 缓存绕过测试', 'error', '检测到缓存响应', timestamps);
            } else {
                addResult('⚠️ 缓存绕过测试', 'warning', '响应时间过快，可能有缓存', timestamps);
            }
        }

        function testBrowserCache() {
            addResult('浏览器缓存检查', 'info', '检查浏览器缓存机制...');
            
            // 检查Service Worker
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    if (registrations.length === 0) {
                        addResult('✅ Service Worker', 'success', '未检测到Service Worker');
                    } else {
                        addResult('⚠️ Service Worker', 'warning', `检测到${registrations.length}个Service Worker`, 
                                registrations.map(r => r.scope));
                    }
                });
            } else {
                addResult('✅ Service Worker', 'success', '浏览器不支持Service Worker');
            }

            // 检查Application Cache
            if ('applicationCache' in window) {
                const appCache = window.applicationCache;
                if (appCache.status === appCache.UNCACHED) {
                    addResult('✅ Application Cache', 'success', '未使用Application Cache');
                } else {
                    addResult('⚠️ Application Cache', 'warning', `Application Cache状态: ${appCache.status}`);
                }
            } else {
                addResult('✅ Application Cache', 'success', '浏览器不支持Application Cache');
            }

            // 检查localStorage使用情况
            const localStorageKeys = Object.keys(localStorage);
            if (localStorageKeys.length === 0) {
                addResult('✅ localStorage', 'success', 'localStorage为空');
            } else {
                addResult('ℹ️ localStorage', 'info', `localStorage包含${localStorageKeys.length}个项目`, localStorageKeys);
            }
        }

        function testMetaTags() {
            addResult('HTML Meta标签检查', 'info', '检查页面缓存控制标签...');
            
            const metaTags = document.querySelectorAll('meta[http-equiv]');
            const cacheMetaTags = [];
            
            metaTags.forEach(meta => {
                const httpEquiv = meta.getAttribute('http-equiv').toLowerCase();
                if (httpEquiv.includes('cache') || httpEquiv.includes('pragma') || httpEquiv.includes('expires')) {
                    cacheMetaTags.push({
                        'http-equiv': meta.getAttribute('http-equiv'),
                        'content': meta.getAttribute('content')
                    });
                }
            });
            
            if (cacheMetaTags.length >= 3) {
                addResult('✅ HTML Meta标签', 'success', '缓存控制标签配置正确', cacheMetaTags);
            } else {
                addResult('❌ HTML Meta标签', 'error', '缺少必要的缓存控制标签', cacheMetaTags);
            }
        }

        async function runAllTests() {
            clearResults();
            addResult('开始验证', 'info', '正在检查所有缓存配置...');
            
            // 1. 检查HTML Meta标签
            testMetaTags();
            
            // 2. 检查浏览器缓存机制
            testBrowserCache();
            
            // 3. 等待一下再测试服务器响应
            setTimeout(async () => {
                await testResourceHeaders();
                
                // 4. 最后测试缓存绕过
                setTimeout(async () => {
                    await testCacheBypass();
                    
                    addResult('验证完成', 'success', '所有测试已完成，请查看上方结果');
                }, 1000);
            }, 1000);
        }

        // 页面加载时更新时间
        updateTime();
        setInterval(updateTime, 1000);

        // 显示页面加载信息
        window.addEventListener('load', () => {
            addResult('页面加载', 'info', `页面加载完成 - ${new Date().toLocaleString()}`);
        });
    </script>
</body>
</html>