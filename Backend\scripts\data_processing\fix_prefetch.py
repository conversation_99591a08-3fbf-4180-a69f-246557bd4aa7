#!/usr/bin/env python
# 修复prefetch_related问题

import re

# 读取views.py文件
with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    content = f.read()

# 查找是否仍然存在prefetch_related('evaluation_set')调用
pattern = r"OperationLog\.objects\.prefetch_related\('evaluation_set'\)"
match = re.search(pattern, content)

if match:
    print(f"找到错误的prefetch_related调用，位置：{match.start()}")
    
    # 替换为正确的select_related调用
    corrected_content = content.replace(
        "OperationLog.objects.prefetch_related('evaluation_set')",
        "OperationLog.objects.select_related('user')"
    )
    
    # 写回文件
    with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
        f.write(corrected_content)
    
    print("✅ 已修复prefetch_related问题！")
else:
    print("未找到错误的prefetch_related调用，可能已经修复。")

# 检查get_queryset方法是否正确
class_pattern = r'class\s+OperationLogListView.*?def\s+get_queryset\s*\(\s*self\s*\)\s*:(.*?)def'
match = re.search(class_pattern, content, re.DOTALL)

if match:
    method_body = match.group(1)
    print("\n当前get_queryset方法内容：")
    print(method_body)
    
    # 检查是否使用了正确的select_related
    if "select_related('user')" in method_body:
        print("✓ 方法使用了正确的select_related('user')")
    else:
        print("✗ 方法未使用select_related('user')")
    
    # 检查是否包含时间范围筛选
    if "time_range" in method_body:
        print("✓ 方法包含时间范围筛选功能")
    else:
        print("✗ 方法缺少时间范围筛选功能")
else:
    print("无法找到get_queryset方法") 