# Generated by Django 4.2.7 on 2025-03-09 17:06

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('qrmanager', '0025_add_bed_timestamps'),
    ]

    operations = [
        migrations.CreateModel(
            name='APIKey',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text='API密钥用途描述', max_length=100, verbose_name='名称')),
                ('key', models.CharField(db_index=True, max_length=64, unique=True, verbose_name='密钥')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('expires_at', models.DateTimeField(blank=True, help_text='留空表示永不过期', null=True, verbose_name='过期时间')),
                ('rate_limit_day', models.IntegerField(default=1000, help_text='0表示无限制', verbose_name='每日请求限制')),
                ('rate_limit_hour', models.IntegerField(default=100, help_text='0表示无限制', verbose_name='每小时请求限制')),
                ('rate_limit_minute', models.IntegerField(default=10, help_text='0表示无限制', verbose_name='每分钟请求限制')),
                ('last_used_at', models.DateTimeField(blank=True, null=True, verbose_name='最后使用时间')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='created_api_keys', to=settings.AUTH_USER_MODEL, verbose_name='创建人')),
            ],
            options={
                'verbose_name': 'API密钥',
                'verbose_name_plural': 'API密钥',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='APILog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('endpoint', models.CharField(max_length=200, verbose_name='接口')),
                ('method', models.CharField(max_length=10, verbose_name='请求方法')),
                ('status_code', models.IntegerField(verbose_name='状态码')),
                ('status', models.CharField(choices=[('success', '成功'), ('error', '错误')], default='success', max_length=10, verbose_name='状态')),
                ('response_time', models.FloatField(default=0, verbose_name='响应时间(ms)')),
                ('remote_addr', models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('request_data', models.JSONField(blank=True, null=True, verbose_name='请求数据')),
                ('error_message', models.TextField(blank=True, verbose_name='错误信息')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='调用时间')),
                ('api_key', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='logs', to='qrmanager.apikey', verbose_name='API密钥')),
            ],
            options={
                'verbose_name': 'API调用日志',
                'verbose_name_plural': 'API调用日志',
                'ordering': ['-created_at'],
            },
        ),
    ]
