{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item active">API密钥管理</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-key me-1"></i>
            API密钥列表
            <a href="{% url 'qrmanager:apikey_create' %}" class="btn btn-primary btn-sm float-end">
                <i class="fas fa-plus"></i> 创建API密钥
            </a>
        </div>
        <div class="card-body">
            {% if messages %}
                {% for message in messages %}
                    <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                        {{ message }}
                        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                    </div>
                {% endfor %}
            {% endif %}
            
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> API密钥用于授权第三方应用访问系统API。请妥善保管密钥，不要泄露给未授权的人员。
            </div>
            
            <div class="table-responsive">
                <table class="table table-bordered table-striped" id="apikeysTable" width="100%" cellspacing="0">
                    <thead>
                        <tr>
                            <th>名称</th>
                            <th>密钥</th>
                            <th>状态</th>
                            <th>权限</th>
                            <th>资源访问</th>
                            <th>速率限制</th>
                            <th>IP限制</th>
                            <th>创建时间</th>
                            <th>过期时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for apikey in apikeys %}
                        <tr>
                            <td>{{ apikey.name }}</td>
                            <td>
                                <div class="input-group">
                                    <input type="password" class="form-control form-control-sm key-field" value="{{ apikey.key }}" readonly>
                                    <button class="btn btn-outline-secondary btn-sm toggle-key" type="button">
                                        <i class="fas fa-eye"></i>
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm copy-key" type="button" data-key="{{ apikey.key }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </td>
                            <td>
                                {% if apikey.is_active %}
                                    {% if apikey.is_expired %}
                                        <span class="badge bg-warning">已过期</span>
                                    {% else %}
                                        <span class="badge bg-success">激活</span>
                                    {% endif %}
                                {% else %}
                                    <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </td>
                            <td>
                                {% if apikey.can_read %}<span class="badge bg-info">读</span>{% endif %}
                                {% if apikey.can_write %}<span class="badge bg-warning">写</span>{% endif %}
                                {% if apikey.can_delete %}<span class="badge bg-danger">删</span>{% endif %}
                            </td>
                            <td>
                                {% if apikey.can_access_departments %}<span class="badge bg-secondary">科室</span>{% endif %}
                                {% if apikey.can_access_beds %}<span class="badge bg-secondary">床位</span>{% endif %}
                                {% if apikey.can_access_staff %}<span class="badge bg-secondary">员工</span>{% endif %}
                                {% if apikey.can_access_qrcodes %}<span class="badge bg-secondary">二维码</span>{% endif %}
                                {% if apikey.can_access_evaluations %}<span class="badge bg-secondary">评价</span>{% endif %}
                            </td>
                            <td>
                                <small>
                                    {% if apikey.rate_limit_day == 0 %}
                                        日: 无限制<br>
                                    {% else %}
                                        日: {{ apikey.rate_limit_day }}<br>
                                    {% endif %}
                                    
                                    {% if apikey.rate_limit_hour == 0 %}
                                        时: 无限制<br>
                                    {% else %}
                                        时: {{ apikey.rate_limit_hour }}<br>
                                    {% endif %}
                                    
                                    {% if apikey.rate_limit_minute == 0 %}
                                        分: 无限制
                                    {% else %}
                                        分: {{ apikey.rate_limit_minute }}
                                    {% endif %}
                                </small>
                            </td>
                            <td>
                                {% if apikey.allowed_ips %}
                                    <span class="text-truncate d-inline-block" style="max-width: 150px;" title="{{ apikey.allowed_ips }}">
                                        {{ apikey.allowed_ips }}
                                    </span>
                                {% else %}
                                    <span class="text-muted">无限制</span>
                                {% endif %}
                            </td>
                            <td>{{ apikey.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                {% if apikey.expires_at %}
                                    {{ apikey.expires_at|date:"Y-m-d H:i" }}
                                {% else %}
                                    <span class="text-muted">永不过期</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'qrmanager:apikey_update' apikey.id %}" class="btn btn-primary btn-sm">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                    <a href="{% url 'qrmanager:apikey_delete' apikey.id %}" class="btn btn-danger btn-sm">
                                        <i class="fas fa-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="10" class="text-center">暂无API密钥</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 初始化数据表格
        $('#apikeysTable').DataTable({
            language: {
                url: "{% static 'qrmanager/js/zh_CN.json' %}"
            },
            order: [[7, 'desc']]
        });
        
        // 显示/隐藏密钥
        $('.toggle-key').on('click', function() {
            var input = $(this).closest('.input-group').find('input');
            var icon = $(this).find('i');
            
            if (input.attr('type') === 'password') {
                input.attr('type', 'text');
                icon.removeClass('fa-eye').addClass('fa-eye-slash');
            } else {
                input.attr('type', 'password');
                icon.removeClass('fa-eye-slash').addClass('fa-eye');
            }
        });
        
        // 复制密钥
        $('.copy-key').on('click', function() {
            var key = $(this).data('key');
            var tempInput = $('<input>');
            $('body').append(tempInput);
            tempInput.val(key).select();
            document.execCommand('copy');
            tempInput.remove();
            
            // 显示提示
            $(this).tooltip({
                title: '已复制!',
                trigger: 'manual',
                placement: 'top'
            }).tooltip('show');
            
            // 2秒后隐藏提示
            var btn = $(this);
            setTimeout(function() {
                btn.tooltip('hide');
            }, 2000);
        });
    });
</script>
{% endblock %} 