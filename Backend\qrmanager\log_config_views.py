from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.admin.views.decorators import staff_member_required
from django.contrib import messages
from django.utils import timezone
from django.db.models import Count
from django.core.cache import cache
from django.db import connection

from .models import LoggingConfiguration, OperationLog
from .utils import LoggerHelper

@login_required
@staff_member_required
def logging_config(request):
    """日志配置管理视图"""
    # 处理表单提交
    if request.method == 'POST':
        # 获取所有配置项
        configs = LoggingConfiguration.objects.all()
        
        # 更新前的配置
        old_config = {
            config.action_type: config.is_enabled 
            for config in configs
        }
        print(f"更新前的日志配置: {old_config}")
        
        # 更新配置
        for config in configs:
            enabled = request.POST.get(f'config_{config.id}') == 'on'
            if config.is_enabled != enabled:
                config.is_enabled = enabled
                config.save()
                print(f"更新日志配置: {config.action_type} -> {enabled}")
        
        # 清除缓存，强制重新加载配置
        cache.delete('logging_config')
        print("已清除日志配置缓存")
        
        # 确认缓存已清除
        current_config = cache.get('logging_config')
        print(f"缓存中的配置: {current_config}")
        
        # 记录操作
        LoggerHelper.log_operation(
            user=request.user,
            action="system_config",
            description="更新日志记录配置",
            extra_data={
                'updated_configs': {
                    config.action_type: config.is_enabled 
                    for config in configs
                }
            }
        )
        
        messages.success(request, "日志配置已更新")
        return redirect('qrmanager:logging_config')
    
    # 获取所有配置项
    configs = LoggingConfiguration.objects.all()
    
    # 按类别分组
    grouped_configs = {
        '数据操作': [],
        '用户操作': [],
        '系统操作': [],
        '其他操作': [],
    }
    
    # 分类配置项
    for config in configs:
        if config.action_type in ['create', 'update', 'delete', 'bulk_operation']:
            grouped_configs['数据操作'].append(config)
        elif config.action_type in ['login', 'logout', 'permission_change']:
            grouped_configs['用户操作'].append(config)
        elif config.action_type in ['system_config', 'api_request', 'error']:
            grouped_configs['系统操作'].append(config)
        else:
            grouped_configs['其他操作'].append(config)
    
    # 获取日志统计信息
    # 获取各类型日志数量
    log_counts = OperationLog.objects.values('action').annotate(
        count=Count('id')
    ).order_by('-count')
    
    # 获取总日志数
    total_logs = OperationLog.objects.count()
    
    # 获取最近7天的日志数量
    recent_logs = OperationLog.objects.filter(
        created_at__gte=timezone.now() - timezone.timedelta(days=7)
    ).count()
    
    context = {
        'grouped_configs': grouped_configs,
        'log_counts': log_counts,
        'total_logs': total_logs,
        'recent_logs': recent_logs,
    }
    
    return render(request, 'qrmanager/logging_config.html', context)

@login_required
@staff_member_required
def clear_old_logs(request):
    """清理旧日志"""
    if request.method == 'POST':
        days = int(request.POST.get('days', 30))
        
        # 获取系统中最早和最晚的日志日期
        earliest_log = OperationLog.objects.order_by('created_at').first()
        latest_log = OperationLog.objects.order_by('-created_at').first()
        
        if earliest_log and latest_log:
            earliest_date = earliest_log.created_at
            latest_date = latest_log.created_at
            date_range_days = (latest_date - earliest_date).days
            print(f"系统中日志日期范围: 最早 {earliest_date.isoformat()} 到最晚 {latest_date.isoformat()}, 相差 {date_range_days} 天")
        
        # 判断是否是清除全部日志
        if days == 0:
            # 获取所有日志
            logs_to_delete = OperationLog.objects.all()
            count = logs_to_delete.count()
            print(f"准备删除所有日志: {count} 条")
        else:
            # 计算截止日期
            cutoff_date = timezone.now() - timezone.timedelta(days=days)
            # 获取要删除的日志数量
            logs_to_delete = OperationLog.objects.filter(created_at__lt=cutoff_date)
            count = logs_to_delete.count()
            # 添加调试信息
            print(f"准备删除 {count} 条日志，截止日期: {cutoff_date}")
        
        if count > 0:
            # 记录删除前的总数
            total_before = OperationLog.objects.count()
            
            # 尝试删除日志
            try:
                # 直接使用Django ORM删除
                logs_to_delete.delete()
                
                # 检查是否删除成功
                total_after = OperationLog.objects.count()
                deleted_count = total_before - total_after
                
                if deleted_count > 0:
                    print(f"成功删除了 {deleted_count} 条日志")
                    
                    # 记录操作
                    LoggerHelper.log_operation(
                        user=request.user,
                        action="delete",
                        description=f"清理{days}天前的操作日志" if days > 0 else "清理全部操作日志",
                        extra_data={
                            'days': days,
                            'count': deleted_count,
                            'cutoff_date': cutoff_date.isoformat() if days > 0 else "all"
                        }
                    )
                    
                    if days > 0:
                        messages.success(request, f"成功清理 {deleted_count} 条{days}天前的日志")
                    else:
                        messages.success(request, f"成功清理 {deleted_count} 条日志（全部日志）")
                else:
                    print("删除操作执行了，但没有日志被删除")
                    messages.warning(request, f"清理日志操作未成功，可能是权限问题或数据库限制")
            except Exception as e:
                print(f"删除日志时出错: {e}")
                messages.error(request, f"删除日志时出错: {str(e)}")
        else:
            print("没有找到需要删除的日志")
            # 获取最早的日志记录
            earliest_log = OperationLog.objects.order_by('created_at').first()
            if earliest_log:
                if days > 0:
                    earliest_days = (timezone.now() - earliest_log.created_at).days
                    earliest_hours = ((timezone.now() - earliest_log.created_at).seconds // 3600)
                    
                    if earliest_days > 0:
                        time_msg = f"{earliest_days}天"
                    else:
                        time_msg = f"{earliest_hours}小时"
                    
                    messages.warning(
                        request, 
                        f"没有找到需要删除的日志！系统中最早的日志仅有{time_msg}前，不存在{days}天前的日志记录。"
                    )
                else:
                    messages.warning(request, "系统中没有任何日志记录，无需清理全部日志。")
            else:
                messages.warning(request, "系统中没有任何日志记录，无需清理。")
        
        return redirect('qrmanager:logging_config')
    
    return redirect('qrmanager:logging_config') 