<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面 - 感谢您的评价</title>
    <style>
        body {
            font-family: 'PingFang SC', 'Helvetica Neue', Arial, sans-serif;
            background-color: #f8f9fa;
            color: #333;
            line-height: 1.6;
            padding: 20px;
            margin: 0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            border-radius: 8px;
            padding: 30px;
        }
        h1 {
            color: #28a745;
            margin-bottom: 30px;
            text-align: center;
        }
        .debug-section {
            background-color: #f0f0f0;
            border-left: 4px solid #28a745;
            padding: 15px;
            margin-bottom: 20px;
            font-family: monospace;
            white-space: pre-wrap;
            overflow-x: auto;
        }
        .success-icon {
            text-align: center;
            font-size: 60px;
            color: #28a745;
            margin-bottom: 20px;
        }
        .home-button {
            display: inline-block;
            background-color: #007bff;
            color: white;
            text-decoration: none;
            padding: 10px 20px;
            border-radius: 4px;
            margin-top: 20px;
        }
        .home-button:hover {
            background-color: #0069d9;
        }
        .buttons {
            text-align: center;
            margin-top: 30px;
        }
        .debug-title {
            color: #dc3545;
            border-bottom: 1px solid #dee2e6;
            padding-bottom: 10px;
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="success-icon">✓</div>
        <h1>感谢您的评价！</h1>
        
        <p>您的反馈已成功提交，我们非常重视您的意见。</p>
        <p>您的参与将帮助我们提升医疗服务质量，为患者提供更好的医疗体验。</p>
        
        <h2 class="debug-title">调试信息</h2>
        <div class="debug-section" id="debugInfo">
正在收集调试信息...
        </div>
        
        <div class="buttons">
            <a href="index.html" class="home-button">返回首页</a>
        </div>
    </div>

    <script>
        // 收集并显示调试信息
        document.addEventListener('DOMContentLoaded', function() {
            const debugElement = document.getElementById('debugInfo');
            let debugInfo = '';
            
            // 添加URL信息
            debugInfo += '当前页面URL: ' + window.location.href + '\n\n';
            
            // 添加引用页信息
            debugInfo += '引用页面: ' + (document.referrer || '无引用页') + '\n\n';
            
            // 添加localStorage信息
            debugInfo += 'localStorage内容:\n';
            if (window.localStorage) {
                if (localStorage.length > 0) {
                    for (let i = 0; i < localStorage.length; i++) {
                        const key = localStorage.key(i);
                        try {
                            const value = localStorage.getItem(key);
                            let displayValue = value;
                            
                            // 尝试解析JSON
                            try {
                                const jsonValue = JSON.parse(value);
                                displayValue = JSON.stringify(jsonValue, null, 2);
                            } catch (e) {}
                            
                            debugInfo += key + ': ' + displayValue + '\n';
                        } catch (e) {
                            debugInfo += key + ': [无法读取]\n';
                        }
                    }
                } else {
                    debugInfo += '无localStorage数据\n';
                }
            } else {
                debugInfo += 'localStorage不可用\n';
            }
            
            // 添加sessionStorage信息
            debugInfo += '\nsessionStorage内容:\n';
            if (window.sessionStorage) {
                if (sessionStorage.length > 0) {
                    for (let i = 0; i < sessionStorage.length; i++) {
                        const key = sessionStorage.key(i);
                        try {
                            debugInfo += key + ': ' + sessionStorage.getItem(key) + '\n';
                        } catch (e) {
                            debugInfo += key + ': [无法读取]\n';
                        }
                    }
                } else {
                    debugInfo += '无sessionStorage数据\n';
                }
            } else {
                debugInfo += 'sessionStorage不可用\n';
            }
            
            // 添加window.appData信息
            debugInfo += '\nwindow.appData内容:\n';
            if (window.appData) {
                debugInfo += JSON.stringify(window.appData, null, 2) + '\n';
            } else {
                debugInfo += '无window.appData数据\n';
            }
            
            // 添加cookie信息
            debugInfo += '\nCookie内容:\n' + document.cookie || '无Cookie数据';
            
            // 更新调试区域
            debugElement.textContent = debugInfo;
        });
    </script>
</body>
</html>