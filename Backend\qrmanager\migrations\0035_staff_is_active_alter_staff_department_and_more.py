# Generated by Django 4.2.7 on 2025-03-16 13:31

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0034_create_stafftype_and_update_staff'),
    ]

    operations = [
        migrations.AddField(
            model_name='staff',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='是否启用'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='staffs', to='qrmanager.department', verbose_name='所属部门'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='name',
            field=models.CharField(max_length=100, verbose_name='姓名'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='title',
            field=models.CharField(blank=True, max_length=100, verbose_name='职称'),
        ),
        migrations.<PERSON><PERSON><PERSON>ield(
            model_name='staff',
            name='work_number',
            field=models.Char<PERSON>ield(max_length=50, unique=True, verbose_name='工号'),
        ),
    ]
