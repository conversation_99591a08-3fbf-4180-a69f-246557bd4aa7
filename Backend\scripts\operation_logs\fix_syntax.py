#!/usr/bin/env python
# 修复views.py中的语法错误

with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 错误行号
error_line = 1813
context_lines = 20

# 打印错误上下文
start_context = max(0, error_line - context_lines)
end_context = min(len(lines), error_line + context_lines)

print(f"原始代码（行 {start_context+1} 到 {end_context}）：")
for i in range(start_context, end_context):
    print(f"{i+1}: {lines[i].rstrip()}")

# 查找get_queryset方法的开始和结束
method_start = None
for i in range(error_line, 0, -1):
    if "def get_queryset(self):" in lines[i]:
        method_start = i
        break

if method_start is not None:
    # 完全重写get_queryset方法
    print(f"\n找到get_queryset方法，开始于第{method_start+1}行")
    
    # 新的get_queryset方法
    new_method = [
        "    def get_queryset(self):\n",
        "        queryset = OperationLog.objects.select_related('user')\n",
        "\n",
        "        # 过滤条件\n",
        "        user_id = self.request.GET.get('user')\n",
        "        action = self.request.GET.get('action')\n",
        "        status = self.request.GET.get('status')\n",
        "        date_from = self.request.GET.get('date_from')\n",
        "        date_to = self.request.GET.get('date_to')\n",
        "        time_range = self.request.GET.get('time_range')\n",
        "\n",
        "        # 应用过滤器\n",
        "        if user_id and user_id.isdigit():\n",
        "            queryset = queryset.filter(user_id=user_id)\n",
        "        if action:\n",
        "            queryset = queryset.filter(action__icontains=action)\n",
        "        if status:\n",
        "            queryset = queryset.filter(status=status)\n",
        "\n",
        "        # 日期范围过滤\n",
        "        if date_from:\n",
        "            try:\n",
        "                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()\n",
        "                queryset = queryset.filter(created_at__date__gte=date_from)\n",
        "            except ValueError:\n",
        "                pass\n",
        "        if date_to:\n",
        "            try:\n",
        "                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()\n",
        "                queryset = queryset.filter(created_at__date__lte=date_to)\n",
        "            except ValueError:\n",
        "                pass\n",
        "\n",
        "        # 时间范围快捷筛选\n",
        "        if time_range:\n",
        "            now = timezone.now()\n",
        "            if time_range == 'today':\n",
        "                queryset = queryset.filter(created_at__date=now.date())\n",
        "            elif time_range == 'yesterday':\n",
        "                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))\n",
        "            elif time_range == 'this_week':\n",
        "                # 本周的开始（星期一）\n",
        "                week_start = now.date() - timezone.timedelta(days=now.weekday())\n",
        "                queryset = queryset.filter(created_at__date__gte=week_start)\n",
        "            elif time_range == 'this_month':\n",
        "                # 本月的开始（1号）\n",
        "                month_start = now.date().replace(day=1)\n",
        "                queryset = queryset.filter(created_at__date__gte=month_start)\n",
        "            elif time_range == 'last_30_days':\n",
        "                # 过去30天\n",
        "                days_30 = now.date() - timezone.timedelta(days=30)\n",
        "                queryset = queryset.filter(created_at__date__gte=days_30)\n",
        "\n",
        "        return queryset.order_by('-created_at')\n",
        "\n"
    ]
    
    # 找到方法结束的位置
    method_end = None
    bracket_count = 0
    in_method = False
    for i in range(method_start, len(lines)):
        if "def " in lines[i] and i > method_start + 1:
            method_end = i
            break
    
    if method_end is None:
        # 如果找不到下一个方法，就查找到下一个类
        for i in range(method_start, len(lines)):
            if "class " in lines[i] and i > method_start + 1:
                method_end = i
                break
    
    if method_end is not None:
        # 替换方法
        lines[method_start:method_end] = new_method
        
        # 写回文件
        with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
            f.writelines(lines)
        
        print("\n已修复语法错误并重写get_queryset方法！")
    else:
        print("\n无法找到方法的结束位置，请手动修复。")
else:
    print("\n无法找到get_queryset方法，请手动修复。") 