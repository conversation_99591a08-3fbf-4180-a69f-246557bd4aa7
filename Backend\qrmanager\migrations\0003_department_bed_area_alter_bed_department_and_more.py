# Generated by Django 4.2.7 on 2025-02-06 08:52

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0002_staff_alter_bed_options_remove_bed_bed_number_and_more'),
    ]

    operations = [
        migrations.CreateModel(
            name='Department',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, unique=True, verbose_name='科室名称')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '科室',
                'verbose_name_plural': '各科室',
                'ordering': ['name'],
            },
        ),
        migrations.AddField(
            model_name='bed',
            name='area',
            field=models.CharField(choices=[('A', 'A区'), ('B', 'B区')], default='A', max_length=1, verbose_name='区域'),
        ),
        migrations.AlterField(
            model_name='bed',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrmanager.department', verbose_name='科室'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='department',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrmanager.department', verbose_name='科室'),
        ),
    ]
