# 医院服务评价系统 - 前端详细设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-27
- **文档类型**: 前端详细设计
- **目标读者**: 前端开发工程师、UI/UX设计师

## 1. 前端架构概述

### 1.1 技术栈
- **核心语言**: 原生JavaScript (ES6+)
- **样式框架**: Bootstrap 5.3.0 + 自定义CSS
- **构建工具**: 无需构建，直接运行
- **模块化**: IIFE (立即执行函数表达式) 模式
- **状态管理**: 原生JavaScript + localStorage
- **HTTP客户端**: Fetch API

### 1.2 项目结构
```
Frontend/
├── index.html              # 主页面入口 (1,333行)
├── js/                     # JavaScript模块目录
│   ├── main.js            # 主应用控制器 (1,439行)
│   ├── apiService.js      # API通信服务 (243行)
│   ├── staffModule.js     # 工作人员管理模块 (1,509行)
│   ├── ui.js              # UI交互控制模块 (392行)
│   ├── logger.js          # 日志管理模块 (166行)
│   ├── security.js        # 前端安全模块 (117行)
│   ├── ratingModule.js    # 评分模块
│   └── apiService2.js     # 备用API服务
├── css/                   # 样式文件目录
│   ├── bootstrap.min.css  # Bootstrap框架
│   ├── custom.css         # 自定义样式
│   └── responsive.css     # 响应式样式
├── assets/                # 静态资源目录
│   ├── images/            # 图片资源
│   ├── icons/             # 图标资源
│   └── fonts/             # 字体资源
└── manifest.json          # PWA配置文件
```

### 1.3 架构设计原则
- **模块化**: 功能模块独立，职责单一
- **可维护性**: 清晰的代码结构和命名规范
- **性能优化**: 懒加载和资源优化
- **用户体验**: 流畅的交互和友好的错误提示
- **安全性**: 前端数据验证和XSS防护

## 2. 核心模块设计

### 2.1 主应用模块 (main.js)

#### 2.1.1 模块结构
```javascript
(function() {
    'use strict';
    
    // 全局应用状态
    window.appData = {
        isInitialized: false,
        qrData: null,
        evaluationData: null,
        currentStep: 'loading'
    };
    
    // 应用主控制器
    const app = {
        // 初始化方法
        init: function() { /* 应用初始化逻辑 */ },
        
        // 二维码处理
        handleQRCode: function() { /* 二维码验证逻辑 */ },
        
        // 评价提交
        submitEvaluation: function() { /* 评价提交逻辑 */ },
        
        // 错误处理
        handleError: function() { /* 错误处理逻辑 */ }
    };
    
    // 导出全局对象
    window.app = app;
})();
```

#### 2.1.2 核心功能实现

**应用初始化流程**:
```javascript
init: function() {
    console.log('开始初始化医院服务评价系统...');
    
    // 1. 检查浏览器兼容性
    if (!this.checkBrowserSupport()) {
        this.showError('浏览器版本过低，请升级浏览器');
        return;
    }
    
    // 2. 初始化各个模块
    this.initializeModules();
    
    // 3. 绑定事件监听器
    this.bindEventListeners();
    
    // 4. 处理URL参数
    this.handleURLParameters();
    
    // 5. 设置应用状态
    window.appData.isInitialized = true;
    
    console.log('系统初始化完成');
}
```

**二维码验证处理**:
```javascript
handleQRCodeVerification: function(encryptedParam) {
    return new Promise(async (resolve, reject) => {
        try {
            // 显示加载状态
            ui.showLoading();
            
            // 调用API验证二维码
            const response = await apiService.verifyQRCode(encryptedParam);
            
            if (response.status === 'success') {
                // 保存验证结果
                window.appData.qrData = response.data;
                
                // 初始化工作人员模块
                staffModule.initialize(response.data.staff, response.data.staff_types);
                
                // 显示评价表单
                this.showEvaluationForm();
                
                resolve(response.data);
            } else {
                throw new Error(response.message || '二维码验证失败');
            }
        } catch (error) {
            logger.error('二维码验证失败:', error);
            this.handleError(error, '二维码验证失败，请重新扫描');
            reject(error);
        } finally {
            ui.hideLoading();
        }
    });
}
```

**评价提交处理**:
```javascript
submitEvaluation: function() {
    return new Promise(async (resolve, reject) => {
        try {
            // 1. 验证表单数据
            const validationResult = this.validateEvaluationForm();
            if (!validationResult.isValid) {
                ui.showError(validationResult.message);
                return;
            }
            
            // 2. 构建提交数据
            const evaluationData = this.buildEvaluationData();
            
            // 3. 显示提交状态
            ui.showLoading();
            
            // 4. 调用API提交评价
            const response = await apiService.submitEvaluation(evaluationData);
            
            if (response.status === 'success') {
                // 保存提交状态
                sessionStorage.setItem('evaluationSubmitted', 'true');
                
                // 显示成功页面
                this.showSuccessPage(response.data);
                
                resolve(response.data);
            } else {
                throw new Error(response.message || '评价提交失败');
            }
        } catch (error) {
            logger.error('评价提交失败:', error);
            this.handleError(error, '评价提交失败，请重试');
            reject(error);
        } finally {
            ui.hideLoading();
        }
    });
}
```

### 2.2 API服务模块 (apiService.js)

#### 2.2.1 模块结构
```javascript
(function() {
    'use strict';
    
    // API配置
    const API_CONFIG = {
        baseURL: window.location.origin,
        timeout: 30000,
        retryCount: 3,
        retryDelay: 1000
    };
    
    // API服务对象
    const apiService = {
        // 通用请求方法
        request: function(url, options) { /* 通用HTTP请求 */ },
        
        // 二维码验证
        verifyQRCode: function(qrParam) { /* 二维码验证API */ },
        
        // 评价提交
        submitEvaluation: function(data) { /* 评价提交API */ },
        
        // 错误处理
        handleApiError: function(error) { /* API错误处理 */ }
    };
    
    // 导出全局对象
    window.apiService = apiService;
})();
```

#### 2.2.2 核心功能实现

**通用请求方法**:
```javascript
request: function(url, options = {}) {
    return new Promise(async (resolve, reject) => {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            timeout: API_CONFIG.timeout
        };
        
        const finalOptions = { ...defaultOptions, ...options };
        
        // 添加CSRF令牌
        const csrfToken = this.getCSRFToken();
        if (csrfToken && ['POST', 'PUT', 'PATCH', 'DELETE'].includes(finalOptions.method)) {
            finalOptions.headers['X-CSRFToken'] = csrfToken;
        }
        
        let retryCount = 0;
        
        while (retryCount <= API_CONFIG.retryCount) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), finalOptions.timeout);
                
                finalOptions.signal = controller.signal;
                
                const response = await fetch(url, finalOptions);
                clearTimeout(timeoutId);
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const data = await response.json();
                resolve(data);
                return;
                
            } catch (error) {
                retryCount++;
                
                if (retryCount > API_CONFIG.retryCount) {
                    logger.error(`API请求失败 (${retryCount}次重试后): ${url}`, error);
                    reject(this.handleApiError(error));
                    return;
                }
                
                // 等待后重试
                await new Promise(resolve => setTimeout(resolve, API_CONFIG.retryDelay * retryCount));
            }
        }
    });
}
```

**二维码验证API**:
```javascript
verifyQRCode: function(qrParam) {
    logger.info('开始验证二维码参数:', qrParam.substring(0, 20) + '...');
    
    return this.request('/api/v1/public/qrcode/verify/', {
        method: 'POST',
        body: JSON.stringify({
            qr_param: qrParam
        })
    }).then(response => {
        logger.info('二维码验证成功');
        return response;
    }).catch(error => {
        logger.error('二维码验证失败:', error);
        throw error;
    });
}
```

### 2.3 工作人员管理模块 (staffModule.js)

#### 2.3.1 模块结构
```javascript
(function() {
    'use strict';
    
    // 评价管理器类
    class EvaluationManager {
        constructor() {
            this.satisfiedStaff = new Set();
            this.unsatisfiedStaff = new Set();
            this.maxSatisfied = 3;
            this.maxUnsatisfied = 3;
        }
        
        // 添加满意评价
        addSatisfied(staffId) { /* 添加满意评价逻辑 */ }
        
        // 添加不满意评价
        addUnsatisfied(staffId) { /* 添加不满意评价逻辑 */ }
        
        // 验证评价数量
        validateCounts() { /* 验证评价数量逻辑 */ }
    }
    
    // 工作人员模块
    const staffModule = {
        evaluationManager: new EvaluationManager(),
        staffData: [],
        staffTypes: [],
        
        // 初始化模块
        initialize: function(staff, staffTypes) { /* 初始化逻辑 */ },
        
        // 渲染工作人员列表
        renderStaffList: function() { /* 渲染逻辑 */ },
        
        // 处理工作人员选择
        handleStaffSelection: function() { /* 选择处理逻辑 */ }
    };
    
    // 导出全局对象
    window.staffModule = staffModule;
})();
```

#### 2.3.2 评价管理器实现
```javascript
class EvaluationManager {
    constructor() {
        this.satisfiedStaff = new Set();
        this.unsatisfiedStaff = new Set();
        this.maxSatisfied = 3;
        this.maxUnsatisfied = 3;
        this.staffData = new Map(); // 存储工作人员详细信息
    }
    
    addSatisfied(staffId, staffInfo) {
        // 检查是否已在不满意列表中
        if (this.unsatisfiedStaff.has(staffId)) {
            this.unsatisfiedStaff.delete(staffId);
        }
        
        // 检查满意列表数量限制
        if (this.satisfiedStaff.size >= this.maxSatisfied && !this.satisfiedStaff.has(staffId)) {
            throw new Error(`最多只能选择${this.maxSatisfied}个满意的工作人员`);
        }
        
        this.satisfiedStaff.add(staffId);
        this.staffData.set(staffId, { ...staffInfo, evaluation: 'satisfied' });
        
        logger.info(`添加满意评价: ${staffInfo.name} (${staffId})`);
        this.updateUI();
    }
    
    addUnsatisfied(staffId, staffInfo) {
        // 检查是否已在满意列表中
        if (this.satisfiedStaff.has(staffId)) {
            this.satisfiedStaff.delete(staffId);
        }
        
        // 检查不满意列表数量限制
        if (this.unsatisfiedStaff.size >= this.maxUnsatisfied && !this.unsatisfiedStaff.has(staffId)) {
            throw new Error(`最多只能选择${this.maxUnsatisfied}个不满意的工作人员`);
        }
        
        this.unsatisfiedStaff.add(staffId);
        this.staffData.set(staffId, { ...staffInfo, evaluation: 'unsatisfied' });
        
        logger.info(`添加不满意评价: ${staffInfo.name} (${staffId})`);
        this.updateUI();
    }
    
    removeStaff(staffId) {
        this.satisfiedStaff.delete(staffId);
        this.unsatisfiedStaff.delete(staffId);
        this.staffData.delete(staffId);
        
        logger.info(`移除工作人员评价: ${staffId}`);
        this.updateUI();
    }
    
    getEvaluationData() {
        const evaluations = [];
        
        // 添加满意评价
        this.satisfiedStaff.forEach(staffId => {
            const staffInfo = this.staffData.get(staffId);
            evaluations.push({
                staff_id: parseInt(staffId),
                is_satisfied: true,
                staff_name: staffInfo.name,
                staff_type: staffInfo.staff_type
            });
        });
        
        // 添加不满意评价
        this.unsatisfiedStaff.forEach(staffId => {
            const staffInfo = this.staffData.get(staffId);
            evaluations.push({
                staff_id: parseInt(staffId),
                is_satisfied: false,
                staff_name: staffInfo.name,
                staff_type: staffInfo.staff_type
            });
        });
        
        return evaluations;
    }
    
    validateEvaluations() {
        const totalEvaluations = this.satisfiedStaff.size + this.unsatisfiedStaff.size;
        
        if (totalEvaluations === 0) {
            return {
                isValid: false,
                message: '请至少选择一个工作人员进行评价'
            };
        }
        
        return {
            isValid: true,
            message: '评价数据验证通过'
        };
    }
}
```

### 2.4 UI控制模块 (ui.js)

#### 2.4.1 模块结构
```javascript
(function() {
    'use strict';
    
    // UI元素引用
    const elements = {
        loadingIndicator: document.getElementById('loadingIndicator'),
        errorContainer: document.getElementById('errorContainer'),
        successContainer: document.getElementById('successContainer'),
        feedbackForm: document.getElementById('feedbackForm'),
        submitButton: document.getElementById('submitButton')
    };
    
    // UI控制对象
    const ui = {
        // 显示/隐藏加载状态
        showLoading: function() { /* 显示加载逻辑 */ },
        hideLoading: function() { /* 隐藏加载逻辑 */ },
        
        // 显示/隐藏错误信息
        showError: function(message, duration) { /* 显示错误逻辑 */ },
        hideError: function() { /* 隐藏错误逻辑 */ },
        
        // 显示/隐藏成功信息
        showSuccess: function(message, duration) { /* 显示成功逻辑 */ },
        hideSuccess: function() { /* 隐藏成功逻辑 */ },
        
        // 模态框控制
        showModal: function(content, options) { /* 显示模态框逻辑 */ },
        hideModal: function() { /* 隐藏模态框逻辑 */ }
    };
    
    // 导出全局对象
    window.ui = ui;
})();
```

#### 2.4.2 核心UI功能实现

**加载状态管理**:
```javascript
showLoading: function() {
    // 检查是否已经提交过评价
    const isSubmitted = sessionStorage.getItem('evaluationSubmitted') === 'true';
    if (isSubmitted) {
        console.log('用户已经提交过评价，不显示加载指示器');
        return;
    }
    
    if (elements.loadingIndicator) {
        // 更新加载文本
        const loadingText = elements.loadingIndicator.querySelector('.loading-text');
        if (loadingText) {
            loadingText.textContent = isSubmitted ? 
                '您已经提交过评价，请重新扫描二维码' : 
                '请扫描二维码';
        }
        
        elements.loadingIndicator.classList.remove('hidden');
    }
    
    // 禁用提交按钮
    if (elements.submitButton) {
        elements.submitButton.disabled = true;
        elements.submitButton.classList.add('loading');
    }
}
```

**错误信息显示**:
```javascript
showError: function(message, duration = 3000) {
    if (!elements.errorContainer || !elements.errorMessage) return;
    
    // 设置错误消息
    elements.errorMessage.textContent = message;
    
    // 显示错误容器
    elements.errorContainer.classList.remove('hidden');
    elements.errorContainer.classList.add('fade-in');
    
    // 记录错误日志
    logger.error('显示错误信息:', message);
    
    // 自动隐藏
    if (duration > 0) {
        setTimeout(() => {
            this.hideError();
        }, duration);
    }
}
```

## 3. 状态管理设计

### 3.1 全局状态结构
```javascript
window.appData = {
    // 应用状态
    isInitialized: false,
    currentStep: 'loading', // loading, form, success, error
    
    // 二维码数据
    qrData: {
        temp_token: null,
        expires_at: null,
        bed: null,
        department: null,
        staff: [],
        staff_types: []
    },
    
    // 评价数据
    evaluationData: {
        comment: '',
        hospital_number: '',
        phone_number: '',
        staff_evaluations: []
    },
    
    // 用户界面状态
    uiState: {
        isLoading: false,
        hasError: false,
        errorMessage: '',
        isSubmitted: false
    },
    
    // 最后的错误信息
    lastError: null
};
```

### 3.2 状态管理方法
```javascript
const stateManager = {
    // 更新状态
    setState: function(path, value) {
        const keys = path.split('.');
        let current = window.appData;
        
        for (let i = 0; i < keys.length - 1; i++) {
            if (!current[keys[i]]) {
                current[keys[i]] = {};
            }
            current = current[keys[i]];
        }
        
        current[keys[keys.length - 1]] = value;
        
        // 触发状态变更事件
        this.notifyStateChange(path, value);
    },
    
    // 获取状态
    getState: function(path) {
        const keys = path.split('.');
        let current = window.appData;
        
        for (const key of keys) {
            if (current[key] === undefined) {
                return undefined;
            }
            current = current[key];
        }
        
        return current;
    },
    
    // 状态变更通知
    notifyStateChange: function(path, value) {
        logger.debug(`状态变更: ${path} = ${JSON.stringify(value)}`);
        
        // 可以在这里添加状态变更的副作用处理
        if (path === 'uiState.isLoading') {
            value ? ui.showLoading() : ui.hideLoading();
        }
    }
};
```

## 4. 事件处理设计

### 4.1 事件绑定策略
```javascript
const eventManager = {
    // 事件监听器注册表
    listeners: new Map(),
    
    // 绑定事件
    bind: function(element, event, handler, options = {}) {
        if (!element) return;
        
        const wrappedHandler = (e) => {
            try {
                handler.call(this, e);
            } catch (error) {
                logger.error('事件处理器执行失败:', error);
            }
        };
        
        element.addEventListener(event, wrappedHandler, options);
        
        // 记录事件监听器
        const key = `${element.id || 'anonymous'}_${event}`;
        this.listeners.set(key, { element, event, handler: wrappedHandler });
    },
    
    // 解绑事件
    unbind: function(element, event) {
        const key = `${element.id || 'anonymous'}_${event}`;
        const listener = this.listeners.get(key);
        
        if (listener) {
            listener.element.removeEventListener(listener.event, listener.handler);
            this.listeners.delete(key);
        }
    },
    
    // 清理所有事件监听器
    cleanup: function() {
        this.listeners.forEach(listener => {
            listener.element.removeEventListener(listener.event, listener.handler);
        });
        this.listeners.clear();
    }
};
```

### 4.2 表单事件处理
```javascript
const formEventHandlers = {
    // 表单提交处理
    handleFormSubmit: function(event) {
        event.preventDefault();
        
        logger.info('开始处理表单提交');
        
        // 验证表单
        const validation = this.validateForm();
        if (!validation.isValid) {
            ui.showError(validation.message);
            return;
        }
        
        // 提交评价
        app.submitEvaluation()
            .then(result => {
                logger.info('评价提交成功:', result);
            })
            .catch(error => {
                logger.error('评价提交失败:', error);
            });
    },
    
    // 输入验证处理
    handleInputValidation: function(event) {
        const input = event.target;
        const value = input.value.trim();
        
        // 实时验证
        if (input.type === 'tel') {
            this.validatePhoneNumber(input, value);
        } else if (input.name === 'hospital_number') {
            this.validateHospitalNumber(input, value);
        } else if (input.name === 'comment') {
            this.validateComment(input, value);
        }
    },
    
    // 电话号码验证
    validatePhoneNumber: function(input, value) {
        const phoneRegex = /^1[3-9]\d{9}$/;
        const isValid = !value || phoneRegex.test(value);
        
        this.updateInputValidation(input, isValid, 
            isValid ? '' : '请输入正确的手机号码');
    },
    
    // 住院号验证
    validateHospitalNumber: function(input, value) {
        const isValid = !value || (value.length >= 6 && value.length <= 20);
        
        this.updateInputValidation(input, isValid,
            isValid ? '' : '住院号长度应在6-20位之间');
    },
    
    // 评价内容验证
    validateComment: function(input, value) {
        const isValid = value.length >= 10 && value.length <= 1000;
        
        this.updateInputValidation(input, isValid,
            isValid ? '' : '评价内容应在10-1000字之间');
    },
    
    // 更新输入验证状态
    updateInputValidation: function(input, isValid, message) {
        const feedbackElement = input.parentNode.querySelector('.invalid-feedback');
        
        if (isValid) {
            input.classList.remove('is-invalid');
            input.classList.add('is-valid');
        } else {
            input.classList.remove('is-valid');
            input.classList.add('is-invalid');
        }
        
        if (feedbackElement) {
            feedbackElement.textContent = message;
        }
    }
};
```

## 5. 性能优化设计

### 5.1 资源加载优化
```javascript
const performanceOptimizer = {
    // 懒加载图片
    lazyLoadImages: function() {
        const images = document.querySelectorAll('img[data-src]');
        
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.removeAttribute('data-src');
                    observer.unobserve(img);
                }
            });
        });
        
        images.forEach(img => imageObserver.observe(img));
    },
    
    // 预加载关键资源
    preloadCriticalResources: function() {
        const criticalResources = [
            '/css/bootstrap.min.css',
            '/js/main.js',
            '/js/apiService.js'
        ];
        
        criticalResources.forEach(resource => {
            const link = document.createElement('link');
            link.rel = 'preload';
            link.href = resource;
            link.as = resource.endsWith('.css') ? 'style' : 'script';
            document.head.appendChild(link);
        });
    },
    
    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },
    
    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }
};
```

### 5.2 内存管理
```javascript
const memoryManager = {
    // 清理定时器
    timers: new Set(),
    
    // 创建定时器
    createTimer: function(callback, delay, isInterval = false) {
        const timer = isInterval ? 
            setInterval(callback, delay) : 
            setTimeout(callback, delay);
        
        this.timers.add(timer);
        return timer;
    },
    
    // 清理定时器
    clearTimer: function(timer) {
        clearTimeout(timer);
        clearInterval(timer);
        this.timers.delete(timer);
    },
    
    // 清理所有定时器
    clearAllTimers: function() {
        this.timers.forEach(timer => {
            clearTimeout(timer);
            clearInterval(timer);
        });
        this.timers.clear();
    },
    
    // 页面卸载时清理
    cleanup: function() {
        this.clearAllTimers();
        eventManager.cleanup();
        
        // 清理全局状态
        window.appData = null;
        
        logger.info('内存清理完成');
    }
};

// 页面卸载时自动清理
window.addEventListener('beforeunload', () => {
    memoryManager.cleanup();
});
```

---

**文档维护**: 前端开发组  
**审核状态**: 已审核  
**更新频率**: 前端架构变更时同步更新