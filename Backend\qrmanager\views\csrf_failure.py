"""
CSRF失败处理视图
提供友好的错误信息和解决方案
"""

from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
import logging

logger = logging.getLogger(__name__)

@csrf_exempt
@require_http_methods(["GET", "POST"])
def csrf_failure(request, reason=""):
    """
    CSRF验证失败处理
    
    Args:
        request: HTTP请求对象
        reason: 失败原因
    
    Returns:
        JsonResponse: 包含错误信息和解决方案的JSON响应
    """
    
    # 记录CSRF失败日志
    logger.warning(f"CSRF验证失败: {reason}, IP: {request.META.get('REMOTE_ADDR')}, "
                  f"User-Agent: {request.META.get('HTTP_USER_AGENT')}")
    
    # 检查请求类型
    is_ajax = request.headers.get('X-Requested-With') == 'XMLHttpRequest'
    is_api = request.path.startswith('/api/') or request.path.startswith('/service/')
    
    # 构建错误响应
    error_data = {
        'status': 'error',
        'error_code': 'CSRF_FAILURE',
        'message': '安全验证失败，请刷新页面后重试',
        'reason': reason,
        'solutions': [
            '刷新页面重新获取安全令牌',
            '清除浏览器缓存和Cookie',
            '确保JavaScript已启用',
            '检查网络连接是否稳定'
        ],
        'debug_info': {
            'path': request.path,
            'method': request.method,
            'has_csrf_cookie': 'csrftoken' in request.COOKIES,
            'has_csrf_header': 'HTTP_X_CSRFTOKEN' in request.META,
            'referer': request.META.get('HTTP_REFERER', ''),
            'origin': request.META.get('HTTP_ORIGIN', ''),
        } if hasattr(request, 'user') and request.user.is_staff else {}
    }
    
    # 如果是API请求或AJAX请求，返回JSON
    if is_ajax or is_api or request.content_type == 'application/json':
        response = JsonResponse(error_data, status=403)
        
        # 添加CORS头部
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, X-CSRFToken, Authorization'
        
        return response
    
    # 对于普通页面请求，返回HTML错误页面
    from django.shortcuts import render
    from django.http import HttpResponseForbidden
    
    context = {
        'error_message': error_data['message'],
        'solutions': error_data['solutions'],
        'reason': reason
    }
    
    try:
        return render(request, 'errors/csrf_failure.html', context, status=403)
    except:
        # 如果模板不存在，返回简单的HTML响应
        html_content = f"""
        <!DOCTYPE html>
        <html lang="zh-CN">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>安全验证失败</title>
            <style>
                body {{ font-family: Arial, sans-serif; margin: 40px; background: #f5f5f5; }}
                .container {{ max-width: 600px; margin: 0 auto; background: white; padding: 30px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }}
                .error {{ color: #d32f2f; margin-bottom: 20px; }}
                .solutions {{ background: #e3f2fd; padding: 15px; border-radius: 4px; margin: 20px 0; }}
                .btn {{ display: inline-block; padding: 10px 20px; background: #1976d2; color: white; text-decoration: none; border-radius: 4px; margin: 10px 5px 0 0; }}
                .btn:hover {{ background: #1565c0; }}
            </style>
        </head>
        <body>
            <div class="container">
                <h1>🔒 安全验证失败</h1>
                <div class="error">
                    <p><strong>错误信息：</strong>{error_data['message']}</p>
                    <p><strong>失败原因：</strong>{reason}</p>
                </div>
                
                <div class="solutions">
                    <h3>解决方案：</h3>
                    <ul>
                        {''.join([f'<li>{solution}</li>' for solution in error_data['solutions']])}
                    </ul>
                </div>
                
                <div>
                    <a href="javascript:location.reload()" class="btn">刷新页面</a>
                    <a href="javascript:history.back()" class="btn">返回上页</a>
                </div>
                
                <script>
                    // 自动刷新CSRF token
                    setTimeout(function() {{
                        if (window.CSRFManager) {{
                            window.CSRFManager.clearToken();
                            window.CSRFManager.getToken().then(function() {{
                                console.log('CSRF token已刷新');
                            }});
                        }}
                    }}, 1000);
                </script>
            </div>
        </body>
        </html>
        """
        
        return HttpResponseForbidden(html_content)


def get_csrf_token_view(request):
    """
    获取CSRF token的API端点
    """
    from django.middleware.csrf import get_token
    
    token = get_token(request)
    
    return JsonResponse({
        'status': 'success',
        'csrf_token': token,
        'cookie_name': 'csrftoken'
    })
