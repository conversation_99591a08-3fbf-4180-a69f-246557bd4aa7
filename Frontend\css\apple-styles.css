/**
 * 苹果风格样式表 - 移动优化版
 * 提供苹果设计风格的样式，适配移动设备
 */

:root {
  /* 颜色变量 */
  --apple-blue: #007AFF;
  --apple-blue-dark: #0062CC;
  --apple-red: #FF3B30;
  --apple-green: #34C759;
  --apple-yellow: #FFCC00;
  --apple-purple: #AF52DE;
  --apple-gray: #8E8E93;
  --apple-light-gray: #F2F2F7;
  --apple-dark-gray: #1C1C1E;
  
  /* 间距变量 */
  --apple-spacing-xs: 4px;
  --apple-spacing-sm: 8px;
  --apple-spacing-md: 12px;
  --apple-spacing-lg: 16px;
  --apple-spacing-xl: 24px;
  
  /* 圆角和阴影 */
  --apple-border-radius: 12px;
  --apple-border-radius-lg: 16px;
  --apple-shadow: 0 4px 16px rgba(0, 0, 0, 0.12);
  
  /* 字体 */
  --apple-font: -apple-system, BlinkMacSystemFont, "SF Pro Text", "Helvetica Neue", Arial, sans-serif;
}

/* 基础样式覆盖 */
body {
  font-family: var(--apple-font);
  background-color: var(--apple-light-gray);
  color: var(--apple-dark-gray);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Apple风格卡片 */
.apple-card {
  background: white;
  border-radius: var(--apple-border-radius);
  padding: var(--apple-spacing-lg);
  box-shadow: var(--apple-shadow);
  margin-bottom: var(--apple-spacing-lg);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.apple-card:active {
  transform: scale(0.98);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

/* 表单元素样式 */
.apple-input,
.form-group input,
.form-group textarea,
.form-group select {
  border: none;
  border-radius: var(--apple-border-radius);
  background-color: rgba(0, 0, 0, 0.05);
  padding: 14px 16px;
  font-size: 17px;
  font-family: var(--apple-font);
  width: 100%;
  transition: background-color 0.2s ease;
  -webkit-appearance: none;
  color: var(--apple-dark-gray);
  margin-bottom: var(--apple-spacing-md);
}

.apple-input:focus,
.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
  background-color: rgba(0, 0, 0, 0.07);
  outline: none;
}

/* iOS样式选择器 */
.apple-select,
.form-group select {
  border: none;
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%238E8E93" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
  background-repeat: no-repeat;
  background-position: right 16px center;
  background-size: 16px;
  padding-right: 40px;
}

/* 按钮样式 */
.apple-button,
.submit-btn {
  border: none;
  border-radius: 12px;
  background-color: var(--apple-blue);
  color: white;
  font-weight: 600;
  font-size: 17px;
  padding: 14px 20px;
  text-align: center;
  transition: all 0.2s ease;
  cursor: pointer;
  -webkit-tap-highlight-color: transparent;
  box-shadow: 0 2px 10px rgba(0, 123, 255, 0.3);
  width: 100%;
}

/* 触摸反馈 */
.apple-button:active,
.submit-btn:active {
  transform: scale(0.98);
  background-color: var(--apple-blue-dark);
}

.apple-button:disabled,
.submit-btn:disabled {
  background-color: var(--apple-gray);
  opacity: 0.5;
}

/* 标签和标题 */
.apple-label,
.form-group label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--apple-gray);
  margin-bottom: var(--apple-spacing-xs);
}

.apple-title,
.section h2 {
  font-size: 22px;
  font-weight: 600;
  color: var(--apple-dark-gray);
  margin-bottom: var(--apple-spacing-md);
}

/* 评分卡片样式 */
.rating-option {
  background-color: white;
  border-radius: var(--apple-border-radius);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
  transition: all 0.15s ease;
  border: 2px solid transparent;
}

.rating-option:active {
  transform: scale(0.98);
}

.rating-option.active {
  border-color: var(--apple-blue);
  background-color: rgba(0, 122, 255, 0.05);
}

/* 移动端触控适配 */
@media (max-width: 480px) {
  .apple-button,
  .submit-btn {
    padding: 16px 20px;
    font-size: 18px; /* 更大的字体提高可读性 */
  }
  
  .apple-card,
  .section {
    padding: var(--apple-spacing-lg) var(--apple-spacing-md);
  }
  
  /* 触控友好的大小 */
  .apple-input,
  .form-group input,
  .form-group select,
  .form-group textarea {
    padding: 16px;
    font-size: 18px;
  }
  
  /* 确保所有可点击元素至少48px高度 */
  .staff-card,
  .rating-option,
  .submit-btn,
  select,
  button {
    min-height: 48px;
  }
  
  /* 表单组间距调整 */
  .form-group {
    margin-bottom: var(--apple-spacing-xl);
  }
  
  /* 增大输入框间距 */
  .apple-input + .apple-input,
  .form-group + .form-group {
    margin-top: var(--apple-spacing-lg);
  }
}

/* 安全距离适配（适配iPhone X及以上刘海屏） */
@supports (padding: max(0px)) {
  .app-container {
    padding-left: max(15px, env(safe-area-inset-left));
    padding-right: max(15px, env(safe-area-inset-right));
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
  
  .app-footer {
    padding-bottom: max(20px, env(safe-area-inset-bottom));
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --apple-light-gray: #1C1C1E;
    --apple-dark-gray: #F2F2F7;
  }
  
  body {
    background-color: #000;
    color: #fff;
  }
  
  .apple-card,
  .section,
  .rating-section {
    background-color: #1C1C1E;
  }
  
  .apple-input,
  .form-group input,
  .form-group textarea,
  .form-group select {
    background-color: rgba(255, 255, 255, 0.1);
    color: #fff;
  }
  
  .apple-input:focus,
  .form-group input:focus,
  .form-group textarea:focus,
  .form-group select:focus {
    background-color: rgba(255, 255, 255, 0.15);
  }
} 