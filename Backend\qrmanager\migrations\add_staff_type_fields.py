from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0046_remove_staff_evaluation'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff1_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员1 类型'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff2_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员2 类型'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff3_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员3 类型'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff1_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员1 类型'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff2_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员2 类型'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff3_type',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员3 类型'),
        ),
    ] 