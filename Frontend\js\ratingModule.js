/**
 * 星级评分模块 - 简化版
 */
(function() {
    // 星级评分模块命名空间
    window.ratingModule = {
        // 模块初始化标志
        isInitialized: false,

        /**
         * 初始化星级评分模块
         */
        init: function() {
            // 防止重复初始化
            if (this.isInitialized) {
                console.log('星级评分模块已经初始化，跳过重复初始化');
                return;
            }

            console.log('初始化星级评分模块...');

            // 获取DOM元素
            const starsContainer = document.querySelector('.stars');
            const stars = document.querySelectorAll('.star');
            const ratingInput = document.getElementById('hospitalRating');
            const ratingLabels = document.querySelectorAll('.rating-label');
            const commentTextarea = document.getElementById('commentTextarea');

            if (!starsContainer || !stars.length || !ratingInput || !ratingLabels.length) {
                console.error('星级评分组件初始化失败：找不到必要的DOM元素');
                return;
            }

            // 初始化评分值
            const initialRating = parseInt(ratingInput.value) || 0;
            this.updateRating(initialRating);

            // 使用事件委托处理星星点击事件
            starsContainer.addEventListener('click', (e) => {
                const star = e.target.closest('.star');
                if (!star) return;

                const value = parseInt(star.getAttribute('data-value'));
                console.log('点击了星星，评分：', value); // 调试日志

                // 检查当前评分值
                const currentValue = parseInt(ratingInput.value) || 0;

                // 如果点击的星星已经是激活状态（当前评分值等于点击的星星的值），则取消评分
                if (currentValue === value) {
                    // 设置评分值为0
                    ratingInput.value = 0;
                    this.updateRating(0);
                    console.log('取消评分，评分值设置为0'); // 调试日志
                } else {
                    // 正常设置评分值
                    ratingInput.value = value;
                    this.updateRating(value);

                    // 添加点击动画类
                    star.classList.add('clicked');

                    // 移除点击动画类（延迟以确保动画完成）
                    setTimeout(() => {
                        star.classList.remove('clicked');
                    }, 600);
                }

                // 触发触觉反馈（如果设备支持）
                if (window.navigator && window.navigator.vibrate) {
                    window.navigator.vibrate(50);
                }

                // 触发change事件
                const event = new Event('change');
                ratingInput.dispatchEvent(event);
            });

            // 处理评分标签点击事件
            ratingLabels.forEach(label => {
                label.addEventListener('click', (e) => {
                    const value = parseInt(e.target.getAttribute('data-value'));
                    console.log('点击了评分标签，评分：', value); // 调试日志

                    // 检查当前评分值
                    const currentValue = parseInt(ratingInput.value) || 0;

                    // 如果点击的标签已经是激活状态（当前评分值等于点击的标签的值），则取消评分
                    if (currentValue === value) {
                        // 设置评分值为0
                        ratingInput.value = 0;
                        this.updateRating(0);
                        console.log('取消评分，评分值设置为0'); // 调试日志
                    } else {
                        // 正常设置评分值
                        ratingInput.value = value;
                        this.updateRating(value);
                    }

                    // 触发change事件
                    const event = new Event('change');
                    ratingInput.dispatchEvent(event);
                });
            });

            // 添加滑动效果
            // 滑动相关变量
            let isDragging = false;

            // 触摸跟踪变量
            let touchStartTime = 0;
            let touchStartX = 0;
            let touchStartY = 0;
            let touchMoved = false;

            // 触摸阈值配置（放宽条件，让轻触更容易被识别）
            const TOUCH_TIME_THRESHOLD = 500; // 500ms内认为是轻触
            const TOUCH_MOVE_THRESHOLD = 20;  // 20px内认为没有移动

            // 处理鼠标按下事件
            starsContainer.addEventListener('mousedown', (e) => {
                // 只有在星星容器上按下鼠标才开始滑动
                if (e.target.closest('.star') || e.target === starsContainer) {
                    isDragging = true;
                    // 阻止默认行为，防止文本选择
                    e.preventDefault();
                }
            });

            // 处理触摸开始事件 - 简化版本，更好的移动端兼容性
            starsContainer.addEventListener('touchstart', (e) => {
                // 只有在星星容器上触摸才记录触摸信息
                if (e.target.closest('.star') || e.target === starsContainer) {
                    try {
                        const touch = e.touches[0];
                        touchStartTime = Date.now();
                        touchStartX = touch.clientX;
                        touchStartY = touch.clientY;
                        touchMoved = false;

                        console.log('触摸开始，位置:', touchStartX, touchStartY); // 调试日志
                    } catch (error) {
                        console.log('触摸开始事件处理错误:', error);
                        // 如果触摸事件有问题，直接标记为简单触摸
                        touchStartTime = Date.now();
                        touchMoved = false;
                    }
                }
            }, { passive: true });

            // 处理鼠标移动事件
            document.addEventListener('mousemove', (e) => {
                if (!isDragging) return;

                // 获取鼠标相对于星星容器的位置
                const containerRect = starsContainer.getBoundingClientRect();
                const relativeX = e.clientX - containerRect.left;

                // 计算评分值
                const value = this.calculateRatingFromPosition(relativeX, containerRect.width, stars.length);

                // 更新评分
                this.updateRating(value);
                ratingInput.value = value;
            });

            // 处理触摸移动事件 - 简化版本
            document.addEventListener('touchmove', (e) => {
                if (touchStartTime === 0) return; // 没有触摸开始就不处理移动

                try {
                    const touch = e.touches[0];
                    const moveX = Math.abs(touch.clientX - touchStartX);
                    const moveY = Math.abs(touch.clientY - touchStartY);
                    const totalMove = Math.sqrt(moveX * moveX + moveY * moveY);

                    // 如果移动距离超过阈值，标记为已移动并开始拖拽
                    if (totalMove > TOUCH_MOVE_THRESHOLD) {
                        touchMoved = true;
                        if (!isDragging) {
                            isDragging = true;
                            console.log('开始拖拽模式'); // 调试日志
                        }
                    }

                    // 只有在拖拽模式下才更新评分
                    if (isDragging) {
                        // 获取触摸点相对于星星容器的位置
                        const containerRect = starsContainer.getBoundingClientRect();
                        const relativeX = touch.clientX - containerRect.left;

                        // 计算评分值
                        const value = this.calculateRatingFromPosition(relativeX, containerRect.width, stars.length);

                        // 更新评分
                        this.updateRating(value);
                        ratingInput.value = value;

                        // 阻止默认行为，防止滚动
                        e.preventDefault();
                    }
                } catch (error) {
                    console.log('触摸移动事件处理错误:', error);
                    // 如果有错误，标记为移动了
                    touchMoved = true;
                }
            }, { passive: false });

            // 处理鼠标松开事件
            document.addEventListener('mouseup', () => {
                if (!isDragging) return;

                isDragging = false;

                // 触发change事件
                const event = new Event('change');
                ratingInput.dispatchEvent(event);
            });

            // 移动端专用的简单触摸处理 - 优先使用，确保点击正常工作
            starsContainer.addEventListener('touchend', (e) => {
                console.log('移动端touchend事件触发'); // 调试日志

                // 阻止默认行为，防止触发click事件
                e.preventDefault();

                // 找到被触摸的星星
                const touch = e.changedTouches[0];
                const element = document.elementFromPoint(touch.clientX, touch.clientY);
                const star = element ? element.closest('.star') : null;

                if (star) {
                    const value = parseInt(star.getAttribute('data-value'));
                    console.log('移动端触摸星星，评分：', value); // 调试日志

                    // 检查当前评分值
                    const currentValue = parseInt(ratingInput.value) || 0;

                    // 如果点击的星星已经是激活状态，则取消评分
                    if (currentValue === value) {
                        ratingInput.value = 0;
                        this.updateRating(0);
                        console.log('移动端取消评分'); // 调试日志
                    } else {
                        // 正常设置评分值
                        ratingInput.value = value;
                        this.updateRating(value);
                        console.log('移动端设置评分：', value); // 调试日志
                    }

                    // 触发触觉反馈（如果设备支持）
                    if (window.navigator && window.navigator.vibrate) {
                        window.navigator.vibrate(50);
                    }

                    // 触发change事件
                    const event = new Event('change');
                    ratingInput.dispatchEvent(event);
                } else {
                    console.log('移动端触摸位置没有找到星星'); // 调试日志
                }
            }, { passive: false });

            // 处理触摸结束事件 - 简化版本，只处理拖拽结束
            document.addEventListener('touchend', (e) => {
                if (touchStartTime === 0) return; // 没有触摸开始就不处理结束

                const touchEndTime = Date.now();
                const touchDuration = touchEndTime - touchStartTime;

                // 计算移动距离
                const touch = e.changedTouches[0];
                const moveX = Math.abs(touch.clientX - touchStartX);
                const moveY = Math.abs(touch.clientY - touchStartY);
                const totalMove = Math.sqrt(moveX * moveX + moveY * moveY);

                console.log('全局触摸结束，持续时间:', touchDuration, 'ms，移动距离:', totalMove.toFixed(1), 'px'); // 调试日志

                // 只处理拖拽结束，单点触摸由starsContainer的touchend处理
                if (isDragging) {
                    console.log('拖拽结束'); // 调试日志
                    const event = new Event('change');
                    ratingInput.dispatchEvent(event);
                }

                // 重置状态
                isDragging = false;
                touchStartTime = 0;
                touchMoved = false;
            });

            // 处理鼠标离开页面事件
            document.addEventListener('mouseleave', () => {
                if (!isDragging) return;

                isDragging = false;
            });

            // 监听评分变化，处理低评分提示
            ratingInput.addEventListener('change', (e) => {
                const value = parseInt(e.target.value) || 0;

                // 如果评分为1-2星，提示用户必须填写详细原因
                if (value >= 1 && value <= 2 && commentTextarea) {
                    commentTextarea.setAttribute('placeholder', '请详细说明您不满意的原因，以便我们改进服务（至少10个字）...');
                    commentTextarea.setAttribute('minlength', '10');

                    // 添加低评分提示
                    const existingAlert = document.querySelector('.low-rating-alert');
                    if (!existingAlert) {
                        const alertDiv = document.createElement('div');
                        alertDiv.className = 'low-rating-alert';
                        alertDiv.innerHTML = '<i class="ai-alert-circle"></i> 您的评分较低，请在评价内容中详细说明原因，以便我们改进服务。';
                        commentTextarea.parentNode.insertBefore(alertDiv, commentTextarea.nextSibling);

                        // 添加样式
                        alertDiv.style.color = '#e74c3c';
                        alertDiv.style.fontSize = '13px';
                        alertDiv.style.marginTop = '5px';
                        alertDiv.style.display = 'flex';
                        alertDiv.style.alignItems = 'center';
                        alertDiv.querySelector('i').style.marginRight = '5px';
                    }
                } else if (commentTextarea) {
                    // 恢复默认提示
                    commentTextarea.setAttribute('placeholder', '请描述您的就医体验，我们将根据您的反馈不断改进服务...');
                    commentTextarea.setAttribute('minlength', '5');

                    // 移除低评分提示
                    const existingAlert = document.querySelector('.low-rating-alert');
                    if (existingAlert) {
                        existingAlert.remove();
                    }
                }
            });

            // 设置初始化完成标志
            this.isInitialized = true;
            console.log('星级评分模块初始化完成');
        },

        /**
         * 更新评分显示
         * @param {number} value - 评分值（0-5）
         */
        updateRating: function(value) {
            const stars = document.querySelectorAll('.star');
            const ratingLabels = document.querySelectorAll('.rating-label');

            // 确保评分是有效的数字
            value = parseInt(value) || 0;
            console.log('更新星级显示，评分：', value); // 调试日志

            // 更新星星显示
            stars.forEach(star => {
                const starValue = parseInt(star.getAttribute('data-value'));
                const shouldBeActive = starValue <= value;
                console.log('星星值：', starValue, '当前评分：', value, '是否激活：', shouldBeActive); // 调试日志

                if (shouldBeActive) {
                    star.classList.remove('instant-deactivate'); // 移除立即取消类
                    star.classList.add('active');
                    console.log('添加active类到星星', starValue, '当前类名：', star.className); // 调试日志
                } else {
                    // 强制立即移除active类和所有相关效果
                    star.classList.remove('active');
                    star.classList.remove('clicked'); // 移除点击动画类
                    star.classList.add('instant-deactivate');
                    console.log('移除active类从星星', starValue, '当前类名：', star.className); // 调试日志

                    // 强制重绘，确保立即生效
                    star.offsetHeight; // 触发重绘

                    // 短暂延迟后移除立即取消类，恢复正常动画
                    setTimeout(() => {
                        star.classList.remove('instant-deactivate');
                    }, 100);
                }
            });

            // 更新评分标签显示
            ratingLabels.forEach(label => {
                const labelValue = parseInt(label.getAttribute('data-value'));
                if (labelValue === value) {
                    label.classList.add('active');
                } else {
                    label.classList.remove('active');
                }
            });
        },

        /**
         * 获取当前评分
         * @returns {number} 当前评分值（0-5）
         */
        getRating: function() {
            const ratingInput = document.getElementById('hospitalRating');
            return ratingInput ? parseInt(ratingInput.value) || 0 : 0;
        },

        /**
         * 根据位置计算评分值
         * @param {number} positionX - 位置的X坐标
         * @param {number} containerWidth - 容器宽度
         * @param {number} starsCount - 星星数量
         * @returns {number} 评分值（1-5）
         */
        calculateRatingFromPosition: function(positionX, containerWidth, starsCount) {
            // 确保位置在容器范围内
            positionX = Math.max(0, Math.min(positionX, containerWidth));

            // 计算评分值（1-5）
            const percentage = positionX / containerWidth;
            const rawValue = percentage * starsCount;

            // 四舍五入到最接近的整数
            return Math.max(1, Math.min(Math.round(rawValue), starsCount));
        },

        /**
         * 测试移动端触摸功能
         * @param {number} starIndex - 星星索引（1-5）
         */
        testMobileTouch: function(starIndex) {
            const stars = document.querySelectorAll('.star');
            const targetStar = stars[starIndex - 1];

            if (!targetStar) {
                console.log('测试失败：找不到星星', starIndex);
                return;
            }

            console.log('开始测试移动端触摸，目标星星：', starIndex);

            // 获取星星的位置
            const rect = targetStar.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top + rect.height / 2;

            console.log('星星位置：', x, y);

            // 创建触摸事件
            try {
                const touchStart = new TouchEvent('touchstart', {
                    touches: [new Touch({
                        identifier: 0,
                        target: targetStar,
                        clientX: x,
                        clientY: y
                    })],
                    bubbles: true,
                    cancelable: true
                });

                const touchEnd = new TouchEvent('touchend', {
                    changedTouches: [new Touch({
                        identifier: 0,
                        target: targetStar,
                        clientX: x,
                        clientY: y
                    })],
                    bubbles: true,
                    cancelable: true
                });

                // 触发触摸事件
                console.log('触发touchstart事件');
                targetStar.dispatchEvent(touchStart);

                setTimeout(() => {
                    console.log('触发touchend事件');
                    targetStar.dispatchEvent(touchEnd);
                }, 100); // 100ms的轻触

            } catch (error) {
                console.log('创建触摸事件失败，使用简化方法：', error);
                // 如果TouchEvent不支持，直接调用点击逻辑
                this.simulateClick(starIndex);
            }
        },

        /**
         * 模拟点击功能
         * @param {number} starIndex - 星星索引（1-5）
         */
        simulateClick: function(starIndex) {
            const ratingInput = document.getElementById('hospitalRating');
            if (!ratingInput) return;

            console.log('模拟点击星星：', starIndex);

            // 检查当前评分值
            const currentValue = parseInt(ratingInput.value) || 0;

            // 如果点击的星星已经是激活状态，则取消评分
            if (currentValue === starIndex) {
                ratingInput.value = 0;
                this.updateRating(0);
                console.log('取消评分，评分值设置为0');
            } else {
                // 正常设置评分值
                ratingInput.value = starIndex;
                this.updateRating(starIndex);
                console.log('设置评分：', starIndex);
            }

            // 触发change事件
            const event = new Event('change');
            ratingInput.dispatchEvent(event);
        }
    };

    // 在页面加载完成后初始化星级评分模块
    document.addEventListener('DOMContentLoaded', function() {
        // 等待主应用初始化完成后再初始化星级评分模块
        if (window.app && window.app.isInitialized) {
            window.ratingModule.init();
        } else {
            // 如果主应用尚未初始化，等待一段时间后再尝试初始化
            setTimeout(function() {
                window.ratingModule.init();
            }, 500);
        }
    });
})();
