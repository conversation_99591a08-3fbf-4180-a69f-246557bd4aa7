#!/usr/bin/env python
"""
测试真实的HTTP API请求 - 模拟前端实际访问
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
from qrmanager.models import QRCode, Bed, Department, Staff
import uuid

def test_real_http_requests():
    """测试真实的HTTP API请求"""
    print("=" * 80)
    print("🌐 真实HTTP API请求测试")
    print("=" * 80)
    
    # 服务器配置
    BASE_URL = "http://127.0.0.1:8000"
    
    # 生成测试数据
    test_uuid = str(uuid.uuid4())
    print(f"测试UUID: {test_uuid}")
    
    try:
        # 生成加密参数
        encrypted_param = encrypt_qr_param(test_uuid)
        print(f"加密参数: {encrypted_param}")
        print(f"参数长度: {len(encrypted_param)} 字符")
        print()
        
        # 测试1: 验证二维码API
        print("1. 测试验证二维码API:")
        verify_url = f"{BASE_URL}/service/resources/"
        verify_data = {
            "qr_param": encrypted_param,
            "client_ip": "127.0.0.1"
        }
        
        print(f"   请求URL: {verify_url}")
        print(f"   请求数据: {verify_data}")
        
        try:
            response = requests.post(
                verify_url,
                json=verify_data,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            print(f"   响应头: {dict(response.headers)}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"   响应数据: {response_data}")
                    print(f"   验证API: ✅ 成功")
                except json.JSONDecodeError:
                    print(f"   响应内容: {response.text}")
                    print(f"   验证API: ❌ 响应不是JSON格式")
            else:
                print(f"   响应内容: {response.text}")
                print(f"   验证API: ❌ HTTP状态码错误")
                
        except requests.exceptions.RequestException as e:
            print(f"   验证API: ❌ 请求异常 - {e}")
        
        print()
        
        # 测试2: 提交评价API
        print("2. 测试提交评价API:")
        submit_url = f"{BASE_URL}/service/evaluation/"
        submit_data = {
            "qr_param": encrypted_param,
            "comment": "测试评价",
            "staff_evaluations": [
                {"staff_id": 1, "is_satisfied": True}
            ],
            "hospital_number": "TEST123",
            "phone_number": "13800138000"
        }
        
        print(f"   请求URL: {submit_url}")
        print(f"   请求数据: qr_param={encrypted_param[:20]}..., comment='{submit_data['comment']}'")
        
        try:
            response = requests.post(
                submit_url,
                json=submit_data,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                try:
                    response_data = response.json()
                    print(f"   响应数据: {response_data}")
                    print(f"   提交API: ✅ 成功")
                except json.JSONDecodeError:
                    print(f"   响应内容: {response.text}")
                    print(f"   提交API: ❌ 响应不是JSON格式")
            else:
                print(f"   响应内容: {response.text}")
                print(f"   提交API: ❌ HTTP状态码错误")
                
        except requests.exceptions.RequestException as e:
            print(f"   提交API: ❌ 请求异常 - {e}")
        
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")

def test_with_existing_qrcode():
    """使用数据库中现有的二维码进行测试"""
    print("\n" + "=" * 80)
    print("🗄️ 使用现有二维码数据测试")
    print("=" * 80)
    
    try:
        # 查找数据库中的二维码
        qrcodes = QRCode.objects.all()[:3]  # 取前3个
        
        if not qrcodes:
            print("❌ 数据库中没有二维码数据")
            return
        
        print(f"找到 {len(qrcodes)} 个二维码，开始测试...")
        
        BASE_URL = "http://127.0.0.1:8000"
        
        for i, qrcode in enumerate(qrcodes, 1):
            print(f"\n测试 {i}: 二维码 {qrcode.code}")
            
            # 使用新算法加密现有UUID
            try:
                encrypted_param = encrypt_qr_param(qrcode.code)
                print(f"   新加密参数: {encrypted_param}")
                
                # 测试验证API
                verify_url = f"{BASE_URL}/service/resources/"
                verify_data = {
                    "qr_param": encrypted_param,
                    "client_ip": "127.0.0.1"
                }
                
                response = requests.post(
                    verify_url,
                    json=verify_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=5
                )
                
                print(f"   HTTP状态: {response.status_code}")
                if response.status_code == 200:
                    response_data = response.json()
                    print(f"   API响应: {response_data.get('status', 'unknown')}")
                    print(f"   测试结果: ✅ 成功")
                else:
                    print(f"   错误信息: {response.text[:100]}...")
                    print(f"   测试结果: ❌ 失败")
                    
            except Exception as e:
                print(f"   测试结果: ❌ 异常 - {e}")
    
    except Exception as e:
        print(f"❌ 数据库查询异常: {e}")

def test_server_logs():
    """检查服务器日志"""
    print("\n" + "=" * 80)
    print("📋 检查服务器状态")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    # 简单的健康检查
    try:
        response = requests.get(f"{BASE_URL}/", timeout=5)
        print(f"服务器状态: {response.status_code}")
        print(f"服务器响应: {'✅ 正常' if response.status_code in [200, 404] else '❌ 异常'}")
    except requests.exceptions.RequestException as e:
        print(f"服务器状态: ❌ 无法连接 - {e}")

if __name__ == "__main__":
    print("🔧 开始真实HTTP API测试...")
    
    # 检查服务器状态
    test_server_logs()
    
    # 测试新生成的加密参数
    test_real_http_requests()
    
    # 测试现有数据库中的二维码
    test_with_existing_qrcode()
    
    print("\n" + "=" * 80)
    print("🏁 HTTP API测试完成")
    print("=" * 80)
