/* 管理后台登录页面样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #ECECEC, #FFF);
    color: #333;
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
}

.login-container {
    max-width: 400px;
    width: 90%;
    background-color: #fff;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    font-size: 24px;
    font-weight: 500;
    margin-bottom: 30px;
    color: #111;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;
}

input[type="text"],
input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    box-sizing: border-box;
    transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus {
    border-color: #007aff;
    outline: none;
}

button {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #007aff;
    border: none;
    color: #fff;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
    margin-top: 20px;
}

button:hover {
    background-color: #005bb5;
}

.errornote {
    color: #dc3545;
    background: #fff;
    border: 1px solid #dc3545;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 20px;
}

/* 基础样式 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    background: #f5f5f7;
    color: #333;
    line-height: 1.5;
}

/* 容器样式 */
#container {
    width: 100%;
    min-height: 100vh;
}

/* 头部样式 */
#header {
    background: #fff;
    padding: 1rem 2rem;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    display: flex;
    justify-content: space-between;
    align-items: center;
}

#branding h1 {
    margin: 0;
    font-size: 1.5rem;
    color: #007aff;
}

#user-tools {
    display: flex;
    gap: 1rem;
    align-items: center;
}

#user-tools a {
    color: #007aff;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    transition: background-color 0.2s;
}

#user-tools a:hover {
    background-color: #f0f0f0;
}

/* 主要内容区域 */
.main {
    padding: 2rem;
    max-width: 1200px;
    margin: 0 auto;
}

/* 表格样式 */
.results {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    overflow: hidden;
}

.results table {
    width: 100%;
    border-collapse: collapse;
}

.results th, .results td {
    padding: 1rem;
    border-bottom: 1px solid #eee;
}

.results th {
    background: #f8f8f8;
    font-weight: 500;
}

/* 按钮样式 */
.button, input[type="submit"], input[type="button"] {
    background: #007aff;
    color: #fff;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.2s;
}

.button:hover, input[type="submit"]:hover, input[type="button"]:hover {
    background: #0056b3;
}

/* 表单样式 */
form .form-row {
    margin-bottom: 1rem;
}

form label {
    display: block;
    margin-bottom: 0.5rem;
    color: #666;
}

form input[type="text"],
form input[type="password"],
form input[type="email"],
form textarea,
form select {
    width: 100%;
    padding: 0.5rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 1rem;
}

/* 消息提示 */
.messagelist {
    list-style: none;
    padding: 0;
    margin: 0 0 1rem 0;
}

.messagelist li {
    padding: 1rem;
    margin-bottom: 0.5rem;
    border-radius: 6px;
}

.messagelist li.success {
    background: #d4edda;
    color: #155724;
}

.messagelist li.error {
    background: #f8d7da;
    color: #721c24;
} 