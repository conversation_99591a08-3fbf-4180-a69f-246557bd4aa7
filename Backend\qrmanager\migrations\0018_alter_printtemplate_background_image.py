# Generated by Django 4.2.7 on 2025-02-19 07:29

import django.core.validators
from django.db import migrations, models
import qrmanager.models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0017_alter_department_code_alter_department_name_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='printtemplate',
            name='background_image',
            field=models.ImageField(help_text='支持JPG/PNG格式，大小不超过10MB', upload_to=qrmanager.models.template_image_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']), qrmanager.models.validate_image_size], verbose_name='背景图片'),
        ),
    ]
