# Generated by Django 4.2.7 on 2025-02-19 07:23

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import qrmanager.models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0016_alter_department_code'),
    ]

    operations = [
        migrations.AlterField(
            model_name='department',
            name='code',
            field=models.CharField(blank=True, max_length=50, null=True, unique=True, verbose_name='科室编码'),
        ),
        migrations.AlterField(
            model_name='department',
            name='name',
            field=models.CharField(max_length=100, verbose_name='科室名称'),
        ),
        migrations.CreateModel(
            name='PrintTemplate',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='模板名称')),
                ('background_image', models.ImageField(help_text='建议尺寸：800x1200像素，大小不超过2MB，仅支持JPG/PNG格式', upload_to=qrmanager.models.template_image_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']), qrmanager.models.validate_image_size], verbose_name='背景图片')),
                ('qr_position_x', models.IntegerField(default=400, verbose_name='二维码X坐标')),
                ('qr_position_y', models.IntegerField(default=600, verbose_name='二维码Y坐标')),
                ('qr_size', models.IntegerField(default=200, verbose_name='二维码尺寸')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('department', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='print_template', to='qrmanager.department', verbose_name='科室')),
            ],
            options={
                'verbose_name': '打印模板',
                'verbose_name_plural': '打印模板',
                'ordering': ['department__name'],
            },
        ),
    ]
