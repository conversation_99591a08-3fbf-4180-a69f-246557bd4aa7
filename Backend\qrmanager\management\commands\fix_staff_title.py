from django.core.management.base import BaseCommand
import re
import os

class Command(BaseCommand):
    help = '修复views.py文件中的title_id错误和staff错误'

    def handle(self, *args, **options):
        # 获取所有Python文件和备份文件
        python_files = []
        for root, dirs, files in os.walk('qrmanager'):
            for file in files:
                if (file.endswith('.py') or '.py.' in file or file.endswith('.py.bak') or 
                    file.endswith('.py.backup') or 'views.py' in file) and not file.endswith('.pyc'):
                    python_files.append(os.path.join(root, file))
        
        # 添加额外的模式匹配，包括备份和临时文件
        for filename in ['views.py.bak', 'views.py.backup', 'views.py.bak_fixed', 
                      'views.py.bak2', 'views.py.bak3', 'views.py.bak4']:
            filepath = os.path.join('qrmanager', filename)
            if os.path.exists(filepath) and filepath not in python_files:
                python_files.append(filepath)
        
        total_fixes = 0
        
        for file_path in python_files:
            self.stdout.write(f'检查文件: {file_path}')
            
            if not os.path.exists(file_path):
                self.stdout.write(self.style.ERROR(f'找不到文件: {file_path}'))
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as file:
                    content = file.read()
                
                # 查找和替换title_id为title (查询过滤模式)
                pattern1 = r"queryset\s*=\s*queryset\.filter\s*\(\s*title_id\s*=\s*title\s*\)"
                replacement1 = "queryset = queryset.filter(title=title)"
                new_content, count1 = re.subn(pattern1, replacement1, content)
                
                # 查找和替换API字段中的title_id为title
                pattern2 = r"(fields\s*=\s*\[[^\]]*)'title_id'([^\]]*\])"
                replacement2 = r"\1'title'\2"
                new_content, count2 = re.subn(pattern2, replacement2, new_content)
                
                # 额外检查位置参数title_id, 如 (..., title_id=xxx, ...)
                pattern3 = r'(\w+\s*=\s*\w+\.filter\s*\([^)]*),\s*title_id(\s*=\s*[^,\)]+)'
                replacement3 = r'\1, title\2'
                new_content, count3 = re.subn(pattern3, replacement3, new_content)
                
                # 修复staff字段引用问题（在SentimentAnalysisView等处）
                pattern4 = r'(staff_evals\s*=\s*evaluations\.filter\s*\(\s*)staff=staff(\s*\))'
                replacement4 = r'\1Q(satisfied_staff1_id=staff.id) | Q(satisfied_staff2_id=staff.id) | Q(satisfied_staff3_id=staff.id) | Q(unsatisfied_staff1_id=staff.id) | Q(unsatisfied_staff2_id=staff.id) | Q(unsatisfied_staff3_id=staff.id)\2'
                new_content, count4 = re.subn(pattern4, replacement4, new_content)
                
                # 修复select_related中的staff引用
                pattern5 = r'(select_related\s*\([^)]*),\s*\'staff\'([^)]*\))'
                replacement5 = r'\1\2'
                new_content, count5 = re.subn(pattern5, replacement5, new_content)
                
                count = count1 + count2 + count3 + count4 + count5
                
                if count > 0:
                    self.stdout.write(self.style.SUCCESS(f'在 {file_path} 中找到 {count} 处错误'))
                    with open(file_path, 'w', encoding='utf-8') as file:
                        file.write(new_content)
                    total_fixes += count
                    self.stdout.write(self.style.SUCCESS(f'修复了 {file_path} 中的 {count} 处错误'))
            except Exception as e:
                self.stdout.write(self.style.ERROR(f'处理文件 {file_path} 时出错: {str(e)}'))
        
        # 删除所有 .pyc 文件以确保加载最新代码
        self.stdout.write('清理Python缓存文件...')
        pyc_count = 0
        for root, dirs, files in os.walk('qrmanager'):
            for file in files:
                if file.endswith('.pyc'):
                    try:
                        os.remove(os.path.join(root, file))
                        pyc_count += 1
                    except Exception as e:
                        self.stdout.write(self.style.ERROR(f'删除 {file} 时出错: {str(e)}'))
        
        if pyc_count > 0:
            self.stdout.write(self.style.SUCCESS(f'成功删除 {pyc_count} 个缓存文件'))
        
        if total_fixes > 0:
            self.stdout.write(self.style.SUCCESS(f'成功修复了总共 {total_fixes} 处错误'))
        else:
            self.stdout.write(self.style.WARNING('没有找到需要修复的错误')) 