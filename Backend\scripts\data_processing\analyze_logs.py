#!/usr/bin/env python
"""
分析操作日志数据，检查日期范围和分布情况
"""
import os
import sys
import django
from datetime import timedelta
from collections import Counter

# 设置Django环境
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(__file__))))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.utils import timezone
from qrmanager.models import OperationLog

def analyze_logs():
    """分析日志数据"""
    # 获取日志总数
    total_logs = OperationLog.objects.count()
    print(f"系统中共有 {total_logs} 条日志记录")
    
    if total_logs == 0:
        print("没有日志记录可供分析")
        return
    
    # 获取最早和最晚的日志
    earliest_log = OperationLog.objects.order_by('created_at').first()
    latest_log = OperationLog.objects.order_by('-created_at').first()
    
    earliest_date = earliest_log.created_at
    latest_date = latest_log.created_at
    date_range = latest_date - earliest_date
    
    print(f"最早的日志: {earliest_date.isoformat()} (ID: {earliest_log.id}, 操作: {earliest_log.action})")
    print(f"最晚的日志: {latest_date.isoformat()} (ID: {latest_log.id}, 操作: {latest_log.action})")
    print(f"日志时间跨度: {date_range.days}天 {date_range.seconds//3600}小时")
    
    # 分析日志的年份分布
    year_counts = Counter()
    month_counts = Counter()
    day_counts = Counter()
    
    for log in OperationLog.objects.all().values('created_at'):
        date = log['created_at'].date()
        year_counts[date.year] += 1
        month_counts[(date.year, date.month)] += 1
        day_counts[date] += 1
    
    print("\n年份分布:")
    for year, count in sorted(year_counts.items()):
        print(f"  {year}年: {count}条日志")
    
    print("\n最近30天分布:")
    today = timezone.now().date()
    for i in range(30):
        day = today - timedelta(days=i)
        count = day_counts.get(day, 0)
        print(f"  {day.isoformat()}: {count}条日志")
    
    # 检查是否有超过一年的日志
    one_year_ago = timezone.now() - timedelta(days=365)
    old_logs_count = OperationLog.objects.filter(created_at__lt=one_year_ago).count()
    print(f"\n超过一年前的日志数量: {old_logs_count}")
    
    if old_logs_count > 0:
        old_logs = OperationLog.objects.filter(created_at__lt=one_year_ago).order_by('-created_at')[:5]
        print("示例旧日志:")
        for log in old_logs:
            print(f"  ID: {log.id}, 时间: {log.created_at.isoformat()}, 操作: {log.action}")

if __name__ == '__main__':
    analyze_logs() 