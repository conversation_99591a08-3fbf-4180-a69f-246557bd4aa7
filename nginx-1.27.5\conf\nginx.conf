#user  nobody;
worker_processes  1;

#error_log  logs/error.log;
#error_log  logs/error.log  notice;
#error_log  logs/error.log  info;

#pid        logs/nginx.pid;


events {
    worker_connections  1024;
}


http {
    include       mime.types;
    default_type  application/octet-stream;

    #log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
    #                  '$status $body_bytes_sent "$http_referer" '
    #                  '"$http_user_agent" "$http_x_forwarded_for"';

    #access_log  logs/access.log  main;

    sendfile        on;
    #tcp_nopush     on;

    #keepalive_timeout  0;
    keepalive_timeout  65;

    #gzip  on;

    # HTTP服务器 - 将所有HTTP请求重定向到HTTPS
    server {
        listen       80;
        server_name  zg120pj.cn;

        # 重定向到HTTPS
        return 301 https://$host$request_uri;
    }

    # 删除这个服务器块，因为它与HTTPS 8000端口服务器块冲突

    # HTTPS服务器 - 处理前端请求（443端口）
    server {
        listen 443 ssl;
        server_name zg120pj.cn;

        ssl_certificate      "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.crt";
        ssl_certificate_key  "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.key";

        # 添加SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:10m;

        # 静态文件处理 - 前端静态文件
        location ^~ /static/ {
            root C:/前后端分离/Frontend;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        # 媒体文件处理 - 前端媒体文件
        location ^~ /media/ {
            root C:/前后端分离/Frontend;
            expires 30d;
        }

        # 🔧 关键修复：前端文件缓存策略 - 确保手机扫码自动加载最新版本
        # HTML文件 - 禁用缓存，立即更新
        location ~* \.(html|htm)$ {
            root C:/前后端分离/Frontend;
            add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
            add_header Pragma "no-cache";
            add_header Expires "0";
            expires -1;
        }

        # CSS和JS文件 - 禁用缓存，立即更新
        location ~* \.(css|js)$ {
            root C:/前后端分离/Frontend;
            add_header Cache-Control "no-cache, no-store, must-revalidate, max-age=0";
            add_header Pragma "no-cache";
            add_header Expires "0";
            expires -1;
        }

        # 图片文件 - 短缓存，1小时
        location ~* \.(jpg|jpeg|png|gif|ico|svg|webp)$ {
            root C:/前后端分离/Frontend;
            expires 1h;
            add_header Cache-Control "public, max-age=3600";
        }

        # 默认所有请求处理前端内容
        location / {
            root C:/前后端分离/Frontend;
            index index.html;
            try_files $uri $uri/ /index.html;
        }

        # 只暴露必要的API接口，使用隐蔽的名称
        location /service/evaluation/ {
            # 转发到Django的评价提交接口
            proxy_pass http://127.0.0.1:8001/api/v1/public/submit-evaluation/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # 通过设置以下头来修复CSRF问题
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Origin $scheme://$host;
            proxy_set_header Referer $scheme://$host$request_uri;

            # 增加缓冲区大小
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;

            # 确保cookie正确传递
            proxy_cookie_path / /;
            proxy_cookie_domain 127.0.0.1 zg120pj.cn;
        }

        # 二维码验证接口 - 使用隐蔽的名称
        location /service/resources/ {
            # 转发到Django的二维码验证接口
            proxy_pass http://127.0.0.1:8001/api/v1/public/qrcode/verify/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # 通过设置以下头来修复CSRF问题
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Origin $scheme://$host;
            proxy_set_header Referer $scheme://$host$request_uri;

            # 增加缓冲区大小
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;

            # 确保cookie正确传递
            proxy_cookie_path / /;
            proxy_cookie_domain 127.0.0.1 zg120pj.cn;
        }
    }

    # 后端服务器（8000端口）- 使用SSL，但通过HTTP转发到8001端口
    server {
        listen 8000 ssl;
        server_name zg120pj.cn;

        # 使用完整路径指定证书文件
        ssl_certificate      "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.crt";
        ssl_certificate_key  "C:/前后端分离/nginx-1.27.5/conf/ssl/zg120pj.cn.key";

        # 添加SSL配置
        ssl_protocols TLSv1.2 TLSv1.3;
        ssl_prefer_server_ciphers on;
        ssl_ciphers 'ECDHE-ECDSA-AES256-GCM-SHA384:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-ECDSA-CHACHA20-POLY1305:ECDHE-RSA-CHACHA20-POLY1305:ECDHE-ECDSA-AES128-GCM-SHA256:ECDHE-RSA-AES128-GCM-SHA256';
        ssl_session_timeout 1d;
        ssl_session_cache shared:SSL:10m;

        # 静态文件处理
        location ^~ /static/ {
            alias C:/前后端分离/Backend/staticfiles/;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }

        # 媒体文件处理
        location ^~ /media/ {
            alias C:/前后端分离/Backend/media/;
            expires 30d;
        }

        # 二维码验证接口 - 使用隐蔽的名称
        location /service/resources/ {
            # 转发到Django的二维码验证接口
            proxy_pass http://127.0.0.1:8001/api/v1/public/qrcode/verify/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # 通过设置以下头来修复CSRF问题
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Origin $scheme://$host;
            proxy_set_header Referer $scheme://$host$request_uri;

            # 增加缓冲区大小
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;

            # 确保cookie正确传递
            proxy_cookie_path / /;
            proxy_cookie_domain 127.0.0.1 zg120pj.cn;
        }

        # 评价提交接口 - 使用隐蔽的名称
        location /service/evaluation/ {
            # 转发到Django的评价提交接口
            proxy_pass http://127.0.0.1:8001/api/v1/public/submit-evaluation/;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # 通过设置以下头来修复CSRF问题
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Origin $scheme://$host;
            proxy_set_header Referer $scheme://$host$request_uri;

            # 增加缓冲区大小
            proxy_buffer_size 128k;
            proxy_buffers 4 256k;
            proxy_busy_buffers_size 256k;

            # 确保cookie正确传递
            proxy_cookie_path / /;
            proxy_cookie_domain 127.0.0.1 zg120pj.cn;
        }

        # 将所有其他请求转发到Django后端（HTTP 8001端口）
        location / {
            proxy_pass http://127.0.0.1:8001;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto https;
            proxy_set_header X-Forwarded-Host $host;
            proxy_set_header X-Forwarded-Port $server_port;

            # 通过设置以下头来修复CSRF问题
            proxy_set_header X-Scheme $scheme;
            proxy_set_header Origin $scheme://$host:$server_port;
            proxy_set_header Referer $scheme://$host:$server_port$request_uri;

            # 🔧 关键修复：添加超时配置支持批量生成
            proxy_connect_timeout 60s;    # 连接超时
            proxy_send_timeout 300s;      # 发送超时（5分钟）
            proxy_read_timeout 300s;      # 读取超时（5分钟）

            # 🔧 大文件下载优化
            proxy_request_buffering off;  # 禁用请求缓冲，提高上传性能
            proxy_http_version 1.1;       # 使用HTTP/1.1，支持keep-alive

            # 🔧 关键修复：优化缓冲区配置支持大文件快速下载
            proxy_buffer_size 256k;           # 初始缓冲区：256KB
            proxy_buffers 8 1m;               # 8个1MB缓冲区，总计8MB
            proxy_busy_buffers_size 2m;       # 忙碌缓冲区：2MB
            proxy_max_temp_file_size 0;       # 禁用临时文件，强制使用内存缓冲
            proxy_buffering on;               # 启用缓冲（默认开启，明确声明）

            # 确保cookie正确传递
            proxy_cookie_path / /;
            proxy_cookie_domain 127.0.0.1 zg120pj.cn;
        }
    }
}
