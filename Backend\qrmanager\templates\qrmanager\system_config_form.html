{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ subtitle }}</h3>
                </div>
                <div class="card-body">
                    <form method="post">
                        {% csrf_token %}
                        
                        <div class="form-group">
                            <label for="id_key">配置键</label>
                            <input type="text" class="form-control" id="id_key" value="{{ object.key }}" readonly>
                            <small class="form-text text-muted">配置键不可修改</small>
                        </div>
                        
                        <div class="form-group">
                            <label for="id_value">配置值</label>
                            <input type="text" class="form-control" id="id_value" name="value" value="{{ object.value }}" required>
                            {% if object.key == 'qrcode_token_expiry' %}
                                <small class="form-text text-muted">
                                    二维码有效期（秒），0表示永不过期。<br>
                                    常用值参考：<br>
                                    - 0: 永不过期（推荐用于打印的二维码）<br>
                                    - 86400: 1天<br>
                                    - 604800: 1周<br>
                                    - 2592000: 30天<br>
                                    - 31536000: 1年
                                </small>
                            {% endif %}
                        </div>
                        
                        <div class="form-group">
                            <label for="id_description">描述</label>
                            <textarea class="form-control" id="id_description" name="description" rows="3">{{ object.description }}</textarea>
                        </div>
                        
                        <div class="form-group">
                            <div class="custom-control custom-checkbox">
                                <input type="checkbox" class="custom-control-input" id="id_is_public" name="is_public" {% if object.is_public %}checked{% endif %}>
                                <label class="custom-control-label" for="id_is_public">公开配置</label>
                                <small class="form-text text-muted">公开配置可在前端显示</small>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{% url 'qrmanager:system_config_list' %}" class="btn btn-secondary">取消</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 