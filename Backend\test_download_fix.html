<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试下载修复</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px;
            font-size: 16px;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .btn:disabled {
            background-color: #6c757d;
            cursor: not-allowed;
        }
        .alert {
            padding: 15px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .alert-success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .alert-danger {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .alert-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 15px;
            margin: 20px 0;
            font-family: monospace;
            font-size: 14px;
            max-height: 300px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 工作人员模板下载修复测试</h1>
        
        <div class="alert alert-warning">
            <strong>测试说明：</strong>
            <ul>
                <li>点击下载按钮测试新的下载方式</li>
                <li>检查是否还会跳转到 Office Online</li>
                <li>验证文件是否直接下载到本地</li>
            </ul>
        </div>
        
        <button class="btn" id="testDownloadBtn">
            <i>📥</i> 测试下载工作人员模板
        </button>
        
        <button class="btn" id="clearLogBtn">
            <i>🗑️</i> 清除日志
        </button>
        
        <div id="logContainer" class="log">
            <div id="logContent">准备测试下载功能...</div>
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            if (type === 'error') {
                logEntry.style.color = '#dc3545';
            } else if (type === 'success') {
                logEntry.style.color = '#28a745';
            } else if (type === 'warning') {
                logEntry.style.color = '#ffc107';
            }
            
            logContent.appendChild(logEntry);
            logContent.scrollTop = logContent.scrollHeight;
        }

        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.className = `alert alert-${type}`;
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                z-index: 9999;
                min-width: 300px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            `;
            toast.innerHTML = message;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 3000);
        }

        document.getElementById('testDownloadBtn').addEventListener('click', function() {
            const btn = this;
            const originalText = btn.innerHTML;
            
            log('开始测试下载功能...');
            btn.innerHTML = '<i>⏳</i> 准备下载...';
            btn.disabled = true;
            
            // 模拟实际的下载URL（需要根据实际情况调整）
            const downloadUrl = 'https://zg120pj.cn:8000/staff/template/';
            
            log(`测试URL: ${downloadUrl}`);
            
            // 先检查会话状态
            fetch(downloadUrl, {
                method: 'HEAD',
                credentials: 'same-origin'
            })
            .then(response => {
                log(`HEAD请求状态: ${response.status}`);
                
                if (response.ok) {
                    log('会话验证成功，开始下载...', 'success');
                    
                    // 使用 fetch 获取文件内容并强制下载
                    return fetch(downloadUrl, {
                        method: 'GET',
                        credentials: 'same-origin'
                    });
                } else if (response.status === 401) {
                    throw new Error('会话已过期，需要重新登录');
                } else {
                    throw new Error(`服务器错误: ${response.status}`);
                }
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`下载失败: ${response.status}`);
                }
                log('获取文件内容成功，转换为Blob...', 'success');
                return response.blob();
            })
            .then(blob => {
                log(`文件大小: ${blob.size} 字节`, 'success');
                
                // 创建 Blob URL 并强制下载
                const url = window.URL.createObjectURL(blob);
                const link = document.createElement('a');
                link.href = url;
                link.download = '工作人员导入模板.xlsx';
                link.style.display = 'none';
                
                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
                
                window.URL.revokeObjectURL(url);
                
                log('文件下载成功！', 'success');
                showToast('模板下载成功', 'success');
            })
            .catch(error => {
                log(`下载失败: ${error.message}`, 'error');
                showToast(`下载失败: ${error.message}`, 'danger');
            })
            .finally(() => {
                btn.innerHTML = originalText;
                btn.disabled = false;
            });
        });

        document.getElementById('clearLogBtn').addEventListener('click', function() {
            document.getElementById('logContent').innerHTML = '日志已清除...';
        });

        log('测试页面加载完成');
    </script>
</body>
</html>
