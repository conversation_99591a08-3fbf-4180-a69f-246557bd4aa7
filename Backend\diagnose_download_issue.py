#!/usr/bin/env python3
"""
下载问题诊断脚本
分析批量生成完成后无法下载的问题
"""

import os
import sys
import django
import tempfile
import zipfile
import time
import requests
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import User
from qrmanager.models import Department, Bed, QRCode

def test_file_generation():
    """测试文件生成"""
    print("🔍 测试文件生成")
    print("-" * 50)
    
    try:
        # 创建测试ZIP文件
        temp_dir = tempfile.mkdtemp()
        zip_path = os.path.join(temp_dir, "test_download.zip")
        
        print(f"临时目录: {temp_dir}")
        print(f"ZIP文件路径: {zip_path}")
        
        # 创建测试ZIP文件
        with zipfile.ZipFile(zip_path, 'w') as zipf:
            # 添加一些测试文件
            for i in range(5):
                test_content = f"测试文件内容 {i+1}\n" * 100
                zipf.writestr(f"test_file_{i+1}.txt", test_content)
        
        # 检查文件
        if os.path.exists(zip_path):
            file_size = os.path.getsize(zip_path)
            print(f"✅ ZIP文件创建成功")
            print(f"   文件大小: {file_size} 字节 ({file_size/1024:.2f} KB)")
            
            # 测试文件读取
            with open(zip_path, 'rb') as f:
                content = f.read(1024)  # 读取前1KB
                print(f"   文件可读: ✅")
                print(f"   前1KB内容长度: {len(content)} 字节")
            
            return True, zip_path, file_size
        else:
            print("❌ ZIP文件创建失败")
            return False, None, 0
            
    except Exception as e:
        print(f"❌ 文件生成测试失败: {e}")
        return False, None, 0

def test_django_file_response():
    """测试Django FileResponse"""
    print("\n🔍 测试Django FileResponse")
    print("-" * 50)
    
    try:
        # 创建测试文件
        success, zip_path, file_size = test_file_generation()
        if not success:
            return False
        
        # 创建Django测试客户端
        client = Client()
        
        # 创建测试用户
        user, created = User.objects.get_or_create(
            username='test_download_user',
            defaults={'email': '<EMAIL>'}
        )
        
        client.force_login(user)
        
        # 模拟FileResponse
        from django.http import FileResponse
        
        print("创建FileResponse...")
        response = FileResponse(
            open(zip_path, 'rb'), 
            content_type='application/zip'
        )
        response['Content-Disposition'] = 'attachment; filename="test_download.zip"'
        
        print(f"✅ FileResponse创建成功")
        print(f"   Content-Type: {response.get('Content-Type')}")
        print(f"   Content-Disposition: {response.get('Content-Disposition')}")
        
        # 测试响应内容
        content = b''
        for chunk in response:
            content += chunk
        
        print(f"   响应内容长度: {len(content)} 字节")
        
        if len(content) == file_size:
            print(f"✅ 响应内容完整")
            return True
        else:
            print(f"❌ 响应内容不完整 (期望: {file_size}, 实际: {len(content)})")
            return False
            
    except Exception as e:
        print(f"❌ Django FileResponse测试失败: {e}")
        return False

def test_actual_download_endpoint():
    """测试实际的下载端点"""
    print("\n🔍 测试实际下载端点")
    print("-" * 50)
    
    try:
        # 检查是否有可用的科室和床位
        departments = Department.objects.all()
        if not departments:
            print("❌ 没有可用的科室数据")
            return False
        
        department = departments.first()
        print(f"使用科室: {department.name} (ID: {department.id})")
        
        # 检查床位
        beds = Bed.objects.filter(department=department, is_active=True)
        if not beds:
            print("❌ 该科室没有可用的床位")
            return False
        
        print(f"找到 {beds.count()} 个床位")
        
        # 检查二维码
        qrcodes = []
        for bed in beds[:3]:  # 只测试前3个床位
            qrcode = QRCode.objects.filter(bed=bed).first()
            if qrcode:
                qrcodes.append(qrcode)
        
        if not qrcodes:
            print("❌ 没有可用的二维码")
            return False
        
        print(f"找到 {len(qrcodes)} 个二维码")
        
        # 测试下载端点
        download_url = f"https://zg120pj.cn:8000/qrcodes/print/department/{department.id}/zip/?format=png"
        
        print(f"测试下载URL: {download_url}")
        
        # 发送请求
        print("发送下载请求...")
        start_time = time.time()
        
        response = requests.get(
            download_url, 
            timeout=300,  # 5分钟超时
            verify=False,
            stream=True  # 流式下载
        )
        
        end_time = time.time()
        request_time = end_time - start_time
        
        print(f"请求耗时: {request_time:.2f}秒")
        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.headers.get('Content-Type')}")
        print(f"Content-Disposition: {response.headers.get('Content-Disposition')}")
        print(f"Content-Length: {response.headers.get('Content-Length')}")
        
        if response.status_code == 200:
            # 检查内容类型
            content_type = response.headers.get('Content-Type', '')
            
            if 'application/zip' in content_type:
                print("✅ 响应类型正确 (ZIP文件)")
                
                # 下载内容
                content_length = 0
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        content_length += len(chunk)
                
                print(f"✅ 下载完成，总大小: {content_length} 字节 ({content_length/1024:.2f} KB)")
                
                if content_length > 0:
                    print("✅ 下载端点工作正常")
                    return True
                else:
                    print("❌ 下载内容为空")
                    return False
                    
            elif 'application/json' in content_type:
                print("⚠️  响应是JSON，可能是错误信息")
                try:
                    error_data = response.json()
                    print(f"错误信息: {error_data}")
                except:
                    print("无法解析JSON响应")
                return False
            else:
                print(f"❌ 响应类型错误: {content_type}")
                return False
        else:
            print(f"❌ 请求失败，状态码: {response.status_code}")
            if response.text:
                print(f"响应内容: {response.text[:500]}...")
            return False
            
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败")
        return False
    except Exception as e:
        print(f"❌ 下载端点测试失败: {e}")
        return False

def test_nginx_configuration():
    """测试Nginx配置"""
    print("\n🔍 测试Nginx配置")
    print("-" * 50)
    
    try:
        # 检查Nginx配置文件
        nginx_config_path = "nginx-1.27.5/conf/nginx.conf"
        
        if os.path.exists(nginx_config_path):
            print(f"✅ Nginx配置文件存在: {nginx_config_path}")
            
            with open(nginx_config_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查关键配置
            checks = [
                ('proxy_read_timeout', '读取超时'),
                ('proxy_send_timeout', '发送超时'),
                ('proxy_buffer_size', '缓冲区大小'),
                ('client_max_body_size', '最大请求体大小'),
            ]
            
            for config_key, description in checks:
                if config_key in content:
                    # 提取配置值
                    lines = [line.strip() for line in content.split('\n') if config_key in line and not line.strip().startswith('#')]
                    if lines:
                        print(f"✅ {description}: {lines[0]}")
                    else:
                        print(f"⚠️  {description}: 已配置但可能被注释")
                else:
                    print(f"❌ {description}: 未配置")
            
            return True
        else:
            print(f"❌ Nginx配置文件不存在: {nginx_config_path}")
            return False
            
    except Exception as e:
        print(f"❌ Nginx配置检查失败: {e}")
        return False

def test_browser_download_simulation():
    """模拟浏览器下载"""
    print("\n🔍 模拟浏览器下载")
    print("-" * 50)
    
    try:
        # 创建测试会话
        session = requests.Session()
        
        # 设置浏览器头部
        session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
        })
        
        # 首先访问登录页面获取CSRF token
        login_url = "https://zg120pj.cn:8000/admin/login/"
        print(f"访问登录页面: {login_url}")
        
        response = session.get(login_url, verify=False, timeout=30)
        
        if response.status_code == 200:
            print("✅ 登录页面访问成功")
            
            # 这里只是模拟，不进行实际登录
            # 实际使用时需要提供用户名和密码
            print("⚠️  需要登录才能测试下载功能")
            print("建议手动测试：")
            print("1. 在浏览器中登录系统")
            print("2. 访问二维码管理页面")
            print("3. 尝试批量生成并观察下载过程")
            
            return True
        else:
            print(f"❌ 登录页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 浏览器下载模拟失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 下载问题诊断")
    print("=" * 80)
    print(f"诊断时间: {datetime.now()}")
    print("目标：分析批量生成完成后无法下载的问题")
    print()
    
    tests = [
        ("文件生成测试", test_file_generation),
        ("Django FileResponse测试", test_django_file_response),
        ("实际下载端点测试", test_actual_download_endpoint),
        ("Nginx配置检查", test_nginx_configuration),
        ("浏览器下载模拟", test_browser_download_simulation),
    ]
    
    results = []
    
    for name, test_func in tests:
        try:
            print(f"执行: {name}")
            if name == "文件生成测试":
                # 这个测试返回多个值，特殊处理
                result = test_func()[0] if isinstance(test_func(), tuple) else test_func()
            else:
                result = test_func()
            results.append((name, result))
            
            if result:
                print(f"✅ {name}: 通过\n")
            else:
                print(f"❌ {name}: 失败\n")
                
        except Exception as e:
            print(f"❌ {name}执行异常: {e}\n")
            results.append((name, False))
    
    # 总结
    print("📋 诊断结果总结")
    print("=" * 80)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n诊断通过率: {passed}/{len(results)}")
    
    # 分析和建议
    print(f"\n💡 问题分析和建议")
    print("=" * 80)
    
    if passed == len(results):
        print("🎉 所有测试通过！下载功能应该正常工作")
        print("如果仍然无法下载，可能是前端JavaScript问题")
        
    elif passed >= len(results) * 0.7:
        print("⚠️  大部分测试通过，可能是特定环节的问题")
        print("建议检查失败的测试项目")
        
    else:
        print("❌ 多个测试失败，存在系统性问题")
        
    print(f"\n🔧 建议的修复步骤:")
    print("1. 检查Nginx配置中的缓冲区和超时设置")
    print("2. 确认临时文件没有被过早清理")
    print("3. 检查前端JavaScript的blob处理逻辑")
    print("4. 验证文件权限和磁盘空间")
    print("5. 查看Nginx和Django的错误日志")

if __name__ == "__main__":
    main()
