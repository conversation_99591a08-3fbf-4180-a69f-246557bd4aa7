{% extends "qrmanager/base.html" %}
{% load django_bootstrap5 %}

{% block title %}{% if form.instance.pk %}编辑管理账户{% else %}创建管理账户{% endif %}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center">
                        {% if form.instance.pk %}
                            编辑管理账户
                        {% else %}
                            创建管理账户
                        {% endif %}
                    </h1>
                    <form method="post">
                        {% csrf_token %}
                        {% bootstrap_form form %}
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-secondary">返回</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 