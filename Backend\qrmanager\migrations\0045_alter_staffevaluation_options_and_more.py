# Generated by Django 4.2.7 on 2025-04-03 17:03

from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0044_staffevaluation'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='staffevaluation',
            options={'verbose_name': '工作人员评价关联(已弃用)', 'verbose_name_plural': '工作人员评价关联(已弃用)'},
        ),
        migrations.RemoveField(
            model_name='evaluation',
            name='staff',
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff1_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='满意工作人员1 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff1_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员1 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff1_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员1 职称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff2_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='满意工作人员2 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff2_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员2 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff2_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员2 职称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff3_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='满意工作人员3 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff3_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员3 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff3_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员3 职称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff1_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='不满意工作人员1 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff1_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员1 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff1_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员1 职称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff2_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='不满意工作人员2 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff2_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员2 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff2_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员2 职称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff3_id',
            field=models.IntegerField(blank=True, null=True, verbose_name='不满意工作人员3 ID'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff3_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员3 姓名'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff3_title',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员3 职称'),
        ),
        migrations.AddField(
            model_name='staffevaluation',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now, verbose_name='创建时间'),
        ),
        migrations.AlterField(
            model_name='staffevaluation',
            name='evaluation',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='qrmanager.evaluation', verbose_name='评价'),
        ),
        migrations.AlterField(
            model_name='staffevaluation',
            name='is_satisfied',
            field=models.BooleanField(default=True, verbose_name='是否满意'),
        ),
        migrations.AlterField(
            model_name='staffevaluation',
            name='staff',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='qrmanager.staff', verbose_name='工作人员'),
        ),
    ]
