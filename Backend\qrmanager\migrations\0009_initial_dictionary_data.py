from django.db import migrations

def create_initial_dictionaries(apps, schema_editor):
    Dictionary = apps.get_model('qrmanager', 'Dictionary')
    DictionaryItem = apps.get_model('qrmanager', 'DictionaryItem')
    
    # 创建人员类型字典
    staff_type_dict = Dictionary.objects.create(
        code='staff_type',
        name='人员类型',
        description='医院工作人员类型',
        is_active=True
    )
    
    # 创建人员类型字典项
    staff_types = [
        {'code': 'doctor', 'name': '医生', 'sort_order': 1},
        {'code': 'nurse', 'name': '护士', 'sort_order': 2},
        {'code': 'technician', 'name': '医技人员', 'sort_order': 3},
        {'code': 'admin', 'name': '行政人员', 'sort_order': 4},
    ]
    
    for item in staff_types:
        DictionaryItem.objects.create(
            dictionary=staff_type_dict,
            code=item['code'],
            name=item['name'],
            sort_order=item['sort_order'],
            is_active=True
        )
    
    # 创建职称字典
    staff_title_dict = Dictionary.objects.create(
        code='staff_title',
        name='职称',
        description='医院工作人员职称',
        is_active=True
    )
    
    # 创建职称字典项
    staff_titles = [
        {'code': 'chief', 'name': '主任医师', 'sort_order': 1},
        {'code': 'associate_chief', 'name': '副主任医师', 'sort_order': 2},
        {'code': 'attending', 'name': '主治医师', 'sort_order': 3},
        {'code': 'resident', 'name': '住院医师', 'sort_order': 4},
        {'code': 'chief_nurse', 'name': '主管护师', 'sort_order': 5},
        {'code': 'nurse', 'name': '护师', 'sort_order': 6},
        {'code': 'primary_nurse', 'name': '护士', 'sort_order': 7},
    ]
    
    for item in staff_titles:
        DictionaryItem.objects.create(
            dictionary=staff_title_dict,
            code=item['code'],
            name=item['name'],
            sort_order=item['sort_order'],
            is_active=True
        )

def delete_initial_dictionaries(apps, schema_editor):
    Dictionary = apps.get_model('qrmanager', 'Dictionary')
    Dictionary.objects.filter(code__in=['staff_type', 'staff_title']).delete()

class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0008_dictionary_and_dictionary_item'),
    ]

    operations = [
        migrations.RunPython(create_initial_dictionaries, delete_initial_dictionaries),
    ] 