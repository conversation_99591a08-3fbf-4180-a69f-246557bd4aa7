from django import forms
from .models import Department, Staff, Bed, QRCode, Evaluation, PrintTemplate, DictionaryItem, StaffType

class DepartmentForm(forms.ModelForm):
    class Meta:
        model = Department
        fields = ['code', 'name', 'remarks']
        widgets = {
            'code': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '请输入科室编码'}),
            'name': forms.TextInput(attrs={'class': 'form-control', 'placeholder': '请输入科室名称'}),
            'remarks': forms.Textarea(attrs={'class': 'form-control', 'placeholder': '请输入备注信息', 'rows': 3}),
        }

    def clean_code(self):
        code = self.cleaned_data.get('code')
        if not code:
            raise forms.ValidationError('科室编码不能为空')
        
        # 检查科室编码是否已存在
        instance = getattr(self, 'instance', None)
        if instance and instance.pk:
            # 编辑时排除自身
            exists = Department.objects.exclude(pk=instance.pk).filter(code=code).exists()
        else:
            # 创建时直接检查
            exists = Department.objects.filter(code=code).exists()
            
        if exists:
            raise forms.ValidationError('该科室编码已存在，请使用其他编码')
            
        return code

class StaffForm(forms.ModelForm):
    class Meta:
        model = Staff
        fields = ['work_number', 'name', 'staff_type', 'title', 'photo', 'department']
        widgets = {
            'work_number': forms.TextInput(attrs={'class': 'form-control'}),
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'staff_type': forms.Select(attrs={'class': 'form-select'}),
            'title': forms.Select(attrs={'class': 'form-select'}),
            'photo': forms.FileInput(attrs={'class': 'form-control'}),
            'department': forms.Select(attrs={'class': 'form-select'})
        }
        labels = {
            'title': '职称',
        }

    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        # 设置必填项
        self.fields['work_number'].required = True
        self.fields['name'].required = True
        self.fields['staff_type'].required = True
        self.fields['department'].required = True
        
        # 同步StaffType和字典表中的数据
        try:
            # 从字典表获取人员类型数据
            dict_staff_types = DictionaryItem.objects.filter(
                dictionary__code='staff_type',
                is_active=True
            ).order_by('sort_order')
            
            # 确保StaffType模型中有对应的数据
            for item in dict_staff_types:
                StaffType.objects.get_or_create(
                    code=item.code,
                    defaults={
                        'name': item.name,
                        'display_order': item.sort_order,
                        'is_active': item.is_active
                    }
                )
            
            # 更新StaffType的显示顺序和状态
            for staff_type in StaffType.objects.all():
                try:
                    dict_item = dict_staff_types.get(code=staff_type.code)
                    if staff_type.display_order != dict_item.sort_order or staff_type.is_active != dict_item.is_active:
                        staff_type.display_order = dict_item.sort_order
                        staff_type.is_active = dict_item.is_active
                        staff_type.save()
                except DictionaryItem.DoesNotExist:
                    # 如果字典表中没有对应的项，则将StaffType设为非活动状态
                    if staff_type.is_active:
                        staff_type.is_active = False
                        staff_type.save()
            
            # 设置staff_type字段的queryset，只显示活动的类型
            self.fields['staff_type'].queryset = StaffType.objects.filter(is_active=True).order_by('display_order')
            
        except Exception as e:
            # 如果同步失败，记录错误但不影响表单显示
            print(f"同步人员类型数据失败: {str(e)}")
        
        # 从字典表中获取职称数据
        try:
            # 使用正确的字典代码'staff_title'
            title_items = DictionaryItem.objects.filter(
                dictionary__code='staff_title',
                is_active=True
            ).order_by('sort_order')
            
            # 创建职称选项
            title_choices = [('', '---------')]
            title_choices.extend([(item.name, item.name) for item in title_items])
            
            # 设置职称字段的选项
            self.fields['title'] = forms.ChoiceField(
                choices=title_choices,
                required=False,
                widget=forms.Select(attrs={'class': 'form-select'}),
                label='职称'
            )
        except Exception as e:
            # 如果获取失败，保持原样
            print(f"获取职称数据失败: {str(e)}")
            
    def clean_department(self):
        department = self.cleaned_data.get('department')
        if not department:
            raise forms.ValidationError('科室是必选项')
        return department
        
    def clean_work_number(self):
        work_number = self.cleaned_data.get('work_number')
        if not work_number:
            raise forms.ValidationError('工号是必填项')
        return work_number
        
    def clean_name(self):
        name = self.cleaned_data.get('name')
        if not name:
            raise forms.ValidationError('姓名是必填项')
        return name
        
    def clean_staff_type(self):
        staff_type = self.cleaned_data.get('staff_type')
        if not staff_type:
            raise forms.ValidationError('人员类型是必选项')
        return staff_type

class BedForm(forms.ModelForm):
    class Meta:
        model = Bed
        fields = ['number', 'department', 'area', 'staff']
        widgets = {
            'number': forms.TextInput(attrs={
                'class': 'form-control',
                'pattern': '^[A-Za-z0-9-]+$',  # 只允许字母、数字和连字符
                'title': '床位号只能包含字母、数字和连字符'
            }),
            'department': forms.Select(attrs={'class': 'form-select'}),
            'area': forms.Select(attrs={'class': 'form-select'}),
            'staff': forms.Select(attrs={'class': 'form-select'})
        }

    def __init__(self, *args, **kwargs):
        department_id = kwargs.pop('department_id', None)
        super().__init__(*args, **kwargs)
        if department_id:
            try:
                department = Department.objects.get(pk=department_id)
                self.fields['department'].initial = department
                self.fields['department'].queryset = Department.objects.filter(pk=department_id)
                self.fields['department'].widget.attrs.update({
                    'readonly': 'readonly',
                    'class': 'form-select bg-light'
                })
                self.fields['department'].label = '科室'
                self.department_id = department_id
            except Department.DoesNotExist:
                pass
        
        # 设置区域字段为可选
        self.fields['area'].required = False
        self.fields['area'].empty_label = '请选择区域'
        # 设置工作人员字段为可选
        self.fields['staff'].required = False
        self.fields['staff'].empty_label = '请选择负责人'

    def clean_number(self):
        """验证床位号格式"""
        number = self.cleaned_data.get('number')
        if not number:
            raise forms.ValidationError('床位号不能为空')
        
        # 不允许"未知床位"
        if number.lower() == '未知床位':
            raise forms.ValidationError('不允许使用"未知床位"作为床位号')
            
        # 检查床位号格式
        import re
        if not re.match('^[A-Za-z0-9-]+$', number):
            raise forms.ValidationError('床位号只能包含字母、数字和连字符')
            
        return number

    def clean(self):
        """验证床位号在同一科室内的唯一性"""
        cleaned_data = super().clean()
        number = cleaned_data.get('number')
        department = cleaned_data.get('department')

        # 如果科室字段被禁用，使用初始化时保存的科室ID
        if hasattr(self, 'department_id'):
            department = Department.objects.get(pk=self.department_id)
            # 确保cleaned_data中有正确的科室
            cleaned_data['department'] = department

        if not department:
            raise forms.ValidationError('科室不能为空')

        if not number:
            raise forms.ValidationError('床位号不能为空')

        # 只在同一科室内检查床位号唯一性
        existing_query = Bed.objects.filter(
            department=department,
            number=number
        )

        # 如果是编辑现有床位，排除自身
        if self.instance.pk:
            existing_query = existing_query.exclude(pk=self.instance.pk)

        if existing_query.exists():
            existing_bed = existing_query.first()
            area_text = f"（{existing_bed.get_area_display()}）" if existing_bed.area else ""
            raise forms.ValidationError(
                f'在{department.name}已存在{number}号床位{area_text}，请使用其他床位号'
            )

        return cleaned_data

class QRCodeForm(forms.ModelForm):
    class Meta:
        model = QRCode
        fields = ['name', 'description', 'bed']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'description': forms.Textarea(attrs={'class': 'form-control', 'rows': 3}),
            'bed': forms.Select(attrs={'class': 'form-select'})
        }

class EvaluationForm(forms.ModelForm):
    class Meta:
        model = Evaluation
        fields = ['is_satisfied', 'comment', 'photo']
        widgets = {
            'is_satisfied': forms.RadioSelect(attrs={'class': 'form-check-input'}),
            'comment': forms.Textarea(attrs={'class': 'form-control', 'rows': 4, 'placeholder': '请输入您的评价原因...'}),
            'photo': forms.FileInput(attrs={'class': 'form-control'})
        }

# 新增用于Excel批量导入工作人员的表单
class BulkStaffImportForm(forms.Form):
    excel_file = forms.FileField(label='Excel文件', required=True)

class PrintTemplateForm(forms.ModelForm):
    class Meta:
        model = PrintTemplate
        fields = ['name', 'print_width', 'print_height', 'background_image', 
                 'qr_position_x', 'qr_position_y', 'qr_size', 'is_active', 'is_public']
        widgets = {
            'name': forms.TextInput(attrs={'class': 'form-control'}),
            'print_width': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '1000',
                'step': '1'
            }),
            'print_height': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '1',
                'max': '1000',
                'step': '1'
            }),
            'background_image': forms.FileInput(attrs={'class': 'form-control'}),
            'qr_position_x': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'step': '1'
            }),
            'qr_position_y': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '0',
                'step': '1'
            }),
            'qr_size': forms.NumberInput(attrs={
                'class': 'form-control',
                'min': '15',
                'max': '50',
                'step': '1'
            }),
            'is_active': forms.CheckboxInput(attrs={'class': 'form-check-input'}),
            'is_public': forms.CheckboxInput(attrs={'class': 'form-check-input'})
        }

    def clean(self):
        cleaned_data = super().clean()
        print_width = cleaned_data.get('print_width')
        print_height = cleaned_data.get('print_height')
        qr_size = cleaned_data.get('qr_size')
        qr_position_x = cleaned_data.get('qr_position_x')
        qr_position_y = cleaned_data.get('qr_position_y')
        is_public = cleaned_data.get('is_public')

        # 验证打印尺寸
        if print_width is not None:
            if float(print_width) < 1 or float(print_width) > 1000:
                raise forms.ValidationError('打印宽度必须在1到1000毫米之间')

        if print_height is not None:
            if float(print_height) < 1 or float(print_height) > 1000:
                raise forms.ValidationError('打印高度必须在1到1000毫米之间')

        # 验证二维码尺寸
        if qr_size is not None:
            if qr_size < 15 or qr_size > 50:
                raise forms.ValidationError('二维码尺寸必须在15到50毫米之间（建议20-30毫米）')

        # 验证坐标位置
        if qr_position_x is not None and print_width is not None:
            if qr_position_x < 0 or qr_position_x > float(print_width):
                raise forms.ValidationError(f'X坐标必须在0到{print_width}毫米之间')

        if qr_position_y is not None and print_height is not None:
            if qr_position_y < 0 or qr_position_y > float(print_height):
                raise forms.ValidationError(f'Y坐标必须在0到{print_height}毫米之间')

        # 验证二维码是否在打印范围内
        if all(v is not None for v in [qr_position_x, qr_position_y, qr_size, print_width, print_height]):
            # 检查右边界
            if qr_position_x + qr_size > float(print_width):
                raise forms.ValidationError('二维码超出了打印范围（右边界）')
            # 检查下边界
            if qr_position_y + qr_size > float(print_height):
                raise forms.ValidationError('二维码超出了打印范围（下边界）')

        # 如果是公共模板，确保没有关联科室
        if is_public and self.instance and self.instance.department:
            raise forms.ValidationError('公共模板不能关联到特定科室')

        return cleaned_data 