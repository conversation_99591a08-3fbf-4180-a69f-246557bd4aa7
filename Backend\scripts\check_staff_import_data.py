#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
工作人员导入数据检查和修复脚本

用途：
1. 检查数据库中科室和人员类型数据的完整性
2. 提供数据修复建议
3. 生成标准的导入模板

使用方法：
python manage.py shell < scripts/check_staff_import_data.py
"""

import os
import sys
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from qrmanager.models import Department, StaffType, DictionaryItem, Dictionary
from django.db import transaction

def check_departments():
    """检查科室数据"""
    print("=" * 50)
    print("🏥 科室数据检查")
    print("=" * 50)
    
    departments = Department.objects.all()
    print(f"总科室数量: {departments.count()}")
    
    if departments.count() == 0:
        print("❌ 警告：没有找到任何科室数据！")
        print("建议：请先添加科室数据")
        return False
    
    print("\n现有科室列表:")
    for i, dept in enumerate(departments, 1):
        print(f"{i:2d}. {dept.name} (ID: {dept.id})")
    
    return True

def check_staff_types():
    """检查人员类型数据"""
    print("\n" + "=" * 50)
    print("👥 人员类型数据检查")
    print("=" * 50)
    
    # 检查字典表中的人员类型
    try:
        staff_type_dict = Dictionary.objects.get(code='staff_type')
        dict_items = DictionaryItem.objects.filter(
            dictionary=staff_type_dict, 
            is_active=True
        )
        print(f"字典表中的人员类型数量: {dict_items.count()}")
        
        if dict_items.count() == 0:
            print("❌ 警告：字典表中没有找到激活的人员类型！")
            return False
        
        print("\n字典表中的人员类型:")
        for i, item in enumerate(dict_items, 1):
            print(f"{i:2d}. {item.name} (代码: {item.code})")
        
        # 检查StaffType表
        staff_types = StaffType.objects.all()
        print(f"\nStaffType表中的记录数量: {staff_types.count()}")
        
        print("\nStaffType表中的记录:")
        for i, st in enumerate(staff_types, 1):
            print(f"{i:2d}. {st.name} (代码: {st.code})")
        
        # 检查数据一致性
        print("\n🔍 数据一致性检查:")
        missing_in_staff_type = []
        for item in dict_items:
            if not StaffType.objects.filter(code=item.code).exists():
                missing_in_staff_type.append(item)
        
        if missing_in_staff_type:
            print("❌ 以下字典项在StaffType表中缺失:")
            for item in missing_in_staff_type:
                print(f"   - {item.name} (代码: {item.code})")
            return False
        else:
            print("✅ 数据一致性检查通过")
        
        return True
        
    except Dictionary.DoesNotExist:
        print("❌ 错误：未找到人员类型字典 (code='staff_type')")
        return False

def check_staff_titles():
    """检查职称数据"""
    print("\n" + "=" * 50)
    print("🎓 职称数据检查")
    print("=" * 50)
    
    try:
        title_dict = Dictionary.objects.get(code='staff_title')
        title_items = DictionaryItem.objects.filter(
            dictionary=title_dict, 
            is_active=True
        )
        print(f"职称数量: {title_items.count()}")
        
        if title_items.count() == 0:
            print("❌ 警告：没有找到激活的职称数据！")
            return False
        
        print("\n现有职称列表:")
        for i, item in enumerate(title_items, 1):
            print(f"{i:2d}. {item.name}")
        
        return True
        
    except Dictionary.DoesNotExist:
        print("❌ 错误：未找到职称字典 (code='staff_title')")
        return False

def create_sample_data():
    """创建示例数据"""
    print("\n" + "=" * 50)
    print("🔧 创建示例数据")
    print("=" * 50)
    
    try:
        with transaction.atomic():
            # 创建示例科室
            sample_departments = [
                "内科", "外科", "儿科", "妇产科", "急诊科", 
                "放射科", "检验科", "药剂科", "护理部", "行政科"
            ]
            
            created_depts = 0
            for dept_name in sample_departments:
                dept, created = Department.objects.get_or_create(
                    name=dept_name,
                    defaults={'description': f'{dept_name}示例数据'}
                )
                if created:
                    created_depts += 1
            
            print(f"✅ 创建了 {created_depts} 个科室")
            
            # 创建人员类型字典
            staff_type_dict, created = Dictionary.objects.get_or_create(
                code='staff_type',
                defaults={'name': '人员类型', 'description': '工作人员类型分类'}
            )
            
            # 创建示例人员类型
            sample_staff_types = [
                ("doctor", "医生"),
                ("nurse", "护士"),
                ("technician", "技师"),
                ("admin", "行政人员"),
                ("other", "其他")
            ]
            
            created_types = 0
            for code, name in sample_staff_types:
                # 创建字典项
                dict_item, created = DictionaryItem.objects.get_or_create(
                    dictionary=staff_type_dict,
                    code=code,
                    defaults={'name': name, 'sort_order': created_types + 1}
                )
                if created:
                    created_types += 1
                
                # 创建StaffType
                staff_type, created = StaffType.objects.get_or_create(
                    code=code,
                    defaults={'name': name, 'display_order': created_types}
                )
            
            print(f"✅ 创建了 {created_types} 个人员类型")
            
            # 创建职称字典
            title_dict, created = Dictionary.objects.get_or_create(
                code='staff_title',
                defaults={'name': '职称', 'description': '专业技术职称'}
            )
            
            # 创建示例职称
            sample_titles = [
                "主任医师", "副主任医师", "主治医师", "住院医师",
                "主任护师", "副主任护师", "主管护师", "护师", "护士",
                "主任技师", "副主任技师", "主管技师", "技师",
                "高级工程师", "工程师", "助理工程师"
            ]
            
            created_titles = 0
            for i, title_name in enumerate(sample_titles):
                title_item, created = DictionaryItem.objects.get_or_create(
                    dictionary=title_dict,
                    code=f'title_{i+1:02d}',
                    defaults={'name': title_name, 'sort_order': i + 1}
                )
                if created:
                    created_titles += 1
            
            print(f"✅ 创建了 {created_titles} 个职称")
            
    except Exception as e:
        print(f"❌ 创建示例数据失败: {str(e)}")

def generate_import_template():
    """生成导入模板"""
    print("\n" + "=" * 50)
    print("📋 生成导入模板")
    print("=" * 50)
    
    try:
        import openpyxl
        from openpyxl.styles import Font, PatternFill, Alignment
        
        wb = openpyxl.Workbook()
        ws = wb.active
        ws.title = "工作人员导入模板"
        
        # 设置表头
        headers = ['工号', '姓名', '人员类型', '职称', '科室']
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.font = Font(bold=True, color="FFFFFF")
            cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
            cell.alignment = Alignment(horizontal="center")
        
        # 添加示例数据
        sample_data = [
            ['001', '张三', '医生', '主治医师', '内科'],
            ['002', '李四', '护士', '主管护师', '外科'],
            ['003', '王五', '技师', '主管技师', '检验科'],
        ]
        
        for row_idx, row_data in enumerate(sample_data, 2):
            for col_idx, value in enumerate(row_data, 1):
                ws.cell(row=row_idx, column=col_idx, value=value)
        
        # 调整列宽
        column_widths = [10, 15, 15, 15, 15]
        for col, width in enumerate(column_widths, 1):
            ws.column_dimensions[openpyxl.utils.get_column_letter(col)].width = width
        
        # 保存文件
        template_path = 'staff_import_template.xlsx'
        wb.save(template_path)
        print(f"✅ 导入模板已生成: {template_path}")
        
        # 显示可用的数据选项
        print("\n📝 可用的数据选项:")
        
        departments = Department.objects.all()
        if departments.exists():
            print("科室选项:")
            for dept in departments:
                print(f"  - {dept.name}")
        
        staff_types = DictionaryItem.objects.filter(
            dictionary__code='staff_type', is_active=True
        )
        if staff_types.exists():
            print("\n人员类型选项:")
            for st in staff_types:
                print(f"  - {st.name}")
        
        titles = DictionaryItem.objects.filter(
            dictionary__code='staff_title', is_active=True
        )
        if titles.exists():
            print("\n职称选项:")
            for title in titles:
                print(f"  - {title.name}")
        
    except ImportError:
        print("❌ 需要安装 openpyxl: pip install openpyxl")
    except Exception as e:
        print(f"❌ 生成模板失败: {str(e)}")

def main():
    """主函数"""
    print("🔍 工作人员导入数据检查工具")
    print("=" * 50)
    
    # 检查各项数据
    dept_ok = check_departments()
    staff_type_ok = check_staff_types()
    title_ok = check_staff_titles()
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 检查结果总结")
    print("=" * 50)
    
    if dept_ok and staff_type_ok and title_ok:
        print("✅ 所有数据检查通过，可以正常导入工作人员")
        generate_import_template()
    else:
        print("❌ 发现数据问题，建议修复后再进行导入")
        
        user_input = input("\n是否创建示例数据？(y/n): ")
        if user_input.lower() in ['y', 'yes', '是']:
            create_sample_data()
            print("\n✅ 示例数据创建完成，请重新运行检查")

if __name__ == "__main__":
    main()
