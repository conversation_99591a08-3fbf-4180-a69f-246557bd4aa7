import os
import re
from django.core.management.base import BaseCommand
from django.conf import settings
from qrmanager.models import PrintTemplate, Department

class Command(BaseCommand):
    help = '清理冗余的打印模板文件，只保留每个科室最新的模板文件'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将要删除的文件，不实际删除',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # 获取模板目录
        template_dir = os.path.join(settings.MEDIA_ROOT, 'print_templates')
        if not os.path.exists(template_dir):
            self.stdout.write(self.style.ERROR(f'模板目录不存在: {template_dir}'))
            return
            
        # 获取所有科室编码
        department_codes = list(Department.objects.values_list('code', flat=True))
        department_codes.append('public')  # 添加公共模板
        
        # 获取数据库中的模板记录
        db_templates = {}
        for template in PrintTemplate.objects.all():
            code = template.department.code if template.department else 'public'
            db_templates[code] = os.path.basename(template.background_image.name)
        
        # 正则表达式匹配模板文件名 - 支持新旧两种格式
        old_template_pattern = re.compile(r'template_([a-zA-Z0-9]+)_(\d+)\.([a-zA-Z]+)')
        new_template_pattern = re.compile(r'template_([a-zA-Z0-9]+)\.([a-zA-Z]+)')
        # 匹配带后缀的格式，如template_1024_KCrpOKA.JPG
        suffix_pattern = re.compile(r'template_([a-zA-Z0-9]+)_([a-zA-Z0-9]+)\.([a-zA-Z]+)')
        
        # 按科室分组文件
        files_by_dept = {}
        for filename in os.listdir(template_dir):
            # 尝试匹配旧格式（带时间戳）
            match = old_template_pattern.match(filename)
            if match:
                dept_code = match.group(1)
                timestamp = match.group(2)
                if dept_code not in files_by_dept:
                    files_by_dept[dept_code] = []
                files_by_dept[dept_code].append((filename, timestamp, 'old'))
                continue
                
            # 尝试匹配新格式（不带时间戳）
            match = new_template_pattern.match(filename)
            if match:
                dept_code = match.group(1)
                if dept_code not in files_by_dept:
                    files_by_dept[dept_code] = []
                # 使用文件修改时间作为排序依据
                file_path = os.path.join(template_dir, filename)
                mod_time = str(int(os.path.getmtime(file_path)))
                files_by_dept[dept_code].append((filename, mod_time, 'new'))
                continue
                
            # 尝试匹配带后缀的格式
            match = suffix_pattern.match(filename)
            if match:
                dept_code = match.group(1)
                if dept_code not in files_by_dept:
                    files_by_dept[dept_code] = []
                # 使用文件修改时间作为排序依据
                file_path = os.path.join(template_dir, filename)
                mod_time = str(int(os.path.getmtime(file_path)))
                files_by_dept[dept_code].append((filename, mod_time, 'suffix'))
        
        # 统计信息
        total_files = 0
        deleted_files = 0
        
        # 处理每个科室的文件
        for dept_code, files in files_by_dept.items():
            # 按时间戳排序，最新的在最后
            files.sort(key=lambda x: x[1])
            
            # 如果数据库中有记录，使用数据库中的文件名作为要保留的文件
            keep_file = None
            if dept_code in db_templates:
                keep_file = db_templates[dept_code]
            # 否则保留最新的一个文件
            elif files:
                keep_file = files[-1][0]
                
            # 删除其他文件
            for filename, _, _ in files:
                if filename != keep_file:
                    file_path = os.path.join(template_dir, filename)
                    total_files += 1
                    
                    if dry_run:
                        self.stdout.write(f'将删除: {filename}')
                    else:
                        try:
                            os.remove(file_path)
                            deleted_files += 1
                            self.stdout.write(f'已删除: {filename}')
                        except Exception as e:
                            self.stdout.write(self.style.ERROR(f'删除失败 {filename}: {str(e)}'))
        
        # 输出结果
        if dry_run:
            self.stdout.write(self.style.SUCCESS(f'发现 {total_files} 个冗余模板文件可以删除'))
        else:
            self.stdout.write(self.style.SUCCESS(f'已删除 {deleted_files} 个冗余模板文件，共发现 {total_files} 个')) 