#!/usr/bin/env python
"""
完整的API流程测试 - 验证新加密字符串的端到端功能
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param, secure_qr_access
from qrmanager.models import QRCode, Evaluation
import uuid

def test_complete_api_flow():
    """完整的API流程测试"""
    print("=" * 80)
    print("🔄 完整API流程测试 - 新加密算法端到端验证")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    try:
        # 步骤1: 获取数据库中现有的二维码
        print("步骤1: 获取测试数据")
        qrcode_obj = QRCode.objects.first()
        
        if not qrcode_obj:
            print("❌ 数据库中没有二维码，无法测试")
            return False
        
        print(f"   测试二维码: {qrcode_obj.code}")
        print(f"   关联床位: {qrcode_obj.bed}")
        print(f"   是否激活: {qrcode_obj.is_active}")
        print()
        
        # 步骤2: 使用新算法加密
        print("步骤2: 新算法加密")
        try:
            encrypted_param = encrypt_qr_param(qrcode_obj.code)
            print(f"   原始UUID: {qrcode_obj.code}")
            print(f"   加密结果: {encrypted_param}")
            print(f"   加密长度: {len(encrypted_param)} 字符")
            print(f"   加密状态: ✅ 成功")
        except Exception as e:
            print(f"   加密状态: ❌ 失败 - {e}")
            return False
        print()
        
        # 步骤3: 本地解密验证
        print("步骤3: 本地解密验证")
        try:
            # 直接解密测试
            decrypted_data = decrypt_qr_param(encrypted_param)
            decrypted_uuid = decrypted_data['uuid']
            print(f"   解密UUID: {decrypted_uuid}")
            print(f"   UUID匹配: {'✅ 是' if decrypted_uuid == str(qrcode_obj.code) else '❌ 否'}")
            
            # secure_qr_access测试
            secure_uuid = secure_qr_access(encrypted_param)
            print(f"   安全访问: {secure_uuid}")
            print(f"   安全匹配: {'✅ 是' if secure_uuid == str(qrcode_obj.code) else '❌ 否'}")
            print(f"   本地验证: ✅ 成功")
        except Exception as e:
            print(f"   本地验证: ❌ 失败 - {e}")
            return False
        print()
        
        # 步骤4: API验证接口测试
        print("步骤4: API验证接口测试")
        verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
        verify_data = {
            "qr_param": encrypted_param,
            "client_ip": "127.0.0.1"
        }
        
        print(f"   请求URL: {verify_url}")
        print(f"   请求参数: qr_param={encrypted_param[:30]}...")
        
        try:
            response = requests.post(
                verify_url,
                json=verify_data,
                headers={
                    'Content-Type': 'application/json',
                    'Accept': 'application/json'
                },
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"   响应状态: {response_data.get('status', 'unknown')}")
                
                # 检查返回的数据
                if 'data' in response_data:
                    data = response_data['data']
                    print(f"   返回床位: {data.get('bed_info', {}).get('number', 'N/A')}")
                    print(f"   返回科室: {data.get('department_info', {}).get('name', 'N/A')}")
                    print(f"   临时令牌: {'有' if data.get('temp_token') else '无'}")
                
                print(f"   验证API: ✅ 成功")
                verify_success = True
                temp_token = response_data.get('data', {}).get('temp_token')
            else:
                print(f"   错误信息: {response.text}")
                print(f"   验证API: ❌ 失败")
                verify_success = False
                temp_token = None
                
        except Exception as e:
            print(f"   验证API: ❌ 异常 - {e}")
            verify_success = False
            temp_token = None
        print()
        
        # 步骤5: API提交接口测试（如果验证成功）
        if verify_success and temp_token:
            print("步骤5: API提交接口测试")
            submit_url = f"{BASE_URL}/api/v1/public/submit-evaluation/"
            submit_data = {
                "qr_param": encrypted_param,
                "temp_token": temp_token,
                "comment": "新加密算法测试评价 - 自动化测试",
                "staff_evaluations": [],
                "hospital_number": "AUTO_TEST_001",
                "phone_number": "13800138000"
            }
            
            print(f"   请求URL: {submit_url}")
            print(f"   评价内容: {submit_data['comment']}")
            print(f"   临时令牌: {temp_token[:20]}...")
            
            try:
                response = requests.post(
                    submit_url,
                    json=submit_data,
                    headers={
                        'Content-Type': 'application/json',
                        'Accept': 'application/json'
                    },
                    timeout=10
                )
                
                print(f"   响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    print(f"   响应状态: {response_data.get('status', 'unknown')}")
                    print(f"   提交API: ✅ 成功")
                    submit_success = True
                    
                    # 验证数据库中是否创建了评价记录
                    try:
                        latest_evaluation = Evaluation.objects.filter(
                            qrcode=qrcode_obj,
                            comment__contains="新加密算法测试"
                        ).latest('created_at')
                        print(f"   数据库记录: ✅ 已创建 (ID: {latest_evaluation.id})")
                    except Evaluation.DoesNotExist:
                        print(f"   数据库记录: ❌ 未找到")
                else:
                    print(f"   错误信息: {response.text}")
                    print(f"   提交API: ❌ 失败")
                    submit_success = False
                    
            except Exception as e:
                print(f"   提交API: ❌ 异常 - {e}")
                submit_success = False
        else:
            print("步骤5: 跳过提交测试（验证失败或无令牌）")
            submit_success = False
        print()
        
        # 步骤6: 结果总结
        print("步骤6: 测试结果总结")
        print("=" * 80)
        
        all_success = verify_success and submit_success
        
        print(f"✅ 加密功能: 正常")
        print(f"✅ 本地解密: 正常")
        print(f"{'✅' if verify_success else '❌'} API验证: {'正常' if verify_success else '失败'}")
        print(f"{'✅' if submit_success else '❌'} API提交: {'正常' if submit_success else '失败'}")
        
        if all_success:
            print("\n🎉 完整流程测试成功！")
            print("✅ 新加密算法与前端API完全兼容")
            print("✅ 加密字符串可以正确访问API接口")
            print("✅ 解密功能完全正常")
            print("✅ 数据提交功能正常")
        elif verify_success:
            print("\n⚠️  部分功能正常")
            print("✅ 验证API正常工作")
            print("❌ 提交API存在问题")
        else:
            print("\n❌ 测试失败")
            print("❌ API验证失败，需要进一步调试")
        
        return all_success
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        return False

def test_multiple_qrcodes():
    """测试多个二维码的兼容性"""
    print("\n" + "=" * 80)
    print("🔄 多二维码兼容性测试")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    # 获取多个二维码进行测试
    qrcodes = QRCode.objects.filter(is_active=True)[:3]
    
    if not qrcodes:
        print("❌ 没有可用的二维码进行测试")
        return False
    
    success_count = 0
    total_count = len(qrcodes)
    
    for i, qrcode_obj in enumerate(qrcodes, 1):
        print(f"\n测试 {i}/{total_count}: {qrcode_obj.code}")
        
        try:
            # 加密
            encrypted = encrypt_qr_param(qrcode_obj.code)
            print(f"   加密: ✅ {encrypted[:30]}...")
            
            # API验证
            verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
            response = requests.post(
                verify_url,
                json={"qr_param": encrypted, "client_ip": "127.0.0.1"},
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"   API验证: ✅ 成功")
                success_count += 1
            else:
                print(f"   API验证: ❌ 失败 ({response.status_code})")
                
        except Exception as e:
            print(f"   测试: ❌ 异常 - {e}")
    
    success_rate = (success_count / total_count) * 100
    print(f"\n📊 兼容性测试结果:")
    print(f"   成功: {success_count}/{total_count}")
    print(f"   成功率: {success_rate:.1f}%")
    
    return success_rate == 100.0

if __name__ == "__main__":
    print("🚀 开始新加密算法的完整API测试...")
    
    # 完整流程测试
    main_success = test_complete_api_flow()
    
    # 多二维码兼容性测试
    compatibility_success = test_multiple_qrcodes()
    
    print("\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if main_success and compatibility_success:
        print("🎉 所有测试完美通过！")
        print("✅ 新加密字符串完全兼容前端API接口")
        print("✅ 解密功能100%正常")
        print("✅ 可以安全部署到生产环境")
    elif main_success:
        print("⚠️  主要功能正常，兼容性需要关注")
        print("✅ 核心API流程正常")
        print("❌ 部分二维码存在兼容性问题")
    else:
        print("❌ 测试失败，需要进一步调试")
        print("❌ API接口存在问题")
    
    print("=" * 80)
