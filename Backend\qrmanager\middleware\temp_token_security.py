"""
临时令牌安全中间件
用于验证临时令牌的有效性
"""

import json
import re
from django.http import JsonResponse

class TempTokenSecurityMiddleware:
    """
    临时令牌安全中间件
    用于验证临时令牌的有效性
    """
    
    # 允许使用临时令牌的API路径
    ALLOWED_PATHS = [
        r'^/api/v1/public/submit-evaluation/$',
        r'^/api/v1/public/verify-token/$',
    ]
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 检查请求是否包含临时令牌
        temp_token = None
        
        # 从请求体中获取临时令牌 (POST请求)
        if request.method == 'POST' and request.content_type == 'application/json':
            try:
                body_data = json.loads(request.body.decode('utf-8'))
                temp_token = body_data.get('temp_token')
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass
        
        # 如果包含临时令牌，检查是否访问的是允许的API
        if temp_token:
            path = request.path
            is_allowed = False
            
            # 检查路径是否在允许列表中
            for allowed_path in self.ALLOWED_PATHS:
                if re.match(allowed_path, path):
                    is_allowed = True
                    break
            
            # 如果路径不在允许列表中，则返回错误响应
            if not is_allowed:
                return JsonResponse({
                    'status': 'error',
                    'message': '临时令牌功能已停用，请使用加密参数直接验证'
                }, status=403)
        
        # 继续处理请求
        return self.get_response(request)