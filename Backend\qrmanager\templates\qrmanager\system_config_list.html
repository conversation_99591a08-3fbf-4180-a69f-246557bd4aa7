{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">{{ subtitle }}</h3>
                    <div class="card-tools">
                        <a href="{% url 'qrmanager:initialize_system_configs' %}" class="btn btn-sm btn-outline-primary">
                            <i class="fas fa-sync"></i> 初始化配置
                        </a>
                    </div>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> 
                        <strong>提示：</strong> 系统配置用于控制系统的全局行为。修改这些配置可能会影响系统的运行，请谨慎操作。
                        <br>
                        <strong>二维码有效期</strong>：对于需要打印的二维码，建议设置为0（永不过期）。如果设置了有效期，请确保在打印二维码时告知使用者。
                        <br>
                        <strong>前端URL(frontend_url)</strong>：用于生成二维码和重定向到前端页面，可以是IP地址(如http://************:8000)或域名(如https://hospital.example.com)。
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-bordered table-striped">
                            <thead>
                                <tr>
                                    <th>配置键</th>
                                    <th>配置值</th>
                                    <th>描述</th>
                                    <th>公开</th>
                                    <th>更新时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for config in configs %}
                                <tr>
                                    <td>{{ config.key }}</td>
                                    <td>
                                        {% if config.key == 'qrcode_token_expiry' %}
                                            {% if config.value == '0' %}
                                                <span class="badge badge-success">永不过期</span>
                                            {% else %}
                                                {{ config.value }} 秒
                                                (约 {{ config.value|floatformat:"0"|divisibleby:"86400" }} 天)
                                            {% endif %}
                                        {% elif config.key == 'frontend_url' %}
                                            <a href="{{ config.value }}" target="_blank" class="text-primary">
                                                <i class="fas fa-external-link-alt"></i> {{ config.value }}
                                            </a>
                                        {% elif config.key == 'enable_sentiment_analysis' or config.key == 'enable_qrcode_encryption' %}
                                            {% if config.value == 'true' %}
                                                <span class="badge badge-success">已启用</span>
                                            {% else %}
                                                <span class="badge badge-danger">已禁用</span>
                                            {% endif %}
                                        {% else %}
                                            {{ config.value }}
                                        {% endif %}
                                    </td>
                                    <td>{{ config.description }}</td>
                                    <td>
                                        {% if config.is_public %}
                                            <span class="badge badge-success">是</span>
                                        {% else %}
                                            <span class="badge badge-secondary">否</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ config.updated_at|date:"Y-m-d H:i" }}</td>
                                    <td>
                                        <a href="{% url 'qrmanager:system_config_update' config.id %}" class="btn btn-sm btn-primary">
                                            <i class="fas fa-edit"></i> 编辑
                                        </a>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center">暂无配置，请点击"初始化配置"按钮</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 