#!/usr/bin/env python
"""
基于二维码的精准安全系统
- 通过56字符加密字符串认证
- 每个二维码(UUID)一分钟只能评价2次
- IP记录和指纹追踪
- 异常行为检测
"""

import time
import hashlib
import json
from django.core.cache import cache
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.utils import timezone
import logging

logger = logging.getLogger('qrcode_security')

class QRCodeSecurityMiddleware(MiddlewareMixin):
    """
    二维码安全中间件 - 基于加密字符串认证的安全控制
    """
    
    # 🔒 核心安全配置
    SECURITY_CONFIG = {
        # 二维码评价限制：每个UUID一分钟最多2次评价
        'qrcode_evaluation_limit': {
            'max_evaluations': 2,
            'time_window': 60,  # 秒
        },
        
        # 二维码验证限制：每个UUID一分钟最多10次验证
        'qrcode_verification_limit': {
            'max_verifications': 10,
            'time_window': 60,
        },
        
        # IP级别限制：防止单个IP大量请求
        'ip_limits': {
            'max_requests_per_minute': 30,
            'max_unique_qrcodes_per_hour': 20,  # 每小时最多访问20个不同二维码
        },
        
        # 异常检测阈值
        'anomaly_detection': {
            'rapid_qrcode_switching': 5,  # 1分钟内访问超过5个不同二维码
            'repeated_failed_attempts': 5,  # 连续5次失败尝试
            'suspicious_user_agent': True,
        }
    }
    
    def process_request(self, request):
        """处理请求前的安全检查"""
        # 只处理公开API
        if not self._is_qrcode_api(request):
            return None
        
        # 获取客户端信息
        client_info = self._extract_client_info(request)
        
        # 1. 基础安全检查
        basic_check = self._basic_security_check(request, client_info)
        if basic_check:
            return basic_check
        
        # 2. 提取并验证二维码参数
        qr_validation = self._validate_qrcode_param(request)
        if qr_validation['error']:
            return qr_validation['response']
        
        uuid = qr_validation['uuid']
        encrypted_param = qr_validation['encrypted_param']
        
        # 3. 二维码级别的安全检查
        qrcode_check = self._qrcode_security_check(request, uuid, client_info)
        if qrcode_check:
            return qrcode_check
        
        # 4. IP级别的安全检查
        ip_check = self._ip_security_check(request, client_info, uuid)
        if ip_check:
            return ip_check
        
        # 5. 异常行为检测
        anomaly_check = self._anomaly_detection(request, client_info, uuid)
        if anomaly_check:
            return anomaly_check
        
        # 6. 记录合法访问
        self._record_access(request, uuid, client_info)
        
        # 将UUID附加到请求对象，供后续使用
        request.qrcode_uuid = uuid
        request.client_info = client_info
        
        return None
    
    def _is_qrcode_api(self, request):
        """判断是否为二维码相关API"""
        qrcode_paths = [
            '/api/v1/public/qrcode/verify/',
            '/api/v1/public/submit-evaluation/'
        ]
        return any(request.path.startswith(path) for path in qrcode_paths)
    
    def _extract_client_info(self, request):
        """提取客户端信息"""
        # 获取真实IP
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            client_ip = x_forwarded_for.split(',')[0].strip()
        else:
            client_ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
        
        # 生成浏览器指纹
        fingerprint_data = {
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'accept_language': request.META.get('HTTP_ACCEPT_LANGUAGE', ''),
            'accept_encoding': request.META.get('HTTP_ACCEPT_ENCODING', ''),
            'accept': request.META.get('HTTP_ACCEPT', ''),
        }
        
        # 生成指纹哈希
        fingerprint_string = '|'.join(fingerprint_data.values())
        fingerprint_hash = hashlib.md5(fingerprint_string.encode()).hexdigest()[:16]
        
        return {
            'ip': client_ip,
            'fingerprint': fingerprint_hash,
            'user_agent': fingerprint_data['user_agent'],
            'timestamp': int(time.time())
        }
    
    def _basic_security_check(self, request, client_info):
        """基础安全检查"""
        # 检查User-Agent
        if not client_info['user_agent']:
            logger.warning(f"缺少User-Agent: {client_info['ip']}")
            return JsonResponse({
                'status': 'error',
                'message': '请求格式不正确',
                'error_code': 'MISSING_USER_AGENT'
            }, status=400)
        
        # 检查恶意User-Agent
        malicious_patterns = ['sqlmap', 'nikto', 'nmap', 'masscan', 'bot', 'crawler']
        user_agent_lower = client_info['user_agent'].lower()
        if any(pattern in user_agent_lower for pattern in malicious_patterns):
            logger.warning(f"恶意User-Agent: {client_info['ip']} - {client_info['user_agent']}")
            return JsonResponse({
                'status': 'error',
                'message': '访问被拒绝',
                'error_code': 'MALICIOUS_USER_AGENT'
            }, status=403)
        
        return None
    
    def _validate_qrcode_param(self, request):
        """验证二维码参数"""
        try:
            # 从请求中获取加密参数
            if request.method == 'POST':
                data = json.loads(request.body)
                encrypted_param = data.get('qr_param', '')
            else:
                encrypted_param = request.GET.get('qr_param', '')
            
            if not encrypted_param:
                return {
                    'error': True,
                    'response': JsonResponse({
                        'status': 'error',
                        'message': '缺少二维码参数',
                        'error_code': 'MISSING_QR_PARAM'
                    }, status=400)
                }
            
            # 使用现有的安全访问函数解密
            from qrmanager.security import secure_qr_access
            uuid = secure_qr_access(encrypted_param)
            
            return {
                'error': False,
                'uuid': uuid,
                'encrypted_param': encrypted_param
            }
            
        except Exception as e:
            logger.warning(f"二维码参数验证失败: {e}")
            return {
                'error': True,
                'response': JsonResponse({
                    'status': 'error',
                    'message': '二维码参数无效',
                    'error_code': 'INVALID_QR_PARAM'
                }, status=400)
            }
    
    def _qrcode_security_check(self, request, uuid, client_info):
        """二维码级别的安全检查"""
        current_time = int(time.time())
        
        # 确定操作类型
        if 'evaluation' in request.path:
            operation = 'evaluation'
            config = self.SECURITY_CONFIG['qrcode_evaluation_limit']
        else:
            operation = 'verification'
            config = self.SECURITY_CONFIG['qrcode_verification_limit']
        
        # 检查二维码操作频率
        cache_key = f"qrcode_limit:{uuid}:{operation}"
        window_start = current_time - config['time_window']
        
        # 获取时间窗口内的操作记录
        operations = cache.get(cache_key, [])
        operations = [t for t in operations if t > window_start]
        
        if len(operations) >= config['max_evaluations' if operation == 'evaluation' else 'max_verifications']:
            logger.warning(f"二维码{operation}频率超限: {uuid}, IP: {client_info['ip']}")
            
            # 记录超限事件
            self._record_security_event('qrcode_rate_limit', {
                'uuid': uuid,
                'operation': operation,
                'client_info': client_info,
                'attempts': len(operations)
            })
            
            return JsonResponse({
                'status': 'error',
                'message': f'该二维码{operation}过于频繁，请稍后再试',
                'error_code': 'QRCODE_RATE_LIMITED',
                'retry_after': config['time_window']
            }, status=429)
        
        # 更新操作记录
        operations.append(current_time)
        cache.set(cache_key, operations, config['time_window'])
        
        return None
    
    def _ip_security_check(self, request, client_info, uuid):
        """IP级别的安全检查"""
        current_time = int(time.time())
        ip = client_info['ip']
        
        # 1. IP请求频率检查
        ip_requests_key = f"ip_requests:{ip}:{current_time // 60}"
        ip_requests = cache.get(ip_requests_key, 0)
        
        if ip_requests >= self.SECURITY_CONFIG['ip_limits']['max_requests_per_minute']:
            logger.warning(f"IP请求频率超限: {ip}")
            return JsonResponse({
                'status': 'error',
                'message': '请求过于频繁，请稍后再试',
                'error_code': 'IP_RATE_LIMITED'
            }, status=429)
        
        cache.set(ip_requests_key, ip_requests + 1, 60)
        
        # 2. IP访问的唯一二维码数量检查
        ip_qrcodes_key = f"ip_qrcodes:{ip}:{current_time // 3600}"
        ip_qrcodes = cache.get(ip_qrcodes_key, set())
        ip_qrcodes.add(uuid)
        
        if len(ip_qrcodes) > self.SECURITY_CONFIG['ip_limits']['max_unique_qrcodes_per_hour']:
            logger.warning(f"IP访问二维码数量超限: {ip}, 数量: {len(ip_qrcodes)}")
            
            # 记录可疑行为
            self._record_security_event('suspicious_qrcode_access', {
                'ip': ip,
                'unique_qrcodes': len(ip_qrcodes),
                'fingerprint': client_info['fingerprint']
            })
            
            return JsonResponse({
                'status': 'error',
                'message': '访问异常，请联系管理员',
                'error_code': 'SUSPICIOUS_ACCESS'
            }, status=403)
        
        cache.set(ip_qrcodes_key, ip_qrcodes, 3600)
        
        return None
    
    def _anomaly_detection(self, request, client_info, uuid):
        """异常行为检测"""
        current_time = int(time.time())
        ip = client_info['ip']
        fingerprint = client_info['fingerprint']
        
        # 1. 快速切换二维码检测
        qrcode_switching_key = f"qrcode_switching:{ip}:{current_time // 60}"
        recent_qrcodes = cache.get(qrcode_switching_key, set())
        recent_qrcodes.add(uuid)
        
        if len(recent_qrcodes) > self.SECURITY_CONFIG['anomaly_detection']['rapid_qrcode_switching']:
            logger.warning(f"检测到快速切换二维码: {ip}, 数量: {len(recent_qrcodes)}")
            
            self._record_security_event('rapid_qrcode_switching', {
                'ip': ip,
                'fingerprint': fingerprint,
                'qrcode_count': len(recent_qrcodes)
            })
            
            return JsonResponse({
                'status': 'error',
                'message': '检测到异常访问模式',
                'error_code': 'ANOMALY_DETECTED'
            }, status=429)
        
        cache.set(qrcode_switching_key, recent_qrcodes, 60)
        
        # 2. 指纹一致性检查
        fingerprint_key = f"fingerprint_consistency:{uuid}"
        stored_fingerprints = cache.get(fingerprint_key, set())
        stored_fingerprints.add(fingerprint)
        
        # 如果同一个二维码被太多不同指纹访问，可能是异常
        if len(stored_fingerprints) > 10:  # 阈值可调整
            logger.info(f"二维码被多个指纹访问: {uuid}, 指纹数: {len(stored_fingerprints)}")
        
        cache.set(fingerprint_key, stored_fingerprints, 3600)
        
        return None
    
    def _record_access(self, request, uuid, client_info):
        """记录合法访问"""
        access_record = {
            'uuid': uuid,
            'ip': client_info['ip'],
            'fingerprint': client_info['fingerprint'],
            'user_agent': client_info['user_agent'],
            'path': request.path,
            'method': request.method,
            'timestamp': client_info['timestamp']
        }
        
        # 存储访问记录
        access_key = f"access_record:{uuid}:{client_info['timestamp']}"
        cache.set(access_key, access_record, 86400)  # 保存24小时
        
        # 更新统计信息
        stats_key = f"qrcode_stats:{uuid}:{client_info['timestamp'] // 3600}"
        stats = cache.get(stats_key, {'access_count': 0, 'unique_ips': set()})
        stats['access_count'] += 1
        stats['unique_ips'].add(client_info['ip'])
        cache.set(stats_key, stats, 3600)
    
    def _record_security_event(self, event_type, event_data):
        """记录安全事件"""
        event_record = {
            'type': event_type,
            'data': event_data,
            'timestamp': int(time.time())
        }
        
        # 存储安全事件
        event_key = f"security_event:{event_type}:{event_record['timestamp']}"
        cache.set(event_key, event_record, 86400)
        
        # 记录到日志
        logger.warning(f"安全事件: {event_type} - {event_data}")

# 🔧 安全管理工具函数

def get_qrcode_security_stats(uuid):
    """获取二维码的安全统计"""
    current_time = int(time.time())
    
    stats = {
        'uuid': uuid,
        'recent_access': {
            'last_hour': 0,
            'last_24_hours': 0,
            'unique_ips': set(),
            'unique_fingerprints': set()
        },
        'rate_limits': {
            'evaluation': {'current': 0, 'limit': 2},
            'verification': {'current': 0, 'limit': 10}
        },
        'security_events': []
    }
    
    # 统计最近访问
    for hours_back in range(24):
        hour_timestamp = current_time - (hours_back * 3600)
        stats_key = f"qrcode_stats:{uuid}:{hour_timestamp // 3600}"
        hour_stats = cache.get(stats_key, {'access_count': 0, 'unique_ips': set()})
        
        if hours_back == 0:
            stats['recent_access']['last_hour'] = hour_stats['access_count']
        
        stats['recent_access']['last_24_hours'] += hour_stats['access_count']
        stats['recent_access']['unique_ips'].update(hour_stats['unique_ips'])
    
    # 统计当前速率限制状态
    for operation in ['evaluation', 'verification']:
        cache_key = f"qrcode_limit:{uuid}:{operation}"
        operations = cache.get(cache_key, [])
        window_start = current_time - 60
        recent_operations = [t for t in operations if t > window_start]
        stats['rate_limits'][operation]['current'] = len(recent_operations)
    
    # 转换set为list以便JSON序列化
    stats['recent_access']['unique_ips'] = list(stats['recent_access']['unique_ips'])
    stats['recent_access']['unique_fingerprints'] = list(stats['recent_access']['unique_fingerprints'])
    
    return stats

def get_ip_security_profile(ip):
    """获取IP的安全档案"""
    current_time = int(time.time())
    
    profile = {
        'ip': ip,
        'activity': {
            'requests_last_minute': 0,
            'unique_qrcodes_last_hour': 0,
            'total_requests_today': 0
        },
        'security_events': [],
        'risk_score': 0
    }
    
    # 统计活动
    requests_key = f"ip_requests:{ip}:{current_time // 60}"
    profile['activity']['requests_last_minute'] = cache.get(requests_key, 0)
    
    qrcodes_key = f"ip_qrcodes:{ip}:{current_time // 3600}"
    qrcodes = cache.get(qrcodes_key, set())
    profile['activity']['unique_qrcodes_last_hour'] = len(qrcodes)
    
    # 计算风险评分
    if profile['activity']['requests_last_minute'] > 20:
        profile['risk_score'] += 30
    if profile['activity']['unique_qrcodes_last_hour'] > 15:
        profile['risk_score'] += 40
    
    return profile
