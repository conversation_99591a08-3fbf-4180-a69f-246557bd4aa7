"""
环境变量处理模块，用于从.env文件加载环境变量
"""

import os
import re
from pathlib import Path

def load_env(env_file='.env'):
    """
    从.env文件加载环境变量
    
    参数:
        env_file (str): .env文件路径，默认为'.env'
    """
    # 获取项目根目录
    base_dir = Path(__file__).resolve().parent.parent
    env_path = os.path.join(base_dir, env_file)
    
    # 检查.env文件是否存在
    if not os.path.exists(env_path):
        print(f"警告: 环境变量文件 {env_path} 不存在")
        return
    
    # 读取.env文件
    with open(env_path, 'r', encoding='utf-8') as f:
        for line in f:
            # 跳过注释和空行
            if line.startswith('#') or not line.strip():
                continue
            
            # 解析键值对
            if '=' in line:
                key, value = line.strip().split('=', 1)
                key = key.strip()
                value = value.strip()
                
                # 处理引号
                if (value.startswith('"') and value.endswith('"')) or \
                   (value.startswith("'") and value.endswith("'")):
                    value = value[1:-1]
                
                # 处理布尔值
                if value.lower() == 'true':
                    value = 'True'
                elif value.lower() == 'false':
                    value = 'False'
                
                # 处理逗号分隔的列表
                if ',' in value and not re.search(r'[\'"].*,.*[\'"]', value):
                    # 确保不是在引号内的逗号
                    value = '[' + ', '.join(f'"{item.strip()}"' for item in value.split(',')) + ']'
                
                # 设置环境变量
                os.environ.setdefault(key, value)

def get_env_variable(var_name, default=None, cast_type=None):
    """
    获取环境变量，支持类型转换
    
    参数:
        var_name (str): 环境变量名称
        default: 默认值，如果环境变量不存在则返回此值
        cast_type: 类型转换函数，如int, float, bool等
        
    返回:
        转换后的环境变量值
    """
    value = os.environ.get(var_name, default)
    
    # 如果值为None且没有默认值，返回None
    if value is None:
        return None
    
    # 类型转换
    if cast_type is not None:
        if cast_type == bool:
            # 特殊处理布尔值
            return value.lower() in ('true', 'yes', 'y', '1', 'True')
        elif cast_type == list:
            # 特殊处理列表
            if value.startswith('[') and value.endswith(']'):
                # 已经是列表格式
                import ast
                return ast.literal_eval(value)
            elif ',' in value:
                # 逗号分隔的字符串
                return [item.strip() for item in value.split(',')]
            else:
                # 单个值
                return [value]
        else:
            # 其他类型转换
            return cast_type(value)
    
    return value

# 加载环境变量
load_env()