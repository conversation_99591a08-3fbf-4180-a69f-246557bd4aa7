{% extends "qrmanager/base.html" %}

{% block title %}{% if form.instance.pk %}编辑科室{% else %}新增科室{% endif %}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card fade-in">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="card-title">{% if form.instance.pk %}编辑科室{% else %}新增科室{% endif %}</h1>
                        <a href="{% url 'qrmanager:department_list' %}" class="btn btn-secondary">
                            <i class="fas fa-arrow-left me-2"></i>返回科室列表
                        </a>
                    </div>

                    <form method="post" novalidate>
                        {% csrf_token %}
                        
                        <div class="row g-3">
                            <!-- 科室编码 -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.code.id_for_label }}" class="form-label">科室编码 <span class="text-danger">*</span></label>
                                    {{ form.code }}
                                    {% if form.code.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.code.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">请输入唯一的科室编码，用于系统识别</div>
                                </div>
                            </div>

                            <!-- 科室名称 -->
                            <div class="col-md-6">
                                <div class="form-group">
                                    <label for="{{ form.name.id_for_label }}" class="form-label">科室名称 <span class="text-danger">*</span></label>
                                    {{ form.name }}
                                    {% if form.name.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.name.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                </div>
                            </div>

                            <!-- 备注 -->
                            <div class="col-12">
                                <div class="form-group">
                                    <label for="{{ form.remarks.id_for_label }}" class="form-label">备注</label>
                                    {{ form.remarks }}
                                    {% if form.remarks.errors %}
                                    <div class="invalid-feedback d-block">
                                        {% for error in form.remarks.errors %}
                                        {{ error }}
                                        {% endfor %}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">可选：添加关于该科室的补充说明</div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
}

.form-label {
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.form-text {
    color: #6c757d;
    font-size: 0.875rem;
    margin-top: 0.25rem;
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>
{% endblock %} 