/* 基础样式设置 */
:root {
    --primary-color: #0066cc;
    --primary-light: #3399ff;
    --primary-dark: #004499;
    --secondary-color: #8BC34A;
    --accent-color: #FF5722;
    --text-color: #333333;
    --text-light: #666666;
    --bg-color: #f8f9fa;
    --card-bg: #ffffff;
    --border-color: #dddddd;
    --success-color: #4CAF50;
    --warning-color: #FF9800;
    --error-color: #F44336;
    --shadow-light: 0 2px 5px rgba(0, 0, 0, 0.1);
    --shadow-medium: 0 4px 8px rgba(0, 0, 0, 0.1);
    --border-radius: 8px;
    --transition-speed: 0.3s;
    --medical-blue: #0078D7;
    --medical-green: #00A884;
    --medical-light-blue: #E6F3FF;
}

* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

body {
    font-family: -apple-system, BlinkMacSystemF<PERSON>, "<PERSON><PERSON><PERSON> UI", Robot<PERSON>, Helvetica, Arial, sans-serif;
    background-color: var(--bg-color);
    color: var(--text-color);
    line-height: 1.5;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

.app-container {
    max-width: 800px;
    margin: 0 auto;
    padding: 1rem;
    position: relative;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 背景气泡装饰 */
.bubbles {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    pointer-events: none;
    z-index: -1;
    overflow: hidden;
}

.bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 120, 215, 0.05);
    animation: float 15s infinite ease-in-out;
}

.bubble:nth-child(1) {
    width: 130px;
    height: 130px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.bubble:nth-child(2) {
    width: 80px;
    height: 80px;
    top: 20%;
    right: 15%;
    animation-delay: 1s;
}

.bubble:nth-child(3) {
    width: 100px;
    height: 100px;
    bottom: 30%;
    left: 5%;
    animation-delay: 2s;
}

.bubble:nth-child(4) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    right: 10%;
    animation-delay: 3s;
}

.bubble:nth-child(5) {
    width: 70px;
    height: 70px;
    top: 40%;
    left: 20%;
    animation-delay: 4s;
}

.bubble:nth-child(6) {
    width: 120px;
    height: 120px;
    bottom: 40%;
    right: 15%;
    animation-delay: 5s;
}

@keyframes float {
    0%, 100% {
        transform: translateY(0) rotate(0deg);
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
    }
}

/* 头部样式 - 现代苹果风格 */
.app-header {
    position: relative;
    padding: 1.5rem 0;
    margin-bottom: 2rem;
    overflow: hidden;
    display: flex;
    align-items: center;
    border-radius: 18px;
    z-index: 10;
}

.header-blur-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(210, 210, 210, 0.5);
    border-radius: 18px;
    z-index: -1;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.header-content {
    width: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 0.5rem 1.5rem;
}

.app-title-container {
    display: flex;
    align-items: center;
    margin-bottom: 0.5rem;
}

.hospital-logo {
    width: 48px;
    height: 48px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
    background: linear-gradient(145deg, #0078d7, #0067b8);
    margin-right: 15px;
    box-shadow: 
        0 8px 16px rgba(0, 102, 204, 0.15),
        0 2px 4px rgba(0, 102, 204, 0.1),
        inset 0 1px 1px rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 24px;
    transform: perspective(800px) rotateX(5deg);
    transition: all 0.3s ease;
}

.hospital-logo:hover {
    transform: perspective(800px) rotateX(0deg) scale(1.05);
    box-shadow: 
        0 12px 24px rgba(0, 102, 204, 0.2),
        0 4px 8px rgba(0, 102, 204, 0.15),
        inset 0 1px 1px rgba(255, 255, 255, 0.3);
}

.hospital-logo i {
    color: inherit;
}

.app-header h1 {
    font-size: 1.6rem;
    color: #222;
    font-weight: 600;
    margin: 0;
    letter-spacing: -0.02em;
}

.hospital-name {
    font-size: 0.95rem;
    color: #666;
    font-weight: 400;
    letter-spacing: -0.01em;
}

.medical-decoration {
    position: absolute;
    border-radius: 50%;
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(225, 239, 255, 0.8) 0%, rgba(200, 225, 255, 0) 70%);
    right: -20px;
    top: -20px;
    z-index: -1;
}

/* 主内容区域样式 */
.main-content {
    flex: 1;
    padding: 1rem 0;
}

/* 部分通用样式 */
.section {
    background: var(--card-bg);
    border-radius: var(--border-radius);
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: var(--shadow-light);
    position: relative;
    overflow: hidden;
    transition: all var(--transition-speed) ease;
}

.section h2 {
    color: var(--primary-dark);
    margin-bottom: 1.2rem;
    font-size: 1.4rem;
    font-weight: 500;
    display: flex;
    align-items: center;
}

.section h2 i {
    margin-right: 10px;
    color: var(--primary-color);
    font-size: 1.3rem;
}

.section-header {
    display: flex;
    align-items: center;
    margin-bottom: 1rem;
}

.section-header i {
    margin-right: 10px;
    color: var(--success-color);
    font-size: 1.5rem;
}

/* 表单元素样式 */
.form-group {
    margin-bottom: 1.2rem;
}

.form-group label {
    display: block;
    margin-bottom: 0.5rem;
    color: var(--text-color);
    font-weight: 500;
}

input[type="text"],
input[type="tel"],
input[type="email"],
select,
textarea {
    width: 100%;
    padding: 0.8rem 1rem;
    border: 1px solid var(--border-color);
    border-radius: var(--border-radius);
    background-color: #ffffff;
    font-size: 1rem;
    transition: border-color var(--transition-speed) ease;
}

input[type="text"]:focus,
input[type="tel"]:focus,
input[type="email"]:focus,
select:focus,
textarea:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 2px rgba(0, 102, 204, 0.2);
}

select {
    appearance: none;
    background-image: url("data:image/svg+xml;charset=utf-8,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' viewBox='0 0 24 24' fill='none' stroke='%23333' stroke-width='2' stroke-linecap='round' stroke-linejoin='round'%3E%3Cpath d='M6 9l6 6 6-6'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 10px center;
    padding-right: 30px;
}

.btn-primary {
    background-color: var(--primary-color);
    color: white;
    border: none;
    padding: 0.8rem 1.5rem;
    border-radius: var(--border-radius);
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-speed) ease;
    box-shadow: var(--shadow-light);
}

.btn-primary:hover {
    background-color: var(--primary-dark);
}

.btn-primary i {
    margin-right: 8px;
}

.form-actions {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.submit-btn {
    background-color: var(--success-color);
    color: white;
    border: none;
    padding: 0.8rem 1.8rem;
    border-radius: var(--border-radius);
    font-size: 1.1rem;
    font-weight: 500;
    cursor: pointer;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: background-color var(--transition-speed) ease, transform 0.2s ease;
    box-shadow: var(--shadow-medium);
}

.submit-btn:hover {
    background-color: #3d8c40;
    transform: translateY(-2px);
}

.submit-btn:disabled {
    background-color: #cccccc;
    cursor: not-allowed;
    transform: none;
}

.submit-btn i {
    margin-right: 8px;
}

/* QR码相关样式 */
.qr-code-required {
    text-align: center;
    padding: 2rem 1.5rem;
}

.qr-notice-icon {
    font-size: 2.5rem;
    color: var(--warning-color);
    margin-bottom: 1rem;
}

.qr-notice-content {
    margin: 1.5rem 0;
}

.qr-scan-illustration {
    width: 150px;
    height: 150px;
    margin: 1.5rem auto;
    background-color: var(--medical-light-blue);
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    border: 2px dashed var(--medical-blue);
}

.qr-scan-illustration i {
    font-size: 60px;
    color: var(--medical-blue);
}

.qr-scan-animation {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background-color: rgba(0, 120, 215, 0.8);
    animation: scan 2s infinite ease-in-out;
}

@keyframes scan {
    0% {
        top: 0;
        opacity: 0.8;
    }
    50% {
        top: 100%;
        opacity: 0.6;
    }
    100% {
        top: 0;
        opacity: 0.8;
    }
}

.qr-notice-help {
    color: var(--text-light);
    font-size: 0.9rem;
    margin-top: 1rem;
}

.qr-manual-input {
    margin-top: 2rem;
    padding-top: 1.5rem;
    border-top: 1px solid var(--border-color);
}

.qr-manual-input h3 {
    font-size: 1.1rem;
    color: var(--text-color);
    margin-bottom: 1rem;
    font-weight: 500;
}

.qr-form {
    max-width: 400px;
    margin: 0 auto;
}

/* 信息卡片样式 */
.info-card {
    background-color: var(--medical-light-blue);
    padding: 1rem;
    border-radius: var(--border-radius);
    margin-top: 1rem;
}

.info-item {
    display: flex;
    margin-bottom: 0.5rem;
}

.info-item:last-child {
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    margin-right: 0.5rem;
    min-width: 60px;
}

.info-value {
    color: var(--primary-dark);
}

/* 人员列表样式 */
.staff-list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
    gap: 1rem;
    margin-top: 1.5rem;
}

.staff-card {
    background-color: #ffffff;
    border-radius: var(--border-radius);
    padding: 1rem;
    box-shadow: var(--shadow-light);
    cursor: pointer;
    border: 2px solid transparent;
    transition: all var(--transition-speed) ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
}

.staff-card:hover {
    transform: translateY(-3px);
    box-shadow: var(--shadow-medium);
}

.staff-card.selected {
    border-color: var(--primary-color);
    background-color: rgba(0, 102, 204, 0.05);
}

.staff-avatar {
    width: 70px;
    height: 70px;
    border-radius: 50%;
    background-color: var(--medical-light-blue);
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 0.8rem;
    overflow: hidden;
}

.staff-avatar i {
    font-size: 40px;
    color: var(--medical-blue);
}

.staff-avatar img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.staff-name {
    font-weight: 500;
    margin-bottom: 0.3rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.staff-role {
    font-size: 0.8rem;
    color: var(--text-light);
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    max-width: 100%;
}

.selected-staff {
    margin-top: 1.2rem;
    font-weight: 500;
    color: var(--primary-color);
    padding: 0.8rem;
    background-color: rgba(0, 102, 204, 0.08);
    border-radius: var(--border-radius);
    display: flex;
    align-items: center;
}

.selected-staff span {
    margin-left: 0.5rem;
}

/* 加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.medical-loader {
    width: 100px;
    height: 100px;
    position: relative;
    margin-bottom: 20px;
}

.ecg-line {
    width: 5px;
    height: 5px;
    background: var(--medical-blue);
    border-radius: 50%;
    position: absolute;
    top: 50%;
    left: 0;
    transform: translateY(-50%);
    box-shadow: 0 0 10px rgba(0, 120, 215, 0.6);
    animation: ecg 2s infinite linear;
}

@keyframes ecg {
    0% {
        left: 0;
        box-shadow: 0 0 10px rgba(0, 120, 215, 0.6);
    }
    20% {
        box-shadow: 0 0 15px rgba(0, 120, 215, 0.8);
    }
    40% {
        top: 30%;
        box-shadow: 0 0 10px rgba(0, 120, 215, 0.6);
    }
    60% {
        top: 80%;
        box-shadow: 0 0 15px rgba(0, 120, 215, 0.8);
    }
    80% {
        top: 10%;
        box-shadow: 0 0 10px rgba(0, 120, 215, 0.6);
    }
    100% {
        left: 100%;
        top: 50%;
        box-shadow: 0 0 10px rgba(0, 120, 215, 0.6);
    }
}

.medical-pulse {
    width: 100%;
    height: 2px;
    background-color: rgba(0, 120, 215, 0.2);
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
}

.medical-cross {
    position: absolute;
    width: 50px;
    height: 50px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    border-radius: 5px;
}

.medical-cross:before, 
.medical-cross:after {
    content: '';
    position: absolute;
    background-color: var(--medical-blue);
}

.medical-cross:before {
    width: 40px;
    height: 8px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.medical-cross:after {
    width: 8px;
    height: 40px;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
}

.loading-text {
    color: var(--text-color);
    font-size: 1rem;
    text-align: center;
}

/* 错误信息容器 */
.error-container {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: white;
    padding: 1rem 1.5rem;
    border-radius: var(--border-radius);
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
    display: flex;
    align-items: center;
    max-width: calc(100% - 40px);
    z-index: 1001;
    border-left: 4px solid var(--error-color);
}

.error-icon {
    width: 30px;
    height: 30px;
    background-color: var(--error-color);
    color: white;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-weight: bold;
    flex-shrink: 0;
}

.error-container #errorMessage {
    flex: 1;
}

.close-btn {
    background: none;
    border: none;
    color: var(--text-light);
    font-size: 1.5rem;
    cursor: pointer;
    padding: 0 0 0 10px;
}

/* 成功消息样式 */
.success-message {
    text-align: center;
    padding: 3rem 1.5rem;
    background-color: white;
    border-radius: var(--border-radius);
    box-shadow: var(--shadow-medium);
    margin-bottom: 2rem;
    position: relative;
    overflow: hidden;
}

.success-icon {
    font-size: 4rem;
    color: var(--success-color);
    margin-bottom: 1.5rem;
}

.success-message h2 {
    color: var(--success-color);
    margin-bottom: 1rem;
    font-size: 1.8rem;
}

.success-message p {
    color: var(--text-color);
    max-width: 500px;
    margin: 0 auto;
}

/* 网络状态栏 */
.network-status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 8px 16px;
    background-color: #f8f9fa;
    border-top: 1px solid var(--border-color);
    display: flex;
    align-items: center;
    font-size: 0.85rem;
    color: var(--text-light);
    z-index: 900;
}

.network-status {
    display: flex;
    align-items: center;
}

.status-online {
    color: var(--success-color);
}

.status-offline {
    color: var(--warning-color);
}

.status-unknown {
    color: var(--text-light);
}

/* 页脚样式 - 协调的现代设计 */
.app-footer {
    padding: 2rem 0;
    margin-top: 3rem;
    position: relative;
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    border-radius: 18px;
    z-index: 10;
}

.footer-blur-bg {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(15px);
    -webkit-backdrop-filter: blur(15px);
    border: 1px solid rgba(210, 210, 210, 0.5);
    border-radius: 18px;
    z-index: -1;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
}

.footer-content {
    position: relative;
    width: 100%;
    max-width: 500px;
    padding: 0 1.5rem;
}

.footer-brand {
    display: flex;
    align-items: center;
}

.footer-logo {
    width: 42px;
    height: 42px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    background: linear-gradient(145deg, #0078d7, #0067b8);
    margin-right: 15px;
    box-shadow: 
        0 8px 16px rgba(0, 102, 204, 0.15),
        0 2px 4px rgba(0, 102, 204, 0.1),
        inset 0 1px 1px rgba(255, 255, 255, 0.2);
    color: white;
    font-size: 20px;
    flex-shrink: 0;
}

.footer-info {
    display: flex;
    flex-direction: column;
}

.footer-hospital {
    font-size: 1rem;
    color: #222;
    font-weight: 500;
    letter-spacing: -0.01em;
    margin-bottom: 3px;
}

.footer-copyright {
    font-size: 0.85rem;
    color: #666;
    font-weight: 400;
    letter-spacing: -0.01em;
    margin: 0;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
    .header-blur-bg, .footer-blur-bg {
        background: rgba(30, 30, 30, 0.85);
        border: 1px solid rgba(70, 70, 70, 0.5);
    }
    
    .app-header h1, .footer-hospital {
        color: #f5f5f7;
    }
    
    .hospital-name, .footer-copyright {
        color: #aaa;
    }
    
    .hospital-logo, .footer-logo {
        background: linear-gradient(145deg, #0086f5, #0067c0);
    }
}

/* API调试面板样式 */
.debug-panel {
    background-color: #f5f5f5;
    border-radius: var(--border-radius);
    padding: 1rem;
    margin-top: 2rem;
    border: 1px solid #ddd;
}

.debug-panel h3 {
    font-size: 1rem;
    color: #333;
    margin-bottom: 0.8rem;
    font-weight: 500;
}

.debug-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.5rem;
    margin-bottom: 1rem;
}

.debug-btn {
    background-color: #444;
    color: white;
    border: none;
    padding: 0.4rem 0.8rem;
    border-radius: 4px;
    font-size: 0.8rem;
    cursor: pointer;
}

.debug-btn:hover {
    background-color: #222;
}

.debug-data {
    background-color: #1e1e1e;
    color: #f8f8f8;
    padding: 0.8rem;
    border-radius: 4px;
    font-family: monospace;
    font-size: 0.85rem;
    overflow: auto;
    max-height: 300px;
    white-space: pre-wrap;
}

/* 实用工具类 */
.hidden {
    display: none !important;
}

.text-center {
    text-align: center;
}

/* 响应式调整 */
@media (max-width: 600px) {
    .app-header h1 {
        font-size: 1.5rem;
    }
    
    .staff-list {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    }
    
    .section {
        padding: 1.2rem;
    }
    
    .section h2 {
        font-size: 1.3rem;
    }
    
    .success-message {
        padding: 2rem 1rem;
    }
    
    .success-icon {
        font-size: 3rem;
    }
    
    .success-message h2 {
        font-size: 1.5rem;
    }
}

/* 无效参数提示样式 */
.info-icon {
    font-size: 3rem;
    color: var(--primary-color);
    margin-bottom: 1.5rem;
}

.invalid-message {
    color: var(--text-color);
    margin: 0.8rem auto;
    max-width: 500px;
    font-size: 1.1rem;
} 