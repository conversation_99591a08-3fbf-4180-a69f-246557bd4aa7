{% extends "qrmanager/base.html" %}
{% load django_bootstrap5 %}

{% block title %}登录 - 医院服务评价系统{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center mb-4">系统登录</h1>
                    {% if form.errors %}
                    <div class="alert alert-danger">
                        用户名或密码错误，请重试。
                    </div>
                    {% endif %}
                    <form method="post" action="{% url 'login' %}">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_username" class="form-label">用户名</label>
                            <input type="text" name="username" class="form-control" id="id_username" required>
                        </div>
                        <div class="mb-3">
                            <label for="id_password" class="form-label">密码</label>
                            <div class="password-container position-relative">
                                <input type="password" name="password" class="form-control" id="id_password" required>
                                <span class="password-toggle-icon position-absolute" id="togglePassword">
                                    <i class="bi bi-eye-fill"></i>
                                </span>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">登录</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .password-container {
        position: relative;
    }

    .password-toggle-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #4f46e5; /* 使用二维码管理的蓝紫色 */
        opacity: 0;
        transition: all 0.3s ease;
        font-size: 1.2rem;
    }

    .password-container:focus-within .password-toggle-icon {
        opacity: 1;
    }

    .password-toggle-icon:hover {
        color: #6366f1; /* 悬停时使用管理后台的靛蓝色 */
        transform: translateY(-50%) scale(1.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        const togglePassword = document.getElementById('togglePassword');
        const passwordInput = document.getElementById('id_password');

        togglePassword.addEventListener('click', function() {
            // 切换密码可见性
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);

            // 切换图标
            const icon = this.querySelector('i');
            if (type === 'password') {
                icon.classList.remove('bi-eye-slash-fill');
                icon.classList.add('bi-eye-fill');
            } else {
                icon.classList.remove('bi-eye-fill');
                icon.classList.add('bi-eye-slash-fill');
            }
        });
    });
</script>
{% endblock %}