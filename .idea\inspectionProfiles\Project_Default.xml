<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="47">
            <item index="0" class="java.lang.String" itemvalue="jieba" />
            <item index="1" class="java.lang.String" itemvalue="PyYAML" />
            <item index="2" class="java.lang.String" itemvalue="qrcode" />
            <item index="3" class="java.lang.String" itemvalue="python-dateutil" />
            <item index="4" class="java.lang.String" itemvalue="cffi" />
            <item index="5" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="6" class="java.lang.String" itemvalue="MarkupSafe" />
            <item index="7" class="java.lang.String" itemvalue="numpy" />
            <item index="8" class="java.lang.String" itemvalue="pycparser" />
            <item index="9" class="java.lang.String" itemvalue="python-slugify" />
            <item index="10" class="java.lang.String" itemvalue="requests" />
            <item index="11" class="java.lang.String" itemvalue="drf-yasg" />
            <item index="12" class="java.lang.String" itemvalue="django-extensions" />
            <item index="13" class="java.lang.String" itemvalue="sqlparse" />
            <item index="14" class="java.lang.String" itemvalue="python-magic" />
            <item index="15" class="java.lang.String" itemvalue="pyOpenSSL" />
            <item index="16" class="java.lang.String" itemvalue="sentry-sdk" />
            <item index="17" class="java.lang.String" itemvalue="bleach" />
            <item index="18" class="java.lang.String" itemvalue="certifi" />
            <item index="19" class="java.lang.String" itemvalue="urllib3" />
            <item index="20" class="java.lang.String" itemvalue="djangorestframework" />
            <item index="21" class="java.lang.String" itemvalue="django-cors-headers" />
            <item index="22" class="java.lang.String" itemvalue="uritemplate" />
            <item index="23" class="java.lang.String" itemvalue="six" />
            <item index="24" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="25" class="java.lang.String" itemvalue="django-crispy-forms" />
            <item index="26" class="java.lang.String" itemvalue="text-unidecode" />
            <item index="27" class="java.lang.String" itemvalue="tzdata" />
            <item index="28" class="java.lang.String" itemvalue="asgiref" />
            <item index="29" class="java.lang.String" itemvalue="cryptography" />
            <item index="30" class="java.lang.String" itemvalue="packaging" />
            <item index="31" class="java.lang.String" itemvalue="whitenoise" />
            <item index="32" class="java.lang.String" itemvalue="django-simple-captcha" />
            <item index="33" class="java.lang.String" itemvalue="gunicorn" />
            <item index="34" class="java.lang.String" itemvalue="django-admin-interface" />
            <item index="35" class="java.lang.String" itemvalue="pandas" />
            <item index="36" class="java.lang.String" itemvalue="colorama" />
            <item index="37" class="java.lang.String" itemvalue="Django" />
            <item index="38" class="java.lang.String" itemvalue="django-colorfield" />
            <item index="39" class="java.lang.String" itemvalue="django-redis" />
            <item index="40" class="java.lang.String" itemvalue="charset-normalizer" />
            <item index="41" class="java.lang.String" itemvalue="pillow" />
            <item index="42" class="java.lang.String" itemvalue="snownlp" />
            <item index="43" class="java.lang.String" itemvalue="pytz" />
            <item index="44" class="java.lang.String" itemvalue="webencodings" />
            <item index="45" class="java.lang.String" itemvalue="idna" />
            <item index="46" class="java.lang.String" itemvalue="inflection" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>