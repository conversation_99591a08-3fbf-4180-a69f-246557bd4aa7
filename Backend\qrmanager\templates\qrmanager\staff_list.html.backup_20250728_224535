{% extends "qrmanager/base.html" %}
{% load static %}
{% load django_bootstrap5 %}

{% block title %}工作人员管理{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <div class="card fade-in">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h1 class="card-title">工作人员管理</h1>
                        <div>
                            <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>返回仪表板
                            </a>
                            <a href="{% url 'qrmanager:staff_create' %}" class="btn btn-primary me-2">
                                <i class="fas fa-user-plus me-2"></i>添加工作人员
                            </a>
                            <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#importModal">
                                <i class="fas fa-upload me-2"></i>批量导入
                            </button>
                            <button type="button" class="btn btn-danger" id="bulkDeleteBtn" disabled>
                                <i class="fas fa-trash me-2"></i>批量删除
                            </button>
                        </div>
                    </div>

                    <!-- 搜索表单 -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-2">
                                <input type="text" name="work_number" class="form-control" placeholder="工号..." value="{{ request.GET.work_number }}">
                            </div>
                            <div class="col-md-2">
                                <input type="text" name="name" class="form-control" placeholder="姓名..." value="{{ request.GET.name }}">
                            </div>
                            <div class="col-md-2">
                                <select name="department" class="form-select">
                                    <option value="">选择科室</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"s" %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="staff_type" class="form-select">
                                    <option value="">选择人员类型</option>
                                    {% for type in staff_types %}
                                    <option value="{{ type.id }}" {% if request.GET.staff_type == type.id|stringformat:"s" %}selected{% endif %}>
                                        {{ type.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <select name="title" class="form-select">
                                    <option value="">选择职称</option>
                                    {% for title in staff_titles %}
                                    <option value="{{ title.id }}" {% if request.GET.title == title.id|stringformat:"s" %}selected{% endif %}>
                                        {{ title.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary w-100">
                                    <i class="fas fa-search me-2"></i>搜索
                                </button>
                            </div>
                        </div>
                    </form>

                    <form id="bulkDeleteForm" action="{% url 'qrmanager:staff_bulk_delete' %}" method="post">
                        {% csrf_token %}
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll">
                                                <label class="form-check-label" for="selectAll"></label>
                                            </div>
                                        </th>
                                        <th>工号</th>
                                        <th>姓名</th>
                                        <th>人员类型</th>
                                        <th>职称</th>
                                        <th>科室</th>
                                        <th>照片</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for staff in staff_list %}
                                    <tr>
                                        <td>
                                            <div class="form-check">
                                                <input class="form-check-input staff-checkbox" type="checkbox" name="staff_ids" value="{{ staff.id }}" id="staff-{{ staff.id }}">
                                                <label class="form-check-label" for="staff-{{ staff.id }}"></label>
                                            </div>
                                        </td>
                                        <td>{{ staff.work_number }}</td>
                                        <td>{{ staff.name }}</td>
                                        <td>{{ staff.staff_type.name }}</td>
                                        <td>{{ staff.title.name }}</td>
                                        <td>{{ staff.department.name|default:'-' }}</td>
                                        <td>
                                            {% if staff.photo %}
                                            <img src="{{ staff.photo.url }}" alt="{{ staff.name }}" class="img-thumbnail" style="max-height: 50px;">
                                            {% else %}
                                            -
                                            {% endif %}
                                        </td>
                                        <td>
                                            <div class="btn-group">
                                                <a href="{% url 'qrmanager:staff_edit' staff.pk %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-edit me-1"></i>编辑
                                                </a>
                                                <a href="{% url 'qrmanager:staff_delete' staff.pk %}" class="btn btn-sm btn-outline-danger">
                                                    <i class="fas fa-trash me-1"></i>删除
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                    {% empty %}
                                    <tr>
                                        <td colspan="8" class="text-center">暂无工作人员数据</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        {% if is_paginated %}
                        <div class="d-flex justify-content-between align-items-center mt-4">
                            <div class="d-flex align-items-center">
                                <span class="me-2">每页显示:</span>
                                <select id="page-size-select" class="form-select form-select-sm" style="width: auto;">
                                    <option value="10" {% if page_obj.paginator.per_page == 10 %}selected{% endif %}>10</option>
                                    <option value="20" {% if page_obj.paginator.per_page == 20 %}selected{% endif %}>20</option>
                                    <option value="50" {% if page_obj.paginator.per_page == 50 %}selected{% endif %}>50</option>
                                    <option value="100" {% if page_obj.paginator.per_page == 100 %}selected{% endif %}>100</option>
                                </select>
                                <span class="ms-3">共 {{ page_obj.paginator.count }} 条记录，{{ page_obj.paginator.num_pages }} 页</span>
                            </div>
                            
                            <nav aria-label="Page navigation">
                                <ul class="pagination justify-content-center mb-0">
                                    <!-- 首页按钮 -->
                                    <li class="page-item {% if not page_obj.has_previous %}disabled{% endif %}">
                                        <a class="page-link" href="?page=1{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                            <span aria-hidden="true">&laquo;&laquo;</span>
                                        </a>
                                    </li>
                                    
                                    <!-- 上一页按钮 - 始终显示 -->
                                    <li class="page-item {% if not page_obj.has_previous %}disabled{% endif %}">
                                        <a class="page-link" href="{% if page_obj.has_previous %}?page={{ page_obj.previous_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}javascript:void(0){% endif %}" aria-label="Previous">
                                            <span aria-hidden="true">&laquo;</span>
                                        </a>
                                    </li>

                                    <!-- 页码 -->
                                    {% for num in page_obj.paginator.page_range %}
                                        {% if page_obj.number == num %}
                                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                        {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                            <li class="page-item">
                                                <a class="page-link" href="?page={{ num }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                            </li>
                                        {% endif %}
                                    {% endfor %}

                                    <!-- 下一页按钮 - 始终显示 -->
                                    <li class="page-item {% if not page_obj.has_next %}disabled{% endif %}">
                                        <a class="page-link" href="{% if page_obj.has_next %}?page={{ page_obj.next_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}{% else %}javascript:void(0){% endif %}" aria-label="Next">
                                            <span aria-hidden="true">&raquo;</span>
                                        </a>
                                    </li>
                                    
                                    <!-- 末页按钮 -->
                                    <li class="page-item {% if not page_obj.has_next %}disabled{% endif %}">
                                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                            <span aria-hidden="true">&raquo;&raquo;</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 批量导入模态框 - 医疗风格设计 -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="fas fa-upload me-2"></i>批量导入工作人员
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="关闭"></button>
            </div>
            <div class="modal-body">
                <div class="row">
                    <div class="col-md-5">
                        <div class="import-info p-3 rounded bg-light mb-3 border">
                            <h5 class="text-primary"><i class="fas fa-info-circle me-2"></i>导入说明</h5>
                            <div class="ps-3 border-start border-primary">
                                <p class="mb-2">请按以下格式准备Excel文件：</p>
                                <div class="table-responsive">
                                    <table class="table table-sm table-bordered">
                                        <thead class="table-primary">
                                            <tr>
                                                <th>工号*</th>
                                                <th>姓名*</th>
                                                <th>人员类型*</th>
                                                <th>职称*</th>
                                                <th>科室*</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>001</td>
                                                <td>张三</td>
                                                <td>医生</td>
                                                <td>主任医师</td>
                                                <td>内科</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                                <p class="text-danger mb-1"><i class="fas fa-exclamation-triangle me-1"></i> 所有标*字段为必填项</p>
                                <p class="text-danger mb-1"><i class="fas fa-exclamation-triangle me-1"></i> 人员类型必须是系统中已有的类型</p>
                                <p class="text-danger mb-1"><i class="fas fa-exclamation-triangle me-1"></i> 职称必须是系统中已有的职称</p>
                                <p class="text-danger mb-0"><i class="fas fa-exclamation-triangle me-1"></i> 科室必须是系统中已有的科室</p>
                            </div>
                        </div>
                        
                        <div class="valid-types p-3 rounded bg-light border">
                            <h5 class="text-primary"><i class="fas fa-list-ul me-2"></i>有效的人员类型</h5>
                            <div class="ps-3 border-start border-primary">
                                <ul class="list-unstyled mb-0">
                                    {% for type in staff_types %}
                                    <li><i class="fas fa-check-circle text-success me-2"></i>{{ type.name }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <button type="button" class="btn btn-outline-primary" id="downloadStaffTemplateBtn">
                                <i class="fas fa-download me-2"></i>下载导入模板
                            </button>
                        </div>
                    </div>
                    
                    <div class="col-md-7">
                        <div class="upload-area p-4 rounded bg-light mb-3 border">
                            <form id="importForm" method="post" action="{% url 'qrmanager:staff_bulk_import' %}" enctype="multipart/form-data">
                                {% csrf_token %}
                                <div class="text-center mb-4">
                                    <div class="upload-icon mb-3">
                                        <i class="fas fa-file-excel fa-4x text-success"></i>
                                    </div>
                                    <h5 class="text-primary">上传Excel文件</h5>
                                    <p class="text-muted">支持.xlsx或.xls格式</p>
                                </div>
                                
                                <div class="mb-3">
                                    <div class="custom-file-upload">
                                        <input type="file" class="form-control" id="excel_file" name="excel_file" required accept=".xlsx,.xls">
                                    </div>
                                </div>
                                
                                <div id="fileInfo" class="d-none mt-3 p-3 rounded bg-white border">
                                    <div class="d-flex align-items-center">
                                        <i class="fas fa-file-excel text-success me-3 fa-2x"></i>
                                        <div>
                                            <h6 id="fileName" class="mb-1"></h6>
                                            <p id="fileSize" class="mb-0 text-muted small"></p>
                                        </div>
                                        <button type="button" id="removeFile" class="btn btn-sm btn-outline-danger ms-auto">
                                            <i class="fas fa-times"></i>
                                        </button>
                                    </div>
                                </div>
                                
                                <div id="validationResults" class="mt-3 d-none">
                                    <h6 class="text-primary"><i class="fas fa-check-circle me-2"></i>文件验证</h6>
                                    <div id="validationMessages" class="ps-3 border-start border-primary">
                                        <!-- 验证消息将在这里动态显示 -->
                                    </div>
                                </div>
                            </form>
                        </div>
                        
                        <div class="import-actions text-center">
                            <button type="button" class="btn btn-secondary me-2" data-bs-dismiss="modal">
                                <i class="fas fa-times me-2"></i>取消
                            </button>
                            <button type="button" class="btn btn-primary" id="validateBtn">
                                <i class="fas fa-check me-2"></i>验证文件
                            </button>
                            <button type="button" class="btn btn-success d-none" id="submitImport">
                                <i class="fas fa-upload me-2"></i>开始导入
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 自动提交表单当选择改变时
    const form = document.querySelector('form');
    const selects = form.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', () => form.submit());
    });
    
    // 文件上传处理
    const fileInput = document.getElementById('excel_file');
    const fileInfo = document.getElementById('fileInfo');
    const fileName = document.getElementById('fileName');
    const fileSize = document.getElementById('fileSize');
    const removeFile = document.getElementById('removeFile');
    const validateBtn = document.getElementById('validateBtn');
    const submitImport = document.getElementById('submitImport');
    const validationResults = document.getElementById('validationResults');
    const validationMessages = document.getElementById('validationMessages');
    
    // 文件选择事件
    fileInput.addEventListener('change', function() {
        if (this.files.length > 0) {
            const file = this.files[0];
            fileName.textContent = file.name;
            fileSize.textContent = formatFileSize(file.size);
            fileInfo.classList.remove('d-none');
            validateBtn.disabled = false;
            
            // 重置验证结果
            validationResults.classList.add('d-none');
            validationMessages.innerHTML = '';
            submitImport.classList.add('d-none');
        } else {
            fileInfo.classList.add('d-none');
            validateBtn.disabled = true;
        }
    });
    
    // 移除文件
    removeFile.addEventListener('click', function() {
        fileInput.value = '';
        fileInfo.classList.add('d-none');
        validationResults.classList.add('d-none');
        submitImport.classList.add('d-none');
        validateBtn.disabled = true;
    });
    
    // 验证文件按钮点击事件
    validateBtn.addEventListener('click', function() {
        if (fileInput.files.length === 0) {
            alert('请选择要导入的Excel文件');
            return;
        }
        
        // 显示验证中状态
        validateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>验证中...';
        validateBtn.disabled = true;
        
        // 创建FormData对象
        const formData = new FormData();
        formData.append('excel_file', fileInput.files[0]);
        formData.append('validate', 'true');
        formData.append('csrfmiddlewaretoken', document.querySelector('[name=csrfmiddlewaretoken]').value);
        
        // 发送AJAX请求
        fetch('{% url "qrmanager:staff_bulk_import" %}', {
            method: 'POST',
            body: formData,
            credentials: 'same-origin'
        })
        .then(response => response.json())
        .then(data => {
            // 显示验证结果
            validationResults.classList.remove('d-none');
            
            if (data.status === 'success') {
                // 验证成功
                validationMessages.innerHTML = `
                    <p class="text-success mb-1"><i class="fas fa-check-circle me-2"></i>文件格式正确</p>
                    <p class="text-success mb-1"><i class="fas fa-check-circle me-2"></i>检测到数据行: ${data.data.row_count}行</p>
                    <p class="text-info mb-0"><i class="fas fa-info-circle me-2"></i>文件验证通过，可以开始导入</p>
                `;
                
                // 显示导入按钮
                submitImport.classList.remove('d-none');
            } else {
                // 验证失败
                let errorHtml = `<p class="text-danger mb-2"><i class="fas fa-times-circle me-2"></i>${data.message}</p>`;
                
                // 如果有详细错误列表，显示它们
                if (data.errors && data.errors.length > 0) {
                    errorHtml += '<div class="alert alert-danger p-2 mt-2">';
                    errorHtml += '<ul class="mb-0 ps-3">';
                    data.errors.forEach(error => {
                        errorHtml += `<li>${error}</li>`;
                    });
                    errorHtml += '</ul></div>';
                }
                
                validationMessages.innerHTML = errorHtml;
                
                // 隐藏导入按钮
                submitImport.classList.add('d-none');
            }
            
            // 恢复验证按钮状态
            validateBtn.innerHTML = '<i class="fas fa-check me-2"></i>验证文件';
            validateBtn.disabled = false;
        })
        .catch(error => {
            // 处理错误
            validationResults.classList.remove('d-none');
            validationMessages.innerHTML = `
                <p class="text-danger mb-0"><i class="fas fa-times-circle me-2"></i>验证过程中发生错误: ${error.message}</p>
            `;
            
            // 恢复验证按钮状态
            validateBtn.innerHTML = '<i class="fas fa-check me-2"></i>验证文件';
            validateBtn.disabled = false;
            
            // 隐藏导入按钮
            submitImport.classList.add('d-none');
        });
    });
    
    // 导入表单提交
    submitImport.addEventListener('click', function() {
        const importForm = document.getElementById('importForm');
        
        if (fileInput.files.length === 0) {
            alert('请选择要导入的Excel文件');
            return;
        }
        
        // 显示导入中状态
        submitImport.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>导入中...';
        submitImport.disabled = true;
        
        // 提交表单
        importForm.submit();
    });
    
    // 格式化文件大小
    function formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }
    
    // 模态框打开时重置表单
    const importModal = document.getElementById('importModal');
    importModal.addEventListener('hidden.bs.modal', function() {
        fileInput.value = '';
        fileInfo.classList.add('d-none');
        validationResults.classList.add('d-none');
        submitImport.classList.add('d-none');
        validateBtn.disabled = false;
        validateBtn.innerHTML = '<i class="fas fa-check me-2"></i>验证文件';
        submitImport.disabled = false;
        submitImport.innerHTML = '<i class="fas fa-upload me-2"></i>开始导入';
    });
    
    // 处理每页显示数量变化
    const pageSizeSelect = document.getElementById('page-size-select');
    if (pageSizeSelect) {
        pageSizeSelect.addEventListener('change', function() {
            const pageSize = this.value;
            const currentUrl = new URL(window.location.href);
            
            // 设置新的page_size参数
            currentUrl.searchParams.set('page_size', pageSize);
            
            // 重置页码为1
            currentUrl.searchParams.set('page', '1');
            
            // 跳转到新URL
            window.location.href = currentUrl.toString();
        });
    }
    
    // 批量删除功能
    const selectAllCheckbox = document.getElementById('selectAll');
    const staffCheckboxes = document.querySelectorAll('.staff-checkbox');
    const bulkDeleteBtn = document.getElementById('bulkDeleteBtn');
    const bulkDeleteForm = document.getElementById('bulkDeleteForm');
    
    // 全选/取消全选
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const isChecked = this.checked;
            staffCheckboxes.forEach(checkbox => {
                checkbox.checked = isChecked;
            });
            updateBulkDeleteButton();
        });
    }
    
    // 单个复选框变化时更新全选框和批量删除按钮状态
    staffCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            updateSelectAllCheckbox();
            updateBulkDeleteButton();
        });
    });
    
    // 更新全选复选框状态
    function updateSelectAllCheckbox() {
        const totalCheckboxes = staffCheckboxes.length;
        const checkedCheckboxes = document.querySelectorAll('.staff-checkbox:checked').length;
        
        if (selectAllCheckbox) {
            selectAllCheckbox.checked = totalCheckboxes > 0 && totalCheckboxes === checkedCheckboxes;
            selectAllCheckbox.indeterminate = checkedCheckboxes > 0 && checkedCheckboxes < totalCheckboxes;
        }
    }
    
    // 更新批量删除按钮状态
    function updateBulkDeleteButton() {
        const checkedCount = document.querySelectorAll('.staff-checkbox:checked').length;
        bulkDeleteBtn.disabled = checkedCount === 0;
        
        // 更新按钮文本
        if (checkedCount > 0) {
            bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>删除选中(${checkedCount})`;
        } else {
            bulkDeleteBtn.innerHTML = `<i class="fas fa-trash me-2"></i>批量删除`;
        }
    }
    
    // 批量删除按钮点击事件
    if (bulkDeleteBtn) {
        bulkDeleteBtn.addEventListener('click', function() {
            const checkedCount = document.querySelectorAll('.staff-checkbox:checked').length;

            if (checkedCount === 0) {
                alert('请至少选择一名工作人员');
                return;
            }

            if (confirm(`确定要删除选中的 ${checkedCount} 名工作人员吗？此操作不可恢复！`)) {
                bulkDeleteForm.submit();
            }
        });
    }

    // 工作人员模板下载按钮点击事件
    const downloadStaffTemplateBtn = document.getElementById('downloadStaffTemplateBtn');
    if (downloadStaffTemplateBtn) {
        downloadStaffTemplateBtn.addEventListener('click', function(e) {
            e.preventDefault();

            const downloadUrl = '{% url "qrmanager:staff_template" %}';
            console.log('下载工作人员模板，URL:', downloadUrl);

            // 显示加载提示
            const originalText = this.innerHTML;
            this.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>准备下载...';
            this.disabled = true;

            // 先检查URL是否可访问（会话是否有效）
            fetch(downloadUrl, {
                method: 'HEAD',
                credentials: 'same-origin',
                headers: {
                    'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                }
            })
            .then(response => {
                // 恢复按钮状态
                this.innerHTML = originalText;
                this.disabled = false;

                if (response.ok) {
                    // 会话有效，开始下载
                    console.log('会话验证成功，开始下载模板');

                    // 使用 fetch 获取文件内容并强制下载
                    fetch(downloadUrl, {
                        method: 'GET',
                        credentials: 'same-origin',
                        headers: {
                            'X-CSRFToken': document.querySelector('[name=csrfmiddlewaretoken]').value
                        }
                    })
                    .then(response => {
                        if (!response.ok) {
                            throw new Error('下载失败');
                        }
                        return response.blob();
                    })
                    .then(blob => {
                        // 创建 Blob URL 并强制下载
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.download = '工作人员导入模板.xlsx';
                        link.style.display = 'none';

                        // 添加到 DOM，点击，然后移除
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        // 释放 Blob URL
                        window.URL.revokeObjectURL(url);

                        // 显示成功提示
                        showToast('模板下载成功', 'success');
                    })
                    .catch(error => {
                        console.error('下载过程中出错:', error);
                        showToast('下载失败，请稍后重试', 'danger');
                    });
                } else if (response.status === 401) {
                    // 会话已过期
                    console.warn('会话已过期，重定向到登录页面');
                    alert('会话已过期，请重新登录');

                    // 延迟2秒后重定向到登录页面
                    setTimeout(function() {
                        window.location.href = '{% url "login" %}';
                    }, 2000);
                } else {
                    // 其他错误
                    console.error('模板下载失败，状态码:', response.status);
                    showToast('模板下载失败，请稍后重试', 'danger');
                }
            })
            .catch(error => {
                // 恢复按钮状态
                this.innerHTML = originalText;
                this.disabled = false;

                console.error('模板下载请求失败:', error);

                // 网络错误，尝试直接下载
                console.log('网络请求失败，尝试直接下载');
                showToast('网络请求失败，尝试直接下载...', 'warning');

                // 直接打开下载链接
                window.open(downloadUrl, '_blank');
            });
        });
    }

    // Toast 提示函数
    function showToast(message, type = 'info') {
        // 创建 toast 元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
        toast.style.cssText = `
            top: 20px;
            right: 20px;
            z-index: 9999;
            min-width: 300px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        `;

        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 3秒后自动移除
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 3000);
    }
});
</script>
{% endblock %}

{% endblock %} 