<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端星星评分测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
            padding: 20px;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .stars {
            display: flex;
            justify-content: center;
            margin: 20px 0;
        }
        
        .star {
            font-size: 0;
            cursor: pointer;
            margin: 0 8px;
            transition: all 0.4s ease;
            position: relative;
            width: 36px;
            height: 36px;
            filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
        }
        
        .star::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e0e0e0' stroke='%23bdbdbd' stroke-width='1'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
            background-size: contain;
            background-repeat: no-repeat;
            transition: all 0.3s ease;
            transform-origin: center;
        }
        
        .star.active::before {
            background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFD700' stroke='%23FF8F00' stroke-width='1.5'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
            filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8)) drop-shadow(0 0 4px rgba(255, 143, 0, 0.6));
            transform: scale(1.1);
        }
        
        .current-rating {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #0070e4;
            margin: 20px 0;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        
        .log-touch { color: #28a745; }
        .log-click { color: #007bff; }
        .log-rating { color: #dc3545; font-weight: bold; }
        
        .test-buttons {
            text-align: center;
            margin: 20px 0;
        }
        
        .test-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 15px;
            margin: 5px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📱 移动端星星评分测试</h1>
        
        <div class="stars">
            <span class="star" data-value="1" title="很不满意">★</span>
            <span class="star" data-value="2" title="不满意">★</span>
            <span class="star" data-value="3" title="一般">★</span>
            <span class="star" data-value="4" title="满意">★</span>
            <span class="star" data-value="5" title="非常满意">★</span>
        </div>
        
        <div class="current-rating">
            当前评分：<span id="currentRating">未评分</span>
        </div>
        
        <div class="test-buttons">
            <button class="test-btn" onclick="testTouch(3)">测试触摸第3颗星</button>
            <button class="test-btn" onclick="testTouch(5)">测试触摸第5颗星</button>
            <button class="test-btn" onclick="resetRating()">重置评分</button>
        </div>
        
        <div class="test-log">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>操作日志：</strong>
                <button class="clear-btn" onclick="clearLog()">清空日志</button>
            </div>
            <div id="logContainer"></div>
        </div>
    </div>

    <script>
        let currentRating = 0;
        let logCount = 0;
        
        // 触摸跟踪变量
        let touchStartTime = 0;
        let touchStartX = 0;
        let touchStartY = 0;
        let touchMoved = false;
        let isDragging = false;
        
        // 触摸阈值配置
        const TOUCH_TIME_THRESHOLD = 500; // 500ms内认为是轻触
        const TOUCH_MOVE_THRESHOLD = 20;  // 20px内认为没有移动
        
        function addLog(message, type = '') {
            logCount++;
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + (type ? 'log-' + type : '');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('logContainer').appendChild(logEntry);
            document.getElementById('logContainer').scrollTop = document.getElementById('logContainer').scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('logContainer').innerHTML = '';
            logCount = 0;
        }
        
        function updateRating(value) {
            currentRating = value;
            const stars = document.querySelectorAll('.star');
            const currentRatingSpan = document.getElementById('currentRating');
            
            // 更新星星显示
            stars.forEach(star => {
                const starValue = parseInt(star.getAttribute('data-value'));
                if (starValue <= value) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
            
            // 更新评分显示
            currentRatingSpan.textContent = value === 0 ? '未评分' : value + ' 星';
            addLog(`评分已更新：${value} 星`, 'rating');
        }
        
        function testTouch(starIndex) {
            const stars = document.querySelectorAll('.star');
            const targetStar = stars[starIndex - 1];
            
            if (!targetStar) {
                addLog(`测试失败：找不到星星 ${starIndex}`);
                return;
            }
            
            addLog(`开始测试触摸第${starIndex}颗星`, 'touch');
            
            // 获取星星的位置
            const rect = targetStar.getBoundingClientRect();
            const x = rect.left + rect.width / 2;
            const y = rect.top + rect.height / 2;
            
            addLog(`星星位置：(${x.toFixed(1)}, ${y.toFixed(1)})`, 'touch');
            
            // 创建触摸事件
            try {
                const touchStart = new TouchEvent('touchstart', {
                    touches: [new Touch({
                        identifier: 0,
                        target: targetStar,
                        clientX: x,
                        clientY: y
                    })],
                    bubbles: true,
                    cancelable: true
                });
                
                const touchEnd = new TouchEvent('touchend', {
                    changedTouches: [new Touch({
                        identifier: 0,
                        target: targetStar,
                        clientX: x,
                        clientY: y
                    })],
                    bubbles: true,
                    cancelable: true
                });
                
                // 触发触摸事件
                addLog('触发touchstart事件', 'touch');
                targetStar.dispatchEvent(touchStart);
                
                setTimeout(() => {
                    addLog('触发touchend事件', 'touch');
                    targetStar.dispatchEvent(touchEnd);
                }, 100); // 100ms的轻触
                
            } catch (error) {
                addLog(`创建触摸事件失败：${error.message}`);
                // 如果TouchEvent不支持，直接调用点击逻辑
                simulateClick(starIndex);
            }
        }
        
        function simulateClick(starIndex) {
            addLog(`模拟点击第${starIndex}颗星`, 'click');
            
            // 如果点击的星星已经是激活状态，则取消评分
            if (currentRating === starIndex) {
                updateRating(0);
                addLog('取消评分，评分值设置为0', 'click');
            } else {
                // 正常设置评分值
                updateRating(starIndex);
                addLog(`设置评分：${starIndex}`, 'click');
            }
        }
        
        function resetRating() {
            updateRating(0);
            addLog('重置评分');
        }
        
        // 初始化事件监听
        document.addEventListener('DOMContentLoaded', function() {
            const starsContainer = document.querySelector('.stars');
            
            // 处理触摸开始事件
            starsContainer.addEventListener('touchstart', (e) => {
                if (e.target.closest('.star') || e.target === starsContainer) {
                    const touch = e.touches[0];
                    touchStartTime = Date.now();
                    touchStartX = touch.clientX;
                    touchStartY = touch.clientY;
                    touchMoved = false;
                    
                    addLog('触摸开始，位置: (' + touchStartX.toFixed(1) + ', ' + touchStartY.toFixed(1) + ')', 'touch');
                }
            }, { passive: true });
            
            // 处理触摸移动事件
            document.addEventListener('touchmove', (e) => {
                if (touchStartTime === 0) return;
                
                const touch = e.touches[0];
                const moveX = Math.abs(touch.clientX - touchStartX);
                const moveY = Math.abs(touch.clientY - touchStartY);
                const totalMove = Math.sqrt(moveX * moveX + moveY * moveY);
                
                if (totalMove > TOUCH_MOVE_THRESHOLD) {
                    touchMoved = true;
                    if (!isDragging) {
                        isDragging = true;
                        addLog('开始拖拽模式，移动距离: ' + totalMove.toFixed(1) + 'px', 'touch');
                    }
                }
            }, { passive: false });
            
            // 处理触摸结束事件
            document.addEventListener('touchend', (e) => {
                if (touchStartTime === 0) return;
                
                const touchEndTime = Date.now();
                const touchDuration = touchEndTime - touchStartTime;
                
                const touch = e.changedTouches[0];
                const moveX = Math.abs(touch.clientX - touchStartX);
                const moveY = Math.abs(touch.clientY - touchStartY);
                const totalMove = Math.sqrt(moveX * moveX + moveY * moveY);
                
                addLog(`触摸结束，持续时间: ${touchDuration}ms，移动距离: ${totalMove.toFixed(1)}px`, 'touch');
                
                const isQuickTap = touchDuration < TOUCH_TIME_THRESHOLD && totalMove < TOUCH_MOVE_THRESHOLD;
                
                if (isQuickTap) {
                    addLog('检测到轻触，执行点击逻辑', 'touch');
                    
                    const element = document.elementFromPoint(touch.clientX, touch.clientY);
                    const star = element ? element.closest('.star') : null;
                    
                    if (star) {
                        const value = parseInt(star.getAttribute('data-value'));
                        addLog(`轻触星星，评分：${value}`, 'touch');
                        
                        if (currentRating === value) {
                            updateRating(0);
                            addLog('取消评分，评分值设置为0', 'touch');
                        } else {
                            updateRating(value);
                            addLog(`设置评分：${value}`, 'touch');
                        }
                    } else {
                        addLog('轻触位置没有找到星星元素', 'touch');
                    }
                } else if (isDragging) {
                    addLog('拖拽结束', 'touch');
                } else {
                    addLog('触摸结束，但不符合轻触或拖拽条件', 'touch');
                }
                
                // 重置状态
                isDragging = false;
                touchStartTime = 0;
                touchMoved = false;
            });
            
            // 处理点击事件（桌面端）
            starsContainer.addEventListener('click', (e) => {
                const star = e.target.closest('.star');
                if (!star) return;
                
                const value = parseInt(star.getAttribute('data-value'));
                addLog(`点击星星，评分：${value}`, 'click');
                
                if (currentRating === value) {
                    updateRating(0);
                    addLog('取消评分，评分值设置为0', 'click');
                } else {
                    updateRating(value);
                    addLog(`设置评分：${value}`, 'click');
                }
            });
            
            addLog('页面加载完成，等待用户操作...');
        });
    </script>
</body>
</html>
