{% load static %}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="description" content="自贡市第四人民医院服务评价系统">
    <title>{% block title %}自贡市第四人民医院服务评价系统{% endblock %}</title>
    <link href="{% static 'vendor/bootstrap/css/bootstrap.min.css' %}" rel="stylesheet">
    <link rel="stylesheet" href="{% static 'vendor/fontawesome/css/all.min.css' %}">
    <link rel="stylesheet" href="{% static 'qrmanager/css/style.css' %}">
    {% block extra_css %}{% endblock %}
    <style>
        .text-purple {
            color: #6f42c1 !important;
        }
        .btn-outline-purple {
            color: #6f42c1;
            border-color: #6f42c1;
        }
        .btn-outline-purple:hover {
            color: #fff;
            background-color: #6f42c1;
            border-color: #6f42c1;
        }
        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }
        @keyframes fadeIn {
            from { opacity: 0; }
            to { opacity: 1; }
        }
        .navbar {
            background-color: transparent;
            padding: 0.75rem 0;
        }
        .navbar-brand {
            color: #2563eb !important;
            font-weight: 600;
        }
        /* 导航链接基础样式 */
        .nav-link {
            color: #1e293b !important;
            font-weight: 500;
            padding: 0.5rem 1rem;
            border-radius: 8px;
            transition: all 0.2s ease;
            margin: 0 0.25rem;
        }
        /* 用户名显示样式 */
        .nav-item .nav-link .fa-user {
            color: #4f46e5;
        }
        /* 快捷键样式 */
        .nav-link.shortcut {
            display: flex;
            align-items: center;
            padding: 0.625rem 1.25rem;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        /* 二维码管理 */
        .nav-link.shortcut-qrcode {
            color: #4f46e5 !important;
        }
        .nav-link.shortcut-qrcode:hover {
            background: rgba(79, 70, 229, 0.1);
            border-color: rgba(79, 70, 229, 0.2);
        }
        .nav-link.shortcut-qrcode .fas {
            color: #4f46e5;
        }
        /* 科室管理 */
        .nav-link.shortcut-department {
            color: #0ea5e9 !important;
        }
        .nav-link.shortcut-department:hover {
            background: rgba(14, 165, 233, 0.1);
            border-color: rgba(14, 165, 233, 0.2);
        }
        .nav-link.shortcut-department .fas {
            color: #0ea5e9;
        }
        /* 工作人员 */
        .nav-link.shortcut-staff {
            color: #10b981 !important;
        }
        .nav-link.shortcut-staff:hover {
            background: rgba(16, 185, 129, 0.1);
            border-color: rgba(16, 185, 129, 0.2);
        }
        .nav-link.shortcut-staff .fas {
            color: #10b981;
        }
        /* 情感分析 */
        .nav-link.shortcut-sentiment {
            color: #f59e0b !important;
        }
        .nav-link.shortcut-sentiment:hover {
            background: rgba(245, 158, 11, 0.1);
            border-color: rgba(245, 158, 11, 0.2);
        }
        .nav-link.shortcut-sentiment .fas {
            color: #f59e0b;
        }
        /* 管理后台 */
        .nav-link.shortcut-dashboard {
            color: #6366f1 !important;
        }
        .nav-link.shortcut-dashboard:hover {
            background: rgba(99, 102, 241, 0.1);
            border-color: rgba(99, 102, 241, 0.2);
        }
        .nav-link.shortcut-dashboard .fas {
            color: #6366f1;
        }
        /* 系统管理图标 */
        .nav-link.shortcut-system {
            color: #00838f !important;
        }
        .nav-link.shortcut-system:hover {
            background: rgba(0, 131, 143, 0.1);
            border-color: rgba(0, 131, 143, 0.2);
        }
        .nav-link.shortcut-system .fas {
            color: #00838f;
        }
        .nav-link.shortcut-system:hover .fas {
            transform: translateY(-1px) rotate(15deg);
        }
        /* 系统管理下拉菜单 */
        .dropdown-menu {
            border: 1px solid rgba(226, 232, 240, 0.8);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            border-radius: 12px;
            padding: 0.5rem;
            z-index: 1050;
            position: absolute;
            margin-top: 5px !important;
        }
        .dropdown-item {
            border-radius: 6px;
            padding: 0.5rem 1rem;
            transition: all 0.2s ease;
        }
        .dropdown-item:hover {
            background-color: rgba(99, 102, 241, 0.05);
        }

        /* 下拉菜单图标颜色 */
        .dropdown-item .fa-history {
            color: #6c757d;
        }
        .dropdown-item .fa-book {
            color: #0d6efd;
        }
        .dropdown-item .fa-users-cog {
            color: #6f42c1;
        }
        .dropdown-item .fa-user-cog {
            color: #20c997;
        }
        .dropdown-item .fa-laptop-code {
            color: #fd7e14;
        }
        .dropdown-item .fa-sign-out-alt {
            color: #dc3545;
        }
        .dropdown-item .fa-shield-alt {
            color: #e74c3c;
        }

        /* 图标悬停效果 */
        .nav-link:hover .fas {
            transform: translateY(-1px);
            transition: transform 0.2s ease;
        }
    </style>
</head>
<body>
    <header class="container-fluid">
        <nav class="navbar navbar-expand-lg navbar-light">
            <div class="container">
                <a class="navbar-brand" href="{% url 'qrmanager:index' %}">自贡市第四人民医院服务评价系统</a>
                <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                    <span class="navbar-toggler-icon"></span>
                </button>
                <div class="collapse navbar-collapse" id="navbarNav">
                    <ul class="navbar-nav me-auto">
                        {% if user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link shortcut shortcut-qrcode" href="{% url 'qrmanager:qrcode_list' %}">
                                    <i class="fas fa-qrcode me-2"></i>二维码管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link shortcut shortcut-department" href="{% url 'qrmanager:department_list' %}">
                                    <i class="fas fa-hospital me-2"></i>科室管理
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link shortcut shortcut-staff" href="{% url 'qrmanager:staff_list' %}">
                                    <i class="fas fa-user-md me-2"></i>工作人员
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link shortcut shortcut-sentiment" href="{% url 'qrmanager:sentiment_analysis' %}">
                                    <i class="fas fa-chart-pie me-2"></i>情感分析
                                </a>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'qrmanager:index' %}">首页</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="https://www.zg120.cn/" target="_blank">关于我们</a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" href="#" data-bs-toggle="modal" data-bs-target="#contactModal">联系我们</a>
                            </li>
                        {% endif %}
                    </ul>
                    <ul class="navbar-nav">
                        {% if user.is_authenticated %}
                            <li class="nav-item">
                                <a class="nav-link shortcut shortcut-dashboard" href="{% url 'qrmanager:dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-2"></i>管理后台
                                </a>
                            </li>
                            <li class="nav-item me-3">
                                <span class="nav-link">
                                    <i class="fas fa-user me-1"></i>{{ user.username }}
                                </span>
                            </li>
                            <li class="nav-item dropdown">
                                <a class="nav-link dropdown-toggle shortcut-system" href="#" id="systemDropdown" role="button" data-bs-toggle="dropdown">
                                    <i class="fas fa-cogs me-2"></i>系统管理
                                </a>
                                <ul class="dropdown-menu dropdown-menu-end">
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:operation_logs' %}">
                                            <i class="fas fa-history me-2"></i>操作日志
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:security_monitoring' %}">
                                            <i class="fas fa-shield-alt me-2"></i>安全监控
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:security_management' %}">
                                            <i class="fas fa-tools me-2"></i>安全管理
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:dictionary_list' %}">
                                            <i class="fas fa-book me-2"></i>字典管理
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:admin_permissions' %}">
                                            <i class="fas fa-users-cog me-2"></i>用户权限
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:account_settings' %}">
                                            <i class="fas fa-user-cog me-2"></i>账号设置
                                        </a>
                                    </li>
                                    <li>
                                        <a class="dropdown-item" href="{% url 'qrmanager:api_management' %}">
                                            <i class="fas fa-laptop-code me-2"></i>API管理
                                        </a>
                                    </li>
                                    <li><hr class="dropdown-divider"></li>
                                    <li>
                                        <form method="post" action="{% url 'logout' %}" style="display: inline;">
                                            {% csrf_token %}
                                            <button type="submit" class="dropdown-item text-danger" style="background: none; border: none; width: 100%; text-align: left;">
                                                <i class="fas fa-sign-out-alt me-2"></i>退出
                                            </button>
                                        </form>
                                    </li>
                                </ul>
                            </li>
                        {% else %}
                            <li class="nav-item">
                                <a class="nav-link" href="{% url 'login' %}">
                                    <i class="fas fa-sign-in-alt me-2"></i>登录
                                </a>
                            </li>
                        {% endif %}
                    </ul>
                </div>
            </div>
        </nav>
    </header>

    <main class="container my-4">
        {% if messages %}
        <div class="messages">
            {% for message in messages %}
            <div class="alert alert-{{ message.tags }}">
                {{ message }}
            </div>
            {% endfor %}
        </div>
        {% endif %}

        {% block content %}{% endblock %}
    </main>

    <footer class="footer mt-auto py-3">
        <div class="container text-center">
            <p>&copy; {% now "Y" %} 自贡市第四人民医院服务评价系统 | <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">隐私政策</a></p>
        </div>
    </footer>

    <!-- 联系我们弹窗 -->
    <div class="modal fade" id="contactModal" tabindex="-1" aria-labelledby="contactModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="contactModalLabel">联系我们</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-4">
                        <h6 class="fw-bold">联系电话</h6>
                        <ul class="list-unstyled">
                            <li>急救电话：0813-120</li>
                            <li>预约挂号电话：0813-2401126、0813-2401026</li>
                            <li>投诉电话：2300337</li>
                            <li>联系电话(传真）：0813-2201981</li>
                            <li>电子邮箱：<EMAIL></li>
                        </ul>
                    </div>
                    <div class="mb-4">
                        <h6 class="fw-bold">医院地址</h6>
                        <ul class="list-unstyled">
                            <li>檀木林院区：四川省自贡市自流井区檀木林街19号</li>
                            <li>汇东院区：四川省自贡市自流井区丹桂大街400号</li>
                        </ul>
                    </div>
                    <div>
                        <h6 class="fw-bold">办公时间</h6>
                        <p>上午8：00-12：00，下午14：30-17：30（周一至周五，节假日除外）</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 隐私政策弹窗 -->
    <div class="modal fade" id="privacyModal" tabindex="-1" aria-labelledby="privacyModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="privacyModalLabel">隐私政策</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <h6 class="mb-3">自贡市第四人民医院服务评价系统隐私政策</h6>
                    <div class="privacy-content">
                        <p>我们非常重视您的隐私保护，承诺将按照以下政策保护您的个人信息：</p>
                        <ol>
                            <li>信息收集：我们仅收集评价所必需的信息，包括评分和反馈内容。</li>
                            <li>信息使用：收集的信息仅用于服务质量改进和医院管理。</li>
                            <li>访问权限：只有经过授权的工作人员才能访问相关信息。</li>
                        </ol>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局模态框 -->
    <div class="modal fade" id="globalModal" tabindex="-1">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="globalModalLabel"></h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body" id="globalModalBody">
                </div>
                <div class="modal-footer" id="globalModalFooter">
                </div>
            </div>
        </div>
    </div>

    <!-- jQuery -->
    <script src="{% static 'vendor/jquery/jquery.min.js' %}"></script>
    <!-- Bootstrap Bundle JS -->
    <script src="{% static 'vendor/bootstrap/js/bootstrap.bundle.min.js' %}"></script>
    <!-- Bootstrap Icons -->
    <link rel="stylesheet" href="{% static 'vendor/bootstrap-icons/font/bootstrap-icons-1.8.0.css' %}">

    <!-- 其他可选的JS库 -->
    <!-- Chart.js -->
    <script src="{% static 'vendor/chart.js/chart.min.js' %}"></script>

    <!-- 自定义JS -->
    {% block extra_js %}{% endblock %}

    <script>
    // 全局函数
    function showModal(title, content, footer = '') {
        // 获取模态框元素
        const modalElement = document.getElementById('globalModal');

        // 如果已经存在模态框实例，先销毁
        const existingModal = bootstrap.Modal.getInstance(modalElement);
        if (existingModal) {
            existingModal.dispose();
        }

        // 设置标题和内容
        document.getElementById('globalModalLabel').textContent = title;
        document.getElementById('globalModalBody').innerHTML = content;

        // 设置页脚（如果有）
        const footerElement = document.getElementById('globalModalFooter');
        if (footer) {
            footerElement.innerHTML = footer;
            footerElement.style.display = 'block';
        } else {
            footerElement.style.display = 'none';
        }

        // 创建新的模态框实例并显示
        const modal = new bootstrap.Modal(modalElement, {
            backdrop: 'static',
            keyboard: false
        });
        modal.show();

        // 返回模态框实例（以便调用者可以进行进一步操作）
        return modal;
    }

    // 会话超时自动登出功能
    {% if user.is_authenticated %}
    (function() {
        // 配置
        const SESSION_TIMEOUT = 20 * 60 * 1000; // 20分钟，与服务器端SESSION_COOKIE_AGE一致
        const WARNING_TIME = 30 * 1000; // 提前30秒显示警告
        const CHECK_INTERVAL = 1000; // 每秒检查一次
        const PING_INTERVAL = 20 * 1000; // 每20秒刷新一次会话

        // 为当前页面生成唯一ID
        const PAGE_ID = Date.now().toString() + Math.random().toString(36).substr(2, 9);

        // localStorage键名
        const LAST_ACTIVITY_KEY = 'session_last_activity';
        const WARNING_DISPLAYED_KEY = 'session_warning_displayed';
        const ACTIVE_PAGES_KEY = 'session_active_pages';

        // 页面状态
        let warningModal = null;
        let logoutTimeout = null;
        let countdownInterval = null;

        // 初始化活跃页面列表
        function initActivePages() {
            const activePages = getActivePages();
            activePages[PAGE_ID] = {
                lastSeen: Date.now(),
                hasWarning: false
            };
            localStorage.setItem(ACTIVE_PAGES_KEY, JSON.stringify(activePages));
        }

        // 获取活跃页面列表
        function getActivePages() {
            try {
                const pagesJson = localStorage.getItem(ACTIVE_PAGES_KEY);
                return pagesJson ? JSON.parse(pagesJson) : {};
            } catch (e) {
                console.error('解析活跃页面列表失败:', e);
                return {};
            }
        }

        // 更新当前页面状态
        function updatePageStatus(hasWarning = false) {
            const activePages = getActivePages();
            activePages[PAGE_ID] = {
                lastSeen: Date.now(),
                hasWarning: hasWarning
            };
            localStorage.setItem(ACTIVE_PAGES_KEY, JSON.stringify(activePages));
        }

        // 清理不活跃的页面
        function cleanupInactivePages() {
            const activePages = getActivePages();
            const now = Date.now();
            let changed = false;

            // 移除超过2分钟未更新的页面
            Object.keys(activePages).forEach(pageId => {
                if (now - activePages[pageId].lastSeen > 2 * 60 * 1000) {
                    delete activePages[pageId];
                    changed = true;
                }
            });

            if (changed) {
                localStorage.setItem(ACTIVE_PAGES_KEY, JSON.stringify(activePages));
            }

            return Object.keys(activePages).length;
        }

        // 检查是否有任何页面显示警告
        function anyPageHasWarning() {
            const activePages = getActivePages();
            return Object.values(activePages).some(page => page.hasWarning);
        }

        // 获取最后活动时间
        function getLastActivity() {
            const storedTime = localStorage.getItem(LAST_ACTIVITY_KEY);
            return storedTime ? parseInt(storedTime, 10) : Date.now();
        }

        // 活动检测函数
        function resetActivityTime() {
            // 更新localStorage中的最后活动时间
            localStorage.setItem(LAST_ACTIVITY_KEY, Date.now().toString());

            // 更新页面状态
            updatePageStatus(false);

            // 如果有警告对话框，关闭它
            closeWarningModal();

            // 清除登出超时
            if (logoutTimeout) {
                clearTimeout(logoutTimeout);
                logoutTimeout = null;
            }

            // 清除倒计时
            if (countdownInterval) {
                clearInterval(countdownInterval);
                countdownInterval = null;
            }
        }

        // 关闭警告模态框
        function closeWarningModal() {
            if (warningModal) {
                const modalElement = document.getElementById('sessionTimeoutModal');
                if (modalElement) {
                    const bsModal = bootstrap.Modal.getInstance(modalElement);
                    if (bsModal) {
                        bsModal.hide();
                    }
                    // 移除模态框
                    modalElement.remove();
                }
                warningModal = null;

                // 更新页面状态
                updatePageStatus(false);
            }
        }

        // 刷新会话函数
        function refreshSession() {
            fetch('/ping/', {
                method: 'GET',
                credentials: 'same-origin',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                }
            }).catch(error => console.error('会话刷新失败:', error));
        }

        // 执行登出的函数，通过POST请求
        function performLogout() {
            // 创建表单
            const form = document.createElement('form');
            form.method = 'POST';
            form.action = "{% url 'logout' %}";
            form.style.display = 'none';

            // 添加CSRF令牌
            const csrfToken = document.createElement('input');
            csrfToken.type = 'hidden';
            csrfToken.name = 'csrfmiddlewaretoken';
            csrfToken.value = '{{ csrf_token }}';
            form.appendChild(csrfToken);

            // 添加到文档并提交
            document.body.appendChild(form);
            form.submit();
        }

        // 显示警告对话框
        function showWarning() {
            // 检查是否已经有警告对话框
            if (warningModal) return;

            // 检查其他页面是否有活动
            const lastActivity = getLastActivity();
            const currentTime = Date.now();
            const idleTime = currentTime - lastActivity;

            // 如果最近有活动，不显示警告
            if (idleTime < (SESSION_TIMEOUT - WARNING_TIME)) {
                return;
            }

            // 更新页面状态
            updatePageStatus(true);

            // 创建警告模态框
            const warningContent = `
                <div class="modal fade" id="sessionTimeoutModal" tabindex="-1" aria-labelledby="sessionTimeoutModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-dialog-centered">
                        <div class="modal-content">
                            <div class="modal-header bg-warning">
                                <h5 class="modal-title" id="sessionTimeoutModalLabel">会话即将过期</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <p>由于长时间无活动，您的会话将在<span id="countdown">30</span>秒后过期。</p>
                                <p>点击"继续"按钮保持登录状态，或点击"登出"按钮安全退出。</p>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" id="logoutNowBtn">登出</button>
                                <button type="button" class="btn btn-primary" id="stayLoggedInBtn">继续</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 添加到文档
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = warningContent;
            document.body.appendChild(tempDiv.firstElementChild);

            // 显示模态框
            const modalElement = document.getElementById('sessionTimeoutModal');
            warningModal = new bootstrap.Modal(modalElement, {
                backdrop: 'static',
                keyboard: false
            });
            warningModal.show();

            // 设置倒计时
            let countdown = Math.floor(WARNING_TIME / 1000);
            const countdownElement = document.getElementById('countdown');
            countdownInterval = setInterval(() => {
                countdown--;
                if (countdownElement) {
                    countdownElement.textContent = countdown;
                }
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    countdownInterval = null;
                }
            }, 1000);

            // 设置按钮事件
            document.getElementById('stayLoggedInBtn').addEventListener('click', function() {
                resetActivityTime();
                refreshSession();
                closeWarningModal();
            });

            document.getElementById('logoutNowBtn').addEventListener('click', function() {
                performLogout();
            });

            // 设置自动登出
            logoutTimeout = setTimeout(() => {
                // 再次检查是否有其他页面活动
                const lastActivity = getLastActivity();
                const currentTime = Date.now();
                const idleTime = currentTime - lastActivity;

                // 如果最近有活动，取消登出
                if (idleTime < (SESSION_TIMEOUT - WARNING_TIME / 2)) {
                    closeWarningModal();
                    return;
                }

                performLogout();
            }, WARNING_TIME);
        }

        // 检查会话状态
        function checkSession() {
            // 清理不活跃的页面
            const activePageCount = cleanupInactivePages();

            // 更新当前页面状态
            updatePageStatus(warningModal !== null);

            // 获取最后活动时间
            const lastActivity = getLastActivity();
            const currentTime = Date.now();
            const idleTime = currentTime - lastActivity;

            // 如果空闲时间超过警告时间但小于会话超时时间，显示警告
            if (idleTime >= (SESSION_TIMEOUT - WARNING_TIME) && idleTime < SESSION_TIMEOUT && !warningModal) {
                showWarning();
            }

            // 如果空闲时间超过会话超时时间，自动登出
            if (idleTime >= SESSION_TIMEOUT) {
                // 再次检查是否有其他页面活动
                const activePages = getActivePages();
                const anyRecentActivity = Object.values(activePages).some(page => {
                    return (currentTime - page.lastSeen) < 5000; // 5秒内有活动
                });

                // 如果没有任何页面有最近活动，执行登出
                if (!anyRecentActivity) {
                    performLogout();
                } else {
                    // 如果有其他页面活动，重置活动时间
                    resetActivityTime();
                }
            }
        }

        // 监听localStorage变化
        window.addEventListener('storage', function(e) {
            if (e.key === LAST_ACTIVITY_KEY) {
                // 如果其他页面更新了活动时间，关闭当前页面的警告
                if (warningModal) {
                    closeWarningModal();
                }
            } else if (e.key === ACTIVE_PAGES_KEY) {
                // 如果活跃页面列表变化，检查是否需要关闭警告
                if (warningModal && !anyPageHasWarning()) {
                    closeWarningModal();
                }
            }
        });

        // 监听页面可见性变化
        document.addEventListener('visibilitychange', function() {
            if (!document.hidden) {
                // 页面变为可见时，更新页面状态
                updatePageStatus(warningModal !== null);
            }
        });

        // 监听用户活动
        const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart'];
        activityEvents.forEach(event => {
            document.addEventListener(event, resetActivityTime, true);
        });

        // 页面卸载前清理
        window.addEventListener('beforeunload', function() {
            // 从活跃页面列表中移除当前页面
            const activePages = getActivePages();
            delete activePages[PAGE_ID];
            localStorage.setItem(ACTIVE_PAGES_KEY, JSON.stringify(activePages));
        });

        // 定期检查会话状态
        setInterval(checkSession, CHECK_INTERVAL);

        // 定期刷新会话（仅当用户活跃时）
        setInterval(() => {
            const lastActivity = getLastActivity();
            const idleTime = Date.now() - lastActivity;
            if (idleTime < (SESSION_TIMEOUT - WARNING_TIME)) {
                refreshSession();
            }
        }, PING_INTERVAL);

        // 初始化
        initActivePages();
        resetActivityTime();
        refreshSession();
    })();
    {% endif %}
    </script>
</body>
</html>