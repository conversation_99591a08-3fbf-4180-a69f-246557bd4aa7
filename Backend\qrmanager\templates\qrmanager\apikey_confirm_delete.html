{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:apikey_list' %}">API密钥管理</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-trash me-1"></i>
            删除API密钥
        </div>
        <div class="card-body">
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-triangle me-1"></i> 
                警告：删除API密钥后，所有使用该密钥的应用将无法访问API。此操作不可逆！
            </div>
            
            <p>您确定要删除以下API密钥吗？</p>
            
            <div class="card mb-3">
                <div class="card-body">
                    <h5 class="card-title">{{ object.name }}</h5>
                    <p class="card-text">
                        <strong>密钥ID：</strong> {{ object.id }}<br>
                        <strong>创建时间：</strong> {{ object.created_at|date:"Y-m-d H:i" }}<br>
                        <strong>创建者：</strong> {{ object.created_by.username }}<br>
                        <strong>状态：</strong> 
                        {% if object.is_active %}
                            {% if object.is_expired %}
                                <span class="badge bg-warning">已过期</span>
                            {% else %}
                                <span class="badge bg-success">激活</span>
                            {% endif %}
                        {% else %}
                            <span class="badge bg-danger">禁用</span>
                        {% endif %}
                    </p>
                </div>
            </div>
            
            <form method="post">
                {% csrf_token %}
                <button type="submit" class="btn btn-danger">
                    <i class="fas fa-trash me-1"></i> 确认删除
                </button>
                <a href="{% url 'qrmanager:apikey_list' %}" class="btn btn-secondary">
                    <i class="fas fa-times me-1"></i> 取消
                </a>
            </form>
        </div>
    </div>
 