# 二维码验证问题排查指南

## 问题描述

当访问带有加密参数的二维码页面（如 `http://10.10.68.110/q/{加密参数}`）时，页面可能会显示"未找到此二维码信息，请确认扫描正确"的错误信息。

## 原因分析

此问题可能有多种原因：

1. API令牌未正确设置或已过期
2. API接口变更（从查询参数格式改为路径参数格式）
3. 二维码参数格式不正确
4. 后端API请求频率限制（每分钟最多10次请求）

## 解决方案

我们已经实现了以下改进来解决这些问题：

1. 修改API调用过程，使用正确的验证端点
2. 添加调试面板，方便设置API令牌
3. 优化参数处理逻辑，兼容多种参数格式
4. 增强错误处理和调试日志

## 使用调试面板

为了方便排查问题，我们已在开发环境添加了API调试面板：

1. 在页面右下角找到蓝色的"D"按钮并点击它
2. 调试面板会显示当前使用的API令牌
3. 可以输入新的API令牌并点击"保存令牌"按钮
4. 使用"测试API"按钮检查API连接状态

## 令牌管理说明

1. API令牌保存在浏览器本地存储中，刷新页面后仍然有效
2. 当前默认的API令牌：`bb98666c-ae62-4722-951c-88052cb5433b`
3. 如需更新令牌，请联系管理员获取最新令牌

## 典型问题排查步骤

### 1. 二维码页面显示"未找到此二维码信息"

**排查步骤**：
1. 打开浏览器控制台（F12）
2. 检查网络请求，找到二维码验证API请求
   - URL格式应为：`/api/v1/qrcode/verify/q/{参数}/`
   - 状态码应为200
3. 如果状态码是401，可能是令牌问题
   - 打开调试面板设置正确的API令牌
4. 如果状态码是429，表示请求频率过高
   - 等待1分钟后再试

### 2. 二维码参数格式问题

如果二维码参数格式不正确，系统会尝试清理和修正格式。常见的格式问题：
- 参数开头有额外的等号`=`
- 参数含有URL编码字符`%`
- 参数长度不足或没有`.`分隔符

### 3. API令牌过期

如果API返回的错误是"令牌已过期"或"无效令牌"：
1. 点击页面右下角的"D"按钮打开调试面板
2. 输入新的API令牌
3. 点击"保存令牌"按钮
4. 点击"测试API"按钮确认令牌有效
5. 刷新页面重试

## 测试二维码

以下是可以用于测试的有效二维码参数：

```
0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5ADM4UzYwMWMlFzYlVGNyYmYiRDMhN2Y1ICI6ISZyVHdh52ZpNnIgwiI4MTMiFTMkFjIgojI0xWYzJCIsIyN5cTNwMTM0ETOxIWL2EmY50CNwQGNtQDM2QWL4MTMiFTMkFjIgojIklWd1Jye.1d11b138
```

完整的测试URL:
```
http://10.10.68.110/q/0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5ADM4UzYwMWMlFzYlVGNyYmYiRDMhN2Y1ICI6ISZyVHdh52ZpNnIgwiI4MTMiFTMkFjIgojI0xWYzJCIsIyN5cTNwMTM0ETOxIWL2EmY50CNwQGNtQDM2QWL4MTMiFTMkFjIgojIklWd1Jye.1d11b138
```

## 联系支持

如仍无法解决问题，请联系技术支持并提供以下信息：
1. 完整的URL
2. 浏览器控制台中的错误信息
3. API响应状态码和内容

---

文档更新日期：2025-03-19 