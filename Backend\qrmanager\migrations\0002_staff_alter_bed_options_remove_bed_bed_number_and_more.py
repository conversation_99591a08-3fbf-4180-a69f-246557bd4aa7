# Generated by Django 4.2.7 on 2025-02-06 08:39

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='Staff',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='姓名')),
                ('title', models.CharField(max_length=50, verbose_name='职位')),
                ('phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('email', models.EmailField(blank=True, max_length=254, verbose_name='邮箱')),
                ('photo', models.ImageField(blank=True, upload_to='staff_photos/', verbose_name='照片')),
                ('department', models.<PERSON><PERSON><PERSON><PERSON>(max_length=50, verbose_name='科室')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '工作人员',
                'verbose_name_plural': '工作人员',
                'ordering': ['name'],
            },
        ),
        migrations.AlterModelOptions(
            name='bed',
            options={'ordering': ['number'], 'verbose_name': '床位', 'verbose_name_plural': '床位'},
        ),
        migrations.RemoveField(
            model_name='bed',
            name='bed_number',
        ),
        migrations.AddField(
            model_name='bed',
            name='department',
            field=models.CharField(default='未知科室', max_length=50, verbose_name='所属科室'),
        ),
        migrations.AddField(
            model_name='bed',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='是否启用'),
        ),
        migrations.AddField(
            model_name='bed',
            name='number',
            field=models.CharField(default='未知床位', max_length=10, unique=True, verbose_name='床号'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='photo',
            field=models.ImageField(blank=True, upload_to='evaluation_photos/', verbose_name='照片'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='sentiment',
            field=models.CharField(choices=[('negative', '负面'), ('neutral', '中性'), ('positive', '正面')], default='neutral', max_length=10, verbose_name='情感倾向'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='bed',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrmanager.bed', verbose_name='关联床位'),
        ),
        migrations.AddField(
            model_name='qrcode',
            name='is_active',
            field=models.BooleanField(default=True, verbose_name='是否启用'),
        ),
        migrations.AlterField(
            model_name='evaluation',
            name='qr_code',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='qrmanager.qrcode', verbose_name='二维码'),
        ),
        migrations.AlterField(
            model_name='evaluation',
            name='rating',
            field=models.IntegerField(choices=[(1, '非常不满意'), (2, '不满意'), (3, '一般'), (4, '满意'), (5, '非常满意')], validators=[django.core.validators.MinValueValidator(1), django.core.validators.MaxValueValidator(5)], verbose_name='评分'),
        ),
        migrations.AddField(
            model_name='bed',
            name='staff',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrmanager.staff', verbose_name='负责人'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='staff',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='qrmanager.staff', verbose_name='相关工作人员'),
        ),
    ]
