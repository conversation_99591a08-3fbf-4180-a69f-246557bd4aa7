# Generated manually for hospital rating feature

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0001_initial'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='hospital_rating',
            field=models.IntegerField(
                blank=True,
                choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')],
                help_text='1-5星评分，用户对医院整体服务的评价',
                null=True,
                verbose_name='医院整体评分'
            ),
        ),
    ]
