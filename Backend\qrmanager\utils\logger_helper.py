"""
日志记录工具
用于记录用户操作日志
"""

from django.core.cache import cache
from django.utils import timezone

class LoggerHelper:
    """
    日志记录工具类
    用于记录用户操作日志
    """
    
    @staticmethod
    def should_log_action(action_type):
        """
        检查是否应该记录特定类型的操作
        
        参数:
            action_type: 操作类型
            
        返回:
            bool: 是否应该记录
        """
        # 使用缓存存储配置，避免频繁查询数据库
        config = cache.get('logging_config')
        if config is None:
            try:
                # 从数据库加载配置
                from ..models import LoggingConfiguration
                config = {
                    item.action_type: item.is_enabled
                    for item in LoggingConfiguration.objects.all()
                }
                # 缓存配置，有效期1小时
                cache.set('logging_config', config, 3600)
            except:
                # 如果数据库查询失败，使用默认配置
                config = {
                    'create': True,
                    'update': True,
                    'delete': True,
                    'login': True,
                    'logout': True,
                    'error': True,
                    'permission_change': True,
                    'system_config': True,
                }
        
        # 检查是否应该记录此类型的操作
        return config.get(action_type, True)
    
    @staticmethod
    def log_operation(user, action, description, status='success', **kwargs):
        """
        记录用户操作日志
        
        参数:
            user: 用户对象或用户ID
            action: 操作类型
            description: 操作描述
            status: 操作状态，默认为成功
            **kwargs: 额外参数，如request、extra_data等
        """
        # 提取操作类型
        action_type = None
        
        # 处理常见操作类型
        if 'create' in action.lower():
            action_type = 'create'
        elif 'update' in action.lower() or 'edit' in action.lower() or 'modify' in action.lower():
            action_type = 'update'
        elif 'delete' in action.lower() or 'remove' in action.lower():
            action_type = 'delete'
        elif 'login' in action.lower():
            action_type = 'login'
        elif 'logout' in action.lower():
            action_type = 'logout'
        elif 'view' in action.lower() or 'get' in action.lower():
            action_type = 'view'
        elif 'export' in action.lower():
            action_type = 'export'
        elif 'import' in action.lower():
            action_type = 'import'
        elif 'print' in action.lower():
            action_type = 'print'
        elif 'generate' in action.lower():
            action_type = 'generate'
        elif 'permission' in action.lower():
            action_type = 'permission_change'
        elif 'config' in action.lower():
            action_type = 'system_config'
        elif 'api' in action.lower():
            action_type = 'api_request'
        elif 'file_upload' in action.lower() or 'upload' in action.lower():
            action_type = 'file_upload'
        elif 'file_download' in action.lower() or 'download' in action.lower():
            action_type = 'file_download'
        elif 'bulk' in action.lower():
            action_type = 'bulk_operation'
        elif 'error' in action.lower():
            action_type = 'error'
        
        # 如果无法识别操作类型，不记录日志
        if action_type is None:
            print(f"无法识别的操作类型: {action}")
            return
            
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action(action_type):
            print(f"根据配置不记录操作类型: {action_type}")
            return
        
        # 从kwargs中获取额外参数
        request = kwargs.get('request')
        extra_data = kwargs.get('extra_data')
        
        # 获取客户端IP地址
        ip_address = None
        if request:
            # 从请求中获取IP地址
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0].strip()
            else:
                ip_address = request.META.get('REMOTE_ADDR')
        
        # 延迟导入OperationLog，避免循环导入
        from ..models import OperationLog
        
        # 创建操作日志
        OperationLog.objects.create(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            browser=request.META.get('HTTP_USER_AGENT') if request else None,
            os=request.META.get('HTTP_USER_AGENT') if request else None,
            status=status,
            extra_data=extra_data
        )
    
    @staticmethod
    def log_model_operation(user, instance, operation_type, description=None, old_data=None, new_data=None, related_data=None, extra_data=None, request=None):
        """
        记录模型操作日志
        
        参数:
            user: 用户对象或用户ID
            instance: 模型实例
            operation_type: 操作类型（create, update, delete）
            description: 操作描述，如果为None则自动生成
            old_data: 更新前的数据（仅用于update操作）
            new_data: 更新后的数据（仅用于update操作）
            related_data: 关联数据（仅用于delete操作）
            extra_data: 额外数据
            request: HTTP请求对象
        """
        # 获取模型名称
        model_name = instance.__class__.__name__ if instance else 'Unknown'

        # 如果没有提供描述，则自动生成
        if description is None:
            if operation_type == 'create':
                description = f"创建{model_name}: {instance if instance else 'bulk_operation'}"
            elif operation_type == 'update':
                description = f"更新{model_name}: {instance if instance else 'bulk_operation'}"
            elif operation_type == 'delete':
                description = f"删除{model_name}: {instance if instance else 'bulk_operation'}"
            else:
                description = f"{operation_type} {model_name}: {instance if instance else 'bulk_operation'}"
        
        # 准备额外数据
        extra_data_dict = {
            'model': model_name,
            'instance_id': instance.id if instance else None,
            'instance_str': str(instance) if instance else 'None',
            'timestamp': timezone.now().isoformat()
        }
        
        # 添加旧数据和新数据（如果有）
        if old_data:
            extra_data_dict['old_data'] = old_data
        if new_data:
            extra_data_dict['new_data'] = new_data
        if related_data:
            extra_data_dict['related_data'] = related_data
        if extra_data:
            extra_data_dict.update(extra_data)
        
        # 记录操作日志
        LoggerHelper.log_operation(
            user=user,
            action=f"{operation_type}_{model_name.lower()}",
            description=description,
            extra_data=extra_data_dict,
            request=request
        )