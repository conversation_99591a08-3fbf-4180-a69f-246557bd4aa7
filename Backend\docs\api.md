# API 文档

## 1. 概述
本文档描述了医院服务评价系统的所有API接口。除评价提交接口外，所有接口都需要进行身份验证。

## 2. 基础信息

### 2.1 基础URL
```
http://your-domain/api/
```

### 2.2 认证方式
- 使用 Django 内置的会话认证
- 需要在登录后获取 CSRF Token
- CSRF Token 需要在请求头中携带：`X-CSRFToken`

## 3. API 接口列表

### 3.1 基础页面接口

#### 首页
- URL: `/`
- 方法: GET
- 说明: 系统首页

#### 关于页面
- URL: `/about/`
- 方法: GET
- 说明: 关于页面

#### 联系页面
- URL: `/contact/`
- 方法: GET
- 说明: 联系页面

#### 隐私政策
- URL: `/privacy/`
- 方法: GET
- 说明: 隐私政策页面

### 3.2 评价相关接口

#### 提交评价
- URL: `/evaluation/<str:qr_code>/`
- 方法: POST
- 参数:
  ```json
  {
    "rating": "整数，1-5",
    "comment": "评价内容，可选",
    "photo": "照片文件，可选"
  }
  ```
- 响应:
  ```json
  {
    "status": "success",
    "message": "评价提交成功"
  }
  ```

#### 评价列表
- URL: `/evaluations/`
- 方法: GET
- 参数:
  - page: 页码
  - department: 科室ID（可选）
  - staff: 工作人员ID（可选）
  - date_from: 开始日期（可选）
  - date_to: 结束日期（可选）

#### 情感分析
- URL: `/sentiment/`
- 方法: GET
- 说明: 获取评价情感分析统计数据

### 3.3 管理后台接口

#### 仪表盘
- URL: `/dashboard/`
- 方法: GET
- 说明: 获取系统统计数据

### 3.4 二维码管理接口

#### 创建二维码
- URL: `/qr/create/`
- 方法: POST
- 参数:
  ```json
  {
    "name": "二维码名称",
    "description": "描述信息，可选",
    "bed_id": "关联床位ID，可选"
  }
  ```

#### 二维码列表
- URL: `/qr/list/`
- 方法: GET
- 参数:
  - department: 科室ID（可选）
  - area: 区域（可选）
  - search: 搜索关键词（可选）

#### 更新二维码
- URL: `/qr/<int:pk>/edit/`
- 方法: PUT
- 参数: 同创建

#### 删除二维码
- URL: `/qr/<int:pk>/delete/`
- 方法: DELETE

#### 重新生成二维码
- URL: `/qr/<int:pk>/regenerate/`
- 方法: POST

### 3.5 工作人员管理接口

#### 工作人员列表
- URL: `/staff/`
- 方法: GET
- 参数:
  - department: 科室ID（可选）
  - staff_type: 人员类型（可选）
  - search: 搜索关键词（可选）

#### 创建工作人员
- URL: `/staff/create/`
- 方法: POST
- 参数:
  ```json
  {
    "work_number": "工号",
    "name": "姓名",
    "staff_type": "人员类型ID",
    "title": "职称ID",
    "department": "科室ID，可选",
    "photo": "照片文件，可选"
  }
  ```

#### 批量导入工作人员
- URL: `/staff/bulk_import/`
- 方法: POST
- 参数: 
  - file: Excel文件

#### 下载导入模板
- URL: `/staff/template/`
- 方法: GET
- 说明: 下载工作人员批量导入模板

### 3.6 床位管理接口

#### 床位列表
- URL: `/beds/`
- 方法: GET
- 参数:
  - department: 科室ID（可选）
  - area: 区域（可选）
  - is_active: 是否启用（可选）

#### 创建床位
- URL: `/beds/create/`
- 方法: POST
- 参数:
  ```json
  {
    "number": "床号",
    "department": "科室ID",
    "area": "区域",
    "staff": "负责人ID，可选"
  }
  ```

#### 床位二维码预览
- URL: `/beds/<int:pk>/qrcode/`
- 方法: GET
- 说明: 获取床位关联的二维码预览图

### 3.7 科室管理接口

#### 科室列表
- URL: `/departments/`
- 方法: GET

#### 创建科室
- URL: `/departments/create/`
- 方法: POST
- 参数:
  ```json
  {
    "code": "科室编码",
    "name": "科室名称",
    "remarks": "备注说明，可选"
  }
  ```

### 3.8 字典管理接口

#### 字典列表
- URL: `/dictionaries/`
- 方法: GET

#### 字典项列表
- URL: `/dictionaries/<int:dictionary_id>/items/`
- 方法: GET

### 3.9 打印模板管理接口

#### 创建打印模板
- URL: `/departments/<int:department_id>/print-template/create/`
- 方法: POST
- 参数:
  - template_file: 模板文件

#### 预览打印模板
- URL: `/print-templates/<int:pk>/preview/`
- 方法: GET

### 3.10 管理员账户接口

#### 创建管理员账户
- URL: `/admin/create/`
- 方法: POST
- 参数:
  ```json
  {
    "username": "用户名",
    "password": "密码",
    "email": "邮箱",
    "permissions": ["权限列表"]
  }
  ```

#### 重置用户密码
- URL: `/admin/reset_password/<int:pk>/`
- 方法: POST
- 参数:
  ```json
  {
    "new_password": "新密码"
  }
  ```

### 3.11 操作日志接口

#### 操作日志列表
- URL: `/operation_logs/`
- 方法: GET
- 参数:
  - user: 用户ID（可选）
  - action: 操作类型（可选）
  - date_from: 开始日期（可选）
  - date_to: 结束日期（可选）

## 4. 状态码说明
- 200: 请求成功
- 201: 创建成功
- 400: 请求参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

## 5. 注意事项
1. 所有时间使用UTC时间
2. 文件上传大小限制：
   - 照片：最大10MB
   - 导入文件：最大20MB
3. 支持的图片格式：JPG、JPEG、PNG
4. API访问频率限制：每分钟60次请求

## 6. 更新记录
- 2024-02-21: 根据实际urls.py更新API文档 