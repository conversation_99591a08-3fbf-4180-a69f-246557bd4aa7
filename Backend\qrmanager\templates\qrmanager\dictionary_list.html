{% extends 'qrmanager/base.html' %}
{% load django_bootstrap5 %}

{% block title %}字典管理{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h2>字典管理</h2>
        <a href="{% url 'qrmanager:dictionary_create' %}" class="btn btn-primary">
            <i class="bi bi-plus-lg"></i> 新增字典
        </a>
    </div>

    {% if messages %}
    <div class="messages">
        {% for message in messages %}
        <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
            {{ message }}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>
        {% endfor %}
    </div>
    {% endif %}

    <div class="card">
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>编码</th>
                            <th>名称</th>
                            <th>描述</th>
                            <th>状态</th>
                            <th>创建时间</th>
                            <th>操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for dictionary in dictionaries %}
                        <tr>
                            <td>{{ dictionary.code }}</td>
                            <td>{{ dictionary.name }}</td>
                            <td>{{ dictionary.description|default:'-' }}</td>
                            <td>
                                {% if dictionary.is_active %}
                                <span class="badge bg-success">启用</span>
                                {% else %}
                                <span class="badge bg-danger">禁用</span>
                                {% endif %}
                            </td>
                            <td>{{ dictionary.created_at|date:"Y-m-d H:i" }}</td>
                            <td>
                                <div class="btn-group" role="group">
                                    <a href="{% url 'qrmanager:dictionary_item_list' dictionary.id %}" 
                                       class="btn btn-sm btn-info" title="查看字典项">
                                        <i class="bi bi-list-ul"></i>
                                    </a>
                                    <a href="{% url 'qrmanager:dictionary_update' dictionary.id %}" 
                                       class="btn btn-sm btn-warning" title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </a>
                                    <a href="{% url 'qrmanager:dictionary_delete' dictionary.id %}" 
                                       class="btn btn-sm btn-danger" title="删除">
                                        <i class="bi bi-trash"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="6" class="text-center">暂无数据</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
{% endblock %} 