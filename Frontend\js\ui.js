/**
 * UI模块 - 处理界面交互和动画效果
 * 2025-03-24 重构版
 */

(function() {
    // 创建全局初始化命名空间（如果不存在）
    window._appInitFlags = window._appInitFlags || {};

    // 页面元素引用
    const elements = {
        loadingIndicator: document.getElementById('loadingIndicator'),
        errorContainer: document.getElementById('errorContainer'),
        errorMessage: document.getElementById('errorMessage'),
        successContainer: document.getElementById('successContainer'),
        successMessage: document.getElementById('successMessage'),
        feedbackForm: document.getElementById('feedbackForm'),
        submitButton: document.getElementById('submitButton'),
        ratingButtons: document.querySelectorAll('.rating-button'),
        modalContainer: document.getElementById('modalContainer'),
        modalContent: document.getElementById('modalContent'),
        modalClose: document.getElementById('modalClose')
    };

    // UI模块公共方法
    const ui = {
        /**
         * 初始化UI模块
         */
        init: function() {
            // 防止重复初始化 - 使用全局初始化标志
            if (window._appInitFlags.uiInitialized) {
                console.log('UI模块已经初始化，跳过重复初始化');
                return;
            }

            console.log('初始化UI模块...');

            // 注册事件处理程序
            this.registerEventHandlers();

            // 添加页面过渡动画
            this.addPageTransition();

            // 设置全局初始化标志
            window._appInitFlags.uiInitialized = true;

            console.log('UI模块初始化完成');
        },

        /**
         * 注册事件处理程序
         */
        registerEventHandlers: function() {
            // 评分按钮点击事件
            if (elements.ratingButtons && elements.ratingButtons.length > 0) {
                elements.ratingButtons.forEach(button => {
                    button.addEventListener('click', this.handleRatingClick.bind(this));
                });
            }

            // 表单提交事件
            if (elements.feedbackForm) {
                elements.feedbackForm.addEventListener('submit', this.handleFormSubmit.bind(this));
            }

            // 模态框关闭按钮
            if (elements.modalClose) {
                elements.modalClose.addEventListener('click', this.hideModal.bind(this));
            }

            // 模态框点击外部关闭
            if (elements.modalContainer) {
                elements.modalContainer.addEventListener('click', event => {
                    if (event.target === elements.modalContainer) {
                        this.hideModal();
                    }
                });
            }

            // ESC键关闭模态框
            document.addEventListener('keydown', event => {
                if (event.key === 'Escape') {
                    this.hideModal();
                }
            });
        },

        /**
         * 处理评分按钮点击
         * @param {Event} event - 点击事件
         */
        handleRatingClick: function(event) {
            const button = event.currentTarget;
            const rating = parseInt(button.dataset.rating, 10);

            if (isNaN(rating)) return;

            // 更新全局评分
            if (window.appData) {
                window.appData.rating = rating;
            }

            // 更新按钮UI
            elements.ratingButtons.forEach(btn => {
                btn.classList.remove('active');
            });

            button.classList.add('active');
        },

        /**
         * 处理表单提交
         * @param {Event} event - 提交事件
         */
        handleFormSubmit: function(event) {
            // 表单验证由主模块处理
            if (elements.submitButton) {
                elements.submitButton.disabled = true;
                elements.submitButton.classList.add('loading');
            }
        },

        /**
         * 显示加载指示器
         */
        showLoading: function() {
            // 检查是否已经提交过评价，如果是，不显示加载指示器
            const isSubmitted = sessionStorage.getItem('evaluationSubmitted') === 'true';
            if (isSubmitted) {
                console.log('用户已经提交过评价，不显示加载指示器');
                return;
            }

            if (elements.loadingIndicator) {
                // 更新加载文本
                const loadingText = elements.loadingIndicator.querySelector('.loading-text');
                if (loadingText) {
                    if (isSubmitted) {
                        loadingText.textContent = '您已经提交过评价，请重新扫描二维码';
                    } else {
                        loadingText.textContent = '请扫描二维码';
                    }
                }

                elements.loadingIndicator.classList.remove('hidden');
            }

            if (elements.submitButton) {
                elements.submitButton.disabled = true;
                elements.submitButton.classList.add('loading');
            }
        },

        /**
         * 隐藏加载指示器
         */
        hideLoading: function() {
            if (elements.loadingIndicator) {
                elements.loadingIndicator.classList.add('hidden');
            }

            if (elements.submitButton) {
                elements.submitButton.disabled = false;
                elements.submitButton.classList.remove('loading');
            }
        },

        /**
         * 显示错误消息
         * @param {string} message - 错误消息
         * @param {number} duration - 显示时长（毫秒），0表示不自动隐藏
         */
        showError: function(message, duration = 3000) {
            if (!elements.errorContainer || !elements.errorMessage) return;

            // 设置错误消息
            elements.errorMessage.textContent = message;

            // 显示错误容器
            elements.errorContainer.classList.remove('hidden');

            // 添加淡入效果
            elements.errorContainer.classList.add('fade-in');

            // 如果设置了时长，则自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    this.hideError();
                }, duration);
            }
        },

        /**
         * 隐藏错误消息
         */
        hideError: function() {
            if (!elements.errorContainer) return;

            // 添加淡出效果
            elements.errorContainer.classList.add('fade-out');

            // 延迟后隐藏
            setTimeout(() => {
                elements.errorContainer.classList.remove('fade-in', 'fade-out');
                elements.errorContainer.classList.add('hidden');
            }, 300);
        },

        /**
         * 显示成功消息
         * @param {string} message - 成功消息
         * @param {number} duration - 显示时长（毫秒），0表示不自动隐藏
         */
        showSuccess: function(message, duration = 3000) {
            if (!elements.successContainer || !elements.successMessage) return;

            // 设置成功消息
            elements.successMessage.textContent = message;

            // 显示成功容器
            elements.successContainer.classList.remove('hidden');

            // 添加淡入效果
            elements.successContainer.classList.add('fade-in');

            // 如果设置了时长，则自动隐藏
            if (duration > 0) {
                setTimeout(() => {
                    this.hideSuccess();
                }, duration);
            }
        },

        /**
         * 隐藏成功消息
         */
        hideSuccess: function() {
            if (!elements.successContainer) return;

            // 添加淡出效果
            elements.successContainer.classList.add('fade-out');

            // 延迟后隐藏
            setTimeout(() => {
                elements.successContainer.classList.remove('fade-in', 'fade-out');
                elements.successContainer.classList.add('hidden');
            }, 300);
        },

        /**
         * 显示模态框
         * @param {string} content - 模态框内容
         * @param {object} options - 选项
         */
        showModal: function(content, options = {}) {
            if (!elements.modalContainer || !elements.modalContent) return;

            // 设置内容
            if (typeof content === 'string') {
                elements.modalContent.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                elements.modalContent.innerHTML = '';
                elements.modalContent.appendChild(content);
            }

            // 设置选项
            if (options.width) {
                elements.modalContent.style.width = options.width;
            }

            if (options.height) {
                elements.modalContent.style.height = options.height;
            }

            if (options.className) {
                elements.modalContainer.className = `modal-container ${options.className}`;
            } else {
                elements.modalContainer.className = 'modal-container';
            }

            // 显示模态框
            elements.modalContainer.classList.remove('hidden');

            // 添加淡入效果
            setTimeout(() => {
                elements.modalContainer.classList.add('active');
            }, 10);

            // 阻止背景滚动
            document.body.style.overflow = 'hidden';
        },

        /**
         * 隐藏模态框
         */
        hideModal: function() {
            if (!elements.modalContainer) return;

            // 移除活动状态
            elements.modalContainer.classList.remove('active');

            // 延迟后隐藏
            setTimeout(() => {
                elements.modalContainer.classList.add('hidden');

                // 恢复背景滚动
                document.body.style.overflow = '';
            }, 300);
        },

        /**
         * 添加页面过渡动画
         */
        addPageTransition: function() {
            // 添加页面加载完成动画
            document.body.classList.add('page-loaded');

            // 监听链接点击，添加页面切换动画
            document.addEventListener('click', event => {
                const link = event.target.closest('a');

                if (link && !link.target && link.href && !link.dataset.noTransition) {
                    event.preventDefault();

                    // 淡出当前页面
                    document.body.classList.add('page-leaving');

                    // 延迟后跳转
                    setTimeout(() => {
                        window.location.href = link.href;
                    }, 300);
                }
            });
        },

        /**
         * 切换元素显示状态
         * @param {string|Element} element - 元素ID或DOM元素
         * @param {boolean} show - 是否显示
         */
        toggleElement: function(element, show) {
            const el = typeof element === 'string' ? document.getElementById(element) : element;

            if (!el) return;

            if (show) {
                el.classList.remove('hidden');
            } else {
                el.classList.add('hidden');
            }
        },

        /**
         * 平滑滚动到目标元素
         * @param {string|Element} element - 元素ID或DOM元素
         * @param {number} offset - 偏移量（像素）
         * @param {number} duration - 动画时长（毫秒）
         */
        scrollToElement: function(element, offset = 0, duration = 500) {
            const el = typeof element === 'string' ? document.getElementById(element) : element;

            if (!el) return;

            const targetPosition = el.getBoundingClientRect().top + window.pageYOffset - offset;
            const startPosition = window.pageYOffset;
            const distance = targetPosition - startPosition;
            let startTime = null;

            function animation(currentTime) {
                if (startTime === null) startTime = currentTime;
                const timeElapsed = currentTime - startTime;
                const scroll = ease(timeElapsed, startPosition, distance, duration);
                window.scrollTo(0, scroll);
                if (timeElapsed < duration) requestAnimationFrame(animation);
            }

            // 缓动函数
            function ease(t, b, c, d) {
                t /= d / 2;
                if (t < 1) return c / 2 * t * t + b;
                t--;
                return -c / 2 * (t * (t - 2) - 1) + b;
            }

            requestAnimationFrame(animation);
        }
    };

    // 导出为全局对象
    window.ui = ui;
})();