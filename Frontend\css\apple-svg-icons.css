/**
 * 增强医疗风格彩色SVG图标 - 苹果设计美学 2.0版
 * 高质量矢量图标，无需加载额外字体文件
 * 专为医疗服务评价系统设计，全面医疗主题色彩优化
 */

/* 图标基础样式 */
[class^="ai-"],
[class*=" ai-"] {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 1.2em;
  height: 1.2em;
  vertical-align: -0.25em;
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
}

/* 图标尺寸 - 移动端更大更易点击 */
.ai-sm {
  font-size: 1em;
}

.ai-lg {
  font-size: 1.5em;
}

.ai-2x {
  font-size: 2.2em;
}

.ai-3x {
  font-size: 3.2em;
}

/* 标题中的图标自动适配 */
h1 [class^="ai-"], h1 [class*=" ai-"],
h2 [class^="ai-"], h2 [class*=" ai-"],
h3 [class^="ai-"], h3 [class*=" ai-"] {
  margin-right: 0.3em;
}

/* 移动端小屏幕调整 */
@media (max-width: 480px) {
  [class^="ai-"],
  [class*=" ai-"] {
    width: 1.4em;
    height: 1.4em;
  }
  
  .ai-2x {
    font-size: 2.4em;
  }
  
  .ai-3x {
    font-size: 3.4em;
  }
}

/* 旋转动画 */
.ai-spin {
  animation: ai-spin 2s infinite linear;
}

@keyframes ai-spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 增强医疗风格图标定义 - 精美彩色版 */
.ai-hospital {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="4" y="4" width="16" height="16" rx="2" ry="2" fill="%23e1f5fe"/><path d="M9 9h6v12H9z" fill="%23bbdefb"/><path d="M12 3v6" stroke="%230070e4"/><path d="M9 15h6" stroke="%230070e4"/><path d="M8 3v2" stroke="%230070e4"/><path d="M16 3v2" stroke="%230070e4"/><path d="M12 12v6" stroke="%230070e4"/><rect x="9" y="7" width="6" height="2" fill="%23f44336" rx="1"/></svg>');
}

.ai-user {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" fill="%23bbdefb"/><path d="M20 21v-2a6 6 0 0 0-12 0v2" fill="%23e1f5fe"/><path d="M20 21v-2a6 6 0 0 0-12 0v2h12z" fill="%23e1f5fe"/></svg>');
}

.ai-star {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffca28" stroke="%23fb8c00" stroke-width="1" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"/></svg>');
}

.ai-doctor {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 11a4 4 0 1 0 0-8 4 4 0 0 0 0 8z" fill="%23bbdefb"/><path d="M20 21v-2a6 6 0 0 0-12 0v2" fill="%23e1f5fe"/><path d="M20 21v-2a6 6 0 0 0-12 0v2h12z" fill="%23e1f5fe"/><rect x="8" y="6" width="8" height="2" fill="%23f44336" rx="1"/><rect x="11" y="4" width="2" height="6" fill="%23f44336" rx="1"/><circle cx="12" cy="16" r="2" fill="%23bbdefb" stroke="%230070e4"/></svg>');
}

.ai-bed {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M2 4v16" stroke="%230070e4"/><path d="M22 16V7a2 2 0 0 0-2-2h-8a2 2 0 0 0-2 2v9" stroke="%230070e4"/><path d="M2 16h20" stroke="%230070e4"/><path d="M4 16v-4h15a1 1 0 0 1 1 1v3" stroke="%230070e4"/><rect x="4" y="12" width="16" height="4" fill="%23e1f5fe" rx="1"/><rect x="10" y="5" width="10" height="7" fill="%23bbdefb" rx="1"/></svg>');
}

.ai-users {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2" stroke="%230070e4"/><circle cx="8.5" cy="7" r="4" fill="%23bbdefb" stroke="%230070e4"/><path d="M23 21v-2a4 4 0 0 0-3-3.87" stroke="%230070e4"/><path d="M16 3.13a4 4 0 0 1 0 7.75" stroke="%230070e4"/><circle cx="17" cy="7" r="3" fill="%23e1f5fe" stroke="%230070e4"/><path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2h15z" fill="%23e1f5fe"/></svg>');
}

.ai-comment {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e1f5fe" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/></svg>');
}

.ai-send {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23bbdefb" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><line x1="22" y1="2" x2="11" y2="13"/><polygon points="22 2 15 22 11 13 2 9 22 2"/></svg>');
}

.ai-check-circle {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2366bb6a" stroke="%23388e3c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22 4 12 14.01 9 11.01"/></svg>');
}

.ai-warning {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23ffb74d" stroke="%23ef6c00" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"/><line x1="12" y1="9" x2="12" y2="13"/><line x1="12" y1="17" x2="12.01" y2="17"/></svg>');
}

.ai-close {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23e53935" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><line x1="18" y1="6" x2="6" y2="18"/><line x1="6" y1="6" x2="18" y2="18"/></svg>');
}

.ai-like {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2366bb6a" stroke="%23388e3c" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3H14z"/><path d="M7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"/></svg>');
}

.ai-dislike {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935" stroke="%23c62828" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M10 15v4a3 3 0 0 0 3 3l4-9V2H5.72a2 2 0 0 0-2 1.7l-1.38 9a2 2 0 0 0 2 2.3H10z"/><path d="M17 2h3a2 2 0 0 1 2 2v7a2 2 0 0 1-2 2h-3"/></svg>');
}

.ai-time {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e1f5fe" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><polyline points="12 6 12 12 16 14"/></svg>');
}

/* 增强医疗图标 */
.ai-heartbeat {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23e53935" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z" fill="%23ffcdd2"/><polyline points="3.5 12 7.5 12 9.5 8 12.5 16 15.5 12 20.5 12" stroke="%23e53935" stroke-width="1.5"/></svg>');
}

.ai-pill {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M10.5 4.5a7.78 7.78 0 0 0-11 11l11 11 11-11a7.78 7.78 0 0 0-11-11z" transform="rotate(45 12 12)" fill="%23e1f5fe"/><line x1="8.7" y1="8.7" x2="15.3" y2="15.3" stroke="%230070e4" stroke-width="2"/><path d="M10.5 4.5a7.78 7.78 0 0 0-11 11l5.5 5.5" transform="rotate(45 12 12)" fill="%23bbdefb"/></svg>');
}

.ai-stethoscope {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M4.5 12h6" stroke="%230070e4"/><path d="M4.5 4.5a3 3 0 0 1 6 0V12a6 6 0 0 0 6 6v0a3 3 0 1 0 3-3h-1.5V9a1.5 1.5 0 1 0-3 0v6" stroke="%230070e4"/><path d="M4.5 4.5v7.5" stroke="%230070e4"/><path d="M10.5 4.5v7.5" stroke="%230070e4"/><circle cx="19.5" cy="15" r="1.5" fill="%23bbdefb"/><path d="M4.5 4.5a3 3 0 0 1 6 0V12" fill="%23e1f5fe"/></svg>');
}

.ai-medical-cross {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935" stroke="%23c62828" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="7" y="3" width="10" height="18" rx="2" fill="%23e53935"/><rect x="3" y="7" width="18" height="10" rx="2" fill="%23e53935"/></svg>');
}

.ai-medkit {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="6" width="18" height="14" rx="2" ry="2" fill="%23e1f5fe"/><path d="M16 6V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v2" stroke="%230070e4"/><rect x="10" y="11" width="4" height="4" fill="%23e53935"/><line x1="12" y1="10" x2="12" y2="16" stroke="%23ffffff" stroke-width="1.5"/><line x1="9" y1="13" x2="15" y2="13" stroke="%23ffffff" stroke-width="1.5"/></svg>');
}

/* 新增医疗图标 */
.ai-thermometer {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M14 14.76V3.5a2.5 2.5 0 0 0-5 0v11.26a4.5 4.5 0 1 0 5 0z" stroke="%230070e4"/><circle cx="11.5" cy="18" r="2.5" fill="%23e53935"/><path d="M11.5 3.5v11" stroke="%230070e4" stroke-width="1"/></svg>');
}

.ai-syringe {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M4 18L18 4" stroke="%230070e4" stroke-width="1.5"/><path d="M9 13l2 2" stroke="%230070e4" stroke-width="1.5"/><path d="M13 9l2 2" stroke="%230070e4" stroke-width="1.5"/><path d="M10 14l3.95 3.95a2.5 2.5 0 0 0 3.536 0v0a2.5 2.5 0 0 0 0-3.536L14 11" stroke="%230070e4" stroke-width="1.5" fill="%23bbdefb"/><path d="M5 19l-1 1" stroke="%230070e4" stroke-width="1.5"/><rect x="12" y="4" width="8" height="2" rx="1" transform="rotate(45 12 4)" fill="%23ffcdd2" stroke="%23e53935" stroke-width="0.5"/></svg>');
}

.ai-ambulance {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M7 17a2 2 0 1 0 4 0a2 2 0 0 0-4 0z" fill="%23607d8b"/><path d="M17 17a2 2 0 1 0 4 0a2 2 0 0 0-4 0z" fill="%23607d8b"/><path d="M5 17H3V6a2 2 0 0 1 2-2h8l6 6v7h-2" stroke="%230070e4"/><path d="M13 4v5h5" stroke="%230070e4"/><rect x="8" y="10" width="5" height="5" rx="1" fill="%23fff" stroke="%23e53935" stroke-width="1.5"/><path d="M10.5 15v-5" stroke="%23e53935" stroke-width="1.5"/><path d="M8 12.5h5" stroke="%23e53935" stroke-width="1.5"/><path d="M3 9h4" stroke="%230070e4"/><path d="M3 13h3" stroke="%230070e4"/><path d="M3 6h4" stroke="%230070e4"/><rect x="3" y="6" width="10" height="11" rx="2" fill="%23ffffff"/></svg>');
}

.ai-lungs {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M12 2v5" stroke="%230070e4"/><path d="M9 4.5h6" stroke="%230070e4"/><path d="M6 10c0-1.7 1.3-3 3-3 1 0 1 .7 1 1v8c0 1.7-1.3 3-3 3s-3-1.3-3-3c0-4 1-6 1-10" fill="%23e1f5fe" stroke="%230070e4"/><path d="M18 10c0-1.7-1.3-3-3-3-1 0-1 .7-1 1v8c0 1.7 1.3 3 3 3s3-1.3 3-3c0-4-1-6-1-10" fill="%23e1f5fe" stroke="%230070e4"/><path d="M9 13c-1-2-2-3-4-3" stroke="%230070e4"/><path d="M15 13c1-2 2-3 4-3" stroke="%230070e4"/></svg>');
}

.ai-pulse {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="%23e53935" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"><path d="M22 12h-4l-3 9-10-18-3 9H2" stroke="%23e53935"/><circle cx="12" cy="12" r="10" stroke="%230070e4" stroke-width="1.5" stroke-dasharray="2 2" fill="%23e1f5fe" opacity="0.5"/></svg>');
}