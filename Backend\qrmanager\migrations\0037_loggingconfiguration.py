# Generated by Django 4.2.7 on 2025-03-17 17:00

from django.db import migrations, models


def create_initial_logging_config(apps, schema_editor):
    """创建初始日志配置数据"""
    LoggingConfiguration = apps.get_model('qrmanager', 'LoggingConfiguration')
    
    # 默认只启用重要操作的日志记录
    default_configs = [
        # 默认启用的操作类型
        {'action_type': 'create', 'is_enabled': True, 'description': '记录所有创建操作'},
        {'action_type': 'update', 'is_enabled': True, 'description': '记录所有更新操作'},
        {'action_type': 'delete', 'is_enabled': True, 'description': '记录所有删除操作'},
        {'action_type': 'login', 'is_enabled': True, 'description': '记录用户登录'},
        {'action_type': 'logout', 'is_enabled': True, 'description': '记录用户退出'},
        {'action_type': 'permission_change', 'is_enabled': True, 'description': '记录权限变更'},
        {'action_type': 'system_config', 'is_enabled': True, 'description': '记录系统配置变更'},
        {'action_type': 'error', 'is_enabled': True, 'description': '记录错误操作'},
        
        # 默认禁用的操作类型
        {'action_type': 'view', 'is_enabled': False, 'description': '记录查看操作'},
        {'action_type': 'export', 'is_enabled': False, 'description': '记录导出操作'},
        {'action_type': 'import', 'is_enabled': False, 'description': '记录导入操作'},
        {'action_type': 'print', 'is_enabled': False, 'description': '记录打印操作'},
        {'action_type': 'generate', 'is_enabled': False, 'description': '记录生成操作'},
        {'action_type': 'api_request', 'is_enabled': False, 'description': '记录API请求'},
        {'action_type': 'file_upload', 'is_enabled': False, 'description': '记录文件上传'},
        {'action_type': 'file_download', 'is_enabled': False, 'description': '记录文件下载'},
        {'action_type': 'bulk_operation', 'is_enabled': False, 'description': '记录批量操作'},
    ]
    
    for config in default_configs:
        LoggingConfiguration.objects.create(**config)


def reverse_initial_logging_config(apps, schema_editor):
    """删除所有日志配置数据"""
    LoggingConfiguration = apps.get_model('qrmanager', 'LoggingConfiguration')
    LoggingConfiguration.objects.all().delete()


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0036_alter_staff_title'),
    ]

    operations = [
        migrations.CreateModel(
            name='LoggingConfiguration',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('action_type', models.CharField(choices=[('create', '创建操作'), ('update', '更新操作'), ('delete', '删除操作'), ('login', '登录操作'), ('logout', '退出操作'), ('view', '查看操作'), ('export', '导出操作'), ('import', '导入操作'), ('print', '打印操作'), ('generate', '生成操作'), ('permission_change', '权限变更'), ('system_config', '系统配置'), ('api_request', 'API请求'), ('file_upload', '文件上传'), ('file_download', '文件下载'), ('bulk_operation', '批量操作'), ('error', '错误操作')], max_length=50, unique=True, verbose_name='操作类型')),
                ('is_enabled', models.BooleanField(default=True, verbose_name='是否记录')),
                ('description', models.TextField(blank=True, verbose_name='说明')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '日志配置',
                'verbose_name_plural': '日志配置',
                'ordering': ['action_type'],
            },
        ),
        # 添加数据迁移
        migrations.RunPython(create_initial_logging_config, reverse_initial_logging_config),
    ]
