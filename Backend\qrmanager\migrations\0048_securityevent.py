# Generated by Django 5.1.7 on 2025-06-03 04:43

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0047_migrate_flagged_evaluations'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='SecurityEvent',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('event_type', models.CharField(choices=[('qrcode_rate_limit', '二维码速率限制'), ('ip_rate_limit', 'IP速率限制'), ('suspicious_access', '可疑访问'), ('rapid_qrcode_switching', '快速切换二维码'), ('anomaly_detected', '异常行为检测'), ('malicious_user_agent', '恶意用户代理'), ('invalid_qr_param', '无效二维码参数'), ('ip_banned', 'IP被封禁'), ('ip_unbanned', 'IP被解封')], max_length=50, verbose_name='事件类型')),
                ('severity', models.CharField(choices=[('low', '低'), ('medium', '中'), ('high', '高'), ('critical', '严重')], default='medium', max_length=20, verbose_name='严重程度')),
                ('source_ip', models.GenericIPAddressField(verbose_name='源IP地址')),
                ('target_uuid', models.CharField(blank=True, max_length=100, verbose_name='目标二维码UUID')),
                ('user_agent', models.TextField(blank=True, verbose_name='用户代理')),
                ('fingerprint', models.CharField(blank=True, max_length=50, verbose_name='浏览器指纹')),
                ('details', models.JSONField(default=dict, verbose_name='事件详情')),
                ('timestamp', models.DateTimeField(auto_now_add=True, verbose_name='发生时间')),
                ('resolved', models.BooleanField(default=False, verbose_name='已处理')),
                ('resolved_at', models.DateTimeField(blank=True, null=True, verbose_name='处理时间')),
                ('notes', models.TextField(blank=True, verbose_name='处理备注')),
                ('resolved_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='处理人')),
            ],
            options={
                'verbose_name': '安全事件',
                'verbose_name_plural': '安全事件',
                'ordering': ['-timestamp'],
                'indexes': [models.Index(fields=['event_type', 'timestamp'], name='qrmanager_s_event_t_0c8a24_idx'), models.Index(fields=['source_ip', 'timestamp'], name='qrmanager_s_source__a5067a_idx'), models.Index(fields=['severity', 'resolved'], name='qrmanager_s_severit_032ce6_idx')],
            },
        ),
    ]
