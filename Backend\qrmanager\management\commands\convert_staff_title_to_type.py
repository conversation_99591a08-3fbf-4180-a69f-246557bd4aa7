from django.core.management.base import BaseCommand
from django.db import transaction
from django.db.models import F
from qrmanager.models import Evaluation, Staff, StaffType

class Command(BaseCommand):
    help = '将评价表中的工作人员职称(title)转换为工作人员类型(type)'

    def add_arguments(self, parser):
        parser.add_argument('--dry-run', action='store_true', help='只显示将要进行的更改，不实际执行')

    def handle(self, *args, **options):
        dry_run = options.get('dry_run', False)
        
        if dry_run:
            self.stdout.write(self.style.WARNING('执行演示模式 (dry run) - 不会实际修改数据库'))
        
        # 1. 创建临时字段存储工作人员类型
        self.stdout.write('开始转换工作人员职称为类型...')
        
        # 2. 获取所有工作人员类型
        staff_types = {st.name: st for st in StaffType.objects.all()}
        self.stdout.write(f'系统中共有 {len(staff_types)} 种工作人员类型')
        self.stdout.write('可用的工作人员类型:')
        for type_name in staff_types.keys():
            self.stdout.write(f'  - {type_name}')
        
        # 3. 创建职称到工作人员类型的映射
        # 这里使用一个简单的映射规则，实际应用中可能需要更复杂的逻辑
        title_to_type_map = {
            '主任医师': '医生',
            '副主任医师': '医生',
            '主治医师': '医生',
            '住院医师': '医生',
            '医生': '医生',
            '医师': '医生',
            '主管护师': '护士',
            '护师': '护士',
            '护士': '护士',
            '医技': '技师',
            '技师': '技师',
            '主任技师': '技师',
            '副主任技师': '技师',
            '行政': '行政人员',
            '管理': '行政人员'
        }
        
        self.stdout.write(f'职称映射规则:')
        for title, staff_type in title_to_type_map.items():
            self.stdout.write(f'  - {title} -> {staff_type}')
        
        # 4. 处理没有类型映射的默认规则
        default_type = '其他人员'
        if default_type not in staff_types:
            self.stdout.write(self.style.WARNING(f'未找到默认类型 "{default_type}"，将使用第一个可用的类型'))
            default_type = next(iter(staff_types.keys())) if staff_types else None
            if default_type:
                self.stdout.write(self.style.WARNING(f'使用 "{default_type}" 作为默认类型'))
            else:
                self.stdout.write(self.style.ERROR('没有找到任何工作人员类型，无法继续!'))
                return
        
        # 5. 开始处理评价数据
        evaluations = Evaluation.objects.all()
        total_count = evaluations.count()
        self.stdout.write(f'开始处理 {total_count} 条评价记录')
        
        # 统计职称分布
        title_counts = {}
        for eval in evaluations:
            for i in range(1, 4):
                for prefix in ['satisfied_staff', 'unsatisfied_staff']:
                    title_field = f'{prefix}{i}_title'
                    title_value = getattr(eval, title_field, '')
                    if title_value:
                        if title_value not in title_counts:
                            title_counts[title_value] = 0
                        title_counts[title_value] += 1
        
        self.stdout.write('现有职称分布统计:')
        for title, count in sorted(title_counts.items(), key=lambda x: x[1], reverse=True):
            if count > 0:
                self.stdout.write(f'  - {title}: {count}次')
        
        # 创建临时的类型字段
        processed_count = 0
        type_stats = {}
        
        with transaction.atomic():
            for eval in evaluations:
                updated = False
                
                # 处理满意工作人员
                for i in range(1, 4):
                    title_field = f'satisfied_staff{i}_title'
                    title_value = getattr(eval, title_field, '')
                    
                    if title_value:
                        # 尝试查找对应的类型
                        type_value = None
                        
                        # 首先尝试直接匹配
                        if title_value in title_to_type_map:
                            type_value = title_to_type_map[title_value]
                        else:
                            # 尝试部分匹配
                            for title_key, type_name in title_to_type_map.items():
                                if title_key in title_value or title_value in title_key:
                                    type_value = type_name
                                    break
                        
                        # 如果找不到映射，使用默认类型
                        if not type_value:
                            type_value = default_type
                        
                        # 统计类型分布
                        if type_value not in type_stats:
                            type_stats[type_value] = 0
                        type_stats[type_value] += 1
                            
                        # 设置类型值
                        if not dry_run:
                            setattr(eval, f'satisfied_staff{i}_type', type_value)
                            updated = True
                
                # 处理不满意工作人员
                for i in range(1, 4):
                    title_field = f'unsatisfied_staff{i}_title'
                    title_value = getattr(eval, title_field, '')
                    
                    if title_value:
                        # 尝试查找对应的类型
                        type_value = None
                        
                        # 首先尝试直接匹配
                        if title_value in title_to_type_map:
                            type_value = title_to_type_map[title_value]
                        else:
                            # 尝试部分匹配
                            for title_key, type_name in title_to_type_map.items():
                                if title_key in title_value or title_value in title_key:
                                    type_value = type_name
                                    break
                        
                        # 如果找不到映射，使用默认类型
                        if not type_value:
                            type_value = default_type
                        
                        # 统计类型分布
                        if type_value not in type_stats:
                            type_stats[type_value] = 0
                        type_stats[type_value] += 1
                            
                        # 设置类型值
                        if not dry_run:
                            setattr(eval, f'unsatisfied_staff{i}_type', type_value)
                            updated = True
                
                if updated and not dry_run:
                    eval.save()
                    processed_count += 1
                    
                    if processed_count % 100 == 0:
                        self.stdout.write(f'已处理 {processed_count}/{total_count} 条记录...')
        
        self.stdout.write('转换后的类型分布统计:')
        for type_name, count in sorted(type_stats.items(), key=lambda x: x[1], reverse=True):
            self.stdout.write(f'  - {type_name}: {count}次')
        
        if dry_run:
            self.stdout.write(self.style.SUCCESS('演示完成。若要实际执行转换，请移除 --dry-run 参数'))
        else:
            self.stdout.write(self.style.SUCCESS(f'成功处理 {processed_count} 条评价记录的工作人员类型'))
            self.stdout.write(self.style.WARNING('转换完成! 现在可以在前端使用工作人员类型字段了')) 