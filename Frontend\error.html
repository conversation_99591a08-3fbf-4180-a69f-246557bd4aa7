<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>提交失败</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .error-container {
            text-align: center;
            padding: 50px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .error-icon {
            font-size: 80px;
            color: #dc3545;
            margin-bottom: 30px;
        }
        .error-title {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        .error-message {
            font-size: 18px;
            margin-bottom: 30px;
            color: #666;
            line-height: 1.6;
        }
        .error-details {
            background-color: #f8f9fa;
            border-left: 4px solid #dc3545;
            padding: 15px;
            margin-bottom: 25px;
            text-align: left;
            font-family: monospace;
            font-size: 14px;
            color: #333;
            word-break: break-word;
        }
        .retry-button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
            margin-right: 10px;
        }
        .home-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .retry-button:hover {
            background-color: #218838;
            color: white;
            text-decoration: none;
        }
        .home-button:hover {
            background-color: #0069d9;
            color: white;
            text-decoration: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="error-container">
            <div class="error-icon">✗</div>
            <h1 class="error-title">提交失败</h1>
            <p class="error-message">
                很抱歉，您的评价提交失败。这可能是由于网络问题或服务器暂时不可用。
            </p>
            <div class="error-details" id="errorDetails">
                错误详情将显示在这里
            </div>
            <a href="javascript:history.back()" class="retry-button">返回重试</a>
            <a href="index.html" class="home-button">返回首页</a>
        </div>
    </div>

    <script src="js/jquery-3.5.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        // 从URL获取错误信息
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const urlParams = new URLSearchParams(window.location.search);
                const errorMessage = urlParams.get('error') || '未知错误';
                
                // 解码错误信息并显示
                const decodedError = decodeURIComponent(errorMessage);
                document.getElementById('errorDetails').textContent = decodedError;
            } catch (error) {
                console.error('解析错误信息失败:', error);
                document.getElementById('errorDetails').textContent = '获取错误详情失败';
            }
        });
    </script>
</body>
</html>