<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#ffffff">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>会话超时 - 医院服务评价系统</title>
    <link rel="stylesheet" href="/styles.css">
    <!-- 引入苹果风格设计 -->
    <link rel="stylesheet" href="/css/apple-styles.css">
    <link rel="stylesheet" href="/css/apple-svg-icons.css">
    <meta http-equiv="Content-Security-Policy" content="default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline'; img-src 'self' data:; font-src 'self' data:; connect-src 'self';">
    <style>
        .timeout-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
            text-align: center;
            background-color: var(--apple-light-gray, #f5f7fa);
            -webkit-text-size-adjust: 100%;
            touch-action: manipulation;
        }
        
        .timeout-card {
            background-color: white;
            border-radius: var(--apple-border-radius, 12px);
            padding: 30px 20px;
            width: 90%;
            max-width: 400px;
            box-shadow: var(--apple-shadow, 0 4px 20px rgba(0, 0, 0, 0.1));
            animation: fadeIn 0.5s ease-out;
        }
        
        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }
        
        .timeout-icon {
            font-size: 64px;
            color: var(--apple-blue, #007AFF);
            margin-bottom: 24px;
            animation: pulseIcon 1.5s infinite alternate ease-in-out;
        }
        
        @keyframes pulseIcon {
            from { transform: scale(1); opacity: 0.8; }
            to { transform: scale(1.05); opacity: 1; }
        }
        
        .timeout-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 12px;
            color: var(--apple-dark-gray, #333);
        }
        
        .timeout-message {
            font-size: 16px;
            color: var(--apple-gray, #666);
            margin-bottom: 30px;
            max-width: 500px;
            line-height: 1.5;
        }
        
        .timeout-button {
            background-color: var(--apple-blue, #007AFF);
            color: white;
            border: none;
            border-radius: 12px;
            padding: 16px 32px;
            font-size: 17px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.2s ease;
            -webkit-tap-highlight-color: transparent;
            width: 100%;
            max-width: 300px;
        }
        
        .timeout-button:active {
            transform: scale(0.98);
            background-color: #0062CC;
        }
        
        /* 安全区域适配 */
        @supports (padding: max(0px)) {
            .timeout-container {
                padding-left: max(20px, env(safe-area-inset-left));
                padding-right: max(20px, env(safe-area-inset-right));
                padding-bottom: max(20px, env(safe-area-inset-bottom));
            }
        }
        
        /* 移动端适配 */
        @media (max-width: 480px) {
            .timeout-button {
                padding: 18px 28px;
                font-size: 18px;
            }
            
            .timeout-title {
                font-size: 22px;
            }
            
            .timeout-icon {
                font-size: 58px;
            }
        }
    </style>
</head>
<body>
    <div class="timeout-container">
        <div class="timeout-card">
            <div class="timeout-icon"><i class="ai-time ai-3x"></i></div>
            <h1 class="timeout-title">会话已超时</h1>
            <p class="timeout-message">由于长时间未操作，您的会话已超时。这是为了保护您的信息安全。</p>
            <button class="timeout-button" onclick="location.href='/'">返回首页</button>
        </div>
    </div>
    
    <script>
        // 添加触摸反馈
        const button = document.querySelector('.timeout-button');
        button.addEventListener('touchstart', function() {
            this.style.opacity = '0.9';
        });
        button.addEventListener('touchend', function() {
            this.style.opacity = '1';
        });
    </script>
</body>
</html> 