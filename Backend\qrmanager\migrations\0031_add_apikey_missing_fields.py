# Generated by Django 4.2.7 on 2025-03-13 05:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0030_alter_operationlog_browser_alter_operationlog_os'),
    ]

    operations = [
        migrations.CreateModel(
            name='SystemConfig',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('key', models.CharField(max_length=50, unique=True, verbose_name='配置键')),
                ('value', models.TextField(verbose_name='配置值')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('is_public', models.BooleanField(default=False, help_text='公开配置可在前端显示', verbose_name='是否公开')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '系统配置',
                'verbose_name_plural': '系统配置',
                'ordering': ['key'],
            },
        ),
        migrations.AddField(
            model_name='apikey',
            name='allowed_ips',
            field=models.TextField(blank=True, help_text='多个IP地址用逗号分隔', null=True, verbose_name='允许的IP地址'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_access_beds',
            field=models.BooleanField(default=False, verbose_name='访问床位'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_access_departments',
            field=models.BooleanField(default=False, verbose_name='访问科室'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_access_evaluations',
            field=models.BooleanField(default=False, verbose_name='访问评价'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_access_qrcodes',
            field=models.BooleanField(default=False, verbose_name='访问二维码'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_access_staff',
            field=models.BooleanField(default=False, verbose_name='访问员工'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_delete',
            field=models.BooleanField(default=False, verbose_name='删除权限'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_read',
            field=models.BooleanField(default=True, verbose_name='读取权限'),
        ),
        migrations.AddField(
            model_name='apikey',
            name='can_write',
            field=models.BooleanField(default=False, verbose_name='写入权限'),
        ),
    ]
