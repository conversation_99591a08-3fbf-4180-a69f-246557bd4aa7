{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}修改密码{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card fade-in">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">
                        <i class="fas fa-key me-2"></i>修改密码
                    </h2>
                    <form method="post">
                        {% csrf_token %}
                        {% if form.errors %}
                        <div class="alert alert-danger">
                            请修正以下错误：
                            {{ form.errors }}
                        </div>
                        {% endif %}
                        <div class="mb-3">
                            <label for="id_old_password" class="form-label">当前密码</label>
                            <div class="password-container position-relative">
                                <input type="password" name="old_password" class="form-control" id="id_old_password" required>
                                <span class="password-toggle-icon position-absolute" id="toggleOldPassword">
                                    <i class="bi bi-eye-fill"></i>
                                </span>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="id_new_password1" class="form-label">新密码</label>
                            <div class="password-container position-relative">
                                <input type="password" name="new_password1" class="form-control" id="id_new_password1" required>
                                <span class="password-toggle-icon position-absolute" id="toggleNewPassword1">
                                    <i class="bi bi-eye-fill"></i>
                                </span>
                            </div>
                            <div class="form-text">
                                <ul class="mb-0">
                                    <li>密码不能与个人信息太相似</li>
                                    <li>密码至少包含8个字符</li>
                                    <li>密码不能是常见的密码</li>
                                    <li>密码不能全为数字</li>
                                </ul>
                            </div>
                        </div>
                        <div class="mb-3">
                            <label for="id_new_password2" class="form-label">确认新密码</label>
                            <div class="password-container position-relative">
                                <input type="password" name="new_password2" class="form-control" id="id_new_password2" required>
                                <span class="password-toggle-icon position-absolute" id="toggleNewPassword2">
                                    <i class="bi bi-eye-fill"></i>
                                </span>
                            </div>
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>修改密码
                            </button>
                            <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .password-container {
        position: relative;
    }

    .password-toggle-icon {
        position: absolute;
        right: 10px;
        top: 50%;
        transform: translateY(-50%);
        cursor: pointer;
        color: #4f46e5; /* 使用二维码管理的蓝紫色 */
        opacity: 0;
        transition: all 0.3s ease;
        font-size: 1.2rem;
    }

    .password-container:focus-within .password-toggle-icon {
        opacity: 1;
    }

    .password-toggle-icon:hover {
        color: #6366f1; /* 悬停时使用管理后台的靛蓝色 */
        transform: translateY(-50%) scale(1.1);
    }
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 为每个密码输入框添加切换功能
        setupPasswordToggle('toggleOldPassword', 'id_old_password');
        setupPasswordToggle('toggleNewPassword1', 'id_new_password1');
        setupPasswordToggle('toggleNewPassword2', 'id_new_password2');

        // 设置密码切换功能
        function setupPasswordToggle(toggleId, inputId) {
            const toggleButton = document.getElementById(toggleId);
            const passwordInput = document.getElementById(inputId);

            if (toggleButton && passwordInput) {
                toggleButton.addEventListener('click', function() {
                    // 切换密码可见性
                    const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
                    passwordInput.setAttribute('type', type);

                    // 切换图标
                    const icon = this.querySelector('i');
                    if (type === 'password') {
                        icon.classList.remove('bi-eye-slash-fill');
                        icon.classList.add('bi-eye-fill');
                    } else {
                        icon.classList.remove('bi-eye-fill');
                        icon.classList.add('bi-eye-slash-fill');
                    }
                });
            }
        }
    });
</script>
{% endblock %}