#!/usr/bin/env python
# 修复views.py中缺少TruncDate导入的问题

with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    content = f.readlines()

# 查找django.db.models导入行
for i, line in enumerate(content):
    if line.startswith('from django.db.models import'):
        # 已经添加了Prefetch，现在需要添加functions.TruncDate
        break

# 添加TruncDate导入
content.insert(i+1, 'from django.db.models.functions import TruncDate\n')

# 写回文件
with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
    f.writelines(content)

print("已成功添加TruncDate导入！") 