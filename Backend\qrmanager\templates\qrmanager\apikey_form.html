{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:apikey_list' %}">API密钥管理</a></li>
        <li class="breadcrumb-item active">{{ title }}</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-key me-1"></i>
            {% if is_create %}创建{% else %}编辑{% endif %}API密钥
        </div>
        <div class="card-body">
            <form method="post">
                {% csrf_token %}
                
                <div class="row mb-3">
                    <div class="col-md-6">
                        <div class="form-group mb-3">
                            <label for="id_name" class="form-label">名称 <span class="text-danger">*</span></label>
                            <input type="text" name="name" id="id_name" class="form-control" value="{{ form.name.value|default:'' }}" required>
                            <div class="form-text">为API密钥提供一个描述性名称，例如"移动应用API"</div>
                            {% if form.name.errors %}
                                <div class="invalid-feedback d-block">{{ form.name.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="id_expires_at" class="form-label">过期时间</label>
                            <input type="datetime-local" name="expires_at" id="id_expires_at" class="form-control" value="{{ form.expires_at.value|date:'Y-m-d\TH:i'|default:'' }}">
                            <div class="form-text">留空表示永不过期</div>
                            {% if form.expires_at.errors %}
                                <div class="invalid-feedback d-block">{{ form.expires_at.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="form-group mb-3">
                            <label for="id_allowed_ips" class="form-label">允许的IP地址</label>
                            <input type="text" name="allowed_ips" id="id_allowed_ips" class="form-control" value="{{ form.allowed_ips.value|default:'' }}">
                            <div class="form-text">多个IP地址用逗号分隔，留空表示允许所有IP</div>
                            {% if form.allowed_ips.errors %}
                                <div class="invalid-feedback d-block">{{ form.allowed_ips.errors }}</div>
                            {% endif %}
                        </div>
                        
                        <div class="card mb-3">
                            <div class="card-header bg-success text-white">
                                <i class="fas fa-tachometer-alt me-1"></i> 速率限制
                            </div>
                            <div class="card-body">
                                <div class="form-group mb-2">
                                    <label for="id_rate_limit_day" class="form-label">每日请求限制</label>
                                    <input type="number" name="rate_limit_day" id="id_rate_limit_day" class="form-control" value="{{ form.rate_limit_day.value|default:'1000' }}" min="0">
                                    <div class="form-text">每天允许的最大请求次数，0表示无限制</div>
                                    {% if form.rate_limit_day.errors %}
                                        <div class="invalid-feedback d-block">{{ form.rate_limit_day.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-2">
                                    <label for="id_rate_limit_hour" class="form-label">每小时请求限制</label>
                                    <input type="number" name="rate_limit_hour" id="id_rate_limit_hour" class="form-control" value="{{ form.rate_limit_hour.value|default:'100' }}" min="0">
                                    <div class="form-text">每小时允许的最大请求次数，0表示无限制</div>
                                    {% if form.rate_limit_hour.errors %}
                                        <div class="invalid-feedback d-block">{{ form.rate_limit_hour.errors }}</div>
                                    {% endif %}
                                </div>
                                
                                <div class="form-group mb-2">
                                    <label for="id_rate_limit_minute" class="form-label">每分钟请求限制</label>
                                    <input type="number" name="rate_limit_minute" id="id_rate_limit_minute" class="form-control" value="{{ form.rate_limit_minute.value|default:'10' }}" min="0">
                                    <div class="form-text">每分钟允许的最大请求次数，0表示无限制</div>
                                    {% if form.rate_limit_minute.errors %}
                                        <div class="invalid-feedback d-block">{{ form.rate_limit_minute.errors }}</div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <div class="form-check form-switch mb-3">
                            <input class="form-check-input" type="checkbox" name="is_active" id="id_is_active" {% if form.is_active.value %}checked{% endif %}>
                            <label class="form-check-label" for="id_is_active">是否激活</label>
                            {% if form.is_active.errors %}
                                <div class="invalid-feedback d-block">{{ form.is_active.errors }}</div>
                            {% endif %}
                        </div>
                    </div>
                    
                    <div class="col-md-6">
                        <div class="card mb-3">
                            <div class="card-header bg-primary text-white">
                                <i class="fas fa-lock me-1"></i> 权限控制
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_read" id="id_can_read" {% if form.can_read.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_read">读取权限</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_write" id="id_can_write" {% if form.can_write.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_write">写入权限</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_delete" id="id_can_delete" {% if form.can_delete.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_delete">删除权限</label>
                                </div>
                            </div>
                        </div>
                        
                        <div class="card">
                            <div class="card-header bg-info text-white">
                                <i class="fas fa-database me-1"></i> 资源访问控制
                            </div>
                            <div class="card-body">
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_access_departments" id="id_can_access_departments" {% if form.can_access_departments.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_access_departments">访问科室</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_access_beds" id="id_can_access_beds" {% if form.can_access_beds.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_access_beds">访问床位</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_access_staff" id="id_can_access_staff" {% if form.can_access_staff.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_access_staff">访问员工</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_access_qrcodes" id="id_can_access_qrcodes" {% if form.can_access_qrcodes.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_access_qrcodes">访问二维码</label>
                                </div>
                                <div class="form-check form-switch mb-2">
                                    <input class="form-check-input" type="checkbox" name="can_access_evaluations" id="id_can_access_evaluations" {% if form.can_access_evaluations.value %}checked{% endif %}>
                                    <label class="form-check-label" for="id_can_access_evaluations">访问评价</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-1"></i> 
                    请注意：API密钥一旦创建，密钥值将只显示一次。请确保安全保存密钥，不要泄露给未授权的人员。
                </div>
                
                <div class="mt-3">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-save me-1"></i> 保存
                    </button>
                    <a href="{% url 'qrmanager:apikey_list' %}" class="btn btn-secondary">
                        <i class="fas fa-times me-1"></i> 取消
                    </a>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %} 