from django.core.management.base import BaseCommand
from qrmanager.models import APILog
from django.utils import timezone
import datetime
import json

class Command(BaseCommand):
    help = '检查API错误日志'

    def handle(self, *args, **options):
        self.stdout.write('检查最近的API错误日志...')
        
        # 获取最近30分钟的日志
        time_threshold = timezone.now() - datetime.timedelta(minutes=30)
        
        # 查询API错误日志
        error_logs = APILog.objects.filter(
            created_at__gte=time_threshold,
            status_code__gte=400  # 错误状态码
        ).order_by('-created_at')
        
        if not error_logs.exists():
            self.stdout.write('未找到最近30分钟的API错误日志')
            return
            
        self.stdout.write(f'找到{error_logs.count()}条API错误日志')
        
        for log in error_logs:
            self.stdout.write(self.style.ERROR(f'===== 错误日志 ID: {log.id} ====='))
            self.stdout.write(f'时间: {log.created_at}')
            self.stdout.write(f'端点: {log.endpoint}')
            self.stdout.write(f'方法: {log.method}')
            self.stdout.write(f'状态码: {log.status_code}')
            self.stdout.write(f'状态: {log.status}')
            self.stdout.write(f'IP地址: {log.remote_addr}')
            self.stdout.write(f'用户代理: {log.user_agent}')
            
            if log.error_message:
                self.stdout.write(f'错误信息: {log.error_message}')
            
            # 尝试解析并格式化请求数据
            if log.request_data:
                self.stdout.write('请求数据:')
                try:
                    req_data = json.loads(log.request_data)
                    self.stdout.write(json.dumps(req_data, indent=2, ensure_ascii=False))
                except:
                    self.stdout.write(f'  {log.request_data}')
            
            self.stdout.write('')  # 空行分隔
        
        # 检查评价提交API的成功日志
        self.stdout.write('\n检查最近的评价提交成功日志...')
        success_logs = APILog.objects.filter(
            created_at__gte=time_threshold,
            endpoint__contains='submit-evaluation',
            status_code__lt=400
        ).order_by('-created_at')
        
        if success_logs.exists():
            self.stdout.write(f'找到{success_logs.count()}条评价提交成功日志')
            
            for log in success_logs[:3]:  # 只显示最近3条
                self.stdout.write(self.style.SUCCESS(f'===== 成功日志 ID: {log.id} ====='))
                self.stdout.write(f'时间: {log.created_at}')
                self.stdout.write(f'端点: {log.endpoint}')
                self.stdout.write(f'状态码: {log.status_code}')
                
                # 尝试解析并格式化请求数据
                if log.request_data:
                    self.stdout.write('请求数据:')
                    try:
                        req_data = json.loads(log.request_data)
                        # 不显示敏感数据
                        if 'qr_param' in req_data:
                            req_data['qr_param'] = req_data['qr_param'][:20] + '...'
                        self.stdout.write(json.dumps(req_data, indent=2, ensure_ascii=False))
                    except:
                        self.stdout.write(f'  {log.request_data}')
                
                self.stdout.write('')  # 空行分隔
        else:
            self.stdout.write('未找到最近30分钟的评价提交成功日志') 