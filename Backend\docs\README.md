# 医院服务评价系统文档中心

## 项目概述
医院服务评价系统是一个基于Django的Web应用，用于收集和管理医院服务的评价信息。系统通过二维码扫描方式收集患者对医疗服务的评价，并提供数据分析和管理功能。

### 主要功能
- 科室管理：添加、编辑、删除科室信息
- 工作人员管理：管理医院工作人员信息
- 床位管理：按科室管理床位，添加、编辑、删除床位
- 二维码管理：自动生成二维码，按科室批量打印
- 评价收集和分析：收集患者评价并进行数据分析
- 情感分析：分析评价文本的情感倾向
- 数据导出：导出各类数据报表
- 操作日志记录：记录系统操作日志
- 设备指纹防刷机制：防止恶意刷评价

### 技术栈
- 后端：Python 3.8+, Django 4.2.7
- 数据库：SQLite3
- 前端：HTML5, CSS3, JavaScript
- 工具：qrcode, Pillow, coverage

## 文档导航

### 1. [开发文档](development_guidelines.md)
- 代码规范
- 开发流程
- Git提交规范
- 自动化流程
- 代码审查清单

### 2. [数据库文档](database_structure.md)
- 数据库结构
- 表关系
- 字段说明
- 索引设计
- 数据迁移说明

### 3. [API文档](api.md)
- API接口列表
- 认证说明
- 请求/响应格式
- 错误码说明
- 使用示例

### 4. [用户手册](user_manual.md)
- 系统功能说明
- 操作指南
- 常见问题
- 注意事项
- 联系支持

### 5. [部署文档](deployment.md)
- 系统要求
- 部署步骤
- 环境配置
- 维护指南
- 故障排除

### 6. [测试文档](testing.md)
- 测试环境
- 测试类型
- 测试用例
- 测试数据
- 持续集成

### 7. [更新检查清单](update_checklist.md)
- 数据库变更检查
- 代码文档检查
- 迁移管理检查
- 文档更新检查

## 系统架构

### 前端架构
```
前端/
├── pages/          # 页面文件
├── assets/         # 静态资源
│   ├── css/       # 样式文件
│   ├── js/        # JavaScript文件
│   └── images/    # 图片资源
└── index.html     # 入口文件
```

### 后端架构
```
后端/
├── HospitalQRCode/    # 项目配置
├── qrmanager/         # 主应用
│   ├── models.py      # 数据模型
│   ├── views.py       # 视图逻辑
│   ├── urls.py        # URL配置
│   ├── forms.py       # 表单定义
│   ├── utils.py       # 工具函数
│   ├── middleware.py  # 中间件
│   ├── security.py    # 安全功能
│   ├── qrcode_utils.py # 二维码工具
│   └── templates/     # HTML模板
├── static/            # 静态文件
├── media/             # 上传文件
├── logs/              # 日志文件
├── docs/              # 文档
└── requirements.txt   # 项目依赖
```

## 安全特性
1. 设备指纹防刷机制
   - 收集设备信息
   - 访问频率控制
   - 设备验证

2. 数据安全
   - CSRF保护
   - XSS防护
   - SQL注入防护
   - 文件上传验证

3. 访问控制
   - 基于角色的权限控制
   - 登录认证
   - 操作日志记录

## 性能优化
1. 数据库优化
   - 合理的索引设计
   - 查询优化
   - 关联预加载

2. 前端优化
   - 静态资源压缩
   - 懒加载
   - 缓存策略

3. 后端优化
   - 数据缓存
   - 异步处理
   - 批量操作

## 最新更新

### 二维码管理页面优化 (v1.3.0)
1. 移除了不必要的"打印二维码"按钮
2. 移除了"批量打印"按钮和相关模态框
3. 保留了"按科室批量打印"功能，更符合实际使用场景
4. 简化了用户界面，提高了系统的易用性

这些修改使系统更加简洁和易用，同时保留了核心功能。系统现在更加专注于按科室管理和打印二维码，这符合医院实际工作流程。

## 项目规划

### 当前版本
- 版本号：v1.3.0
- 发布日期：2025-03-15
- 主要功能：二维码管理页面优化

### 后续计划
1. v1.4.0
   - 添加更多数据分析报表
   - 优化用户界面
   - 添加更多统计功能

2. v1.5.0
   - 添加移动端APP
   - 支持多语言
   - 添加更多自定义选项

## 维护者
[维护者信息]

## 许可证
[许可证信息]

## 更新记录
- 2025-03-15: v1.3.0 - 二维码管理页面优化
- 2025-03-07: v1.2.0 - API管理界面优化
- 2024-02-20: v1.0.0 - 初始版本 