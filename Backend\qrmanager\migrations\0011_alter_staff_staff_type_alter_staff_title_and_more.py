# Generated by Django 4.2.7 on 2025-02-07 04:27

from django.db import migrations, models
import django.db.models.deletion
import qrmanager.models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0010_update_staff_fields'),
    ]

    operations = [
        migrations.AlterField(
            model_name='staff',
            name='staff_type',
            field=models.ForeignKey(limit_choices_to={'dictionary__code': 'staff_type'}, on_delete=django.db.models.deletion.PROTECT, related_name='staff_types', to='qrmanager.dictionaryitem', verbose_name='人员类型'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='title',
            field=models.ForeignKey(limit_choices_to={'dictionary__code': 'staff_title'}, on_delete=django.db.models.deletion.PROTECT, related_name='staff_titles', to='qrmanager.dictionaryitem', verbose_name='职称'),
        ),
        migrations.Alter<PERSON>ield(
            model_name='staff',
            name='work_number',
            field=models.Char<PERSON>ield(default=qrmanager.models.generate_work_number, max_length=20, unique=True, verbose_name='工号'),
        ),
    ]
