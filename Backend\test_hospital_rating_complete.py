#!/usr/bin/env python3
"""
医院评分功能完整测试
测试前端提交、后端处理、数据库存储、管理界面显示的完整流程
"""

import os
import sys
import django
import json
import requests
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.models import Evaluation, QRCode, Bed, Department
from qrmanager.security import encrypt_qr_param
import uuid

def test_database_field():
    """测试数据库字段是否正确添加"""
    print("🔍 测试数据库字段")
    print("=" * 60)
    
    try:
        # 检查Evaluation模型是否有hospital_rating字段
        from qrmanager.models import Evaluation
        field_names = [field.name for field in Evaluation._meta.fields]
        
        if 'hospital_rating' in field_names:
            print("✅ hospital_rating字段已成功添加到Evaluation模型")
            
            # 检查字段属性
            hospital_rating_field = Evaluation._meta.get_field('hospital_rating')
            print(f"  字段类型: {type(hospital_rating_field).__name__}")
            print(f"  允许空值: {hospital_rating_field.null}")
            print(f"  允许空白: {hospital_rating_field.blank}")
            print(f"  选择项: {hospital_rating_field.choices}")
            print(f"  帮助文本: {hospital_rating_field.help_text}")
        else:
            print("❌ hospital_rating字段未找到")
            return False
            
    except Exception as e:
        print(f"❌ 数据库字段测试失败: {e}")
        return False
    
    return True

def test_api_endpoint():
    """测试API接口是否正确处理hospital_rating"""
    print(f"\n🔍 测试API接口")
    print("=" * 60)
    
    try:
        # 获取一个测试用的二维码
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试用的二维码")
            return False
        
        # 生成加密参数
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 构建测试数据
        test_data = {
            "qr_param": encrypted_param,
            "comment": "测试医院评分功能",
            "hospital_rating": 4,  # 4星评分
            "hospital_number": "TEST001",
            "phone_number": "13800138000",
            "staff_evaluations": []
        }
        
        print(f"测试数据:")
        print(f"  二维码: {qr_code.name}")
        print(f"  加密参数: {encrypted_param}")
        print(f"  医院评分: {test_data['hospital_rating']}星")
        
        # 发送POST请求到API
        api_url = "http://localhost:8000/api/evaluation/submit/"
        
        try:
            response = requests.post(api_url, json=test_data, timeout=10)
            
            if response.status_code == 200:
                result = response.json()
                print(f"✅ API请求成功")
                print(f"  响应状态: {result.get('status')}")
                print(f"  响应消息: {result.get('message')}")
                
                # 检查返回的数据中是否包含hospital_rating
                data = result.get('data', {})
                if 'hospital_rating' in data:
                    print(f"  返回的医院评分: {data['hospital_rating']}")
                    return data.get('evaluation_id')
                else:
                    print("⚠️  API响应中未包含hospital_rating字段")
                    return data.get('evaluation_id')
            else:
                print(f"❌ API请求失败: {response.status_code}")
                print(f"  响应内容: {response.text}")
                return False
                
        except requests.exceptions.ConnectionError:
            print("⚠️  无法连接到API服务器，跳过API测试")
            print("  请确保Django开发服务器正在运行")
            return "skip"
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_direct_database_save():
    """直接测试数据库保存"""
    print(f"\n🔍 测试数据库直接保存")
    print("=" * 60)
    
    try:
        # 获取测试用的床位
        bed = Bed.objects.first()
        if not bed:
            print("❌ 没有找到测试用的床位")
            return False
        
        # 创建测试评价
        evaluation = Evaluation.objects.create(
            bed=bed,
            is_satisfied=True,
            comment="直接数据库测试 - 医院评分功能",
            hospital_rating=5,  # 5星评分
            hospital_number="DB_TEST001",
            phone_number="13900139000"
        )
        
        print(f"✅ 评价记录创建成功")
        print(f"  评价ID: {evaluation.id}")
        print(f"  医院评分: {evaluation.hospital_rating}星")
        print(f"  床位: {evaluation.bed.number}")
        print(f"  科室: {evaluation.bed.department.name}")
        
        # 验证保存的数据
        saved_evaluation = Evaluation.objects.get(id=evaluation.id)
        if saved_evaluation.hospital_rating == 5:
            print(f"✅ 医院评分正确保存到数据库")
        else:
            print(f"❌ 医院评分保存错误: 期望5，实际{saved_evaluation.hospital_rating}")
        
        return evaluation.id
        
    except Exception as e:
        print(f"❌ 数据库保存测试失败: {e}")
        return False

def test_evaluation_display():
    """测试评价显示功能"""
    print(f"\n🔍 测试评价显示功能")
    print("=" * 60)
    
    try:
        # 获取有医院评分的评价记录
        evaluations_with_rating = Evaluation.objects.filter(hospital_rating__isnull=False)
        evaluations_without_rating = Evaluation.objects.filter(hospital_rating__isnull=True)
        
        print(f"数据库中的评价统计:")
        print(f"  有医院评分的评价: {evaluations_with_rating.count()}条")
        print(f"  无医院评分的评价: {evaluations_without_rating.count()}条")
        
        if evaluations_with_rating.exists():
            print(f"\n有评分的评价示例:")
            for eval in evaluations_with_rating[:3]:
                print(f"  评价ID {eval.id}: {eval.hospital_rating}星 - {eval.comment[:30]}...")
        
        # 测试模型的__str__方法
        if evaluations_with_rating.exists():
            eval_with_rating = evaluations_with_rating.first()
            str_representation = str(eval_with_rating)
            print(f"\n模型字符串表示测试:")
            print(f"  {str_representation}")
            
            if f"({eval_with_rating.hospital_rating}星)" in str_representation:
                print(f"✅ 模型字符串表示包含医院评分")
            else:
                print(f"⚠️  模型字符串表示未包含医院评分")
        
        return True
        
    except Exception as e:
        print(f"❌ 评价显示测试失败: {e}")
        return False

def test_rating_validation():
    """测试评分验证功能"""
    print(f"\n🔍 测试评分验证功能")
    print("=" * 60)
    
    try:
        bed = Bed.objects.first()
        if not bed:
            print("❌ 没有找到测试用的床位")
            return False
        
        # 测试有效评分 (1-5)
        valid_ratings = [1, 2, 3, 4, 5]
        for rating in valid_ratings:
            try:
                evaluation = Evaluation.objects.create(
                    bed=bed,
                    is_satisfied=True,
                    comment=f"测试{rating}星评分",
                    hospital_rating=rating
                )
                print(f"✅ {rating}星评分保存成功")
                evaluation.delete()  # 清理测试数据
            except Exception as e:
                print(f"❌ {rating}星评分保存失败: {e}")
        
        # 测试无效评分
        invalid_ratings = [0, 6, -1, 10]
        for rating in invalid_ratings:
            try:
                evaluation = Evaluation.objects.create(
                    bed=bed,
                    is_satisfied=True,
                    comment=f"测试无效{rating}星评分",
                    hospital_rating=rating
                )
                print(f"⚠️  {rating}星评分意外保存成功（应该被拒绝）")
                evaluation.delete()
            except Exception as e:
                print(f"✅ {rating}星评分正确被拒绝: {str(e)[:50]}...")
        
        # 测试空值
        try:
            evaluation = Evaluation.objects.create(
                bed=bed,
                is_satisfied=True,
                comment="测试空评分",
                hospital_rating=None
            )
            print(f"✅ 空评分保存成功（允许空值）")
            evaluation.delete()
        except Exception as e:
            print(f"❌ 空评分保存失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 评分验证测试失败: {e}")
        return False

def test_frontend_integration():
    """测试前端集成"""
    print(f"\n🔍 测试前端集成")
    print("=" * 60)
    
    try:
        # 检查前端文件是否包含hospital_rating
        frontend_files = [
            "Frontend/js/main.js",
            "Frontend/index.html"
        ]
        
        for file_path in frontend_files:
            if os.path.exists(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if 'hospital_rating' in content:
                        print(f"✅ {file_path} 包含hospital_rating字段")
                    else:
                        print(f"⚠️  {file_path} 未包含hospital_rating字段")
            else:
                print(f"⚠️  {file_path} 文件不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 前端集成测试失败: {e}")
        return False

def generate_test_report():
    """生成测试报告"""
    print(f"\n📊 生成测试报告")
    print("=" * 60)
    
    try:
        # 统计评价数据
        total_evaluations = Evaluation.objects.count()
        evaluations_with_rating = Evaluation.objects.filter(hospital_rating__isnull=False).count()
        evaluations_without_rating = Evaluation.objects.filter(hospital_rating__isnull=True).count()
        
        # 评分分布统计
        rating_distribution = {}
        for rating in range(1, 6):
            count = Evaluation.objects.filter(hospital_rating=rating).count()
            rating_distribution[rating] = count
        
        print(f"医院评分功能测试报告:")
        print(f"  总评价数: {total_evaluations}")
        print(f"  有评分评价: {evaluations_with_rating}")
        print(f"  无评分评价: {evaluations_without_rating}")
        print(f"  评分覆盖率: {(evaluations_with_rating/total_evaluations*100):.1f}%" if total_evaluations > 0 else "  评分覆盖率: 0%")
        
        print(f"\n评分分布:")
        for rating, count in rating_distribution.items():
            percentage = (count/evaluations_with_rating*100) if evaluations_with_rating > 0 else 0
            stars = "★" * rating + "☆" * (5-rating)
            print(f"  {stars} {rating}星: {count}条 ({percentage:.1f}%)")
        
        # 平均评分
        if evaluations_with_rating > 0:
            avg_rating = sum(rating * count for rating, count in rating_distribution.items()) / evaluations_with_rating
            print(f"\n平均评分: {avg_rating:.2f}星")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试报告生成失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 医院评分功能完整测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    test_results = []
    
    # 执行各项测试
    tests = [
        ("数据库字段测试", test_database_field),
        ("API接口测试", test_api_endpoint),
        ("数据库保存测试", test_direct_database_save),
        ("评价显示测试", test_evaluation_display),
        ("评分验证测试", test_rating_validation),
        ("前端集成测试", test_frontend_integration),
    ]
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            test_results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name}执行异常: {e}")
            test_results.append((test_name, False))
    
    # 生成测试报告
    generate_test_report()
    
    # 总结测试结果
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        if result is True:
            status = "✅ 通过"
            passed += 1
        elif result == "skip":
            status = "⏭️  跳过"
        elif result:
            status = "✅ 通过"
            passed += 1
        else:
            status = "❌ 失败"
        
        print(f"  {test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！医院评分功能完整可用！")
    elif passed >= total * 0.8:
        print("⚠️  大部分测试通过，功能基本可用，建议检查失败项")
    else:
        print("❌ 多项测试失败，需要修复问题")
    
    print(f"\n💡 使用建议:")
    print("1. 确保Django开发服务器运行以测试API")
    print("2. 访问管理界面查看评分显示效果")
    print("3. 测试前端评分提交功能")
    print("4. 检查评分统计和分析功能")

if __name__ == "__main__":
    main()
