#!/usr/bin/env python3
"""
医院评分情感倾向功能最终演示
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.http import HttpRequest
from django.contrib.auth.models import AnonymousUser
from qrmanager.api import PublicEvaluationSubmitAPI
from qrmanager.models import Evaluation, QRCode
from qrmanager.security import encrypt_qr_param

def create_mock_request(data):
    """创建模拟请求"""
    request = HttpRequest()
    request.method = 'POST'
    request.META['CONTENT_TYPE'] = 'application/json'
    request._body = json.dumps(data).encode('utf-8')
    request.user = AnonymousUser()
    return request

def demo_sentiment_mapping():
    """演示医院评分到情感倾向的映射"""
    print("🎯 医院评分情感倾向映射演示")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试二维码")
            return False
        
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 演示所有评分的映射关系
        rating_mapping = [
            (1, 'negative', '⭐', '非常不满意'),
            (2, 'negative', '⭐⭐', '不满意'),
            (3, 'neutral', '⭐⭐⭐', '一般'),
            (4, 'positive', '⭐⭐⭐⭐', '满意'),
            (5, 'positive', '⭐⭐⭐⭐⭐', '非常满意'),
        ]
        
        print("医院评分 → 情感倾向映射关系:")
        print("-" * 60)
        
        for rating, expected_sentiment, stars, description in rating_mapping:
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"演示{rating}星评分 - {description}",
                "hospital_rating": rating,
                "hospital_number": f"DEMO_{rating}STAR",
                "phone_number": "13800138000",
                "staff_evaluations": []
            }
            
            request = create_mock_request(test_data)
            api_view = PublicEvaluationSubmitAPI()
            response = api_view.post(request)
            
            if response.status_code == 200:
                response_data = json.loads(response.content.decode())
                evaluation_id = response_data.get('data', {}).get('evaluation_id')
                
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    actual_sentiment = evaluation.sentiment
                    
                    # 情感倾向图标
                    sentiment_icons = {
                        'positive': '😊',
                        'negative': '😞',
                        'neutral': '😐'
                    }
                    
                    sentiment_colors = {
                        'positive': '正面',
                        'negative': '负面',
                        'neutral': '中性'
                    }
                    
                    icon = sentiment_icons.get(actual_sentiment, '❓')
                    color = sentiment_colors.get(actual_sentiment, '未知')
                    
                    status = "✅" if actual_sentiment == expected_sentiment else "❌"
                    
                    print(f"{status} {stars} {rating}星 ({description}) → {icon} {color}")
                    
                    # 清理测试数据
                    evaluation.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ 演示失败: {e}")
        return False

def demo_priority_logic():
    """演示优先级逻辑"""
    print(f"\n🔄 优先级逻辑演示")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 获取一个工作人员ID
        bed = qr_code.bed
        if bed and bed.department:
            from qrmanager.models import Staff
            staff = Staff.objects.filter(department=bed.department, is_active=True).first()
            if not staff:
                print("⚠️  没有找到测试用的工作人员，跳过优先级演示")
                return True
            
            # 演示医院评分优先于工作人员评价
            test_cases = [
                {
                    "name": "医院评分(2星) + 工作人员满意",
                    "hospital_rating": 2,
                    "staff_satisfied": True,
                    "expected": "negative",
                    "reason": "医院评分优先，2星 → 负面"
                },
                {
                    "name": "医院评分(4星) + 工作人员不满意",
                    "hospital_rating": 4,
                    "staff_satisfied": False,
                    "expected": "positive",
                    "reason": "医院评分优先，4星 → 正面"
                },
                {
                    "name": "无医院评分 + 工作人员满意",
                    "hospital_rating": None,
                    "staff_satisfied": True,
                    "expected": "positive",
                    "reason": "无医院评分，工作人员满意 → 正面"
                },
                {
                    "name": "无医院评分 + 工作人员不满意",
                    "hospital_rating": None,
                    "staff_satisfied": False,
                    "expected": "negative",
                    "reason": "无医院评分，工作人员不满意 → 负面"
                }
            ]
            
            print("优先级逻辑测试:")
            print("-" * 60)
            
            for i, case in enumerate(test_cases, 1):
                test_data = {
                    "qr_param": encrypted_param,
                    "comment": f"优先级测试 - {case['name']}",
                    "hospital_number": f"PRIORITY_TEST_{i}",
                    "phone_number": "13800138000",
                    "staff_evaluations": [{
                        "staff_id": staff.id,
                        "is_satisfied": case['staff_satisfied']
                    }]
                }
                
                if case['hospital_rating'] is not None:
                    test_data['hospital_rating'] = case['hospital_rating']
                
                request = create_mock_request(test_data)
                api_view = PublicEvaluationSubmitAPI()
                response = api_view.post(request)
                
                if response.status_code == 200:
                    response_data = json.loads(response.content.decode())
                    evaluation_id = response_data.get('data', {}).get('evaluation_id')
                    
                    if evaluation_id:
                        evaluation = Evaluation.objects.get(id=evaluation_id)
                        actual_sentiment = evaluation.sentiment
                        
                        status = "✅" if actual_sentiment == case['expected'] else "❌"
                        
                        print(f"{status} {case['name']}")
                        print(f"    期望: {case['expected']} | 实际: {actual_sentiment}")
                        print(f"    原因: {case['reason']}")
                        print()
                        
                        # 清理测试数据
                        evaluation.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ 优先级演示失败: {e}")
        return False

def demo_management_display():
    """演示管理界面显示效果"""
    print(f"\n🖥️  管理界面显示效果演示")
    print("=" * 60)
    
    try:
        # 创建一些示例评价用于演示
        from qrmanager.models import Bed
        bed = Bed.objects.first()
        if not bed:
            print("❌ 没有找到测试床位")
            return False
        
        demo_evaluations = [
            {"rating": 1, "sentiment": "negative", "comment": "服务很差，非常不满意"},
            {"rating": 3, "sentiment": "neutral", "comment": "服务一般，有待改进"},
            {"rating": 5, "sentiment": "positive", "comment": "服务非常好，很满意"},
            {"rating": None, "sentiment": "positive", "comment": "工作人员很好，但没有整体评分"},
        ]
        
        print("管理界面显示效果预览:")
        print("-" * 60)
        
        for i, demo in enumerate(demo_evaluations, 1):
            # 创建演示评价
            evaluation = Evaluation.objects.create(
                bed=bed,
                is_satisfied=demo['sentiment'] != 'negative',
                comment=demo['comment'],
                hospital_rating=demo['rating'],
                sentiment=demo['sentiment'],
                hospital_number=f"DISPLAY_DEMO_{i}",
                phone_number="13800138000"
            )
            
            # 模拟管理界面显示
            sentiment_icons = {
                'positive': '😊 正面',
                'negative': '😞 负面',
                'neutral': '😐 中性'
            }
            
            rating_display = ""
            if evaluation.hospital_rating:
                stars = "★" * evaluation.hospital_rating + "☆" * (5 - evaluation.hospital_rating)
                rating_display = f" ({evaluation.hospital_rating}★)"
            
            sentiment_display = sentiment_icons.get(evaluation.sentiment, '❓ 未知')
            
            print(f"评价 {i}:")
            print(f"  医院评分: {evaluation.hospital_rating or '未评分'}")
            print(f"  情感倾向: {sentiment_display}{rating_display}")
            print(f"  评价内容: {evaluation.comment}")
            print(f"  显示提示: {'基于医院评分' if evaluation.hospital_rating else '基于工作人员评价'}")
            print()
            
            # 清理演示数据
            evaluation.delete()
        
        return True
        
    except Exception as e:
        print(f"❌ 管理界面演示失败: {e}")
        return False

def main():
    """主演示函数"""
    print("🎉 医院评分情感倾向功能完整演示")
    print("=" * 80)
    print("功能说明：根据用户的医院评分自动确定情感倾向")
    print("映射规则：1-2星→负面，3星→中性，4-5星→正面")
    print("优先级：医院评分 > 工作人员评价 > 默认中性")
    print()
    
    demos = [
        ("评分映射演示", demo_sentiment_mapping),
        ("优先级逻辑演示", demo_priority_logic),
        ("管理界面演示", demo_management_display),
    ]
    
    results = []
    for name, demo_func in demos:
        try:
            result = demo_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}执行异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"📋 演示结果总结")
    print("=" * 80)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 成功")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 演示成功")
    
    if passed == len(results):
        print("🎉 所有演示成功！医院评分情感倾向功能完美运行！")
        print("\n🌟 功能亮点:")
        print("  ✨ 智能情感分析：根据1-5星评分自动判断情感倾向")
        print("  🎯 优先级清晰：医院评分优先于工作人员评价")
        print("  🖥️  界面友好：管理界面直观显示评分来源")
        print("  📊 数据准确：后端正确区分正面、中性、负面评价")
        print("  🔄 逻辑完善：处理各种评价组合情况")
    else:
        print("⚠️  部分演示存在问题")

if __name__ == "__main__":
    main()
