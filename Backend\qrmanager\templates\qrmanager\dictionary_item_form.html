{% extends 'qrmanager/base.html' %}
{% load django_bootstrap5 %}

{% block title %}{% if form.instance.pk %}编辑字典项{% else %}新增字典项{% endif %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        {% if form.instance.pk %}
                        编辑字典项 - {{ dictionary.name }}
                        {% else %}
                        新增字典项 - {{ dictionary.name }}
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post" class="form">
                        {% csrf_token %}
                        {% bootstrap_form form %}
                        <div class="d-flex justify-content-between">
                            <a href="{% url 'qrmanager:dictionary_item_list' dictionary.id %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 