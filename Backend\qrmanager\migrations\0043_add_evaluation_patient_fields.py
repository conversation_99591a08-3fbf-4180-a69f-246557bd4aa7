# Generated manually

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0042_add_missing_is_satisfied_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='hospital_number',
            field=models.Char<PERSON>ield(blank=True, max_length=30, null=True, verbose_name='住院号'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='phone_number',
            field=models.CharField(blank=True, max_length=20, null=True, verbose_name='联系电话'),
        ),
    ] 