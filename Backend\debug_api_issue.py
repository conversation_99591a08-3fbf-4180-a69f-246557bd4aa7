#!/usr/bin/env python
"""
调试API问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param, secure_qr_access, is_encrypted_param
from qrmanager.models import QRCode
import uuid

def debug_encryption_issue():
    """调试加密问题"""
    print("=" * 80)
    print("🔍 调试加密参数识别问题")
    print("=" * 80)
    
    # 生成测试UUID
    test_uuid = str(uuid.uuid4())
    print(f"测试UUID: {test_uuid}")
    
    try:
        # 步骤1: 加密
        encrypted = encrypt_qr_param(test_uuid)
        print(f"加密结果: {encrypted}")
        print(f"加密长度: {len(encrypted)}")
        
        # 步骤2: 格式检查
        is_encrypted = is_encrypted_param(encrypted)
        print(f"格式检查: {is_encrypted}")
        
        # 步骤3: 直接解密测试
        try:
            decrypted_data = decrypt_qr_param(encrypted)
            print(f"直接解密: ✅ 成功")
            print(f"解密数据: {decrypted_data}")
        except Exception as e:
            print(f"直接解密: ❌ 失败 - {e}")
        
        # 步骤4: secure_qr_access测试
        try:
            uuid_result = secure_qr_access(encrypted)
            print(f"secure_qr_access: ✅ 成功")
            print(f"返回UUID: {uuid_result}")
        except Exception as e:
            print(f"secure_qr_access: ❌ 失败 - {e}")
        
        # 步骤5: 检查数据库中是否存在这个UUID
        try:
            qrcode_obj = QRCode.objects.filter(code=test_uuid).first()
            if qrcode_obj:
                print(f"数据库查找: ✅ 找到二维码")
                print(f"二维码信息: {qrcode_obj}")
            else:
                print(f"数据库查找: ❌ 未找到二维码")
                print("这可能是API返回'二维码无效'的原因")
        except Exception as e:
            print(f"数据库查找: ❌ 异常 - {e}")
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")

def test_with_existing_qrcode():
    """使用数据库中现有的二维码测试"""
    print("\n" + "=" * 80)
    print("🗄️ 使用现有二维码测试")
    print("=" * 80)
    
    try:
        # 获取数据库中的第一个二维码
        qrcode_obj = QRCode.objects.first()
        
        if not qrcode_obj:
            print("❌ 数据库中没有二维码")
            return
        
        print(f"现有二维码UUID: {qrcode_obj.code}")
        print(f"关联床位: {qrcode_obj.bed}")
        
        # 使用现有UUID进行加密测试
        try:
            encrypted = encrypt_qr_param(qrcode_obj.code)
            print(f"加密结果: {encrypted}")
            
            # 测试secure_qr_access
            uuid_result = secure_qr_access(encrypted)
            print(f"解密结果: {uuid_result}")
            print(f"UUID匹配: {'✅' if uuid_result == qrcode_obj.code else '❌'}")
            
            # 这个应该能在数据库中找到
            found_qrcode = QRCode.objects.filter(code=uuid_result).first()
            if found_qrcode:
                print(f"数据库验证: ✅ 找到匹配的二维码")
                print(f"床位信息: {found_qrcode.bed}")
            else:
                print(f"数据库验证: ❌ 未找到匹配的二维码")
                
        except Exception as e:
            print(f"❌ 现有二维码测试失败: {e}")
            
    except Exception as e:
        print(f"❌ 数据库查询失败: {e}")

def check_api_validation_logic():
    """检查API验证逻辑"""
    print("\n" + "=" * 80)
    print("🔍 检查API验证逻辑")
    print("=" * 80)
    
    # 模拟API验证过程
    test_uuid = str(uuid.uuid4())
    encrypted = encrypt_qr_param(test_uuid)
    
    print(f"模拟API验证过程:")
    print(f"1. 输入参数: {encrypted}")
    print(f"2. 参数长度: {len(encrypted)}")
    
    # 检查长度验证（API中的验证）
    if len(encrypted) < 20:
        print(f"3. 长度验证: ❌ 失败 - 长度不足20")
    else:
        print(f"3. 长度验证: ✅ 通过")
    
    # 检查格式验证
    is_encrypted_format = is_encrypted_param(encrypted)
    print(f"4. 格式验证: {'✅ 通过' if is_encrypted_format else '❌ 失败'}")
    
    # 检查解密过程
    try:
        uuid_result = secure_qr_access(encrypted)
        print(f"5. 解密过程: ✅ 成功 - {uuid_result}")
    except Exception as e:
        print(f"5. 解密过程: ❌ 失败 - {e}")
    
    # 检查UUID格式验证
    from qrmanager.security import is_valid_uuid
    if 'uuid_result' in locals():
        is_valid = is_valid_uuid(uuid_result)
        print(f"6. UUID格式: {'✅ 有效' if is_valid else '❌ 无效'}")
    
    # 检查数据库查找
    if 'uuid_result' in locals():
        try:
            qrcode_obj = QRCode.objects.filter(code=uuid_result).first()
            if qrcode_obj:
                print(f"7. 数据库查找: ✅ 找到")
            else:
                print(f"7. 数据库查找: ❌ 未找到 - 这是API返回错误的原因")
        except Exception as e:
            print(f"7. 数据库查找: ❌ 异常 - {e}")

if __name__ == "__main__":
    debug_encryption_issue()
    test_with_existing_qrcode()
    check_api_validation_logic()
