#!/usr/bin/env python
"""
最终验证测试 - 确认新加密算法API兼容性
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
from qrmanager.models import QRCode

def test_final_verification():
    """最终验证测试"""
    print("=" * 80)
    print("🎯 最终验证：新加密算法API兼容性测试")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    try:
        # 获取数据库中的二维码
        qrcode_obj = QRCode.objects.first()
        
        if not qrcode_obj:
            print("❌ 数据库中没有二维码")
            return False
        
        print(f"测试二维码: {qrcode_obj.code}")
        print(f"关联床位: {qrcode_obj.bed}")
        print()
        
        # 使用新算法加密
        encrypted_param = encrypt_qr_param(qrcode_obj.code)
        print(f"新算法加密: {encrypted_param}")
        print(f"加密长度: {len(encrypted_param)} 字符")
        print()
        
        # 测试验证API
        print("🔍 测试验证API:")
        verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
        verify_data = {
            "qr_param": encrypted_param,
            "client_ip": "127.0.0.1"
        }
        
        response = requests.post(
            verify_url,
            json=verify_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"   API状态: {response_data.get('status', 'unknown')}")
            
            if 'data' in response_data:
                data = response_data['data']
                print(f"   床位信息: {data.get('bed_info', {}).get('number', 'N/A')}")
                print(f"   科室信息: {data.get('department_info', {}).get('name', 'N/A')}")
                print(f"   临时令牌: {'有' if data.get('temp_token') else '无'}")
            
            print(f"   验证结果: ✅ 成功")
            return True
        else:
            print(f"   错误信息: {response.text}")
            print(f"   验证结果: ❌ 失败")
            return False
            
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False

def test_multiple_qrcodes_quick():
    """快速测试多个二维码"""
    print("\n" + "=" * 80)
    print("🔄 多二维码快速验证")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    qrcodes = QRCode.objects.filter(is_active=True)[:5]
    
    if not qrcodes:
        print("❌ 没有可用的二维码")
        return False
    
    success_count = 0
    total_count = len(qrcodes)
    
    for i, qrcode_obj in enumerate(qrcodes, 1):
        try:
            encrypted = encrypt_qr_param(qrcode_obj.code)
            
            response = requests.post(
                f"{BASE_URL}/api/v1/public/qrcode/verify/",
                json={"qr_param": encrypted, "client_ip": "127.0.0.1"},
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"   测试 {i}: ✅ 成功")
                success_count += 1
            else:
                print(f"   测试 {i}: ❌ 失败 ({response.status_code})")
                
        except Exception as e:
            print(f"   测试 {i}: ❌ 异常 - {e}")
    
    success_rate = (success_count / total_count) * 100
    print(f"\n📊 测试结果: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")
    
    return success_rate == 100.0

if __name__ == "__main__":
    print("🚀 开始最终验证测试...")
    
    # 详细测试
    detailed_success = test_final_verification()
    
    # 快速批量测试
    batch_success = test_multiple_qrcodes_quick()
    
    print("\n" + "=" * 80)
    print("🏁 最终验证结论")
    print("=" * 80)
    
    if detailed_success and batch_success:
        print("🎉 验证完全成功！")
        print("✅ 新加密算法与前端API完全兼容")
        print("✅ 加密字符串可以正确访问API接口")
        print("✅ 解密功能100%正常")
        print("✅ 可以安全部署到生产环境")
        print("\n📋 回答用户问题:")
        print("   ✅ 现在加密的字符串可以正确访问前端提供的API接口")
        print("   ✅ 解密功能完全正常")
        print("   ✅ 前端填写页面到后端API接口无需调整")
    elif detailed_success:
        print("⚠️  部分成功")
        print("✅ 单个测试成功")
        print("❌ 批量测试存在问题")
    else:
        print("❌ 验证失败")
        print("❌ 仍存在API兼容性问题")
    
    print("=" * 80)
