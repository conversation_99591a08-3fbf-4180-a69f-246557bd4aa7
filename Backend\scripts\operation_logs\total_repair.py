#!/usr/bin/env python
# 创建一个完整的get_queryset方法替换文件

import os

# 创建一个临时文件，包含正确的get_queryset方法
with open('correct_method.py', 'w', encoding='utf-8') as f:
    f.write("""
    def get_queryset(self):
        queryset = OperationLog.objects.select_related('user')

        # 过滤条件
        user_id = self.request.GET.get('user')
        action = self.request.GET.get('action')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        time_range = self.request.GET.get('time_range')

        # 应用过滤器
        if user_id and user_id.isdigit():
            queryset = queryset.filter(user_id=user_id)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if status:
            queryset = queryset.filter(status=status)

        # 日期范围过滤
        if date_from:
            try:
                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        if date_to:
            try:
                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass

        # 时间范围快捷筛选
        if time_range:
            now = timezone.now()
            if time_range == 'today':
                queryset = queryset.filter(created_at__date=now.date())
            elif time_range == 'yesterday':
                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))
            elif time_range == 'this_week':
                # 本周的开始（星期一）
                week_start = now.date() - timezone.timedelta(days=now.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif time_range == 'this_month':
                # 本月的开始（1号）
                month_start = now.date().replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)
            elif time_range == 'last_30_days':
                # 过去30天
                days_30 = now.date() - timezone.timedelta(days=30)
                queryset = queryset.filter(created_at__date__gte=days_30)

        return queryset.order_by('-created_at')
"""
    )

# 打印后续步骤说明
print("""
临时文件已创建完成。请接下来按照以下步骤操作：

1. 手动打开qrmanager/views.py文件
2. 找到OperationLogListView类中的get_queryset方法
3. 将整个get_queryset方法替换为我们创建的correct_method.py文件中的内容
4. 保存文件并重新启动后端服务

重要提示：在编辑views.py文件时要特别小心，确保只替换get_queryset方法，不要影响其他代码。
""") 