{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}操作日志{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <!-- 顶部控制栏 -->
            <div class="card fade-in mb-4">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <h2 class="card-title mb-0">
                            <i class="fas fa-history me-2 text-primary"></i>操作日志记录
                        </h2>
                        <div>
                            <button type="button" class="btn btn-outline-success me-2" onclick="exportToExcel()">
                                <i class="fas fa-file-excel me-2"></i>导出Excel
                            </button>
                            <button type="button" class="btn btn-outline-danger me-2" onclick="exportToPDF()">
                                <i class="fas fa-file-pdf me-2"></i>导出PDF
                            </button>
                            <a href="{% url 'qrmanager:logging_config' %}" class="btn btn-outline-primary me-2">
                                <i class="fas fa-sliders-h me-2"></i>日志配置
                            </a>
                            <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回仪表板
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 数据统计卡片 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="text-primary">总操作数</h3>
                            <p class="display-4">{{ total_logs }}</p>
                            <p class="text-muted">较上周{{ total_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="text-success">今日操作</h3>
                            <p class="display-4">{{ today_logs }}</p>
                            <p class="text-muted">较昨日{{ today_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="text-warning">活跃用户</h3>
                            <p class="display-4">{{ active_users }}</p>
                            <p class="text-muted">本周活跃用户数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="text-danger">异常操作</h3>
                            <p class="display-4">{{ error_logs }}</p>
                            <p class="text-muted">本周异常操作数</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 筛选条件 -->
            <div class="card fade-in mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">时间范围</label>
                            <select name="time_range" class="form-select">
                                <option value="today">今天</option>
                                <option value="yesterday">昨天</option>
                                <option value="week">最近7天</option>
                                <option value="month">最近30天</option>
                                <option value="custom">自定义</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">操作用户</label>
                            <select name="user" class="form-select">
                                <option value="">全部用户</option>
                                {% for user in users %}
                                <option value="{{ user.id }}">{{ user.username }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">操作类型</label>
                            <select name="action_type" class="form-select">
                                <option value="">全部类型</option>
                                {% for action_value, action_display in action_types_with_display %}
                                <option value="{{ action_value }}">{{ action_display }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">关键词</label>
                            <input type="text" name="keyword" class="form-control" placeholder="搜索...">
                        </div>
                        <div class="col-12 text-end">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-filter me-2"></i>应用筛选
                            </button>
                            <a href="?" class="btn btn-outline-secondary">
                                <i class="fas fa-undo me-2"></i>重置
                            </a>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 图表展示 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">操作趋势</h3>
                            <div id="operationTrendChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">操作类型分布</h3>
                            <div id="operationTypeChart" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 活跃用户排行 -->
            <div class="card fade-in mb-4">
                <div class="card-body">
                    <h3 class="card-title">活跃用户TOP5</h3>
                    <div class="table-responsive">
                        <table class="table">
                            <thead>
                                <tr>
                                    <th>用户名</th>
                                    <th>操作次数</th>
                                    <th>最近操作时间</th>
                                    <th>常用操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in top_users %}
                                <tr>
                                    <td>{{ user.username }}</td>
                                    <td>{{ user.operation_count }}</td>
                                    <td>{{ user.last_operation }}</td>
                                    <td>{{ user.most_common_action }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>

            <!-- 日志列表 -->
            <div class="card fade-in">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h3 class="card-title">日志详情</h3>
                        <div class="d-flex align-items-center">
                            <span class="me-2">每页显示:</span>
                            <select class="form-select form-select-sm" style="width: 70px;" onchange="changePageSize(this.value)">
                                <option value="10">10</option>
                                <option value="20" selected>20</option>
                                <option value="50">50</option>
                                <option value="100">100</option>
                            </select>
                        </div>
                    </div>
                    
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>时间</th>
                                    <th>用户</th>
                                    <th>操作类型</th>
                                    <th>IP地址</th>
                                    <th>操作描述</th>
                                    <th>状态</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                    <td>{{ log.user|default:"系统" }}</td>
                                    <td>
                                        <span class="badge {% if log.action == '登录' %}bg-success{% elif log.action == '退出' %}bg-warning{% else %}bg-primary{% endif %}">
                                            {{ log.display_action }}
                                        </span>
                                    </td>
                                    <td>{{ log.ip_address|default:"-" }}</td>
                                    <td>{{ log.description }}</td>
                                    <td>
                                        <span class="badge {% if log.status == 'success' %}bg-success{% elif log.status == 'error' %}bg-danger{% else %}bg-secondary{% endif %}">
                                            {{ log.get_status_display }}
                                        </span>
                                    </td>
                                    <td>
                                        <button type="button" class="btn btn-sm btn-outline-primary" onclick="showLogDetail({{ log.id }})">
                                            <i class="fas fa-eye"></i>
                                        </button>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="7" class="text-center py-4">暂无操作日志记录</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>

                    {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&laquo;</a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                                {% if page_obj.number == num %}
                                    <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                                {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                                    <li class="page-item">
                                        <a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                                    </li>
                                {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">&raquo;</a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 日志详情模态框 -->
<div class="modal fade" id="logDetailModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">操作详情</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="logDetailContent"></div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/echarts@5.4.3/dist/echarts.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 操作趋势图表
    const operationTrend = echarts.init(document.getElementById('operationTrendChart'));
    operationTrend.setOption({
        title: {
            text: '操作趋势分析'
        },
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: {{ trend_dates|safe }}
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: {{ trend_counts|safe }},
            type: 'line',
            smooth: true
        }]
    });

    // 操作类型分布图表
    const operationType = echarts.init(document.getElementById('operationTypeChart'));
    operationType.setOption({
        title: {
            text: '操作类型分布'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            left: 'left'
        },
        series: [{
            type: 'pie',
            radius: '50%',
            data: {{ type_distribution|safe }},
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowOffsetX: 0,
                    shadowColor: 'rgba(0, 0, 0, 0.5)'
                }
            }
        }]
    });

    // 窗口大小改变时重绘图表
    window.addEventListener('resize', function() {
        operationTrend.resize();
        operationType.resize();
    });
});

// 导出Excel功能
function exportToExcel() {
    // 创建一个表单进行POST提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = window.location.href;
    
    // 添加CSRF令牌
    const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = csrftoken;
    form.appendChild(csrfInput);
    
    // 添加操作类型
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'export_excel';
    form.appendChild(actionInput);
    
    // 添加到文档并提交
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 导出PDF功能
function exportToPDF() {
    // 创建一个表单进行POST提交
    const form = document.createElement('form');
    form.method = 'POST';
    form.action = window.location.href;
    
    // 添加CSRF令牌
    const csrftoken = document.querySelector('[name=csrfmiddlewaretoken]').value;
    const csrfInput = document.createElement('input');
    csrfInput.type = 'hidden';
    csrfInput.name = 'csrfmiddlewaretoken';
    csrfInput.value = csrftoken;
    form.appendChild(csrfInput);
    
    // 添加操作类型
    const actionInput = document.createElement('input');
    actionInput.type = 'hidden';
    actionInput.name = 'action';
    actionInput.value = 'export_pdf';
    form.appendChild(actionInput);
    
    // 添加到文档并提交
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 显示日志详情
function showLogDetail(logId) {
    // TODO: 实现查看日志详情功能
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    document.getElementById('logDetailContent').innerHTML = `正在加载日志ID ${logId} 的详细信息...`;
    modal.show();
}

// 改变每页显示数量
function changePageSize(size) {
    const url = new URL(window.location.href);
    url.searchParams.set('page_size', size);
    window.location.href = url.toString();
}

// 自动提交筛选表单
const filterForm = document.querySelector('form');
const selects = filterForm.querySelectorAll('select');
selects.forEach(select => {
    select.addEventListener('change', () => {
        if (select.name !== 'time_range' || select.value !== 'custom') {
            filterForm.submit();
        }
    });
});
</script>
{% endblock %} 