"""
操作日志中间件
用于记录用户操作日志
"""

from django.utils.deprecation import MiddlewareMixin
from django.core.cache import cache
from django.urls import resolve
import re
import json

class OperationLogMiddleware(MiddlewareMixin):
    """
    操作日志中间件
    记录用户操作日志
    """
    
    # 忽略的路径
    IGNORE_PATHS = [
        r'^/static/',
        r'^/media/',
        r'^/favicon.ico$',
        r'^/__debug__/',
    ]

    # 敏感字段
    SENSITIVE_FIELDS = [
        'password',
        'token',
        'secret',
        'credit_card',
        'phone',
    ]
    
    def __init__(self, get_response=None):
        super().__init__(get_response)
        # 清除缓存，确保从数据库加载最新配置
        cache.delete('logging_config')
        print("已清除日志配置缓存")
    
    def reload_config(self):
        """重新加载日志配置"""
        # 使用缓存存储配置，避免频繁查询数据库
        config = cache.get('logging_config')
        if config is None:
            try:
                # 从数据库加载配置
                from ..models import LoggingConfiguration
                config = {
                    item.action_type: item.is_enabled
                    for item in LoggingConfiguration.objects.all()
                }
                # 缓存配置，有效期1小时
                cache.set('logging_config', config, 3600)
            except:
                # 如果数据库查询失败，使用默认配置
                config = {
                    'create': True,
                    'update': True,
                    'delete': True,
                    'login': True,
                    'logout': True,
                    'error': True,
                    'permission_change': True,
                    'system_config': True,
                }
        self.logging_config = config
    
    def should_log_action(self, action):
        """检查是否应该记录特定类型的操作"""
        # 提取操作类型
        action_type = None
        
        # 处理常见操作类型
        if 'create' in action.lower():
            action_type = 'create'
        elif 'update' in action.lower() or 'edit' in action.lower() or 'modify' in action.lower():
            action_type = 'update'
        elif 'delete' in action.lower() or 'remove' in action.lower():
            action_type = 'delete'
        elif 'login' in action.lower():
            action_type = 'login'
        elif 'logout' in action.lower():
            action_type = 'logout'
        elif 'view' in action.lower() or 'get' in action.lower():
            action_type = 'view'
        elif 'export' in action.lower():
            action_type = 'export'
        elif 'import' in action.lower():
            action_type = 'import'
        elif 'print' in action.lower():
            action_type = 'print'
        elif 'generate' in action.lower():
            action_type = 'generate'
        elif 'permission' in action.lower():
            action_type = 'permission_change'
        elif 'config' in action.lower():
            action_type = 'system_config'
        elif 'api' in action.lower():
            action_type = 'api_request'
        elif 'file_upload' in action.lower() or 'upload' in action.lower():
            action_type = 'file_upload'
        elif 'file_download' in action.lower() or 'download' in action.lower():
            action_type = 'file_download'
        elif 'bulk' in action.lower():
            action_type = 'bulk_operation'
        elif 'error' in action.lower():
            action_type = 'error'
        
        # 如果无法识别操作类型，不记录日志
        if action_type is None:
            print(f"中间件无法识别的操作类型: {action}")
            return False
        
        # 使用LoggerHelper来判断是否记录
        from ..utils import LoggerHelper
        return LoggerHelper.should_log_action(action_type)
    
    def should_log(self, request):
        # 启用中间件日志记录，但排除某些路径
        path = request.path_info
        
        # 静态文件和媒体文件不记录
        for pattern in self.IGNORE_PATHS:
            if re.match(pattern, path):
                return False
        
        # GET请求的评价页面不记录（避免大量无用日志）
        if request.method == 'GET' and '/evaluation/' in path:
            return False
        
        # 对于GET请求，只记录特定的页面
        if request.method == 'GET':
            # 记录登录、仪表板、操作日志等重要页面
            important_paths = [
                '/login/',
                '/dashboard/',
                '/operation_logs/',
                '/beds/',
                '/departments/',
                '/staff/',
                '/qrcodes/',
                '/evaluations/',
                '/admin/'
            ]
            for important_path in important_paths:
                if path.startswith(important_path):
                    # 检查是否应该记录此类型的操作
                    return self.should_log_action("view")
            # 其他GET请求不记录
            return False
        
        # 对于其他请求，根据方法设置操作类型
        if request.method == 'POST':
            return self.should_log_action("create")
        elif request.method == 'PUT' or request.method == 'PATCH':
            return self.should_log_action("update")
        elif request.method == 'DELETE':
            return self.should_log_action("delete")
        
        # 其他未知请求类型不记录
        return False
    
    def get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
            print(f"从HTTP_X_FORWARDED_FOR获取IP: {ip}")
            return ip
        ip = request.META.get('REMOTE_ADDR')
        print(f"从REMOTE_ADDR获取IP: {ip}")
        
        # 打印所有请求头，帮助调试
        print("所有请求头:")
        for key, value in request.META.items():
            if key.startswith('HTTP_'):
                print(f"  {key}: {value}")
        
        return ip
    
    def get_browser_info(self, request):
        """获取浏览器和操作系统信息"""
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        browser = "未知浏览器"
        os = "未知操作系统"
        
        # 简单的浏览器和操作系统检测
        if 'MSIE' in user_agent or 'Trident' in user_agent:
            browser = 'Internet Explorer'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Chrome' in user_agent and 'Edge' not in user_agent:
            browser = 'Chrome'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edge' in user_agent:
            browser = 'Edge'
        
        if 'Windows' in user_agent:
            os = 'Windows'
        elif 'Macintosh' in user_agent:
            os = 'MacOS'
        elif 'Linux' in user_agent:
            os = 'Linux'
        elif 'Android' in user_agent:
            os = 'Android'
        elif 'iPhone' in user_agent or 'iPad' in user_agent:
            os = 'iOS'
        
        return browser, os
    
    def mask_sensitive_data(self, data):
        """遮蔽敏感数据"""
        if isinstance(data, dict):
            masked_data = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in self.SENSITIVE_FIELDS):
                    masked_data[key] = '******'
                else:
                    masked_data[key] = self.mask_sensitive_data(value)
            return masked_data
        elif isinstance(data, list):
            return [self.mask_sensitive_data(item) for item in data]
        else:
            return data
    
    def get_view_name(self, request):
        """获取更详细的视图信息"""
        try:
            resolver_match = resolve(request.path_info)
            view_name = resolver_match.view_name
            app_name = resolver_match.app_name
            url_name = resolver_match.url_name
            return {
                'view_name': view_name,
                'app_name': app_name,
                'url_name': url_name,
                'kwargs': resolver_match.kwargs
            }
        except:
            return {'view_name': '未知视图'}
    
    def process_view(self, request, view_func, view_args, view_kwargs):
        """处理视图，记录操作日志"""
        if not self.should_log(request):
            return None
        
        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)
        
        # 获取请求方法和路径
        method = request.method
        path = request.path_info
        
        # 准备请求数据(遮蔽敏感信息)
        request_data = {
            'POST': self.mask_sensitive_data(dict(request.POST)),
            'FILES': [f.name for f in request.FILES.values()] if request.FILES else [],
        }
        
        # 准备额外数据
        extra_data = {
            'method': method,
            'path': path,
            'request_data': request_data,
        }
        
        # 创建操作描述
        description = f"{method} {path}"
        
        # 确定操作类型
        action = f"{method}请求"
        
        # 检查是否应该记录此类操作
        if not self.should_log_action(action):
            return None
        
        # 记录操作日志
        try:
            # 确保记录IP地址
            if not ip_address:
                ip_address = self.get_client_ip(request)
                print(f"中间件获取IP地址: {ip_address}")
            
            from ..models import OperationLog
            OperationLog.objects.create(
                user=user,
                action=action,
                description=description,
                ip_address=ip_address,
                browser=browser,
                os=os,
                status='success',
                extra_data=extra_data
            )
        except Exception as e:
            # 记录日志失败，但不影响请求处理
            print(f"记录操作日志失败: {e}")
        
        return None
    
    def process_response(self, request, response):
        """记录所有请求的响应"""
        if not self.should_log(request):
            return response
        
        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)
        
        # 记录响应状态
        status_code = response.status_code
        if 200 <= status_code < 300:
            status = 'success'
        elif 400 <= status_code < 500:
            status = 'warning'
        else:
            status = 'error'
        
        try:
            # 只记录非GET请求或错误响应
            if request.method != 'GET' or status != 'success':
                action = "响应状态"
                
                # 检查是否应该记录此类操作
                if status == 'error' or self.should_log_action(action):
                    from ..models import OperationLog
                    OperationLog.objects.create(
                        user=user,
                        action=action,
                        description=f"响应状态码: {status_code}",
                        ip_address=ip_address,
                        browser=browser,
                        os=os,
                        status=status,
                        extra_data={
                            'status_code': status_code,
                            'reason_phrase': response.reason_phrase,
                        }
                    )
        except Exception as e:
            # 记录日志失败，但不影响响应处理
            print(f"记录响应日志失败: {e}")
        
        return response
    
    def process_exception(self, request, exception):
        """记录未处理的异常"""
        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)
        
        try:
            # 检查是否应该记录错误操作
            if self.should_log_action("error"):
                from ..models import OperationLog
                OperationLog.objects.create(
                    user=user,
                    action="系统异常",
                    description=str(exception),
                    ip_address=ip_address,
                    browser=browser,
                    os=os,
                    status='error',
                    extra_data={
                        'exception_type': exception.__class__.__name__,
                        'exception_message': str(exception),
                        'exception_module': exception.__class__.__module__,
                    }
                )
        except Exception as e:
            # 记录日志失败，但不影响异常处理
            print(f"记录异常日志失败: {e}")
        
        return None