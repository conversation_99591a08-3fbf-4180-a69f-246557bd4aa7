# Django安全设置
DJANGO_SECRET_KEY=your_secret_key_here
ENCRYPTION_KEY=your_encryption_key_here

# 是否启用调试模式
DEBUG=False

# 允许的主机
ALLOWED_HOSTS=127.0.0.1,localhost

# 站点域名和前端URL
SITE_DOMAIN=http://localhost
FRONTEND_URL=http://localhost

# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3

# 会话设置
SESSION_COOKIE_AGE=28800
SESSION_EXPIRE_AT_BROWSER_CLOSE=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# CORS设置
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_ALL_ORIGINS=False

# 安全设置
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY