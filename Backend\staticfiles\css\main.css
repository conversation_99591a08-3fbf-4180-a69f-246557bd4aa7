/* Updated main.css - Apple 极简风格设计 */
body {
    font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif;
    margin: 0;
    padding: 0;
    background: linear-gradient(135deg, #ECECEC, #FFF);
    color: #333;
}

.container {
    max-width: 400px;
    margin: 100px auto; /* 页面整体居中，并向下留出一定间距 */
    background-color: #fff;
    padding: 40px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    font-size: 28px;
    font-weight: 500;
    margin-bottom: 30px;
    color: #111;
}

.form-group {
    margin-bottom: 20px;
}

label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    color: #555;
}

input[type="text"],
input[type="password"],
input[type="file"],
textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.2s;
}

input[type="text"]:focus,
input[type="password"]:focus,
textarea:focus {
    border-color: #007aff;
    outline: none;
}

button {
    display: block;
    width: 100%;
    padding: 12px;
    background-color: #007aff;
    border: none;
    color: #fff;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

button:hover {
    background-color: #005bb5;
}

footer {
    text-align: center;
    margin-top: 30px;
    font-size: 12px;
    color: #aaa;
} 