/* 基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", Aria<PERSON>, sans-serif;
    line-height: 1.6;
    color: #333;
    background: #f5f5f5;
    padding: 20px;
}

.container {
    max-width: 800px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

h1 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
    font-size: 28px;
}

/* 类型选择区域 */
.type-select-container {
    margin-bottom: 30px;
}

#staffTypeSelect {
    width: 100%;
    padding: 12px;
    border: 2px solid #e0e0e0;
    border-radius: 8px;
    font-size: 16px;
    color: #333;
    background: #fff;
    cursor: pointer;
    transition: all 0.3s ease;
}

#staffTypeSelect:hover {
    border-color: #bdbdbd;
}

#staffTypeSelect:focus {
    outline: none;
    border-color: #2196F3;
    box-shadow: 0 0 0 3px rgba(33, 150, 243, 0.1);
}

/* 评价计数器 */
.evaluation-counters {
    display: flex;
    justify-content: center;
    gap: 30px;
    margin-bottom: 20px;
}

.counter {
    padding: 10px 20px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: 500;
}

.counter.satisfied {
    background: #E8F5E9;
    color: #2E7D32;
}

.counter.unsatisfied {
    background: #FFEBEE;
    color: #C62828;
}

/* 评价限制提示 */
.evaluation-limit-message {
    text-align: center;
    padding: 10px;
    margin: 20px 0;
    background: #FFF3E0;
    border: 1px solid #FFB74D;
    border-radius: 8px;
    color: #E65100;
}

.hidden {
    display: none;
}

/* 工作人员列表样式 */
.staff-list {
    margin-top: 20px;
}

.staff-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    margin: 10px 0;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.staff-row:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
}

.staff-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.staff-name {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

.staff-title {
    font-size: 14px;
    color: #666;
}

.staff-rating-buttons {
    display: flex;
    gap: 10px;
}

.rating-btn {
    display: inline-flex;
    align-items: center;
    gap: 6px;
    padding: 8px 16px;
    border: 1px solid #ddd;
    border-radius: 20px;
    background: #fff;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.rating-btn.satisfied {
    color: #4CAF50;
}

.rating-btn.unsatisfied {
    color: #F44336;
}

.rating-btn:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.rating-btn.satisfied:hover:not(:disabled) {
    background: #E8F5E9;
    border-color: #4CAF50;
}

.rating-btn.unsatisfied:hover:not(:disabled) {
    background: #FFEBEE;
    border-color: #F44336;
}

.rating-btn.selected {
    font-weight: 600;
}

.rating-btn.satisfied.selected {
    background: #E8F5E9;
    border-color: #4CAF50;
    color: #2E7D32;
}

.rating-btn.unsatisfied.selected {
    background: #FFEBEE;
    border-color: #F44336;
    color: #C62828;
}

.rating-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 选中工作人员展示区样式 */
.selected-staff-container {
    margin: 20px 0;
    padding: 20px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.evaluation-summary {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.satisfied-staff-list,
.unsatisfied-staff-list {
    padding: 15px;
    border-radius: 8px;
    background: linear-gradient(to right, rgba(255,255,255,0.9), rgba(255,255,255,0.95));
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.satisfied-staff-list {
    border: 2px solid #4CAF50;
}

.unsatisfied-staff-list {
    border: 2px solid #F44336;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}

.list-header h3 {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.staff-counter {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 500;
}

.satisfied-staff-list .staff-counter {
    background: #E8F5E9;
    color: #2E7D32;
}

.unsatisfied-staff-list .staff-counter {
    background: #FFEBEE;
    color: #C62828;
}

.staff-tags {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    margin-top: 10px;
}

.staff-tag {
    display: inline-flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    border-radius: 16px;
    background: #fff;
    font-size: 14px;
    transition: all 0.3s ease;
}

.staff-tag.satisfied {
    border: 1px solid #4CAF50;
    color: #2E7D32;
}

.staff-tag.unsatisfied {
    border: 1px solid #F44336;
    color: #C62828;
}

.staff-tag:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.staff-tag .staff-name {
    font-weight: 500;
}

.staff-tag .staff-title {
    color: #666;
    font-size: 12px;
}

.remove-tag {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    padding: 0;
    border: none;
    border-radius: 50%;
    background: transparent;
    color: #999;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.staff-tag.satisfied .remove-tag:hover {
    background: #E8F5E9;
    color: #2E7D32;
}

.staff-tag.unsatisfied .remove-tag:hover {
    background: #FFEBEE;
    color: #C62828;
}

/* 提示信息样式 */
.type-select-message,
.staff-selection-message {
    padding: 20px;
    margin: 20px 0;
    text-align: center;
    border-radius: 8px;
    font-size: 16px;
}

.type-select-message {
    background: #F3E5F5;
    border: 1px solid #CE93D8;
    color: #6A1B9A;
}

.staff-selection-message {
    background: #E3F2FD;
    border: 1px solid #90CAF9;
    color: #1565C0;
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.3s ease-in;
}

.fade-out {
    animation: fadeOut 0.3s ease-out;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes fadeOut {
    from { opacity: 1; transform: translateY(0); }
    to { opacity: 0; transform: translateY(10px); }
}

/* 无工作人员提示 */
.no-staff-message {
    text-align: center;
    padding: 20px;
    color: #666;
}

.reset-filter-btn {
    margin-top: 10px;
    padding: 6px 12px;
    border: 1px solid #2196F3;
    border-radius: 4px;
    background: none;
    color: #2196F3;
    cursor: pointer;
    transition: all 0.3s;
}

.reset-filter-btn:hover {
    background: #2196F3;
    color: white;
}

/* 图标样式 */
.icon-thumbs-up,
.icon-thumbs-down {
    font-size: 16px;
    line-height: 1;
} 