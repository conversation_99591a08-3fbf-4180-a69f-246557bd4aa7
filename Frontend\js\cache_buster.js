/**
 * 缓存破坏器 - 解决手机端缓存问题
 * 强制刷新页面内容，确保用户看到最新版本
 */

(function() {
    'use strict';
    
    // 版本配置
    const VERSION_CONFIG = {
        version: 'v20241225-001',
        buildTime: '2024-12-25 10:00:00',
        forceRefresh: true,
        checkInterval: 30000 // 30秒检查一次
    };
    
    console.log(`🔄 缓存破坏器已加载 - 版本: ${VERSION_CONFIG.version}`);
    
    /**
     * 检查是否需要强制刷新
     */
    function checkForceRefresh() {
        const lastVersion = localStorage.getItem('app_version');
        const currentVersion = VERSION_CONFIG.version;
        
        if (lastVersion && lastVersion !== currentVersion) {
            console.log(`🔄 检测到版本更新: ${lastVersion} → ${currentVersion}`);
            
            // 清除所有缓存
            clearAllCache();
            
            // 更新版本号
            localStorage.setItem('app_version', currentVersion);
            
            // 显示更新提示
            showUpdateNotification();
            
            // 延迟刷新页面
            setTimeout(() => {
                window.location.reload(true);
            }, 2000);
            
            return true;
        } else if (!lastVersion) {
            // 首次访问，记录版本
            localStorage.setItem('app_version', currentVersion);
            console.log(`📝 记录当前版本: ${currentVersion}`);
        }
        
        return false;
    }
    
    /**
     * 清除所有缓存
     */
    function clearAllCache() {
        console.log('🧹 清除缓存中...');
        
        try {
            // 清除localStorage中的缓存数据（保留版本信息）
            const keysToRemove = [];
            for (let i = 0; i < localStorage.length; i++) {
                const key = localStorage.key(i);
                if (key && key !== 'app_version' && key !== 'evaluationSubmitted') {
                    keysToRemove.push(key);
                }
            }
            keysToRemove.forEach(key => localStorage.removeItem(key));
            
            // 清除sessionStorage
            sessionStorage.clear();
            
            // 如果支持，清除Service Worker缓存
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.getRegistrations().then(registrations => {
                    registrations.forEach(registration => {
                        registration.unregister();
                    });
                });
            }
            
            // 如果支持，清除Cache API
            if ('caches' in window) {
                caches.keys().then(names => {
                    names.forEach(name => {
                        caches.delete(name);
                    });
                });
            }
            
            console.log('✅ 缓存清除完成');
        } catch (error) {
            console.warn('⚠️ 缓存清除部分失败:', error);
        }
    }
    
    /**
     * 显示更新通知
     */
    function showUpdateNotification() {
        // 创建更新提示元素
        const notification = document.createElement('div');
        notification.id = 'update-notification';
        notification.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            background: #4CAF50;
            color: white;
            text-align: center;
            padding: 10px;
            z-index: 10000;
            font-size: 14px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.2);
        `;
        notification.innerHTML = `
            🔄 检测到新版本，正在更新... (${VERSION_CONFIG.version})
        `;
        
        document.body.insertBefore(notification, document.body.firstChild);
        
        // 3秒后移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
    
    /**
     * 添加版本信息到页面
     */
    function addVersionInfo() {
        // 在页面底部添加版本信息
        const versionInfo = document.createElement('div');
        versionInfo.style.cssText = `
            position: fixed;
            bottom: 5px;
            right: 5px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 2px 6px;
            border-radius: 3px;
            font-size: 10px;
            z-index: 9999;
            font-family: monospace;
        `;
        versionInfo.textContent = VERSION_CONFIG.version;
        versionInfo.title = `构建时间: ${VERSION_CONFIG.buildTime}`;
        
        document.body.appendChild(versionInfo);
        
        // 点击版本信息强制刷新
        versionInfo.addEventListener('click', () => {
            clearAllCache();
            window.location.reload(true);
        });
    }
    
    /**
     * 强制刷新特定资源
     */
    function forceRefreshResources() {
        const timestamp = Date.now();
        
        // 刷新CSS文件
        const cssLinks = document.querySelectorAll('link[rel="stylesheet"]');
        cssLinks.forEach(link => {
            const href = link.href.split('?')[0];
            link.href = `${href}?v=${timestamp}`;
        });
        
        // 刷新JavaScript文件
        const scriptTags = document.querySelectorAll('script[src]');
        scriptTags.forEach(script => {
            if (script.src && !script.src.includes('cache_buster.js')) {
                const newScript = document.createElement('script');
                const src = script.src.split('?')[0];
                newScript.src = `${src}?v=${timestamp}`;
                newScript.async = script.async;
                newScript.defer = script.defer;
                
                script.parentNode.insertBefore(newScript, script);
                script.parentNode.removeChild(script);
            }
        });
        
        console.log('🔄 资源文件已强制刷新');
    }
    
    /**
     * 检查服务器版本
     */
    async function checkServerVersion() {
        try {
            const response = await fetch(`/version.json?t=${Date.now()}`, {
                cache: 'no-cache',
                headers: {
                    'Cache-Control': 'no-cache',
                    'Pragma': 'no-cache'
                }
            });
            
            if (response.ok) {
                const serverVersion = await response.json();
                const localVersion = localStorage.getItem('app_version');
                
                if (serverVersion.version !== localVersion) {
                    console.log('🔄 服务器版本更新，准备刷新页面');
                    localStorage.setItem('app_version', serverVersion.version);
                    showUpdateNotification();
                    setTimeout(() => {
                        window.location.reload(true);
                    }, 2000);
                }
            }
        } catch (error) {
            console.log('📡 无法检查服务器版本:', error.message);
        }
    }
    
    /**
     * 移动端特殊处理
     */
    function handleMobileCache() {
        const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
        
        if (isMobile) {
            console.log('📱 检测到移动设备，应用移动端缓存策略');
            
            // 移动端更激进的缓存清除
            if (VERSION_CONFIG.forceRefresh) {
                // 添加随机参数到当前URL
                const url = new URL(window.location);
                url.searchParams.set('_t', Date.now());
                url.searchParams.set('_v', VERSION_CONFIG.version);
                
                if (url.toString() !== window.location.href) {
                    console.log('🔄 移动端强制刷新URL');
                    window.location.replace(url.toString());
                    return;
                }
            }
            
            // 监听页面可见性变化
            document.addEventListener('visibilitychange', () => {
                if (!document.hidden) {
                    console.log('📱 页面重新可见，检查更新');
                    setTimeout(checkServerVersion, 1000);
                }
            });
        }
    }
    
    /**
     * 初始化缓存破坏器
     */
    function init() {
        console.log('🚀 初始化缓存破坏器');
        
        // 检查是否需要强制刷新
        if (checkForceRefresh()) {
            return; // 如果需要刷新，直接返回
        }
        
        // 处理移动端缓存
        handleMobileCache();
        
        // 添加版本信息
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', addVersionInfo);
        } else {
            addVersionInfo();
        }
        
        // 定期检查服务器版本
        setInterval(checkServerVersion, VERSION_CONFIG.checkInterval);
        
        // 页面卸载时清理
        window.addEventListener('beforeunload', () => {
            console.log('📄 页面卸载，清理缓存破坏器');
        });
        
        console.log('✅ 缓存破坏器初始化完成');
    }
    
    // 导出全局函数
    window.cacheBuster = {
        clearCache: clearAllCache,
        forceRefresh: () => {
            clearAllCache();
            window.location.reload(true);
        },
        checkVersion: checkServerVersion,
        version: VERSION_CONFIG.version
    };
    
    // 初始化
    init();
    
})();