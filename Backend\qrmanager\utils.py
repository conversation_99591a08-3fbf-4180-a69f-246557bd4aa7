from django.contrib.contenttypes.models import ContentType
from django.core import serializers
import json
from django.utils import timezone
from django.contrib.auth.models import AnonymousUser
import qrcode
import io
import base64
from rest_framework.views import exception_handler
from rest_framework.exceptions import APIException, AuthenticationFailed, NotAuthenticated, PermissionDenied, Throttled
from django.utils.translation import gettext_lazy as _
import logging
from django.core.cache import cache
import datetime

logger = logging.getLogger('qrmanager')

class LoggerHelper:
    @staticmethod
    def serialize_object(obj):
        """将对象序列化为字典"""
        if hasattr(obj, '__dict__'):
            result = {}
            for key, value in obj.__dict__.items():
                if not key.startswith('_'):  # 跳过私有属性
                    if isinstance(value, (datetime.datetime, datetime.date)):
                        result[key] = value.isoformat()
                    elif hasattr(value, '__dict__'):
                        result[key] = LoggerHelper.serialize_object(value)
                    else:
                        result[key] = value
            return result
        return str(obj)

    @staticmethod
    def should_log_action(action_type):
        """检查是否应该记录特定类型的操作"""
        # 从缓存获取配置
        config = cache.get('logging_config')

        print(f"检查是否记录操作: {action_type}, 缓存配置: {config}")

        if config is None:
            try:
                # 延迟导入LoggingConfiguration，避免循环导入
                from .models import LoggingConfiguration

                # 从数据库加载配置
                config = {
                    item.action_type: item.is_enabled
                    for item in LoggingConfiguration.objects.all()
                }
                print(f"从数据库加载配置: {config}")

                # 缓存配置，有效期1小时
                cache.set('logging_config', config, 3600)
            except Exception as e:
                print(f"加载日志配置出错: {e}")
                # 如果数据库查询失败，使用默认配置
                config = {
                    'create': True,
                    'update': True,
                    'delete': True,
                    'login': True,
                    'logout': True,
                    'error': True,
                    'permission_change': True,
                    'system_config': True,
                }
                print(f"使用默认配置: {config}")

        # 根据配置决定是否记录
        # 如果操作类型不在配置中，默认不记录
        result = config.get(action_type, False)
        print(f"操作类型 {action_type} 是否记录: {result}")
        return result

    @staticmethod
    def log_operation(user, action, description, status='success', **kwargs):
        """
        记录用户操作日志

        参数:
            user: 用户对象或用户ID
            action: 操作类型
            description: 操作描述
            status: 操作状态，默认为成功
            **kwargs: 额外参数，如request、extra_data等
        """
        # 提取操作类型
        action_type = None

        # 处理常见操作类型
        if 'create' in action.lower():
            action_type = 'create'
        elif 'update' in action.lower() or 'edit' in action.lower() or 'modify' in action.lower():
            action_type = 'update'
        elif 'delete' in action.lower() or 'remove' in action.lower():
            action_type = 'delete'
        elif 'login' in action.lower():
            action_type = 'login'
        elif 'logout' in action.lower():
            action_type = 'logout'
        elif 'view' in action.lower() or 'get' in action.lower():
            action_type = 'view'
        elif 'export' in action.lower():
            action_type = 'export'
        elif 'import' in action.lower():
            action_type = 'import'
        elif 'print' in action.lower():
            action_type = 'print'
        elif 'generate' in action.lower():
            action_type = 'generate'
        elif 'permission' in action.lower():
            action_type = 'permission_change'
        elif 'config' in action.lower():
            action_type = 'system_config'
        elif 'api' in action.lower():
            action_type = 'api_request'
        elif 'file_upload' in action.lower() or 'upload' in action.lower():
            action_type = 'file_upload'
        elif 'file_download' in action.lower() or 'download' in action.lower():
            action_type = 'file_download'
        elif 'bulk' in action.lower():
            action_type = 'bulk_operation'
        elif 'error' in action.lower():
            action_type = 'error'

        # 如果无法识别操作类型，不记录日志
        if action_type is None:
            print(f"无法识别的操作类型: {action}")
            return

        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action(action_type):
            print(f"根据配置不记录操作类型: {action_type}")
            return

        # 从kwargs中获取额外参数
        request = kwargs.get('request')
        extra_data = kwargs.get('extra_data')

        # 获取客户端IP地址
        ip_address = None
        if request:
            # 从请求中获取IP地址
            x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
            if x_forwarded_for:
                ip_address = x_forwarded_for.split(',')[0].strip()
            else:
                ip_address = request.META.get('REMOTE_ADDR')

        # 延迟导入OperationLog，避免循环导入
        from .models import OperationLog

        # 创建操作日志
        OperationLog.objects.create(
            user=user,
            action=action,
            description=description,
            ip_address=ip_address,
            browser=request.META.get('HTTP_USER_AGENT') if request else None,
            os=request.META.get('HTTP_USER_AGENT') if request else None,
            status=status,
            extra_data=extra_data
        )

    @staticmethod
    def log_model_operation(user, instance, operation_type, description=None, old_data=None, new_data=None, extra_data=None, status='success'):
        """记录模型操作日志"""
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action(operation_type):
            return

        try:
            # 延迟导入OperationLog，避免循环导入
            from .models import OperationLog

            # 序列化实例数据
            instance_data = LoggerHelper.serialize_object(instance) if instance else None

            # 序列化额外数据
            if extra_data:
                extra_data = {
                    k: LoggerHelper.serialize_object(v) if hasattr(v, '__dict__') else v
                    for k, v in extra_data.items()
                }

            # 获取实例信息（安全处理None情况）
            instance_class_name = instance.__class__.__name__ if instance else 'Unknown'
            instance_pk = instance.pk if instance else None

            # 创建日志记录
            log_data = {
                'user': user.username if user else None,
                'action': operation_type,
                'description': description or f"{operation_type} {instance_class_name}",
                'target_type': instance_class_name if instance else None,
                'target_id': instance_pk,
                'old_data': old_data,
                'new_data': new_data,
                'extra_data': extra_data,
                'status': status
            }

            # 记录日志
            OperationLog.objects.create(
                user=user,
                action=f"{instance_class_name.lower() if instance else 'bulk'}_{operation_type}",
                description=log_data['description'],
                status=status,
                extra_data=log_data
            )

        except Exception as e:
            print(f"记录操作日志失败: {str(e)}")

    @staticmethod
    def log_auth_operation(user, operation_type, status='success', extra_data=None):
        """记录认证相关操作日志"""
        # 检查是否应该记录此类操作
        if operation_type in ['login', 'logout'] and not LoggerHelper.should_log_action(operation_type):
            return

        try:
            # 延迟导入OperationLog，避免循环导入
            from .models import OperationLog

            # 序列化额外数据
            if extra_data:
                extra_data = {
                    k: LoggerHelper.serialize_object(v) if hasattr(v, '__dict__') else v
                    for k, v in extra_data.items()
                }

            # 创建日志记录
            OperationLog.objects.create(
                user=user,
                action=f"auth_{operation_type}",
                description=f"用户认证操作: {operation_type}",
                status=status,
                extra_data={
                    'user': user.username if user else None,
                    'operation_type': operation_type,
                    'extra_data': extra_data,
                    'status': status
                }
            )

        except Exception as e:
            print(f"记录认证操作日志失败: {str(e)}")

    @staticmethod
    def log_file_operation(user, operation_type, file_name, file_size=None, **kwargs):
        """
        记录文件操作
        :param user: 操作用户
        :param operation_type: 操作类型 (upload/download)
        :param file_name: 文件名
        :param file_size: 文件大小
        :param kwargs: 额外数据
        """
        # 检查是否应该记录此类操作
        action_type = 'file_upload' if operation_type == 'upload' else 'file_download'
        if not LoggerHelper.should_log_action(action_type):
            return

        description = f"{'上传' if operation_type == 'upload' else '下载'}文件: {file_name}"

        extra_data = {
            'file_name': file_name,
            'file_size': file_size,
            **kwargs
        }

        LoggerHelper.log_operation(
            user=user,
            action=f"file_{operation_type}",
            description=description,
            **extra_data
        )

    @staticmethod
    def log_permission_change(user, target_user, permissions_before, permissions_after):
        """记录权限变更日志"""
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action('permission_change'):
            return

        try:
            # 延迟导入OperationLog，避免循环导入
            from .models import OperationLog

            # 创建日志记录
            OperationLog.objects.create(
                user=user,
                action="permission_change",
                description=f"修改用户 {target_user.username} 的权限",
                status='success',
                extra_data={
                    'user': user.username,
                    'target_user': target_user.username,
                    'permissions_before': permissions_before,
                    'permissions_after': permissions_after
                }
            )

        except Exception as e:
            print(f"记录权限变更日志失败: {str(e)}")

    @staticmethod
    def log_system_config(user, config_name, old_value, new_value, **kwargs):
        """
        记录系统配置变更
        :param user: 操作用户
        :param config_name: 配置项名称
        :param old_value: 原值
        :param new_value: 新值
        :param kwargs: 额外数据
        """
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action('system_config'):
            return

        description = f"修改系统配置: {config_name}"

        extra_data = {
            'config_name': config_name,
            'old_value': old_value,
            'new_value': new_value,
            **kwargs
        }

        LoggerHelper.log_operation(
            user=user,
            action="system_config",
            description=description,
            **extra_data
        )

    @staticmethod
    def log_bulk_operation(user, model_class, operation_type, affected_ids, **kwargs):
        """
        记录批量操作
        :param user: 操作用户
        :param model_class: 模型类
        :param operation_type: 操作类型 (bulk_create/bulk_update/bulk_delete)
        :param affected_ids: 受影响的记录ID列表
        :param kwargs: 额外数据
        """
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action('bulk_operation'):
            return

        model_name = model_class.__name__
        description = f"批量{operation_type.split('_')[1]}{model_name}: {len(affected_ids)}条记录"

        extra_data = {
            'model': model_name,
            'affected_ids': affected_ids,
            **kwargs
        }

        LoggerHelper.log_operation(
            user=user,
            action=operation_type,
            description=description,
            **extra_data
        )

    @staticmethod
    def log_api_request(user, endpoint, method, request_data, response_data, status_code, **kwargs):
        """
        记录API请求
        :param user: 操作用户
        :param endpoint: API端点
        :param method: 请求方法
        :param request_data: 请求数据
        :param response_data: 响应数据
        :param status_code: 状态码
        :param kwargs: 额外数据
        """
        # 检查是否应该记录此类操作
        if not LoggerHelper.should_log_action('api_request'):
            return

        description = f"API请求: {method} {endpoint}"

        extra_data = {
            'endpoint': endpoint,
            'method': method,
            'request_data': request_data,
            'response_data': response_data,
            'status_code': status_code,
            **kwargs
        }

        status = 'success' if 200 <= status_code < 300 else 'error'

        LoggerHelper.log_operation(
            user=user,
            action="api_request",
            description=description,
            status=status,
            **extra_data
        )

# 此函数已移至 qrcode_utils.py，请使用那里的高质量版本
# def generate_qrcode(data, box_size=10, border=0):
#     """
#     统一的二维码生成函数，确保所有地方生成的二维码参数一致
#
#     参数:
#         data (str): 二维码中包含的数据
#         box_size (int): 二维码中每个小方块的像素大小，默认10
#         border (int): 二维码边框宽度，默认0（无边框）
#
#     返回:
#         tuple: (PIL图像对象, base64编码的图像字符串)
#     """
#     # 此函数已移至 qrcode_utils.py，请使用那里的高质量版本
#     from .qrcode_utils import generate_qrcode
#     return generate_qrcode(data, box_size, border)

def custom_exception_handler(exc, context):
    """
    自定义异常处理器

    处理API异常并记录日志
    """
    # 调用REST框架默认的异常处理器
    response = exception_handler(exc, context)

    # 如果异常未被处理，则返回None，让Django处理
    if response is None:
        return None

    # 获取请求和视图信息
    request = context.get('request')
    view = context.get('view')

    # 记录异常信息
    if isinstance(exc, APIException):
        logger.error(
            f"API异常: {exc.__class__.__name__}, "
            f"状态码: {response.status_code}, "
            f"详情: {exc.detail}, "
            f"路径: {request.path if request else 'unknown'}, "
            f"方法: {request.method if request else 'unknown'}, "
            f"视图: {view.__class__.__name__ if view else 'unknown'}"
        )

    # 自定义响应格式
    if response is not None:
        error_data = {
            'error': True,
            'code': response.status_code,
            'message': _get_error_message(exc),
            'detail': response.data
        }
        response.data = error_data

    return response

def _get_error_message(exc):
    """获取友好的错误消息"""
    if isinstance(exc, AuthenticationFailed):
        return _("认证失败，请提供有效的凭据")
    elif isinstance(exc, NotAuthenticated):
        return _("未认证，请先登录")
    elif isinstance(exc, PermissionDenied):
        return _("权限不足，无法执行此操作")
    elif isinstance(exc, Throttled):
        return _("请求过于频繁，请稍后再试")
    else:
        return _("请求处理失败")

def is_safe_url(url, allowed_hosts=None, allowed_paths=None):
    """
    验证URL是否安全，防止开放重定向漏洞

    参数:
        url (str): 要验证的URL
        allowed_hosts (list): 允许的主机列表，默认为None（只允许相对URL）
        allowed_paths (list): 允许的路径前缀列表，默认为None（允许所有路径）

    返回:
        bool: URL是否安全
    """
    from urllib.parse import urlparse, urljoin
    from django.http import HttpRequest
    from django.conf import settings

    # 如果URL为空，则不安全
    if not url:
        return False

    # 创建一个虚拟请求对象，用于构建绝对URL
    request = HttpRequest()
    request.META['SERVER_NAME'] = settings.ALLOWED_HOSTS[0] if settings.ALLOWED_HOSTS else 'localhost'
    request.META['SERVER_PORT'] = '80'

    # 获取当前站点的URL
    current_site_url = f"http://{request.META['SERVER_NAME']}:{request.META['SERVER_PORT']}"

    # 将相对URL转换为绝对URL
    url_parts = urlparse(urljoin(current_site_url, url))

    # 检查URL是否为相对URL（没有网络位置部分）
    # 或者主机是否在允许的主机列表中
    is_valid_host = not url_parts.netloc or url_parts.netloc in (allowed_hosts or [])

    # 如果主机无效，则URL不安全
    if not is_valid_host:
        return False

    # 如果指定了允许的路径前缀，则检查URL路径是否以允许的前缀开头
    if allowed_paths:
        path = url_parts.path
        return any(path.startswith(allowed_path) for allowed_path in allowed_paths)

    # 默认情况下，如果主机有效，则URL安全
    return True