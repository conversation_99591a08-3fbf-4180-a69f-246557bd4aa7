# 工作人员导入错误修复指南

## 🐛 问题描述

用户在使用模板导入工作人员时遇到错误：
```
导入失败：文件处理失败：'NoneType' object has no attribute 'id'
```

## 🔍 问题原因

这个错误通常由以下原因引起：

1. **科室名称不匹配**：Excel文件中的科室名称与数据库中的科室名称不完全一致
2. **人员类型不存在**：Excel文件中的人员类型在系统字典中不存在或未激活
3. **数据关联失败**：外键对象为None时，Django ORM试图访问其id属性

## 🔧 解决方案

### 1. 代码修复

已对 `Backend/qrmanager/views.py` 中的 `StaffBulkImportView` 进行以下改进：

#### 增强的数据验证
- 添加了更详细的错误信息，包括可用选项列表
- 增加了外键对象的空值检查
- 改进了异常处理机制

#### 新增文件验证功能
- 支持导入前的文件验证
- 提供详细的验证报告
- 区分错误和警告信息

### 2. 数据检查工具

创建了数据检查脚本 `Backend/scripts/check_staff_import_data.py`，用于：

- 检查科室数据完整性
- 验证人员类型字典数据
- 检查职称数据
- 生成标准导入模板
- 创建示例数据

### 3. 使用步骤

#### 步骤1：运行数据检查
```bash
cd Backend
python manage.py shell < scripts/check_staff_import_data.py
```

#### 步骤2：修复数据问题
如果检查发现问题，脚本会提供修复建议：
- 创建缺失的科室
- 添加人员类型字典项
- 补充职称数据

#### 步骤3：使用标准模板
脚本会生成 `staff_import_template.xlsx` 模板文件，包含：
- 正确的表头格式
- 示例数据
- 可用选项列表

#### 步骤4：验证文件
在实际导入前，使用验证功能检查文件：
1. 选择Excel文件
2. 点击"验证文件"按钮
3. 查看验证结果
4. 修复发现的问题

#### 步骤5：执行导入
验证通过后，点击"开始导入"进行实际导入。

## 📋 Excel文件格式要求

### 必需字段
- **工号**：唯一标识，不能重复
- **姓名**：员工姓名
- **人员类型**：必须与系统中的人员类型完全匹配
- **科室**：必须与系统中的科室名称完全匹配

### 可选字段
- **职称**：如果提供，必须与系统中的职称匹配

### 格式示例
```
工号    | 姓名  | 人员类型 | 职称     | 科室
001     | 张三  | 医生     | 主治医师 | 内科
002     | 李四  | 护士     | 主管护师 | 外科
003     | 王五  | 技师     | 主管技师 | 检验科
```

## ⚠️ 注意事项

1. **数据匹配**：确保Excel中的科室和人员类型名称与系统中完全一致（不区分大小写）
2. **工号唯一性**：工号必须唯一，重复的工号会更新现有记录
3. **必填字段**：工号、姓名、人员类型、科室为必填字段
4. **职称可选**：职称字段可以为空，但如果填写必须匹配系统数据

## 🚨 常见错误及解决方法

### 错误1：科室不存在
```
行 2: 科室 '内分泌科' 不存在。可用科室: 内科, 外科, 儿科...
```
**解决方法**：
- 检查科室名称拼写
- 在系统中添加缺失的科室
- 使用检查工具查看可用科室列表

### 错误2：人员类型不存在
```
行 3: 人员类型 '医师' 不存在或未激活。可用类型: 医生, 护士, 技师...
```
**解决方法**：
- 使用系统中已有的人员类型名称
- 在字典管理中添加新的人员类型
- 激活被禁用的人员类型

### 错误3：职称不匹配
```
行 4: 职称 '主任' 不存在或未激活
```
**解决方法**：
- 使用完整的职称名称（如"主任医师"而不是"主任"）
- 在字典管理中添加缺失的职称
- 留空职称字段（职称为可选字段）

## 🔄 更新日志

### v1.1 (当前版本)
- ✅ 修复了 'NoneType' object has no attribute 'id' 错误
- ✅ 增加了详细的错误信息和可用选项提示
- ✅ 添加了文件验证功能
- ✅ 创建了数据检查和修复工具
- ✅ 改进了异常处理机制

### v1.0 (原始版本)
- 基础的Excel导入功能
- 简单的数据验证

## 📞 技术支持

如果仍然遇到问题，请提供以下信息：
1. 具体的错误信息
2. 使用的Excel文件示例
3. 数据检查工具的输出结果
4. 系统中现有的科室和人员类型数据

这将帮助快速定位和解决问题。
