#!/usr/bin/env python
"""
安全增强措施 - 立即可实施的安全改进
针对公网API暴露的安全风险提供具体解决方案
"""

import time
import hashlib
import json
from django.core.cache import cache
from django.http import JsonResponse
from django.utils.deprecation import MiddlewareMixin
from django.conf import settings
import logging

logger = logging.getLogger('security')

class EnhancedAPISecurityMiddleware(MiddlewareMixin):
    """
    增强版API安全中间件 - 针对公网暴露的安全加固
    """
    
    # 🔒 严格的速率限制配置
    RATE_LIMITS = {
        'verify_qrcode': {
            'requests': 10,      # 每分钟10次验证
            'per_seconds': 60,
            'burst': 3           # 突发允许3次
        },
        'submit_evaluation': {
            'requests': 5,       # 每分钟5次提交
            'per_seconds': 60,
            'burst': 2
        },
        'global': {
            'requests': 50,      # 全局每分钟50次
            'per_seconds': 60,
            'burst': 10
        }
    }
    
    # 🔒 必需的安全头部
    REQUIRED_HEADERS = {
        'User-Agent': True,      # 必须有User-Agent
        'Accept': True,          # 必须有Accept头
    }
    
    # 🔒 可选的API密钥验证
    API_KEYS = {
        'hospital-qr-v1': {
            'name': '医院二维码服务',
            'rate_limit_multiplier': 2,  # 有密钥的请求限制放宽2倍
            'expires': None
        }
    }
    
    # 🔒 IP黑名单（可动态更新）
    IP_BLACKLIST_CACHE_KEY = 'security:ip_blacklist'
    
    # 🔒 异常检测阈值
    ANOMALY_THRESHOLDS = {
        'rapid_requests': 20,    # 10秒内超过20次请求
        'failed_attempts': 10,   # 1分钟内超过10次失败
        'suspicious_patterns': 5  # 可疑模式检测
    }
    
    def process_request(self, request):
        """处理请求前的安全检查"""
        # 只处理公开API
        if not self._is_public_api(request):
            return None
        
        client_ip = self._get_client_ip(request)
        
        # 1. IP黑名单检查
        if self._is_ip_blacklisted(client_ip):
            logger.warning(f"黑名单IP访问被拒绝: {client_ip}")
            return JsonResponse({
                'status': 'error',
                'message': '访问被拒绝',
                'error_code': 'IP_BLACKLISTED'
            }, status=403)
        
        # 2. 基础安全头部检查
        if not self._validate_headers(request):
            logger.warning(f"缺少必需头部: {client_ip}")
            return JsonResponse({
                'status': 'error',
                'message': '请求格式不正确',
                'error_code': 'INVALID_HEADERS'
            }, status=400)
        
        # 3. 异常行为检测
        if self._detect_anomalies(request, client_ip):
            logger.warning(f"检测到异常行为: {client_ip}")
            return JsonResponse({
                'status': 'error',
                'message': '检测到异常访问模式',
                'error_code': 'ANOMALY_DETECTED'
            }, status=429)
        
        # 4. 增强速率限制
        rate_limit_result = self._enhanced_rate_limit(request, client_ip)
        if rate_limit_result:
            return rate_limit_result
        
        # 5. 记录正常访问
        self._log_access(request, client_ip)
        
        return None
    
    def _is_public_api(self, request):
        """判断是否为公开API"""
        return request.path.startswith('/api/v1/public/')
    
    def _get_client_ip(self, request):
        """获取真实客户端IP"""
        # 优先从nginx代理头获取
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # 取第一个IP（最原始的客户端IP）
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR', '0.0.0.0')
        
        # 验证IP格式
        try:
            import ipaddress
            ipaddress.ip_address(ip)
            return ip
        except ValueError:
            logger.warning(f"无效IP格式: {ip}")
            return '0.0.0.0'
    
    def _is_ip_blacklisted(self, ip):
        """检查IP是否在黑名单中"""
        blacklist = cache.get(self.IP_BLACKLIST_CACHE_KEY, set())
        return ip in blacklist
    
    def _validate_headers(self, request):
        """验证必需的HTTP头部"""
        for header, required in self.REQUIRED_HEADERS.items():
            if required:
                header_key = f'HTTP_{header.upper().replace("-", "_")}'
                if not request.META.get(header_key):
                    return False
        
        # 检查User-Agent是否为已知的恶意爬虫
        user_agent = request.META.get('HTTP_USER_AGENT', '').lower()
        malicious_patterns = ['sqlmap', 'nikto', 'nmap', 'masscan', 'zap']
        if any(pattern in user_agent for pattern in malicious_patterns):
            return False
        
        return True
    
    def _detect_anomalies(self, request, client_ip):
        """异常行为检测"""
        current_time = int(time.time())
        
        # 检测快速连续请求
        rapid_key = f"security:rapid:{client_ip}"
        rapid_requests = cache.get(rapid_key, [])
        
        # 清理10秒前的记录
        rapid_requests = [t for t in rapid_requests if current_time - t < 10]
        rapid_requests.append(current_time)
        
        if len(rapid_requests) > self.ANOMALY_THRESHOLDS['rapid_requests']:
            # 自动加入黑名单5分钟
            self._add_to_blacklist(client_ip, 300, '快速连续请求')
            return True
        
        cache.set(rapid_key, rapid_requests, 10)
        return False
    
    def _enhanced_rate_limit(self, request, client_ip):
        """增强版速率限制"""
        # 确定限制类型
        if 'verify' in request.path:
            limit_type = 'verify_qrcode'
        elif 'evaluation' in request.path:
            limit_type = 'submit_evaluation'
        else:
            limit_type = 'global'
        
        config = self.RATE_LIMITS[limit_type]
        
        # 检查API密钥
        api_key = request.headers.get('X-API-Key')
        multiplier = 1
        if api_key in self.API_KEYS:
            multiplier = self.API_KEYS[api_key]['rate_limit_multiplier']
        
        # 应用速率限制
        max_requests = config['requests'] * multiplier
        window = config['per_seconds']
        
        cache_key = f"rate_limit:{client_ip}:{limit_type}"
        current_time = int(time.time())
        window_start = current_time - window
        
        # 获取请求历史
        requests = cache.get(cache_key, [])
        requests = [t for t in requests if t > window_start]
        
        if len(requests) >= max_requests:
            # 记录速率限制事件
            logger.warning(f"速率限制触发: {client_ip}, 类型: {limit_type}, 请求数: {len(requests)}")
            
            return JsonResponse({
                'status': 'error',
                'message': '请求过于频繁，请稍后再试',
                'error_code': 'RATE_LIMITED',
                'retry_after': window
            }, status=429)
        
        # 更新请求历史
        requests.append(current_time)
        cache.set(cache_key, requests, window)
        
        return None
    
    def _add_to_blacklist(self, ip, duration, reason):
        """将IP添加到黑名单"""
        blacklist = cache.get(self.IP_BLACKLIST_CACHE_KEY, set())
        blacklist.add(ip)
        cache.set(self.IP_BLACKLIST_CACHE_KEY, blacklist, duration)
        
        # 记录黑名单事件
        logger.error(f"IP加入黑名单: {ip}, 原因: {reason}, 时长: {duration}秒")
        
        # 可选：发送告警通知
        self._send_security_alert(f"IP {ip} 因 {reason} 被加入黑名单")
    
    def _log_access(self, request, client_ip):
        """记录访问日志"""
        log_data = {
            'ip': client_ip,
            'path': request.path,
            'method': request.method,
            'user_agent': request.META.get('HTTP_USER_AGENT', ''),
            'timestamp': int(time.time())
        }
        
        # 存储到缓存用于分析
        access_log_key = f"access_log:{client_ip}:{int(time.time() // 60)}"
        cache.set(access_log_key, log_data, 3600)  # 保存1小时
    
    def _send_security_alert(self, message):
        """发送安全告警（可扩展）"""
        logger.critical(f"安全告警: {message}")
        # 这里可以集成邮件、短信、钉钉等告警方式

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    安全头部中间件 - 添加完整的安全头部
    """
    
    def process_response(self, request, response):
        """添加安全头部到响应"""
        # 基础安全头部
        security_headers = {
            'X-Content-Type-Options': 'nosniff',
            'X-Frame-Options': 'DENY',
            'X-XSS-Protection': '1; mode=block',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
            'Content-Security-Policy': "default-src 'self'; script-src 'self' 'unsafe-inline'; style-src 'self' 'unsafe-inline';",
        }
        
        # 如果是HTTPS，添加HSTS
        if request.is_secure():
            security_headers['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains'
        
        # 应用头部
        for header, value in security_headers.items():
            response[header] = value
        
        return response

# 🔧 安全工具函数

def get_security_status():
    """获取当前安全状态"""
    blacklist = cache.get('security:ip_blacklist', set())
    
    return {
        'blacklisted_ips': len(blacklist),
        'active_rate_limits': len([k for k in cache._cache.keys() if 'rate_limit:' in str(k)]),
        'security_events_last_hour': len([k for k in cache._cache.keys() if 'access_log:' in str(k)]),
        'status': 'active'
    }

def manual_blacklist_ip(ip, duration=3600, reason="手动添加"):
    """手动将IP加入黑名单"""
    blacklist = cache.get('security:ip_blacklist', set())
    blacklist.add(ip)
    cache.set('security:ip_blacklist', blacklist, duration)
    
    logger.warning(f"手动加入黑名单: {ip}, 原因: {reason}")
    return True

def remove_from_blacklist(ip):
    """从黑名单移除IP"""
    blacklist = cache.get('security:ip_blacklist', set())
    if ip in blacklist:
        blacklist.remove(ip)
        cache.set('security:ip_blacklist', blacklist, 3600)
        logger.info(f"从黑名单移除: {ip}")
        return True
    return False

def get_ip_stats(ip):
    """获取IP的访问统计"""
    current_time = int(time.time())
    stats = {
        'ip': ip,
        'is_blacklisted': ip in cache.get('security:ip_blacklist', set()),
        'recent_requests': 0,
        'rate_limit_status': {}
    }
    
    # 统计最近的请求
    for limit_type in ['verify_qrcode', 'submit_evaluation', 'global']:
        cache_key = f"rate_limit:{ip}:{limit_type}"
        requests = cache.get(cache_key, [])
        recent = [t for t in requests if current_time - t < 60]
        stats['rate_limit_status'][limit_type] = {
            'requests_last_minute': len(recent),
            'limit': EnhancedAPISecurityMiddleware.RATE_LIMITS[limit_type]['requests']
        }
        stats['recent_requests'] += len(recent)
    
    return stats
