"""
登录尝试限制中间件
用于限制登录尝试次数，防止暴力破解
"""

from django.http import HttpResponseRedirect
from django.urls import reverse
from django.contrib import messages
from django.core.cache import cache
import time
import logging

# 获取日志记录器
logger = logging.getLogger('qrmanager')

class LoginAttemptLimitMiddleware:
    """
    登录尝试限制中间件
    限制每个IP的登录尝试次数
    """

    # 每分钟最大登录尝试次数
    MAX_LOGIN_ATTEMPTS_PER_MINUTE = 10

    # 锁定时间（秒）
    LOCKOUT_TIME = 300  # 5分钟

    # 白名单IP列表 - 生产环境中应该为空或仅包含特定的管理IP
    WHITELIST_IPS = [
        # 移除本地IP以确保功能在本地环境也能正常工作
        # '127.0.0.1',
        # '::1',
    ]

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 只处理登录请求
        if request.path == reverse('login') and request.method == 'POST':
            # 获取客户端IP
            client_ip = self.get_client_ip(request)

            # 白名单IP不受限制
            if client_ip in self.WHITELIST_IPS:
                return self.get_response(request)

            # 检查是否被锁定
            lockout_key = f"login_lockout:{client_ip}"
            if cache.get(lockout_key):
                # 计算剩余锁定时间
                lockout_time = cache.get(lockout_key)
                remaining_time = int(lockout_time - time.time())
                if remaining_time > 0:
                    # 记录锁定事件
                    logger.warning(
                        f"登录尝试被锁定 - IP: {client_ip}, 剩余锁定时间: {remaining_time}秒"
                    )

                    # 添加错误消息
                    messages.error(
                        request,
                        f"由于多次登录失败（超过{self.MAX_LOGIN_ATTEMPTS_PER_MINUTE}次），您的账户已被临时锁定。请在{remaining_time}秒后重试。"
                    )
                    # 重定向到登录页面
                    return HttpResponseRedirect(reverse('login'))

            # 获取当前时间戳（秒）
            current_time = int(time.time())

            # 缓存键：IP地址 + 当前分钟
            cache_key = f"login_attempts:{client_ip}:{current_time // 60}"

            # 获取当前计数
            count = cache.get(cache_key, 0)

            # 如果超过限制，锁定账户
            if count >= self.MAX_LOGIN_ATTEMPTS_PER_MINUTE:
                # 设置锁定时间
                lockout_time = current_time + self.LOCKOUT_TIME
                cache.set(lockout_key, lockout_time, self.LOCKOUT_TIME)

                # 记录锁定事件
                logger.warning(
                    f"IP已被锁定 - IP: {client_ip}, 尝试次数: {count}, 锁定时间: {self.LOCKOUT_TIME}秒"
                )

                # 添加错误消息
                messages.error(
                    request,
                    f"由于多次登录失败（超过{self.MAX_LOGIN_ATTEMPTS_PER_MINUTE}次），您的账户已被临时锁定。请在{self.LOCKOUT_TIME}秒后重试。"
                )

                # 重定向到登录页面
                return HttpResponseRedirect(reverse('login'))

            # 增加计数并设置过期时间（1分钟）
            new_count = count + 1
            cache.set(cache_key, new_count, 60)

            # 记录尝试次数
            logger.info(f"登录尝试 - IP: {client_ip}, 当前尝试次数: {new_count}/{self.MAX_LOGIN_ATTEMPTS_PER_MINUTE}")

        # 继续处理请求
        response = self.get_response(request)

        # 如果是登录失败，增加失败计数
        if (request.path == reverse('login') and
            request.method == 'POST' and
            response.status_code == 200):  # 登录失败时返回200，但带有错误消息

            # 检查是否有登录错误消息
            if hasattr(request, '_messages'):
                for message in messages.get_messages(request):
                    # 扩展错误消息匹配，确保能捕获各种登录失败情况
                    if ('用户名或密码不正确' in str(message) or
                        '无效的用户名或密码' in str(message) or
                        '用户名和密码不匹配' in str(message) or
                        '账号或密码错误' in str(message)):

                        # 记录登录失败
                        client_ip = self.get_client_ip(request)
                        current_time = int(time.time())
                        cache_key = f"login_attempts:{client_ip}:{current_time // 60}"
                        count = cache.get(cache_key, 0)
                        new_count = count + 1
                        cache.set(cache_key, new_count, 60)

                        # 记录失败事件
                        username = request.POST.get('username', '未知用户')
                        logger.warning(
                            f"登录失败 - 用户: {username}, IP: {client_ip}, "
                            f"当前尝试次数: {new_count}/{self.MAX_LOGIN_ATTEMPTS_PER_MINUTE}"
                        )

                        # 检查是否需要锁定
                        if new_count >= self.MAX_LOGIN_ATTEMPTS_PER_MINUTE:
                            lockout_key = f"login_lockout:{client_ip}"
                            lockout_time = current_time + self.LOCKOUT_TIME
                            cache.set(lockout_key, lockout_time, self.LOCKOUT_TIME)
                            logger.warning(
                                f"IP已被锁定 - IP: {client_ip}, 尝试次数: {new_count}, "
                                f"锁定时间: {self.LOCKOUT_TIME}秒"
                            )

                        break

        return response

    def get_client_ip(self, request):
        """
        获取客户端IP地址

        参数:
            request: HTTP请求对象

        返回:
            客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip