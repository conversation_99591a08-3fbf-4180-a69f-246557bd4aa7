{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}账号设置{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card fade-in">
                <div class="card-body">
                    <h2 class="card-title text-center mb-4">
                        <i class="fas fa-user-cog me-2"></i>账号设置
                    </h2>
                    <form method="post">
                        {% csrf_token %}
                        <div class="mb-3">
                            <label for="id_first_name" class="form-label">姓</label>
                            <input type="text" name="first_name" class="form-control" id="id_first_name" value="{{ form.first_name.value|default:'' }}">
                        </div>
                        <div class="mb-3">
                            <label for="id_last_name" class="form-label">名</label>
                            <input type="text" name="last_name" class="form-control" id="id_last_name" value="{{ form.last_name.value|default:'' }}">
                        </div>
                        <div class="mb-3">
                            <label for="id_email" class="form-label">电子邮箱</label>
                            <input type="email" name="email" class="form-control" id="id_email" value="{{ form.email.value|default:'' }}">
                        </div>
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>保存设置
                            </button>
                            <a href="{% url 'qrmanager:password_change' %}" class="btn btn-warning">
                                <i class="fas fa-key me-2"></i>修改密码
                            </a>
                            <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}