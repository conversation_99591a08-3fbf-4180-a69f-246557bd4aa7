#!/usr/bin/env python
"""
二维码安全系统测试 - 全面测试新的安全机制
"""
import os
import sys
import django
import requests
import json
import time
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
from qrmanager.models import QRCode
from qrcode_based_security import get_qrcode_security_stats, get_ip_security_profile

class QRCodeSecurityTester:
    """二维码安全系统测试器"""
    
    def __init__(self):
        self.base_url = "http://127.0.0.1:8000"
        self.test_results = []
        
    def log_test(self, test_name, success, message, details=None):
        """记录测试结果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅" if success else "❌"
        print(f"{status} {test_name}: {message}")
        if details:
            print(f"   详情: {details}")
    
    def test_qrcode_rate_limiting(self):
        """测试二维码级别的速率限制"""
        print("\n🔒 测试1: 二维码级别速率限制")
        
        try:
            # 获取测试二维码
            qrcode_obj = QRCode.objects.first()
            if not qrcode_obj:
                self.log_test("二维码速率限制", False, "数据库中没有二维码")
                return
            
            uuid = str(qrcode_obj.code)
            encrypted_param = encrypt_qr_param(uuid)
            
            print(f"   测试二维码: {uuid}")
            print(f"   加密参数: {encrypted_param[:30]}...")
            
            # 测试验证API的速率限制
            verify_url = f"{self.base_url}/api/v1/public/qrcode/verify/"
            success_count = 0
            rate_limited_count = 0
            
            print("   测试验证API速率限制（每分钟10次）:")
            for i in range(15):  # 尝试15次，应该有5次被限制
                try:
                    response = requests.post(
                        verify_url,
                        json={"qr_param": encrypted_param, "client_ip": "127.0.0.1"},
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        success_count += 1
                        print(f"     请求 {i+1}: ✅ 成功")
                    elif response.status_code == 429:
                        rate_limited_count += 1
                        print(f"     请求 {i+1}: 🚫 速率限制")
                    else:
                        print(f"     请求 {i+1}: ❌ 错误 ({response.status_code})")
                    
                    time.sleep(0.5)  # 短暂延迟
                    
                except Exception as e:
                    print(f"     请求 {i+1}: ❌ 异常 - {e}")
            
            # 评估结果
            if success_count > 0 and rate_limited_count > 0:
                self.log_test("验证API速率限制", True, 
                             f"成功: {success_count}, 限制: {rate_limited_count}",
                             f"速率限制正常工作")
            else:
                self.log_test("验证API速率限制", False,
                             f"成功: {success_count}, 限制: {rate_limited_count}",
                             "速率限制可能未正常工作")
            
            # 等待一段时间后测试评价API
            print("\n   等待5秒后测试评价API...")
            time.sleep(5)
            
            # 测试评价API的速率限制（每分钟2次）
            submit_url = f"{self.base_url}/api/v1/public/submit-evaluation/"
            eval_success = 0
            eval_limited = 0
            
            print("   测试评价API速率限制（每分钟2次）:")
            for i in range(5):  # 尝试5次，应该有3次被限制
                try:
                    response = requests.post(
                        submit_url,
                        json={
                            "qr_param": encrypted_param,
                            "comment": f"测试评价 {i+1}",
                            "staff_evaluations": [],
                            "hospital_number": "TEST001",
                            "phone_number": "13800138000"
                        },
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                    
                    if response.status_code == 200:
                        eval_success += 1
                        print(f"     评价 {i+1}: ✅ 成功")
                    elif response.status_code == 429:
                        eval_limited += 1
                        print(f"     评价 {i+1}: 🚫 速率限制")
                    else:
                        print(f"     评价 {i+1}: ❌ 错误 ({response.status_code})")
                        print(f"       响应: {response.text[:100]}")
                    
                    time.sleep(1)  # 延迟1秒
                    
                except Exception as e:
                    print(f"     评价 {i+1}: ❌ 异常 - {e}")
            
            # 评估评价API结果
            if eval_success <= 2 and eval_limited > 0:
                self.log_test("评价API速率限制", True,
                             f"成功: {eval_success}, 限制: {eval_limited}",
                             "评价速率限制正常工作")
            else:
                self.log_test("评价API速率限制", False,
                             f"成功: {eval_success}, 限制: {eval_limited}",
                             "评价速率限制可能过于宽松")
                
        except Exception as e:
            self.log_test("二维码速率限制", False, f"测试异常: {e}")
    
    def test_ip_tracking(self):
        """测试IP追踪功能"""
        print("\n🌐 测试2: IP追踪功能")
        
        try:
            # 获取测试数据
            qrcode_obj = QRCode.objects.first()
            if not qrcode_obj:
                self.log_test("IP追踪", False, "数据库中没有二维码")
                return
            
            encrypted_param = encrypt_qr_param(qrcode_obj.code)
            
            # 发送几个请求来生成IP活动
            verify_url = f"{self.base_url}/api/v1/public/qrcode/verify/"
            
            print("   发送测试请求生成IP活动...")
            for i in range(3):
                try:
                    response = requests.post(
                        verify_url,
                        json={"qr_param": encrypted_param, "client_ip": "127.0.0.1"},
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                    print(f"     请求 {i+1}: {response.status_code}")
                    time.sleep(1)
                except Exception as e:
                    print(f"     请求 {i+1}: 异常 - {e}")
            
            # 检查IP安全档案
            print("   检查IP安全档案...")
            ip_profile = get_ip_security_profile("127.0.0.1")
            
            print(f"   IP: {ip_profile['ip']}")
            print(f"   最近1分钟请求: {ip_profile['activity']['requests_last_minute']}")
            print(f"   最近1小时二维码: {ip_profile['activity']['unique_qrcodes_last_hour']}")
            print(f"   风险评分: {ip_profile['risk_score']}")
            
            if ip_profile['activity']['requests_last_minute'] > 0:
                self.log_test("IP追踪", True, "IP活动记录正常",
                             f"记录到 {ip_profile['activity']['requests_last_minute']} 次请求")
            else:
                self.log_test("IP追踪", False, "未记录到IP活动")
                
        except Exception as e:
            self.log_test("IP追踪", False, f"测试异常: {e}")
    
    def test_qrcode_statistics(self):
        """测试二维码统计功能"""
        print("\n📊 测试3: 二维码统计功能")
        
        try:
            # 获取测试数据
            qrcode_obj = QRCode.objects.first()
            if not qrcode_obj:
                self.log_test("二维码统计", False, "数据库中没有二维码")
                return
            
            uuid = str(qrcode_obj.code)
            
            # 获取二维码安全统计
            print(f"   获取二维码统计: {uuid}")
            stats = get_qrcode_security_stats(uuid)
            
            print(f"   最近1小时访问: {stats['recent_access']['last_hour']}")
            print(f"   最近24小时访问: {stats['recent_access']['last_24_hours']}")
            print(f"   唯一IP数量: {len(stats['recent_access']['unique_ips'])}")
            print(f"   评价限制状态: {stats['rate_limits']['evaluation']['current']}/{stats['rate_limits']['evaluation']['limit']}")
            print(f"   验证限制状态: {stats['rate_limits']['verification']['current']}/{stats['rate_limits']['verification']['limit']}")
            
            if isinstance(stats, dict) and 'uuid' in stats:
                self.log_test("二维码统计", True, "统计数据获取成功",
                             f"24小时访问: {stats['recent_access']['last_24_hours']}")
            else:
                self.log_test("二维码统计", False, "统计数据格式异常")
                
        except Exception as e:
            self.log_test("二维码统计", False, f"测试异常: {e}")
    
    def test_anomaly_detection(self):
        """测试异常检测功能"""
        print("\n🚨 测试4: 异常检测功能")
        
        try:
            # 获取多个不同的二维码
            qrcodes = QRCode.objects.all()[:3]
            if len(qrcodes) < 2:
                self.log_test("异常检测", False, "需要至少2个二维码进行测试")
                return
            
            verify_url = f"{self.base_url}/api/v1/public/qrcode/verify/"
            
            print("   模拟快速切换二维码行为...")
            for i, qrcode_obj in enumerate(qrcodes):
                encrypted_param = encrypt_qr_param(qrcode_obj.code)
                
                try:
                    response = requests.post(
                        verify_url,
                        json={"qr_param": encrypted_param, "client_ip": "127.0.0.1"},
                        headers={'Content-Type': 'application/json'},
                        timeout=5
                    )
                    
                    print(f"     二维码 {i+1}: {response.status_code}")
                    
                    if response.status_code == 429:
                        response_data = response.json()
                        if 'anomaly' in response_data.get('error_code', '').lower():
                            self.log_test("异常检测", True, "检测到异常行为",
                                         "快速切换二维码被识别")
                            return
                    
                    time.sleep(0.5)  # 快速切换
                    
                except Exception as e:
                    print(f"     二维码 {i+1}: 异常 - {e}")
            
            self.log_test("异常检测", True, "异常检测功能运行正常",
                         "未触发异常阈值（正常情况）")
                
        except Exception as e:
            self.log_test("异常检测", False, f"测试异常: {e}")
    
    def test_security_management_tool(self):
        """测试安全管理工具"""
        print("\n🔧 测试5: 安全管理工具")
        
        try:
            from comprehensive_security_manager import ComprehensiveSecurityManager
            
            manager = ComprehensiveSecurityManager()
            
            # 测试仪表板数据
            print("   获取安全仪表板数据...")
            dashboard = manager.get_security_dashboard()
            
            print(f"   系统状态: {dashboard['system_status']['status']}")
            print(f"   活跃二维码: {dashboard['qrcode_security']['active_qrcodes_count']}")
            print(f"   威胁等级: {dashboard['threat_analysis']['threat_level']}")
            
            if dashboard['system_status']['status'] == 'active':
                self.log_test("安全管理工具", True, "管理工具运行正常",
                             f"威胁等级: {dashboard['threat_analysis']['threat_level']}")
            else:
                self.log_test("安全管理工具", False, "管理工具状态异常")
                
        except Exception as e:
            self.log_test("安全管理工具", False, f"测试异常: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始二维码安全系统全面测试")
        print("=" * 80)
        
        start_time = datetime.now()
        
        # 运行所有测试
        self.test_qrcode_rate_limiting()
        self.test_ip_tracking()
        self.test_qrcode_statistics()
        self.test_anomaly_detection()
        self.test_security_management_tool()
        
        # 生成测试报告
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        print("\n" + "=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for r in self.test_results if r['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"总测试数: {total_tests}")
        print(f"通过: {passed_tests}")
        print(f"失败: {failed_tests}")
        print(f"成功率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"测试时长: {duration:.1f}秒")
        
        print("\n详细结果:")
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            print(f"{status} {result['test_name']}: {result['message']}")
        
        if failed_tests == 0:
            print("\n🎉 所有测试通过！二维码安全系统运行正常！")
        else:
            print(f"\n⚠️  有 {failed_tests} 个测试失败，需要检查安全配置")
        
        return passed_tests == total_tests

if __name__ == "__main__":
    tester = QRCodeSecurityTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n🔒 二维码安全系统测试完成 - 系统就绪！")
    else:
        print("\n🚨 二维码安全系统测试发现问题 - 需要调试！")
