<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>前端API测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            padding: 20px;
            max-width: 800px;
            margin: 0 auto;
            line-height: 1.6;
        }
        h1 {
            color: #333;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
        }
        .container {
            background-color: #f9f9f9;
            padding: 15px;
            border-radius: 5px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #4CAF50;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background-color: #45a049;
        }
        pre {
            background-color: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            overflow-x: auto;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .success {
            color: green;
            font-weight: bold;
        }
        .error {
            color: red;
            font-weight: bold;
        }
        #testResults div {
            margin-bottom: 10px;
            padding: 10px;
            border-radius: 4px;
        }
        .test-success {
            background-color: #e6ffe6;
            border-left: 3px solid green;
        }
        .test-error {
            background-color: #ffe6e6;
            border-left: 3px solid red;
        }
        .token-display {
            background-color: #fff3e0;
            border-left: 3px solid #ff9800;
            padding: 10px;
            margin-top: 5px;
            font-family: monospace;
            word-break: break-all;
        }
        .api-note {
            background-color: #e8f4fd;
            border-left: 3px solid #2196F3;
            padding: 10px;
            margin: 15px 0;
            font-size: 0.9em;
        }
        .btn-primary {
            background-color: #007bff;
        }
        .btn-primary:hover {
            background-color: #0069d9;
        }
        .btn-submit {
            background-color: #28a745;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-weight: bold;
        }
        .btn-submit:hover {
            background-color: #218838;
        }
        .data-section {
            margin-bottom: 15px;
        }
        .data-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
            border-bottom: 1px solid #eee;
            padding-bottom: 5px;
        }
        .data-content {
            background: #f5f5f5;
            padding: 10px;
            border-radius: 4px;
            font-family: monospace;
        }
        .staff-rating-container {
            margin-top: 10px;
        }
        .staff-rating-item {
            display: flex;
            align-items: center;
            margin-bottom: 10px;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 4px;
        }
        .staff-rating-name {
            flex: 1;
            font-weight: bold;
        }
        .staff-rating-title {
            flex: 1;
            color: #666;
            font-style: italic;
        }
        .staff-rating-buttons {
            flex: 1;
        }
        .rating-btn {
            margin-right: 5px;
            padding: 5px 10px;
            border: 1px solid #ddd;
            background: white;
            border-radius: 3px;
            cursor: pointer;
        }
        .rating-btn.satisfied {
            border-color: #28a745;
            color: #28a745;
        }
        .rating-btn.unsatisfied {
            border-color: #dc3545;
            color: #dc3545;
        }
        .rating-btn.selected {
            color: white;
        }
        .rating-btn.satisfied.selected {
            background-color: #28a745;
        }
        .rating-btn.unsatisfied.selected {
            background-color: #dc3545;
        }
        .no-data {
            color: #999;
            font-style: italic;
            padding: 10px;
        }
        .result-details {
            margin-top: 10px;
            border-top: 1px dashed #ddd;
            padding-top: 10px;
        }
        .result-item {
            margin-bottom: 3px;
        }
        /* 添加加载指示器样式 */
        .loading-indicator {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 1000;
            justify-content: center;
            align-items: center;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* 添加响应式布局样式 */
        @media (max-width: 768px) {
            .container {
                width: 100%;
                padding: 10px;
            }
            
            input, select, button {
                width: 100%;
                margin-bottom: 10px;
            }
        }
        
        /* 优化按钮点击样式 */
        button {
            transition: background-color 0.2s;
        }
        
        button:active {
            transform: scale(0.98);
        }
    </style>
</head>
<body onload="initPage()">
    <h1>前端API测试工具</h1>
    
    <!-- 添加加载指示器 -->
    <div class="loading-indicator" id="loadingIndicator">
        <div class="spinner"></div>
    </div>
    
    <div class="container">
        <div class="api-note">
            <strong>注意:</strong> 后端已移除 /evaluation/q/ 和 p= 格式的API接口，现在只支持 /q/ 格式和POST请求格式。
        </div>
        
        <div class="form-group">
            <label for="apiUrl">API基础URL:</label>
            <input type="text" id="apiUrl" value="http://10.10.68.110:8000" />
        </div>
        
        <div class="form-group">
            <label for="apiToken">API Token:</label>
            <input type="text" id="apiToken" value="cb5433b" />
        </div>
        
        <div class="form-group">
            <label for="qrParam">二维码加密参数:</label>
            <input type="text" id="qrParam" value="0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5ADM4UzYwMWMlFzYlVGNyYmYiRDMhN2Y1ICI6ISZyVHdh52ZpNnIgwiI4MTMiFTMkFjIgojI0xWYzJCIsIyN5cTNwMTM0ETOxIWL2EmY50CNwQGNtQDM2QWL4MTMiFTMkFjIgojIklWd1Jye.1d11b138" />
        </div>
        
        <div class="form-group">
            <label for="liAnLink">李安格式测试链接:</label>
            <input type="text" id="liAnLink" value="http://10.10.68.110/q/0nIhlTN2MWO2U2NkZWOhF2NzYGOiVTMzITNzcTYzUzY0EWNxQzY1kTZ2ETN1UTZlRmNhRGOhVjN2UGM4czYiBDZmJCI6ISZyVHdh52ZpNnIgwiI4cDOyYDMkFmIgojI0xWYzJCIsICMkNmZmZzNkVmMiJWL5EGM40SNmNGNtEDZmVWL4cDOyYDMkFmIgojIklWd1Jye.ad062878" />
        </div>
        
        <div>
            <button onclick="testQFormat()">测试 /q/ 格式</button>
            <button onclick="testPostFormat()">测试 POST 请求</button>
            <button onclick="testLiAnFormat()" class="btn-primary">测试李安链接</button>
        </div>
    </div>
    
    <h2>测试结果</h2>
    <div id="testResults" class="container"></div>
    
    <h2>API响应</h2>
    <pre id="response"></pre>
    
    <h2>详细数据分析</h2>
    <div id="detailedData" class="container">
        <div class="data-section">
            <h3>科室信息</h3>
            <div id="departmentInfo" class="data-content">暂无数据</div>
        </div>
        
        <div class="data-section">
            <h3>人员类型</h3>
            <div id="staffTypes" class="data-content">暂无数据</div>
        </div>
        
        <div class="data-section">
            <h3>工作人员列表</h3>
            <div id="staffList" class="data-content">暂无数据</div>
        </div>
        
        <div class="data-section">
            <h3>临时令牌</h3>
            <div id="tempToken" class="data-content">暂无数据</div>
        </div>
    </div>
    
    <h2>提交评价测试</h2>
    <div class="container">
        <div class="form-group">
            <label for="comment">评价内容:</label>
            <textarea id="comment" rows="3" placeholder="请输入您的评价内容（选填）"></textarea>
        </div>
        
        <div class="form-group">
            <label for="hospitalNumber">住院号:</label>
            <input type="text" id="hospitalNumber" placeholder="请输入住院号（选填）" />
        </div>
        
        <div class="form-group">
            <label for="phoneNumber">联系方式:</label>
            <input type="text" id="phoneNumber" placeholder="请输入联系方式（选填）" />
        </div>
        
        <div class="form-group">
            <label>对工作人员的评价:</label>
            <div id="staffRatingContainer" class="staff-rating-container">
                <div class="no-data">请先获取工作人员数据</div>
            </div>
        </div>
        
        <div>
            <button onclick="submitEvaluation()" class="btn-submit">提交评价</button>
        </div>
    </div>
    
    <script>
        // 临时数据存储
        const appData = {
            tempToken: null,
            staffList: [],
            staffRatings: {},
            clientIP: null,  // 存储客户端IP
            qrParam: null  // 存储qr_param
        };
        
        // 获取客户端IP地址
        async function getClientIP() {
            console.log("正在获取本地IP...");
            const localIP = "127.0.0.1";
            appData.clientIP = localIP;
            console.log(`使用本地IP: ${localIP}`);
            return localIP;
        }
        
        // 页面加载时获取IP
        window.addEventListener('DOMContentLoaded', async () => {
            await getClientIP();
        });
        
        // 辅助函数：显示响应
        function showResponse(data, isError = false) {
            const responseElement = document.getElementById('response');
            responseElement.className = isError ? 'error' : 'success';
            responseElement.textContent = typeof data === 'object' ? JSON.stringify(data, null, 2) : data;
        }
        
        // 辅助函数：添加测试结果
        function addTestResult(message, isSuccess, token = null) {
            const resultElement = document.createElement('div');
            resultElement.className = isSuccess ? 'test-success' : 'test-error';
            
            let resultHtml = isSuccess ? 
                `<span class="success">✓ 成功</span>: ${message}` : 
                `<span class="error">✗ 失败</span>: ${message}`;
                
            if (token) {
                resultHtml += `<div class="token-display">临时令牌: ${token}</div>`;
            }
            
            resultElement.innerHTML = resultHtml;
            
            const resultsContainer = document.getElementById('testResults');
            resultsContainer.prepend(resultElement);
        }
        
        // 解析并显示详细数据
        function displayDetailedData(data) {
            if (!data || !data.data) {
                return;
            }
            
            // 存储临时令牌
            if (data.data.temp_token) {
                appData.tempToken = data.data.temp_token;
            }
            
            // 显示科室信息
            const departmentInfo = document.getElementById('departmentInfo');
            if (data.data.department) {
                const dept = data.data.department;
                departmentInfo.innerHTML = `
                    <div><strong>ID:</strong> ${dept.id}</div>
                    <div><strong>名称:</strong> ${dept.name}</div>
                    ${dept.description ? `<div><strong>描述:</strong> ${dept.description}</div>` : ''}
                `;
            } else {
                departmentInfo.textContent = '无科室信息';
            }
            
            // 显示人员类型
            const staffTypesElement = document.getElementById('staffTypes');
            if (data.data.staff_types && data.data.staff_types.length > 0) {
                let html = '<ul>';
                data.data.staff_types.forEach(type => {
                    html += `<li><strong>${type.name}</strong> (ID: ${type.id})</li>`;
                });
                html += '</ul>';
                staffTypesElement.innerHTML = html;
            } else {
                staffTypesElement.textContent = '无人员类型数据';
            }
            
            // 显示工作人员列表
            const staffListElement = document.getElementById('staffList');
            if (data.data.staff && data.data.staff.length > 0) {
                // 存储员工数据
                appData.staffList = data.data.staff;
                
                // 按类型分组显示
                const staffByType = {};
                data.data.staff.forEach(staff => {
                    if (!staffByType[staff.staff_type]) {
                        staffByType[staff.staff_type] = [];
                    }
                    staffByType[staff.staff_type].push(staff);
                });
                
                let html = '';
                Object.keys(staffByType).forEach(typeId => {
                    // 找到类型名称
                    const typeName = data.data.staff_types.find(t => t.id == typeId)?.name || '未知类型';
                    
                    html += `<div class="staff-type-group">
                        <h4>${typeName} (${staffByType[typeId].length}人)</h4>
                        <ul>`;
                    
                    staffByType[typeId].forEach(staff => {
                        html += `<li><strong>${staff.name}</strong> ${staff.title ? `- ${staff.title}` : ''} (ID: ${staff.id})</li>`;
                    });
                    
                    html += '</ul></div>';
                });
                
                staffListElement.innerHTML = html;
                
                // 同时更新评价部分
                renderStaffRatingForm(data.data.staff, data.data.staff_types);
            } else {
                staffListElement.textContent = '无工作人员数据';
            }
            
            // 显示临时令牌
            const tempTokenElement = document.getElementById('tempToken');
            if (data.data.temp_token) {
                tempTokenElement.innerHTML = `
                    <div>${data.data.temp_token}</div>
                    ${data.data.expires_at ? `<div><strong>过期时间:</strong> ${data.data.expires_at}</div>` : ''}
                `;
            } else {
                tempTokenElement.textContent = '无临时令牌';
            }
        }
        
        // 渲染工作人员评价表单
        function renderStaffRatingForm(staffList, staffTypes) {
            const container = document.getElementById('staffRatingContainer');
            if (!container || !staffList || staffList.length === 0) {
                container.innerHTML = '<div class="no-data">无工作人员数据</div>';
                return;
            }
            
            let html = '';
            
            // 对员工按类型分组
            const staffByType = {};
            staffList.forEach(staff => {
                if (!staffByType[staff.staff_type]) {
                    staffByType[staff.staff_type] = [];
                }
                staffByType[staff.staff_type].push(staff);
            });
            
            // 为每种类型创建一个组
            Object.keys(staffByType).forEach(typeId => {
                // 找到类型名称
                const typeName = staffTypes.find(t => t.id == typeId)?.name || '未知类型';
                
                html += `<div class="staff-type-heading">${typeName}</div>`;
                
                // 为每个员工创建评价选项
                staffByType[typeId].forEach(staff => {
                    html += `
                        <div class="staff-rating-item" data-staff-id="${staff.id}">
                            <div class="staff-rating-name">${staff.name}</div>
                            <div class="staff-rating-title">${staff.title || ''}</div>
                            <div class="staff-rating-buttons">
                                <button class="rating-btn satisfied" onclick="rateStaff(${staff.id}, true)">满意</button>
                                <button class="rating-btn unsatisfied" onclick="rateStaff(${staff.id}, false)">不满意</button>
                            </div>
                        </div>
                    `;
                });
            });
            
            container.innerHTML = html;
        }
        
        // 评价工作人员
        function rateStaff(staffId, isSatisfied) {
            appData.staffRatings[staffId] = isSatisfied;
            
            // 更新UI
            const staffItem = document.querySelector(`.staff-rating-item[data-staff-id="${staffId}"]`);
            if (staffItem) {
                // 清除之前的选择
                staffItem.querySelectorAll('.rating-btn').forEach(btn => {
                    btn.classList.remove('selected');
                });
                
                // 标记新的选择
                const selectedBtn = isSatisfied 
                    ? staffItem.querySelector('.satisfied')
                    : staffItem.querySelector('.unsatisfied');
                
                if (selectedBtn) {
                    selectedBtn.classList.add('selected');
                }
            }
            
            console.log(`评价工作人员 ${staffId}: ${isSatisfied ? '满意' : '不满意'}`);
            console.log('当前评价:', appData.staffRatings);
        }
        
        // 提交评价
        async function submitEvaluation() {
            if (!appData.tempToken) {
                alert('请先获取临时令牌');
                return;
            }
            
            const staffIds = Object.keys(appData.staffRatings);
            if (staffIds.length === 0) {
                alert('请至少评价一位工作人员');
                return;
            }
            
            // 如果appData.qrParam为空，尝试从输入框获取QR参数
            if (!appData.qrParam) {
                const inputQrParam = document.getElementById('qrParam').value;
                if (inputQrParam) {
                    appData.qrParam = inputQrParam;
                    console.log('提交前从输入框获取QR参数:', inputQrParam);
                } else {
                    alert('缺少二维码参数，请先验证二维码或直接输入');
                    return;
                }
            }
            
            const comment = document.getElementById('comment').value || ""; // 确保有值，即使是空字符串
            const hospitalNumber = document.getElementById('hospitalNumber').value || "";
            const phoneNumber = document.getElementById('phoneNumber').value || "";
            
            const apiUrl = document.getElementById('apiUrl').value;
            const url = `${apiUrl}/api/v1/public/submit-evaluation/`;
            
            // 显示加载指示器
            showLoading();
            
            // 构建评价数据 - 确保字段名称与后端API完全匹配
            const evaluationData = {
                qr_param: appData.qrParam, // 使用qr_param而不是temp_token
                comment: comment, // 必填参数
                staff_evaluations: Object.keys(appData.staffRatings).map(staffId => ({
                    staff_id: parseInt(staffId),
                    is_satisfied: appData.staffRatings[staffId]
                })),
                hospital_number: hospitalNumber, // 正确的参数名
                phone_number: phoneNumber // 正确的参数名
            };
            
            console.log('提交评价数据:', evaluationData);
            
            try {
                const start = Date.now();
                // 使用与Python脚本完全相同的请求格式
                const response = await fetch(url, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(evaluationData)
                });
                
                const time = Date.now() - start;
                
                // 无论成功失败都隐藏加载指示器
                hideLoading();
                
                // 尝试解析响应
                let data;
                try {
                    const responseText = await response.text();
                    data = JSON.parse(responseText);
                } catch (error) {
                    console.error('解析响应失败:', error);
                    showResponse(`解析响应失败: ${error.message}`, true);
                    addTestResult(`评价提交失败: 解析响应失败`, false);
                    return;
                }
                
                showResponse(data);
                
                if (response.ok && data.status === 'success') {
                    addTestResult(`评价提交成功! 用时: ${time}ms<br>
                        <div class="result-details">
                            <div class="result-item">评价人数: ${staffIds.length}</div>
                            <div class="result-item">评价内容: ${comment ? comment : '(无)'}</div>
                            <div class="result-item">住院号: ${hospitalNumber ? hospitalNumber : '(无)'}</div>
                            <div class="result-item">联系方式: ${phoneNumber ? phoneNumber : '(无)'}</div>
                            <div class="result-item">服务器响应: ${data.message || '提交成功'}</div>
                        </div>`, true);
                } else {
                    addTestResult(`评价提交失败! 用时: ${time}ms<br>
                        <div class="result-item">错误信息: ${data.message || '未知错误'}</div>
                        <div class="result-item">详细信息: ${data.detail || '无详细信息'}</div>`, false);
                }
            } catch (error) {
                hideLoading();
                showResponse(`请求出错: ${error.message}`, true);
                addTestResult(`评价提交出错: ${error.message}`, false);
            }
        }
        
        // 防抖函数，用于限制函数调用频率
        function debounce(func, wait) {
            let timeout;
            return function(...args) {
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(this, args), wait);
            };
        }
        
        // 显示加载指示器
        function showLoading() {
            document.getElementById('loadingIndicator').style.display = 'flex';
        }
        
        // 隐藏加载指示器
        function hideLoading() {
            document.getElementById('loadingIndicator').style.display = 'none';
        }
        
        // 测试 /q/ 格式
        async function testQFormat() {
            const apiUrl = document.getElementById('apiUrl').value;
            const apiToken = document.getElementById('apiToken').value;
            const qrParam = document.getElementById('qrParam').value;
            
            const url = `${apiUrl}/api/v1/qrcode/verify/q/${qrParam}/`;
            
            try {
                const headers = {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                };
                
                if (apiToken) {
                    headers['Authorization'] = `Token ${apiToken}`;
                }
                
                console.log(`测试URL: ${url}`);
                const start = Date.now();
                const response = await fetch(url, {
                    method: 'GET',
                    headers: headers,
                    mode: 'cors',
                    cache: 'no-store'
                });
                
                const time = Date.now() - start;
                
                if (response.ok) {
                    const data = await response.json();
                    showResponse(data);
                    displayDetailedData(data); // 显示详细数据
                    
                    // 验证返回数据结构
                    const isValidData = data && data.status === 'success' && data.data;
                    const hasDepartment = isValidData && data.data.department;
                    const hasStaffTypes = isValidData && Array.isArray(data.data.staff_types) && data.data.staff_types.length > 0;
                    const hasStaff = isValidData && Array.isArray(data.data.staff) && data.data.staff.length > 0;
                    // 根据后端代码，临时令牌字段是temp_token而不是token
                    const hasTempToken = isValidData && data.data.temp_token;
                    
                    // 获取临时令牌值
                    let tempTokenValue = hasTempToken ? data.data.temp_token : null;
                    
                    if (isValidData && hasDepartment && hasStaffTypes && hasStaff) {
                        let resultMessage = `格式 /q/ 请求成功! 用时: ${time}ms<br>
                                       科室: ${data.data.department.name}<br>
                                       人员类型数: ${data.data.staff_types.length}<br>
                                       人员数: ${data.data.staff.length}`;
                                       
                        if (!hasTempToken) {
                            resultMessage += `<br><span class="error">注意: 响应中未包含临时令牌!</span>`;
                        } else {
                            // 如果有过期时间，显示过期时间
                            if (data.data.expires_at) {
                                resultMessage += `<br>令牌过期时间: ${data.data.expires_at}`;
                            }
                        }
                        
                        addTestResult(resultMessage, true, tempTokenValue);
                    } else {
                        let missingFields = [];
                        if (!hasDepartment) missingFields.push("科室信息");
                        if (!hasStaffTypes) missingFields.push("人员类型");
                        if (!hasStaff) missingFields.push("人员数据");
                        if (!hasTempToken) missingFields.push("临时令牌");
                        
                        addTestResult(`格式 /q/ 响应格式不完整! 用时: ${time}ms<br>
                                      缺少字段: ${missingFields.join(", ")}`, false, tempTokenValue);
                    }
                } else {
                    const errorText = await response.text();
                    showResponse(`请求失败，状态码: ${response.status}, ${errorText}`, true);
                    addTestResult(`格式 /q/ 请求失败! 状态码: ${response.status}, 用时: ${time}ms`, false);
                }
            } catch (error) {
                showResponse(`请求出错: ${error.message}`, true);
                addTestResult(`格式 /q/ 请求出错: ${error.message}`, false);
            }
        }
        
        // 优化后的测试函数
        async function testPostFormat() {
            showLoading(); // 显示加载指示器
            
            const apiUrl = document.getElementById('apiUrl').value;
            const apiToken = document.getElementById('apiToken').value;
            let qrParam = document.getElementById('qrParam').value;
            
            // 保存当前设置到本地存储，用于下次加载时恢复
            localStorage.setItem('lastApiUrl', apiUrl);
            localStorage.setItem('lastQrParam', qrParam);
            
            // 验证QR参数格式
            const validateQRParam = (param) => {
                if (!param || param.length < 20) {
                    return { valid: false, error: "QR参数长度不足，至少需要20个字符" };
                }
                
                if (!param.includes('.')) {
                    return { valid: false, error: "QR参数缺少分隔符(.)" };
                }
                
                if (!/^[A-Za-z0-9+/=.]+$/.test(param)) {
                    return { valid: false, error: "QR参数包含非法字符，只允许Base64字符集和点号" };
                }
                
                const parts = param.split('.');
                if (parts.length !== 2 || parts[0].length < 16 || parts[1].length < 4) {
                    return { valid: false, error: "QR参数结构不正确，应为两部分，前缀至少16字符，后缀至少4字符" };
                }
                
                return { valid: true };
            };
            
            // 验证并处理QR参数
            const validation = validateQRParam(qrParam);
            if (!validation.valid) {
                showResponse({ status: "error", message: validation.error }, true);
                addTestResult(`QR参数格式错误: ${validation.error}`, false);
                hideLoading();
                return;
            }
            
            // 如果参数开头有等号，移除它（根据后端处理逻辑）
            if (qrParam.startsWith('=')) {
                qrParam = qrParam.substring(1);
                console.log('移除参数开头的等号:', qrParam);
            }
            
            const url = `${apiUrl}/api/v1/public/qrcode/verify/`;
            
            try {
                // 使用更简洁的请求头
                const headers = {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                };
                
                if (apiToken) {
                    headers['Authorization'] = `Token ${apiToken}`;
                }
                
                // 确保有IP地址但不等待其初始化
                if (!appData.clientIP) {
                    appData.clientIP = '127.0.0.1';
                }
                
                const requestData = { 
                    qr_param: qrParam,
                    client_ip: appData.clientIP
                };
                
                console.log(`测试URL: ${url}`);
                console.log(`请求数据:`, requestData);
                
                // 记录性能
                const start = Date.now();
                
                // 使用性能优化的fetch选项
                const response = await fetch(url, {
                    method: 'POST',
                    headers: headers,
                    mode: 'cors',
                    cache: 'no-cache', // 使用no-cache而不是no-store
                    body: JSON.stringify(requestData)
                });
                
                const time = Date.now() - start;
                
                // 读取响应
                const responseText = await response.text();
                let data;
                
                try {
                    data = JSON.parse(responseText);
                } catch (e) {
                    console.error("响应解析失败:", e);
                    data = { status: "error", message: "响应解析失败: " + responseText.substring(0, 100) };
                }
                
                showResponse(data);
                
                // 只有在成功响应时才显示详细数据
                if (data && data.status === 'success' && data.data) {
                    displayDetailedData(data); // 显示详细数据
                    
                    // 更新appData中的QR参数，确保评价时使用相同参数
                    appData.qrParam = qrParam;
                }
                
                // 验证返回数据结构
                const isValidData = data && data.status === 'success' && data.data;
                const hasDepartment = isValidData && data.data.department;
                const hasStaffTypes = isValidData && Array.isArray(data.data.staff_types) && data.data.staff_types.length > 0;
                const hasStaff = isValidData && Array.isArray(data.data.staff) && data.data.staff.length > 0;
                const hasTempToken = isValidData && data.data.temp_token;
                
                let tempTokenValue = hasTempToken ? data.data.temp_token : null;
                
                if (isValidData && hasDepartment && hasStaffTypes && hasStaff) {
                    let resultMessage = `POST 请求成功! 用时: ${time}ms<br>
                               科室: ${data.data.department.name}<br>
                               人员类型数: ${data.data.staff_types.length}<br>
                               人员数: ${data.data.staff.length}`;
                               
                    if (!hasTempToken) {
                        resultMessage += `<br><span class="error">注意: 响应中未包含临时令牌!</span>`;
                    } else {
                        if (data.data.expires_at) {
                            resultMessage += `<br>令牌过期时间: ${data.data.expires_at}`;
                        }
                    }
                    
                    addTestResult(resultMessage, true, tempTokenValue);
                } else if (data && data.status === 'error') {
                    // 显示API返回的错误信息
                    addTestResult(`请求失败! 状态码: ${response.status}, 用时: ${time}ms<br>
                               错误信息: ${data.message || '未知错误'}<br>
                               详细信息: ${data.detail || '无详细信息'}`, false);
                } else {
                    // 无效数据情况下显示缺失字段
                    let missingFields = [];
                    if (!hasDepartment) missingFields.push("科室信息");
                    if (!hasStaffTypes) missingFields.push("人员类型");
                    if (!hasStaff) missingFields.push("人员数据");
                    if (!hasTempToken) missingFields.push("临时令牌");
                    
                    addTestResult(`POST 响应格式不完整! 用时: ${time}ms<br>
                               缺少字段: ${missingFields.join(", ")}`, false, tempTokenValue);
                }
            } catch (error) {
                showResponse(`请求出错: ${error.message}`, true);
                addTestResult(`POST 请求出错: ${error.message}`, false);
            } finally {
                hideLoading(); // 隐藏加载指示器
            }
        }
        
        // 测试李安格式链接
        async function testLiAnFormat() {
            const liAnLink = document.getElementById('liAnLink').value;
            
            try {
                // 解析链接
                const url = new URL(liAnLink);
                const pathMatch = url.pathname.match(/\/q\/([^\/]+)/);
                
                if (!pathMatch || !pathMatch[1]) {
                    showResponse('无效的李安链接格式，应为: http://域名/q/参数', true);
                    addTestResult('链接格式错误: 应为 http://域名/q/参数', false);
                    return;
                }
                
                const qrParam = pathMatch[1];
                console.log('从李安链接提取参数:', qrParam);
                
                // 获取API地址
                const apiUrl = document.getElementById('apiUrl').value;
                const apiToken = document.getElementById('apiToken').value;
                
                // 确保有IP地址
                if (!appData.clientIP) {
                    await getClientIP();
                }
                
                // 使用POST方式验证
                const apiEndpoint = `${apiUrl}/api/v1/public/qrcode/verify/`;
                
                const headers = {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                };
                
                if (apiToken) {
                    headers['Authorization'] = `Token ${apiToken}`;
                }
                
                // 构建请求数据
                const requestData = { 
                    qr_param: qrParam,
                    client_ip: appData.clientIP // 添加IP参数
                };
                
                console.log(`测试URL: ${apiEndpoint}`);
                console.log(`请求数据:`, requestData);
                
                const start = Date.now();
                const response = await fetch(apiEndpoint, {
                    method: 'POST',
                    headers: headers,
                    mode: 'cors',
                    cache: 'no-store',
                    body: JSON.stringify(requestData)
                });
                
                const time = Date.now() - start;
                
                if (response.ok) {
                    const data = await response.json();
                    showResponse(data);
                    displayDetailedData(data); // 显示详细数据
                    
                    // 验证返回数据结构
                    const isValidData = data && data.status === 'success' && data.data;
                    const hasDepartment = isValidData && data.data.department;
                    const hasStaffTypes = isValidData && Array.isArray(data.data.staff_types) && data.data.staff_types.length > 0;
                    const hasStaff = isValidData && Array.isArray(data.data.staff) && data.data.staff.length > 0;
                    const hasTempToken = isValidData && data.data.temp_token;
                    
                    let tempTokenValue = hasTempToken ? data.data.temp_token : null;
                    
                    if (isValidData && hasDepartment && hasStaffTypes && hasStaff) {
                        let resultMessage = `李安链接请求成功! 用时: ${time}ms<br>
                                       科室: ${data.data.department.name}<br>
                                       人员类型数: ${data.data.staff_types.length}<br>
                                       人员数: ${data.data.staff.length}`;
                                       
                        if (!hasTempToken) {
                            resultMessage += `<br><span class="error">注意: 响应中未包含临时令牌!</span>`;
                        } else {
                            if (data.data.expires_at) {
                                resultMessage += `<br>令牌过期时间: ${data.data.expires_at}`;
                            }
                        }
                        
                        addTestResult(resultMessage, true, tempTokenValue);
                    } else {
                        let missingFields = [];
                        if (!hasDepartment) missingFields.push("科室信息");
                        if (!hasStaffTypes) missingFields.push("人员类型");
                        if (!hasStaff) missingFields.push("人员数据");
                        if (!hasTempToken) missingFields.push("临时令牌");
                        
                        addTestResult(`李安链接响应格式不完整! 用时: ${time}ms<br>
                                      缺少字段: ${missingFields.join(", ")}`, false, tempTokenValue);
                    }
                } else {
                    const errorText = await response.text();
                    showResponse(`请求失败，状态码: ${response.status}, ${errorText}`, true);
                    addTestResult(`李安链接请求失败! 状态码: ${response.status}, 用时: ${time}ms`, false);
                }
            } catch (error) {
                showResponse(`请求出错: ${error.message}`, true);
                addTestResult(`李安链接请求出错: ${error.message}`, false);
            }
        }
        
        // 初始化页面
        function initPage() {
            // 获取URL参数
            const urlParams = new URLSearchParams(window.location.search);
            const qrParam = urlParams.get('qr') || urlParams.get('qr_param') || urlParams.get('qrcode');
            
            // 设置API URL
            document.getElementById('apiUrl').value = 'http://10.10.68.110:8000';
            
            // 从URL或输入框获取QR参数
            if (qrParam) {
                // 如果URL中有QR参数，使用URL中的参数
                appData.qrParam = qrParam;
                console.log('从URL保存QR参数:', qrParam);
                
                // 如果URL中有QR参数，自动填充
                document.getElementById('qrParam').value = qrParam;
                
                // 自动使用这个QR参数测试API
                setTimeout(() => {
                    console.log('URL中有QR参数，自动测试API...');
                    testPostFormat();
                }, 500);
            } else {
                // 从输入框获取预设的QR参数
                const inputQrParam = document.getElementById('qrParam').value;
                if (inputQrParam) {
                    appData.qrParam = inputQrParam;
                    console.log('从输入框保存QR参数:', inputQrParam);
                }
            }
            
            // 正确获取客户端IP (异步方式)
            getClientIP().then(ip => {
                appData.clientIP = ip;
                console.log('IP地址初始化完成:', ip);
            });
        }
        
        // 初始化页面时自动填充上次使用的值
        document.addEventListener('DOMContentLoaded', function() {
            // 检索上次使用的设置
            const lastApiUrl = localStorage.getItem('lastApiUrl');
            const lastQrParam = localStorage.getItem('lastQrParam');
            
            // 如果有保存的设置，则填充表单
            if (lastApiUrl) {
                document.getElementById('apiUrl').value = lastApiUrl;
            }
            
            if (lastQrParam) {
                document.getElementById('qrParam').value = lastQrParam;
            }
            
            // 初始化客户端IP
            getClientIP();
        });
    </script>
</body>
</html> 