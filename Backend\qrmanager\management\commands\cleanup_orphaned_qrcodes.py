from django.core.management.base import BaseCommand
from qrmanager.models import QRCode
from django.utils import timezone
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '清除没有关联床位的二维码记录'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将被删除的记录，不实际删除',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        # 查找没有关联床位的二维码
        orphaned_qrcodes = QRCode.objects.filter(bed__isnull=True)
        count = orphaned_qrcodes.count()
        
        if count == 0:
            self.stdout.write(self.style.SUCCESS('没有找到孤立的二维码记录，数据库已经是干净的！'))
            return
        
        # 显示将被删除的记录
        self.stdout.write(self.style.WARNING(f'找到 {count} 个没有关联床位的二维码记录:'))
        for qr in orphaned_qrcodes:
            self.stdout.write(f'  - ID: {qr.id}, 名称: {qr.name}, 创建时间: {qr.created_at}')
        
        if dry_run:
            self.stdout.write(self.style.WARNING('这是一个演习操作，没有记录被删除。'))
            self.stdout.write(self.style.WARNING('如需实际删除这些记录，请移除 --dry-run 参数。'))
            return
        
        # 确认删除
        self.stdout.write(self.style.WARNING('警告: 即将删除上述所有二维码记录。此操作不可撤销！'))
        
        # 记录删除操作开始时间
        start_time = timezone.now()
        
        # 执行删除
        orphaned_qrcodes.delete()
        
        # 记录删除操作结束时间
        end_time = timezone.now()
        duration = (end_time - start_time).total_seconds()
        
        # 输出结果
        self.stdout.write(self.style.SUCCESS(f'成功删除 {count} 个没有关联床位的二维码记录！'))
        self.stdout.write(self.style.SUCCESS(f'操作耗时: {duration:.2f} 秒'))
        
        # 记录日志
        logger.info(f'清除了 {count} 个没有关联床位的二维码记录，耗时 {duration:.2f} 秒') 