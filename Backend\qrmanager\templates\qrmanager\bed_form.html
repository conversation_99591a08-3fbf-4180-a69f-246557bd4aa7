{% extends "qrmanager/base.html" %}

{% block title %}{% if form.instance.pk %}编辑床位{% else %}新增床位{% endif %}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center">{% if form.instance.pk %}编辑床位{% else %}新增床位{% endif %}</h1>
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        {% for field in form %}
                        <div class="mb-3">
                            <label for="{{ field.id_for_label }}" class="form-label">{{ field.label }}</label>
                            {{ field }}
                            {% if field.help_text %}
                            <div class="form-text">{{ field.help_text }}</div>
                            {% endif %}
                            {% if field.errors %}
                            <div class="alert alert-danger mt-1">
                                {{ field.errors }}
                            </div>
                            {% endif %}
                        </div>
                        {% endfor %}
                        <div class="text-center mt-4">
                            <button type="submit" class="btn btn-primary">保存</button>
                            {% if department %}
                            <a href="{% url 'qrmanager:bed_list' %}?department={{ department.id }}" class="btn btn-secondary">返回</a>
                            {% else %}
                            <a href="{% url 'qrmanager:bed_list' %}" class="btn btn-secondary">返回</a>
                            {% endif %}
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
    // 美化表单字段样式
    document.addEventListener('DOMContentLoaded', function() {
        const formFields = document.querySelectorAll('input, select, textarea');
        formFields.forEach(field => {
            if (!field.classList.contains('form-control') && !field.classList.contains('form-select')) {
                if (field.tagName === 'SELECT') {
                    field.classList.add('form-select');
                } else {
                    field.classList.add('form-control');
                }
            }
        });
    });
</script>
{% endblock %}

{% endblock %} 