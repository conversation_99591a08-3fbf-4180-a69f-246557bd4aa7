{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}情感分析 | 医院服务评价系统{% endblock %}

{% block extra_head %}
<!-- 图表导出库 -->
<script src="https://cdn.jsdelivr.net/npm/html2canvas@1.4.1/dist/html2canvas.min.js"></script>
<style>
    /* 隐藏导航栏 - 全面的选择器确保隐藏所有导航元素 */
    .navbar, .navbar-brand, .navbar-nav, #navbarNav, header.container-fluid, nav.navbar {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
    }

    /* 移除可能存在的顶部内边距 */
    body {
        padding-top: 0 !important;
    }

    /* 全局样式优化 */
    :root {
        --primary-gradient: linear-gradient(135deg, #0066ff 0%, #2c82ff 100%);
        --accent-gradient: linear-gradient(135deg, #4776E6 0%, #8E54E9 100%);
        --success-gradient: linear-gradient(135deg, #2ecc71 0%, #1abc9c 100%);
        --warning-gradient: linear-gradient(135deg, #f39c12 0%, #f1c40f 100%);
        --danger-gradient: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
        --light-gradient: linear-gradient(to right, #ECE9E6 0%, #FFFFFF 100%);
        --shadow-sm: 0 2px 8px rgba(0,0,0,0.08);
        --shadow-md: 0 5px 15px rgba(0,0,0,0.1);
        --shadow-lg: 0 10px 25px rgba(0,0,0,0.15);
        --border-radius: 12px;
        --border-radius-lg: 16px;
        --border-radius-sm: 8px;
    }

    body {
        background-color: #f5f8fa;
        color: #333;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, sans-serif;
        margin: 0;
        padding: 0;
    }

    /* 页面顶部样式 - 全新设计 */
    .page-header-wrapper {
        position: relative;
        padding: 30px 0 70px;
        background: var(--primary-gradient);
        margin-bottom: -40px;
        overflow: hidden;
        border-bottom-left-radius: 50% 20px;
        border-bottom-right-radius: 50% 20px;
        box-shadow: var(--shadow-md);
    }

    .page-header-wrapper::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='100' height='100' viewBox='0 0 100 100'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='rgba(255,255,255,.05)' fill-rule='evenodd'/%3E%3C/svg%3E");
        opacity: 0.5;
    }

    .page-header {
        position: relative;
        z-index: 1;
        color: white;
        padding: 0 20px;
    }

    .page-header-title {
        display: flex;
        align-items: center;
        margin-bottom: 10px;
    }

    .page-header-title h1 {
        font-size: 32px;
        font-weight: 700;
        margin: 0;
        text-shadow: 0 2px 4px rgba(0,0,0,0.15);
    }

    .page-header-title .icon {
        width: 50px;
        height: 50px;
        background: rgba(255,255,255,0.2);
        border-radius: 50%;
        margin-right: 15px;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 24px;
        box-shadow: 0 4px 10px rgba(0,0,0,0.1);
    }

    .page-header-subtitle {
        font-size: 16px;
        opacity: 0.8;
        margin-bottom: 15px;
        margin-left: 65px;
        font-weight: 300;
    }

    .action-buttons {
        margin-top: 20px;
        margin-left: 65px;
    }

    .action-buttons .btn {
        background: rgba(255,255,255,0.2);
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 20px;
        margin-right: 10px;
        transition: all 0.3s ease;
        text-decoration: none;
        display: inline-flex;
        align-items: center;
    }

    .action-buttons .btn:hover {
        background: rgba(255,255,255,0.3);
        transform: translateY(-2px);
    }

    .action-buttons .btn i {
        margin-right: 6px;
    }

    /* 优化的页面标题样式 */
    .page-header-hero {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 40px 0 30px 0;
        margin-bottom: 50px;
        position: relative;
        overflow: hidden;
        min-height: 280px;
    }

    .hero-background {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        z-index: 1;
    }

    .hero-pattern {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="75" cy="75" r="1" fill="rgba(255,255,255,0.1)"/><circle cx="50" cy="10" r="0.5" fill="rgba(255,255,255,0.05)"/><circle cx="10" cy="60" r="0.8" fill="rgba(255,255,255,0.08)"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
        opacity: 0.4;
    }

    .hero-gradient {
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: linear-gradient(45deg, rgba(255,255,255,0.1) 0%, transparent 50%, rgba(255,255,255,0.05) 100%);
    }

    .hero-content {
        position: relative;
        z-index: 2;
        padding: 20px 0;
    }

    .hero-badge {
        display: inline-flex;
        align-items: center;
        background: rgba(255,255,255,0.2);
        padding: 8px 16px;
        border-radius: 20px;
        font-size: 0.9rem;
        font-weight: 500;
        margin-bottom: 20px;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255,255,255,0.3);
    }

    .hero-title {
        font-size: 3rem;
        font-weight: 700;
        margin-bottom: 20px;
        text-shadow: 0 2px 4px rgba(0,0,0,0.2);
        display: flex;
        align-items: center;
    }

    .hero-title i {
        color: rgba(255,255,255,0.9);
        text-shadow: 0 0 20px rgba(255,255,255,0.5);
    }

    .hero-description {
        font-size: 1.2rem;
        line-height: 1.6;
        margin-bottom: 30px;
        opacity: 0.9;
        font-weight: 300;
        max-width: 600px;
    }

    /* 简洁的特性标签样式 */
    .hero-features {
        display: flex;
        gap: 15px;
        margin-top: 25px;
        flex-wrap: wrap;
        align-items: center;
    }

    .feature-tag {
        display: inline-flex;
        align-items: center;
        gap: 6px;
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        border-radius: 20px;
        padding: 6px 12px;
        font-size: 13px;
        font-weight: 500;
        color: #fff;
        transition: all 0.2s ease;
    }

    .feature-tag:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-1px);
    }

    .feature-tag i {
        font-size: 14px;
    }

    /* 彩色图标 */
    .feature-tag.analysis i {
        color: #3498db;
    }

    .feature-tag.stats i {
        color: #2ecc71;
    }

    .feature-tag.insights i {
        color: #f39c12;
    }

    /* 英雄区域按钮样式优化 */
    .hero-actions {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        align-items: center;
        justify-content: flex-start;
    }

    .hero-actions .btn {
        transition: all 0.2s ease;
    }

    .hero-actions .btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }



    .action-card {
        background: rgba(255,255,255,0.1);
        backdrop-filter: blur(15px);
        border: 1px solid rgba(255,255,255,0.2);
        border-radius: 20px;
        padding: 30px;
        text-align: center;
        box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        transition: all 0.3s ease;
    }

    .action-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 12px 40px rgba(0,0,0,0.15);
        background: rgba(255,255,255,0.15);
    }

    .action-card .btn-primary {
        background: linear-gradient(45deg, #3498db, #2980b9);
        border: none;
        border-radius: 15px;
        padding: 15px 30px;
        font-weight: 600;
        box-shadow: 0 4px 15px rgba(52, 152, 219, 0.3);
        transition: all 0.3s ease;
    }

    .action-card .btn-primary:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(52, 152, 219, 0.4);
        background: linear-gradient(45deg, #2980b9, #3498db);
    }

    .action-card .btn-outline-light {
        border: 2px solid rgba(255,255,255,0.3);
        color: white;
        border-radius: 15px;
        padding: 12px 25px;
        font-weight: 500;
        transition: all 0.3s ease;
    }

    .action-card .btn-outline-light:hover {
        background: rgba(255,255,255,0.2);
        border-color: rgba(255,255,255,0.5);
        color: white;
        transform: translateY(-2px);
    }

    /* 下拉菜单优化 */
    .dropdown-menu {
        border: none;
        border-radius: 15px;
        padding: 10px 0;
        background: rgba(255,255,255,0.95);
        backdrop-filter: blur(10px);
        box-shadow: 0 10px 40px rgba(0,0,0,0.15);
        min-width: 280px;
    }

    .dropdown-header {
        color: #666;
        font-weight: 600;
        padding: 10px 20px 5px;
        border-bottom: 1px solid #eee;
        margin-bottom: 5px;
    }

    .dropdown-item {
        padding: 12px 20px;
        display: flex;
        align-items: center;
        gap: 10px;
        transition: all 0.2s ease;
    }

    .dropdown-item:hover {
        background: linear-gradient(45deg, #f8f9fa, #e9ecef);
        transform: translateX(5px);
    }

    .dropdown-item strong {
        color: #333;
    }

    .dropdown-item small {
        font-size: 0.8rem;
    }

    /* 响应式设计 */
    @media (max-width: 768px) {
        .hero-title {
            font-size: 2.2rem;
            flex-direction: column;
            text-align: center;
        }

        .hero-features {
            gap: 10px;
            justify-content: center;
        }

        .feature-tag {
            padding: 5px 10px;
            font-size: 12px;
        }

        .feature-tag i {
            font-size: 12px;
        }

        .action-card {
            margin-top: 30px;
            padding: 20px;
        }

        .page-header-hero {
            padding: 40px 0 30px 0;
            min-height: 280px;
        }
    }

    /* 内容区域间距调整 - 强制应用样式 */
    .container-fluid {
        margin-top: 100px !important;  /* 大幅增加顶部间距，避免与英雄区域重叠 */
        padding-top: 50px !important;
    }

    /* 专门针对筛选区域的样式 */
    .container-fluid .card:first-child {
        margin-top: 80px !important;
    }

    /* 确保主内容区域有足够间距 */
    main {
        margin-top: 0;
        padding-top: 0;
    }

    /* 情感分析面板样式 */
    .summary-card {
        border-radius: var(--border-radius);
        overflow: hidden;
        transition: all 0.3s ease;
        box-shadow: var(--shadow-sm);
        border: none;
    }

    .summary-card:hover {
        transform: translateY(-4px);
        box-shadow: var(--shadow-md);
    }

    .chart-container {
        height: 400px;
        margin-bottom: 20px;
    }

    /* 评分样式 */
    .rating-stars {
        color: #FFD700;
        font-weight: bold;
    }

    /* 最近评价表格样式 */
    .table-responsive {
        overflow-x: auto;
    }

    .comment-cell {
        max-width: 250px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        position: relative;
    }

    .comment-cell:hover {
        overflow: visible;
        white-space: normal;
        z-index: 1;
        background-color: #f8f9fa;
        box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        border-radius: 4px;
        padding: 5px;
        position: absolute;
        max-width: 400px;
        word-wrap: break-word;
    }

    /* 表格布局优化 */
    .table th {
        font-weight: 600;
        white-space: nowrap;
    }

    .table th:nth-child(1) {
        width: 120px;
    }

    .table th:nth-child(2) {
        width: 60px;
    }

    .table th:nth-child(3) {
        width: 90px;
    }

    .table th:nth-child(4) {
        width: 260px;
    }

    .table th:nth-child(5),
    .table th:nth-child(6),
    .table th:nth-child(7) {
        width: 100px;
    }

    /* 卡片样式优化 */
    .card {
        border: none;
        box-shadow: var(--shadow-sm);
        border-radius: var(--border-radius);
        transition: all 0.25s ease;
        background-color: white;
    }

    .card:hover {
        box-shadow: var(--shadow-md);
    }

    .card-title {
        font-weight: 600;
        color: #2c3e50;
        font-size: 1.25rem;
        margin-bottom: 1.25rem;
        display: flex;
        align-items: center;
    }

    .card-title i {
        margin-right: 8px;
        color: #3498db;
    }

    /* 渐进加载动画 */
    .fade-in {
        animation: fadeIn 0.6s ease-in-out;
    }

    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
</style>
{% endblock %}

{% block content %}
<!-- 优化的页面标题区域 -->
<div class="page-header-hero">
    <div class="hero-background">
        <div class="hero-pattern"></div>
        <div class="hero-gradient"></div>
    </div>
    <div class="container">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="hero-content">
                    <div class="hero-badge">
                        <i class="fas fa-chart-line me-2" style="color: #e74c3c;"></i>
                        <span>数据洞察</span>
                    </div>
                    <h1 class="hero-title">
                        <i class="fas fa-brain me-3" style="color: #9b59b6;"></i>
                        情感分析统计
                    </h1>
                    <p class="hero-description">
                        深度分析评价数据中的情感倾向，挖掘患者满意度洞察，为医院服务质量提升提供科学依据
                    </p>

                    <!-- 简洁美观的特性标签 - 深色文字版本 -->
                    <div style="display: flex; gap: 12px; margin-top: 20px; flex-wrap: wrap; align-items: center;">
                        <div style="display: inline-flex; align-items: center; gap: 8px; background: rgba(255, 255, 255, 0.9); border: 2px solid #3498db; border-radius: 20px; padding: 10px 16px; font-size: 14px; font-weight: 600; transition: all 0.2s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <i class="fas fa-brain" style="font-size: 16px; color: #3498db;"></i>
                            <span style="color: #2c3e50;">智能分析</span>
                        </div>
                        <div style="display: inline-flex; align-items: center; gap: 8px; background: rgba(255, 255, 255, 0.9); border: 2px solid #2ecc71; border-radius: 20px; padding: 10px 16px; font-size: 14px; font-weight: 600; transition: all 0.2s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <i class="fas fa-chart-line" style="font-size: 16px; color: #2ecc71;"></i>
                            <span style="color: #2c3e50;">实时统计</span>
                        </div>
                        <div style="display: inline-flex; align-items: center; gap: 8px; background: rgba(255, 255, 255, 0.9); border: 2px solid #f39c12; border-radius: 20px; padding: 10px 16px; font-size: 14px; font-weight: 600; transition: all 0.2s ease; box-shadow: 0 2px 8px rgba(0,0,0,0.1);">
                            <i class="fas fa-lightbulb" style="font-size: 16px; color: #f39c12;"></i>
                            <span style="color: #2c3e50;">洞察发现</span>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="hero-actions" style="display: flex; gap: 15px; align-items: center; justify-content: flex-start;">
                    <!-- 返回按钮 -->
                    <a href="{% url 'qrmanager:dashboard' %}" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        返回仪表板
                    </a>

                    <!-- 导出报告按钮 -->
                    <div class="dropdown">
                        <button class="btn btn-primary btn-lg dropdown-toggle" type="button" id="exportDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="fas fa-download me-2"></i>
                            导出报告
                        </button>
                            <ul class="dropdown-menu shadow-lg" aria-labelledby="exportDropdown">
                                <li>
                                    <h6 class="dropdown-header">
                                        <i class="fas fa-file-export me-2"></i>选择导出格式
                                    </h6>
                                </li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('csv')">
                                    <i class="fas fa-file-csv text-success me-2"></i>
                                    <div>
                                        <strong>CSV格式</strong>
                                        <small class="text-muted d-block">轻量级数据表格</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('excel')">
                                    <i class="fas fa-file-excel text-success me-2"></i>
                                    <div>
                                        <strong>Excel格式</strong>
                                        <small class="text-muted d-block">专业数据分析</small>
                                    </div>
                                </a></li>
                                <li><a class="dropdown-item" href="#" onclick="exportReport('pdf')">
                                    <i class="fas fa-file-pdf text-danger me-2"></i>
                                    <div>
                                        <strong>PDF报告</strong>
                                        <small class="text-muted d-block">完整分析报告</small>
                                    </div>
                                </a></li>
                                <li><hr class="dropdown-divider"></li>
                                <li><a class="dropdown-item" href="#" onclick="exportCharts()">
                                    <i class="fas fa-chart-bar text-info me-2"></i>
                                    <div>
                                        <strong>图表导出</strong>
                                        <small class="text-muted d-block">可视化图表</small>
                                    </div>
                                </a></li>
                            </ul>
                        </div>
                
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid px-4" style="margin-top: 20px; padding-top: 10px;">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <!-- 筛选条件 -->
            <div class="card fade-in mb-4">
                <div class="card-body">
                    <form method="get" class="row g-3">
                        <div class="col-md-3">
                            <label class="form-label">时间范围</label>
                            <select name="time_range" class="form-select">
                                <option value="7">最近7天</option>
                                <option value="30">最近30天</option>
                                <option value="90">最近90天</option>
                                <option value="all">全部时间</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">科室</label>
                            <select name="department" class="form-select">
                                <option value="">全部科室</option>
                                {% for dept in departments %}
                                <option value="{{ dept.id }}">{{ dept.name }}</option>
                                {% endfor %}
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">情感倾向</label>
                            <select name="sentiment" class="form-select">
                                <option value="">全部</option>
                                <option value="positive">正面</option>
                                <option value="neutral">中性</option>
                                <option value="negative">负面</option>
                            </select>
                        </div>
                        <div class="col-md-3">
                            <label class="form-label">&nbsp;</label>
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-filter me-2"></i>应用筛选
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 概览数据 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-primary">总评价数</h3>
                            <p class="display-4">{{ total_evaluations }}</p>
                            <p class="text-muted">较上期{{ total_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-success">平均评分</h3>
                            <p class="display-4">{{ average_rating|floatformat:1 }}</p>
                            <p class="text-muted">较上期{{ rating_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-warning">满意度</h3>
                            <p class="display-4">{{ satisfaction_rate }}%</p>
                            <p class="text-muted">较上期{{ satisfaction_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-danger">投诉率</h3>
                            <p class="display-4">{{ complaint_rate }}%</p>
                            <p class="text-muted">较上期{{ complaint_change_percentage }}%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 工作人员总体统计 -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-info">活跃工作人员</h3>
                            <p class="display-4">{{ staff_overall_stats.active_staff_count }}</p>
                            <p class="text-muted">参与评价人数</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-success">表扬总数</h3>
                            <p class="display-4">{{ staff_overall_stats.total_praised }}</p>
                            <p class="text-muted">平均表扬率 {{ staff_overall_stats.avg_praise_rate }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-danger">批评总数</h3>
                            <p class="display-4">{{ staff_overall_stats.total_criticized }}</p>
                            <p class="text-muted">平均批评率 {{ staff_overall_stats.avg_criticism_rate }}%</p>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card fade-in">
                        <div class="card-body text-center">
                            <h3 class="card-title text-primary">评价总数</h3>
                            <p class="display-4">{{ staff_overall_stats.total_mentions }}</p>
                            <p class="text-muted">工作人员评价总计</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 情感分布和评分分布 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">情感分布</h3>
                            <div class="d-flex justify-content-around mt-3">
                                <div class="text-center">
                                    <div class="sentiment-badge sentiment-positive mb-2">
                                        {{ sentiment_stats.positive|default:0 }}
                                    </div>
                                    <div>正面评价</div>
                                    <small class="text-muted">{{ positive_percentage }}%</small>
                                </div>
                                <div class="text-center">
                                    <div class="sentiment-badge sentiment-neutral mb-2">
                                        {{ sentiment_stats.neutral|default:0 }}
                                    </div>
                                    <div>中性评价</div>
                                    <small class="text-muted">{{ neutral_percentage }}%</small>
                                </div>
                                <div class="text-center">
                                    <div class="sentiment-badge sentiment-negative mb-2">
                                        {{ sentiment_stats.negative|default:0 }}
                                    </div>
                                    <div>负面评价</div>
                                    <small class="text-muted">{{ negative_percentage }}%</small>
                                </div>
                            </div>
                            <div id="sentimentTrendChart" class="mt-4" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>

                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">评分分布</h3>
                            <div class="d-flex justify-content-around mt-3">
                                {% for rating, count in rating_stats.items %}
                                <div class="text-center">
                                    <div class="rating-stars mb-2">
                                        {{ rating }}★
                                    </div>
                                    <div>{{ count }}</div>
                                    <small class="text-muted">
                                        {# 使用dictsort内置过滤器对satisfaction_percentages排序，并查找对应的百分比 #}
                                        {% for pair in satisfaction_percentages|dictsort:'0' %}
                                            {% if pair.0 == rating %}
                                                {{ pair.1 }}%
                                            {% endif %}
                                        {% endfor %}
                                    </small>
                                </div>
                                {% endfor %}
                            </div>
                            <div id="ratingTrendChart" class="mt-4" style="height: 300px;"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科室评价对比 -->
            <div class="card fade-in mb-4">
                <div class="card-body">
                    <h3 class="card-title">科室评价对比</h3>
                    <div id="departmentComparisonChart" style="height: 400px;"></div>
                </div>
            </div>

            <!-- 工作人员评价TOP5 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">表扬工作人员TOP5</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>工作人员</th>
                                            <th>科室</th>
                                            <th>被表扬次数</th>
                                            <th>表扬率</th>
                                            <th>总提及次数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for staff in top_staff %}
                                        <tr>
                                            <td>{{ staff.name }}</td>
                                            <td>{{ staff.department }}</td>
                                            <td><span class="text-success fw-bold">{{ staff.praised_count }}</span></td>
                                            <td>{{ staff.praise_rate }}%</td>
                                            <td><span class="badge bg-primary">{{ staff.total_mentions }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">批评工作人员TOP5</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>工作人员</th>
                                            <th>科室</th>
                                            <th>被批评次数</th>
                                            <th>批评率</th>
                                            <th>总提及次数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for staff in bottom_staff %}
                                        <tr>
                                            <td>{{ staff.name }}</td>
                                            <td>{{ staff.department }}</td>
                                            <td><span class="text-danger fw-bold">{{ staff.criticized_count }}</span></td>
                                            <td><span class="text-warning">{{ staff.criticism_rate }}%</span></td>
                                            <td><span class="badge bg-secondary">{{ staff.total_mentions }}</span></td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科室排名TOP5 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">最佳科室TOP5</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>科室</th>
                                            <th>表扬总数</th>
                                            <th>表扬率</th>
                                            <th>工作人员数</th>
                                            <th>评价总数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dept in top_departments %}
                                        <tr>
                                            <td><strong>{{ dept.name }}</strong></td>
                                            <td><span class="text-success fw-bold">{{ dept.praised_count }}</span></td>
                                            <td><span class="text-success">{{ dept.praise_rate }}%</span></td>
                                            <td><span class="badge bg-info">{{ dept.staff_count }}</span></td>
                                            <td><span class="badge bg-primary">{{ dept.total_mentions }}</span></td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">需要改进科室TOP5</h3>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>科室</th>
                                            <th>批评总数</th>
                                            <th>批评率</th>
                                            <th>工作人员数</th>
                                            <th>评价总数</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dept in bottom_departments %}
                                        <tr>
                                            <td><strong>{{ dept.name }}</strong></td>
                                            <td><span class="text-danger fw-bold">{{ dept.criticized_count }}</span></td>
                                            <td><span class="text-warning">{{ dept.criticism_rate }}%</span></td>
                                            <td><span class="badge bg-info">{{ dept.staff_count }}</span></td>
                                            <td><span class="badge bg-secondary">{{ dept.total_mentions }}</span></td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 科室医院评分分析 -->
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">医院评分最高科室TOP5</h3>
                            <p class="text-muted mb-3">基于患者通过科室二维码给医院的整体评分</p>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>科室</th>
                                            <th>平均评分</th>
                                            <th>评分数量</th>
                                            <th>满意度</th>
                                            <th>不满意度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dept in top_hospital_rating_departments %}
                                        <tr>
                                            <td><strong>{{ dept.name }}</strong></td>
                                            <td>
                                                <span class="text-success fw-bold">{{ dept.avg_rating }}</span>
                                                <span class="text-warning">
                                                    {% for i in "12345" %}
                                                        {% if forloop.counter <= dept.avg_rating|floatformat:0 %}
                                                            ★
                                                        {% else %}
                                                            ☆
                                                        {% endif %}
                                                    {% endfor %}
                                                </span>
                                            </td>
                                            <td><span class="badge bg-primary">{{ dept.total_count }}</span></td>
                                            <td><span class="text-success fw-bold">{{ dept.satisfaction_rate }}%</span></td>
                                            <td><span class="text-danger">{{ dept.dissatisfaction_rate }}%</span></td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card fade-in">
                        <div class="card-body">
                            <h3 class="card-title">医院评分最低科室TOP5</h3>
                            <p class="text-muted mb-3">需要重点关注和改进的科室</p>
                            <div class="table-responsive">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>科室</th>
                                            <th>平均评分</th>
                                            <th>评分数量</th>
                                            <th>满意度</th>
                                            <th>不满意度</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for dept in bottom_hospital_rating_departments %}
                                        <tr>
                                            <td><strong>{{ dept.name }}</strong></td>
                                            <td>
                                                <span class="text-warning fw-bold">{{ dept.avg_rating }}</span>
                                                <span class="text-warning">
                                                    {% for i in "12345" %}
                                                        {% if forloop.counter <= dept.avg_rating|floatformat:0 %}
                                                            ★
                                                        {% else %}
                                                            ☆
                                                        {% endif %}
                                                    {% endfor %}
                                                </span>
                                            </td>
                                            <td><span class="badge bg-secondary">{{ dept.total_count }}</span></td>
                                            <td><span class="text-success">{{ dept.satisfaction_rate }}%</span></td>
                                            <td><span class="text-danger fw-bold">{{ dept.dissatisfaction_rate }}%</span></td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center text-muted">暂无数据</td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block extra_js %}
<script src="{% static 'vendor/echarts/echarts.min.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 情感趋势图表
    const sentimentTrend = echarts.init(document.getElementById('sentimentTrendChart'));
    sentimentTrend.setOption({
        title: {
            text: '情感趋势分析'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: ['正面', '中性', '负面']
        },
        xAxis: {
            type: 'category',
            data: {{ sentiment_trend_dates|safe }}
        },
        yAxis: {
            type: 'value'
        },
        series: [
            {
                name: '正面',
                type: 'line',
                data: {{ sentiment_trend_positive|safe }},
                itemStyle: {color: '#28a745'}
            },
            {
                name: '中性',
                type: 'line',
                data: {{ sentiment_trend_neutral|safe }},
                itemStyle: {color: '#ffc107'}
            },
            {
                name: '负面',
                type: 'line',
                data: {{ sentiment_trend_negative|safe }},
                itemStyle: {color: '#dc3545'}
            }
        ]
    });

    // 评分趋势图表
    const ratingTrend = echarts.init(document.getElementById('ratingTrendChart'));
    ratingTrend.setOption({
        title: {
            text: '评分趋势分析'
        },
        tooltip: {
            trigger: 'axis'
        },
        xAxis: {
            type: 'category',
            data: {{ satisfaction_trend_dates|safe }}
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: {{ satisfaction_trend_values|safe }},
            type: 'line',
            itemStyle: {color: '#4A90E2'}
        }]
    });

    // 科室评价对比图表
    const departmentComparison = echarts.init(document.getElementById('departmentComparisonChart'));

    // 调试信息
    console.log('科室名称:', {{ department_names|safe }});
    console.log('满意度数据:', {{ department_satisfaction_rates|safe }});
    console.log('正面评价率:', {{ department_positive_rates|safe }});
    console.log('负面评价率:', {{ department_negative_rates|safe }});

    departmentComparison.setOption({
        title: {
            text: '科室评价对比'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: ['满意度', '正面评价率', '负面评价率']
        },
        xAxis: {
            type: 'category',
            data: {{ department_names|safe }},
            axisLabel: {
                rotate: 45,  // 旋转标签避免重叠
                interval: 0  // 显示所有标签
            }
        },
        yAxis: [
            {
                type: 'value',
                name: '评分',
                min: 0,
                max: 5
            },
            {
                type: 'value',
                name: '比率',
                min: 0,
                max: 100,
                axisLabel: {
                    formatter: '{value}%'
                }
            }
        ],
        series: [
            {
                name: '满意度',
                type: 'bar',
                data: {{ department_satisfaction_rates|safe }},
                itemStyle: {color: '#5cb85c'}
            },
            {
                name: '正面评价率',
                type: 'line',
                yAxisIndex: 1,
                data: {{ department_positive_rates|safe }},
                itemStyle: {color: '#5bc0de'}
            },
            {
                name: '负面评价率',
                type: 'line',
                yAxisIndex: 1,
                data: {{ department_negative_rates|safe }},
                itemStyle: {color: '#d9534f'}
            }
        ]
    });

    // 窗口大小改变时重绘图表
    window.addEventListener('resize', function() {
        sentimentTrend.resize();
        ratingTrend.resize();
        departmentComparison.resize();
    });

    // 导出报告功能
    window.exportReport = function(format) {
        // 获取当前筛选条件
        const urlParams = new URLSearchParams(window.location.search);
        const timeRange = urlParams.get('time_range') || '30';
        const department = urlParams.get('department') || '';

        // 构建导出URL
        const exportUrl = new URL('{% url "qrmanager:export_sentiment_report" %}', window.location.origin);
        exportUrl.searchParams.set('format', format);
        exportUrl.searchParams.set('time_range', timeRange);
        if (department) {
            exportUrl.searchParams.set('department', department);
        }

        // 显示加载提示
        const loadingToast = showToast('正在生成报告，请稍候...', 'info', 0);

        // 创建隐藏的下载链接
        const downloadLink = document.createElement('a');
        downloadLink.href = exportUrl.toString();
        downloadLink.style.display = 'none';
        document.body.appendChild(downloadLink);

        // 触发下载
        downloadLink.click();

        // 清理
        document.body.removeChild(downloadLink);

        // 延迟关闭加载提示
        setTimeout(() => {
            if (loadingToast) {
                loadingToast.hide();
            }
            showToast(`${format.toUpperCase()}报告导出成功！`, 'success');
        }, 1000);
    };

    // 导出图表功能
    window.exportCharts = function() {
        // 创建一个包含所有图表的容器
        const chartsContainer = document.createElement('div');
        chartsContainer.style.backgroundColor = 'white';
        chartsContainer.style.padding = '20px';

        // 添加标题
        const title = document.createElement('h2');
        title.textContent = '医院服务评价情感分析图表';
        title.style.textAlign = 'center';
        title.style.marginBottom = '30px';
        chartsContainer.appendChild(title);

        // 导出每个图表为图片
        const charts = [sentimentTrend, ratingTrend, departmentComparison];
        const chartTitles = ['情感趋势分析', '评分趋势分析', '科室评价对比'];

        let exportedCount = 0;
        const totalCharts = charts.length;

        charts.forEach((chart, index) => {
            if (chart) {
                // 获取图表的DataURL
                const dataURL = chart.getDataURL({
                    type: 'png',
                    pixelRatio: 2,
                    backgroundColor: '#fff'
                });

                // 创建图片元素
                const img = document.createElement('img');
                img.src = dataURL;
                img.style.width = '100%';
                img.style.marginBottom = '20px';
                img.style.border = '1px solid #ddd';

                // 添加图表标题
                const chartTitle = document.createElement('h3');
                chartTitle.textContent = chartTitles[index];
                chartTitle.style.textAlign = 'center';
                chartTitle.style.marginBottom = '10px';

                chartsContainer.appendChild(chartTitle);
                chartsContainer.appendChild(img);

                exportedCount++;

                // 当所有图表都导出完成时，下载整个容器
                if (exportedCount === totalCharts) {
                    downloadChartsAsImage(chartsContainer);
                }
            }
        });
    };

    // 下载图表容器为图片
    function downloadChartsAsImage(container) {
        // 使用html2canvas库将容器转换为图片
        if (typeof html2canvas !== 'undefined') {
            html2canvas(container, {
                backgroundColor: '#ffffff',
                scale: 2
            }).then(canvas => {
                // 创建下载链接
                const link = document.createElement('a');
                link.download = `sentiment_charts_${new Date().getTime()}.png`;
                link.href = canvas.toDataURL();
                link.click();

                showToast('图表导出成功！', 'success');
            }).catch(error => {
                console.error('图表导出失败:', error);
                showToast('图表导出失败，请稍后重试', 'error');
            });
        } else {
            // 如果没有html2canvas库，提供备选方案
            showToast('图表导出功能需要加载额外组件，请稍后重试', 'warning');
        }
    }

    // 显示提示消息
    function showToast(message, type = 'info', duration = 3000) {
        // 创建toast元素
        const toast = document.createElement('div');
        toast.className = `alert alert-${type === 'error' ? 'danger' : type} alert-dismissible fade show position-fixed`;
        toast.style.top = '20px';
        toast.style.right = '20px';
        toast.style.zIndex = '9999';
        toast.style.minWidth = '300px';

        toast.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        document.body.appendChild(toast);

        // 自动关闭
        if (duration > 0) {
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, duration);
        }

        return {
            hide: () => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }
        };
    }

    // 自动提交筛选表单
    const filterForm = document.querySelector('form');
    const selects = filterForm.querySelectorAll('select');
    selects.forEach(select => {
        select.addEventListener('change', () => filterForm.submit());
    });
});
</script>
{% endblock %}