#!/usr/bin/env python
"""
长期稳定性测试 - 模拟实际使用场景
"""
import os
import sys
import django
import time

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid

def test_long_term_stability():
    """长期稳定性测试 - 模拟打印后长期使用的场景"""
    print("=" * 60)
    print("🏥 医院床头二维码长期稳定性测试")
    print("=" * 60)
    
    # 模拟医院的实际UUID
    hospital_uuids = [
        "bed-0001-ward-a-room-101-patient-001",  # 非标准格式测试
        str(uuid.uuid4()),  # 标准UUID 1
        str(uuid.uuid4()),  # 标准UUID 2
        str(uuid.uuid4()),  # 标准UUID 3
        "12345678-1234-5678-9012-123456789012",  # 固定测试UUID
    ]
    
    print("📋 生成二维码（模拟打印阶段）...")
    qr_database = {}  # 模拟打印时生成的二维码数据库
    
    for i, test_uuid in enumerate(hospital_uuids):
        if len(test_uuid) >= 36:  # 只测试有效的UUID
            try:
                encrypted = encrypt_qr_param(test_uuid)
                qr_database[f"床位_{i+1}"] = {
                    'uuid': test_uuid,
                    'encrypted': encrypted,
                    'print_time': time.time()
                }
                print(f"   床位_{i+1}: {encrypted[:30]}... (长度: {len(encrypted)})")
            except Exception as e:
                print(f"   床位_{i+1}: 生成失败 - {e}")
    
    print(f"\n✅ 成功生成 {len(qr_database)} 个床头二维码")
    
    # 模拟时间流逝和多次验证
    print("\n🕐 模拟长期使用（多次扫描验证）...")
    
    total_scans = 0
    successful_scans = 0
    
    for round_num in range(1, 6):  # 模拟5轮扫描
        print(f"\n--- 第 {round_num} 轮扫描验证 ---")
        
        for bed_name, qr_data in qr_database.items():
            total_scans += 1
            
            try:
                # 模拟扫描二维码
                scanned_encrypted = qr_data['encrypted']
                
                # 解密验证
                decrypted = decrypt_qr_param(scanned_encrypted)
                original_uuid = qr_data['uuid']
                
                if decrypted['uuid'] == original_uuid:
                    successful_scans += 1
                    status = "✓"
                else:
                    status = "✗ UUID不匹配"
                    print(f"      {bed_name}: {status}")
                    print(f"        期望: {original_uuid}")
                    print(f"        实际: {decrypted['uuid']}")
                
            except Exception as e:
                status = f"✗ 解密失败: {e}"
                print(f"      {bed_name}: {status}")
        
        # 短暂延迟模拟时间流逝
        time.sleep(0.1)
    
    # 统计结果
    success_rate = (successful_scans / total_scans) * 100 if total_scans > 0 else 0
    
    print(f"\n" + "=" * 60)
    print("📊 长期稳定性测试结果")
    print("=" * 60)
    print(f"总扫描次数: {total_scans}")
    print(f"成功验证: {successful_scans}")
    print(f"成功率: {success_rate:.1f}%")
    
    if success_rate == 100.0:
        print("🎉 完美！系统具备医院级稳定性！")
        print("✅ 可以安全打印并长期使用床头二维码！")
    else:
        print("🚨 警告：发现稳定性问题！")
        print("❌ 不建议在医院环境中使用！")
    
    return success_rate == 100.0

def test_deterministic_encryption():
    """测试加密的确定性 - 确保算法没有随机因素"""
    print("\n" + "=" * 60)
    print("🔒 确定性加密测试")
    print("=" * 60)
    
    test_uuid = "12345678-1234-5678-9012-123456789012"
    print(f"测试UUID: {test_uuid}")
    
    # 进行100次加密，检查是否完全一致
    results = []
    for i in range(100):
        encrypted = encrypt_qr_param(test_uuid)
        results.append(encrypted)
    
    # 检查所有结果是否相同
    first_result = results[0]
    all_same = all(result == first_result for result in results)
    
    print(f"加密结果: {first_result}")
    print(f"100次加密一致性: {'✓ 完全一致' if all_same else '✗ 存在差异'}")
    
    if not all_same:
        unique_results = set(results)
        print(f"发现 {len(unique_results)} 种不同结果:")
        for i, result in enumerate(unique_results):
            count = results.count(result)
            print(f"  结果{i+1}: {result} (出现{count}次)")
    
    return all_same

if __name__ == "__main__":
    print("🏥 医院二维码系统稳定性验证")
    print("=" * 60)
    
    # 确定性测试
    deterministic_ok = test_deterministic_encryption()
    
    # 长期稳定性测试
    long_term_ok = test_long_term_stability()
    
    print("\n" + "=" * 60)
    print("🏁 最终评估")
    print("=" * 60)
    
    if deterministic_ok and long_term_ok:
        print("🎉 系统通过所有稳定性测试！")
        print("✅ 可以安全用于医院床头二维码打印！")
        print("📋 建议：")
        print("   - 可以批量打印二维码")
        print("   - 打印后的二维码将永久有效")
        print("   - 无需担心验证失败问题")
    else:
        print("🚨 系统存在稳定性风险！")
        print("❌ 不建议用于医院生产环境！")
        print("🔧 需要修复的问题：")
        if not deterministic_ok:
            print("   - 加密算法存在随机性")
        if not long_term_ok:
            print("   - 长期使用存在验证失败风险")
