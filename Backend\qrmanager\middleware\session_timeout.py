"""
会话超时中间件
用于在每次请求时检查会话是否已过期，如果过期则重定向到登录页面
"""

from django.conf import settings
from django.contrib.auth import logout
from django.utils import timezone
from django.shortcuts import redirect
from django.urls import reverse
import logging
import datetime

# 获取日志记录器
logger = logging.getLogger('qrmanager')

class SessionTimeoutMiddleware:
    """
    会话超时中间件
    在每次请求时检查会话是否已过期，如果过期则重定向到登录页面
    """

    def __init__(self, get_response):
        self.get_response = get_response
        # 从设置中获取会话超时时间（秒）
        self.SESSION_COOKIE_AGE = getattr(settings, 'SESSION_COOKIE_AGE', 600)  # 默认10分钟
        # 不需要检查会话超时的路径
        self.EXEMPT_PATHS = [
            '/login/',
            '/logout/',
            '/ping/',
            '/static/',
            '/media/',
            '/favicon.ico',
            '/api/public/',
            '/api/v1/public/',
            '/q/',
        ]

    def __call__(self, request):
        # 如果用户已登录且请求路径不在豁免列表中
        if request.user.is_authenticated and not self._is_exempt_path(request.path):
            # 获取会话的最后活动时间
            last_activity = request.session.get('last_activity')
            
            # 如果没有最后活动时间，则设置当前时间
            if not last_activity:
                request.session['last_activity'] = timezone.now().isoformat()
            else:
                # 将字符串转换为datetime对象
                try:
                    last_activity_time = datetime.datetime.fromisoformat(last_activity)
                    # 如果是naive datetime，添加时区信息
                    if last_activity_time.tzinfo is None:
                        last_activity_time = timezone.make_aware(last_activity_time)
                    
                    # 计算距离上次活动的时间（秒）
                    idle_time = (timezone.now() - last_activity_time).total_seconds()
                    
                    # 如果超过会话超时时间，则登出用户
                    if idle_time > self.SESSION_COOKIE_AGE:
                        # 记录会话超时事件
                        logger.info(f"会话超时 - 用户: {request.user.username}, 空闲时间: {idle_time}秒")
                        
                        # 登出用户
                        logout(request)
                        
                        # 重定向到登录页面
                        return redirect(reverse('login'))
                    
                    # 更新最后活动时间
                    request.session['last_activity'] = timezone.now().isoformat()
                except (ValueError, TypeError) as e:
                    # 如果解析失败，重置最后活动时间
                    logger.warning(f"解析会话最后活动时间失败: {e}")
                    request.session['last_activity'] = timezone.now().isoformat()
        
        # 继续处理请求
        response = self.get_response(request)
        return response
    
    def _is_exempt_path(self, path):
        """
        检查路径是否在豁免列表中
        
        参数:
            path: 请求路径
            
        返回:
            如果路径在豁免列表中，则返回True，否则返回False
        """
        return any(path.startswith(exempt_path) for exempt_path in self.EXEMPT_PATHS)
