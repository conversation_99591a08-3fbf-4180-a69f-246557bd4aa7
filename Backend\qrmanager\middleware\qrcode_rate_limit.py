"""
二维码验证速率限制中间件
用于限制二维码验证请求频率，防止滥用
"""

from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.core.cache import cache
import re
import time

class QRCodeRateLimitMiddleware(MiddlewareMixin):
    """QR码验证访问频率限制中间件"""

    # 全局QR验证请求计数器的缓存键
    GLOBAL_COUNTER_KEY = 'qr_verify_global_counter'

    # 加密参数计数器的缓存键前缀
    QR_PARAM_COUNTER_PREFIX = 'qr_param_counter:'

    # QR验证API路径前缀 - 仅支持/q/格式
    QR_VERIFY_PATH_PREFIX = '/api/v1/qrcode/verify/q/'

    # 每分钟最大全局请求数
    MAX_GLOBAL_REQUESTS_PER_MINUTE = 120

    # 每分钟每个二维码参数的最大请求数
    MAX_PARAM_REQUESTS_PER_MINUTE = 20

    # 每个IP每分钟的最大请求数
    MAX_IP_REQUESTS_PER_MINUTE = 30

    # 白名单IP列表，不受限制
    WHITELIST_IPS = [
        '127.0.0.1',
        '::1',
    ]
    
    def process_request(self, request):
        """
        处理请求，检查是否超过速率限制
        
        参数:
            request: HTTP请求对象
            
        返回:
            如果超过速率限制，返回429响应
            否则返回None，继续处理请求
        """
        # 检查是否是二维码验证请求
        path = request.path
        if not path.startswith(self.QR_VERIFY_PATH_PREFIX):
            return None
            
        # 获取客户端IP
        client_ip = self.get_client_ip(request)
        
        # 白名单IP不受限制
        if client_ip in self.WHITELIST_IPS:
            return None
            
        # 获取当前时间戳（秒）
        current_time = int(time.time())
        current_minute = current_time // 60
        
        # 检查全局速率限制
        global_cache_key = f"{self.GLOBAL_COUNTER_KEY}:{current_minute}"
        global_count = cache.get(global_cache_key, 0)
        
        if global_count >= self.MAX_GLOBAL_REQUESTS_PER_MINUTE:
            return JsonResponse({
                'status': 'error',
                'message': '系统繁忙，请稍后再试'
            }, status=429)
            
        # 检查IP速率限制
        ip_cache_key = f"ip_rate_limit:{client_ip}:{current_minute}"
        ip_count = cache.get(ip_cache_key, 0)
        
        if ip_count >= self.MAX_IP_REQUESTS_PER_MINUTE:
            return JsonResponse({
                'status': 'error',
                'message': '请求过于频繁，请稍后再试'
            }, status=429)
            
        # 获取二维码参数
        qr_param = path.replace(self.QR_VERIFY_PATH_PREFIX, '')
        
        # 检查参数速率限制
        param_cache_key = f"{self.QR_PARAM_COUNTER_PREFIX}{qr_param}:{current_minute}"
        param_count = cache.get(param_cache_key, 0)
        
        if param_count >= self.MAX_PARAM_REQUESTS_PER_MINUTE:
            return JsonResponse({
                'status': 'error',
                'message': '该二维码请求过于频繁，请稍后再试'
            }, status=429)
            
        # 增加计数并设置过期时间（1分钟）
        cache.set(global_cache_key, global_count + 1, 60)
        cache.set(ip_cache_key, ip_count + 1, 60)
        cache.set(param_cache_key, param_count + 1, 60)
        
        # 继续处理请求
        return None
        
    def get_client_ip(self, request):
        """
        获取客户端IP地址
        
        参数:
            request: HTTP请求对象
            
        返回:
            客户端IP地址
        """
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip