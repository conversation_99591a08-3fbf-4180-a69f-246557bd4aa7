# Generated by Django 4.2.7 on 2025-03-10 00:59

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('qrmanager', '0028_evaluation_bed_alter_evaluation_qr_code'),
    ]

    operations = [
        migrations.CreateModel(
            name='QRCodeHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_code', models.UUIDField(verbose_name='旧二维码编码')),
                ('new_code', models.UUIDField(verbose_name='新二维码编码')),
                ('reason', models.CharField(blank=True, max_length=200, verbose_name='更换原因')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('is_security_issue', models.BooleanField(default=False, help_text='标记为安全问题的二维码将被禁用', verbose_name='是否安全问题')),
                ('bed', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='qrcode_history', to='qrmanager.bed', verbose_name='床位')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to=settings.AUTH_USER_MODEL, verbose_name='操作人')),
            ],
            options={
                'verbose_name': '二维码历史',
                'verbose_name_plural': '二维码历史',
                'ordering': ['-created_at'],
            },
        ),
    ]
