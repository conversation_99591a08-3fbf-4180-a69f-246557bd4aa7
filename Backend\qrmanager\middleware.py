import json
from django.utils.deprecation import MiddlewareMixin
from .models import OperationLog, APIKey, APILog, LoggingConfiguration
import re
from django.urls import resolve
from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
import time

class OperationLogMiddleware(MiddlewareMixin):
    IGNORE_PATHS = [
        r'^/static/',
        r'^/media/',
        r'^/favicon.ico$',
        r'^/__debug__/',
    ]

    SENSITIVE_FIELDS = [
        'password',
        'token',
        'secret',
        'credit_card',
        'phone',
    ]

    def __init__(self, get_response=None):
        super().__init__(get_response)
        # 清除缓存，确保从数据库加载最新配置
        cache.delete('logging_config')
        print("已清除日志配置缓存")

    def reload_config(self):
        """重新加载日志配置"""
        # 使用缓存存储配置，避免频繁查询数据库
        config = cache.get('logging_config')
        if config is None:
            try:
                # 从数据库加载配置
                config = {
                    item.action_type: item.is_enabled
                    for item in LoggingConfiguration.objects.all()
                }
                # 缓存配置，有效期1小时
                cache.set('logging_config', config, 3600)
            except:
                # 如果数据库查询失败，使用默认配置
                config = {
                    'create': True,
                    'update': True,
                    'delete': True,
                    'login': True,
                    'logout': True,
                    'error': True,
                    'permission_change': True,
                    'system_config': True,
                }
        self.logging_config = config

    def should_log_action(self, action):
        """检查是否应该记录特定类型的操作"""
        # 提取操作类型
        action_type = None

        # 处理常见操作类型
        if 'create' in action.lower():
            action_type = 'create'
        elif 'update' in action.lower() or 'edit' in action.lower() or 'modify' in action.lower():
            action_type = 'update'
        elif 'delete' in action.lower() or 'remove' in action.lower():
            action_type = 'delete'
        elif 'login' in action.lower():
            action_type = 'login'
        elif 'logout' in action.lower():
            action_type = 'logout'
        elif 'view' in action.lower() or 'get' in action.lower():
            action_type = 'view'
        elif 'export' in action.lower():
            action_type = 'export'
        elif 'import' in action.lower():
            action_type = 'import'
        elif 'print' in action.lower():
            action_type = 'print'
        elif 'generate' in action.lower():
            action_type = 'generate'
        elif 'permission' in action.lower():
            action_type = 'permission_change'
        elif 'config' in action.lower():
            action_type = 'system_config'
        elif 'api' in action.lower():
            action_type = 'api_request'
        elif 'file_upload' in action.lower() or 'upload' in action.lower():
            action_type = 'file_upload'
        elif 'file_download' in action.lower() or 'download' in action.lower():
            action_type = 'file_download'
        elif 'bulk' in action.lower():
            action_type = 'bulk_operation'
        elif 'error' in action.lower():
            action_type = 'error'

        # 如果无法识别操作类型，不记录日志
        if action_type is None:
            print(f"中间件无法识别的操作类型: {action}")
            return False

        # 使用LoggerHelper来判断是否记录
        from .utils import LoggerHelper
        return LoggerHelper.should_log_action(action_type)

    def should_log(self, request):
        # 启用中间件日志记录，但排除某些路径
        path = request.path_info

        # 静态文件和媒体文件不记录
        for pattern in self.IGNORE_PATHS:
            if re.match(pattern, path):
                return False

        # GET请求的评价页面不记录（避免大量无用日志）
        if request.method == 'GET' and '/evaluation/' in path:
            return False

        # 对于GET请求，只记录特定的页面
        if request.method == 'GET':
            # 记录登录、仪表板、操作日志等重要页面
            important_paths = [
                '/login/',
                '/dashboard/',
                '/operation_logs/',
                '/beds/',
                '/departments/',
                '/staff/',
                '/qrcodes/',
                '/evaluations/',
                '/admin/'
            ]
            for important_path in important_paths:
                if path.startswith(important_path):
                    # 检查是否应该记录此类型的操作
                    return self.should_log_action("view")
            # 其他GET请求不记录
            return False

        # 对于其他请求，根据方法设置操作类型
        if request.method == 'POST':
            return self.should_log_action("create")
        elif request.method == 'PUT' or request.method == 'PATCH':
            return self.should_log_action("update")
        elif request.method == 'DELETE':
            return self.should_log_action("delete")

        # 其他未知请求类型不记录
        return False

    def get_client_ip(self, request):
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
            print(f"从HTTP_X_FORWARDED_FOR获取IP: {ip}")
            return ip
        ip = request.META.get('REMOTE_ADDR')
        print(f"从REMOTE_ADDR获取IP: {ip}")

        # 打印所有请求头，帮助调试
        print("所有请求头:")
        for key, value in request.META.items():
            if key.startswith('HTTP_'):
                print(f"  {key}: {value}")

        return ip

    def get_browser_info(self, request):
        user_agent = request.META.get('HTTP_USER_AGENT', '')
        browser = "未知浏览器"
        os = "未知操作系统"

        # 简单的浏览器和操作系统检测
        if 'MSIE' in user_agent or 'Trident' in user_agent:
            browser = 'Internet Explorer'
        elif 'Firefox' in user_agent:
            browser = 'Firefox'
        elif 'Chrome' in user_agent and 'Edge' not in user_agent:
            browser = 'Chrome'
        elif 'Safari' in user_agent and 'Chrome' not in user_agent:
            browser = 'Safari'
        elif 'Edge' in user_agent:
            browser = 'Edge'

        if 'Windows' in user_agent:
            os = 'Windows'
        elif 'Macintosh' in user_agent:
            os = 'MacOS'
        elif 'Linux' in user_agent:
            os = 'Linux'
        elif 'Android' in user_agent:
            os = 'Android'
        elif 'iPhone' in user_agent or 'iPad' in user_agent:
            os = 'iOS'

        return browser, os

    def mask_sensitive_data(self, data):
        """遮蔽敏感数据"""
        if isinstance(data, dict):
            masked_data = {}
            for key, value in data.items():
                if any(sensitive in key.lower() for sensitive in self.SENSITIVE_FIELDS):
                    masked_data[key] = '******'
                else:
                    masked_data[key] = self.mask_sensitive_data(value)
            return masked_data
        elif isinstance(data, list):
            return [self.mask_sensitive_data(item) for item in data]
        else:
            return data

    def get_view_name(self, request):
        """获取更详细的视图信息"""
        try:
            resolver_match = resolve(request.path_info)
            view_name = resolver_match.view_name
            app_name = resolver_match.app_name
            url_name = resolver_match.url_name
            return {
                'view_name': view_name,
                'app_name': app_name,
                'url_name': url_name,
                'kwargs': resolver_match.kwargs
            }
        except:
            return {'view_name': '未知视图'}

    def process_view(self, request, view_func, view_args, view_kwargs):
        if not self.should_log(request):
            return None

        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)

        # 获取请求方法和路径
        method = request.method
        path = request.path_info

        # 准备请求数据(遮蔽敏感信息)
        request_data = {
            'POST': self.mask_sensitive_data(dict(request.POST)),
            'FILES': [f.name for f in request.FILES.values()] if request.FILES else [],
        }

        # 准备额外数据
        extra_data = {
            'method': method,
            'path': path,
            'request_data': request_data,
        }

        # 创建操作描述
        description = f"{method} {path}"

        # 确定操作类型
        action = f"{method}请求"

        # 检查是否应该记录此类操作
        if not self.should_log_action(action):
            return None

        # 记录操作日志
        try:
            # 确保记录IP地址
            if not ip_address:
                ip_address = self.get_client_ip(request)
                print(f"中间件获取IP地址: {ip_address}")

            OperationLog.objects.create(
                user=user,
                action=action,
                description=description,
                ip_address=ip_address,
                browser=browser,
                os=os,
                status='success',
                extra_data=extra_data
            )
        except Exception as e:
            # 记录日志失败，但不影响请求处理
            print(f"记录操作日志失败: {e}")

        return None

    def process_response(self, request, response):
        """记录所有请求的响应"""
        if not self.should_log(request):
            return response

        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)

        # 记录响应状态
        status_code = response.status_code
        if 200 <= status_code < 300:
            status = 'success'
        elif 400 <= status_code < 500:
            status = 'warning'
        else:
            status = 'error'

        try:
            # 只记录非GET请求或错误响应
            if request.method != 'GET' or status != 'success':
                action = "响应状态"

                # 检查是否应该记录此类操作
                if status == 'error' or self.should_log_action(action):
                    OperationLog.objects.create(
                        user=user,
                        action=action,
                        description=f"响应状态码: {status_code}",
                        ip_address=ip_address,
                        browser=browser,
                        os=os,
                        status=status,
                        extra_data={
                            'status_code': status_code,
                            'reason_phrase': response.reason_phrase,
                        }
                    )
        except Exception as e:
            # 记录日志失败，但不影响响应处理
            print(f"记录响应日志失败: {e}")

        return response

    def process_exception(self, request, exception):
        """记录未处理的异常"""
        # 获取用户信息
        user = request.user if request.user.is_authenticated else None
        ip_address = self.get_client_ip(request)
        browser, os = self.get_browser_info(request)

        try:
            # 检查是否应该记录错误操作
            if self.should_log_action("error"):
                OperationLog.objects.create(
                    user=user,
                    action="系统异常",
                    description=str(exception),
                    ip_address=ip_address,
                    browser=browser,
                    os=os,
                    status='error',
                    extra_data={
                        'exception_type': exception.__class__.__name__,
                        'exception_message': str(exception),
                        'exception_module': exception.__class__.__module__,
                    }
                )
        except Exception as e:
            # 记录日志失败，但不影响异常处理
            print(f"记录异常日志失败: {e}")

        return None

class APISecurityMiddleware:
    """
    API安全中间件
    用于保护API接口，实施访问控制、认证验证和速率限制
    """
    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 记录请求开始时间
        start_time = timezone.now()

        # 检查是否是API请求
        if self._is_api_request(request):
            # 根据API类型执行不同的安全检查
            response = self._check_api_security(request)
            if response:
                return response

        # 继续处理请求
        response = self.get_response(request)

        # 对API请求记录日志
        if self._is_api_request(request):
            self._log_api_request(request, response, start_time)

        return response

    def _is_api_request(self, request):
        """判断是否为API请求"""
        path = request.path
        return (
            path.startswith('/api/') or
            path.startswith('/admin/api/') or
            'api' in path
        )

    def _check_api_security(self, request):
        """根据API类型执行安全检查"""
        path = request.path

        # 1. 管理API - 需要登录验证
        if (path.startswith('/admin/api/') or
            (path.startswith('/api/') and not path.startswith('/api/v1/') and not path.startswith('/api/secure/') and not path.startswith('/api/public/'))):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'status': 'error',
                    'message': '未授权访问，请先登录'
                }, status=401)

        # 2. RESTful API - 需要API令牌
        if path.startswith('/api/v1/') and not path.startswith('/api/v1/public/'):
            # 从请求头获取API密钥
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Token '):
                token = auth_header.split(' ')[1].strip()
            else:
                token = request.GET.get('token', '')

            # 如果没有提供令牌
            if not token:
                return JsonResponse({
                    'status': 'error',
                    'message': '需要API令牌'
                }, status=401)

            # 验证API密钥
            try:
                api_key = APIKey.objects.get(key=token)

                # 检查密钥是否有效
                if not api_key.is_active:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'API密钥已禁用'
                    }, status=403)

                # 检查密钥是否过期
                if api_key.expires_at and timezone.now() > api_key.expires_at:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'API密钥已过期'
                    }, status=403)

                # 检查速率限制
                self._check_rate_limits(api_key)

                # 将API密钥附加到请求对象
                request.api_key = api_key

            except APIKey.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': '无效的API密钥'
                }, status=401)

        # 3. 公开API - 实施速率限制
        if path.startswith('/api/secure/') or path.startswith('/api/public/') or path.startswith('/api/v1/public/'):
            # 基于IP的速率限制
            client_ip = self._get_client_ip(request)
            if self._is_ip_rate_limited(client_ip):
                return JsonResponse({
                    'status': 'error',
                    'message': '请求过于频繁，请稍后再试'
                }, status=429)

        # 通过所有安全检查
        return None

    def _check_rate_limits(self, api_key):
        """检查API密钥的速率限制"""
        # 实现速率限制逻辑
        # 这里可以根据api_key的rate_limit_day, rate_limit_hour, rate_limit_minute属性
        # 检查API调用次数是否超过限制
        pass

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

    def _is_ip_rate_limited(self, ip):
        """检查IP是否被限速"""
        # 实现基于IP的速率限制逻辑
        # 可以使用缓存或数据库记录IP的请求次数和时间
        return False

    def _log_api_request(self, request, response, start_time):
        """记录API请求日志"""
        try:
            # 计算响应时间
            end_time = timezone.now()
            response_time = (end_time - start_time).total_seconds() * 1000  # 毫秒

            # 提取请求数据
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    request_data = request.body.decode('utf-8')
                    if request_data and request_data.strip():
                        import json
                        request_data = json.loads(request_data)
                    else:
                        request_data = {}
                except:
                    request_data = {}
            else:
                request_data = {}

            # 创建API日志记录
            APILog.objects.create(
                api_key=getattr(request, 'api_key', None),
                endpoint=request.path,
                method=request.method,
                status_code=response.status_code,
                status='success' if 200 <= response.status_code < 400 else 'error',
                response_time=response_time,
                remote_addr=self._get_client_ip(request),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_data=request_data,
                error_message='' if 200 <= response.status_code < 400 else str(response.content),
            )
        except Exception as e:
            # 记录日志失败不应影响API响应
            print(f"Failed to log API call: {e}")

class RateLimitMiddleware(MiddlewareMixin):
    """
    速率限制中间件
    防止API被滥用，对特定端点实施速率限制
    """
    # 需要限制的API路径正则表达式
    RATELIMIT_PATHS = [
        r'^/api/v1/qrcode/verify/', # 二维码验证API
        r'^/api/v1/qrcode/generate-param/', # 生成参数API
        r'^/api/v1/public/submit-evaluation/' # 公开评价提交API
    ]

    # 各种限制配置
    RATE_LIMITS = {
        'default': {'num_requests': 120, 'per_seconds': 60}, # 默认：每分钟120个请求
        'verify_qrcode': {'num_requests': 30, 'per_seconds': 60}, # 验证二维码：每分钟30个请求
        'generate_param': {'num_requests': 20, 'per_seconds': 60}, # 生成参数：每分钟20个请求
        'submit_evaluation': {'num_requests': 20, 'per_seconds': 60}, # 提交评价：每分钟20个请求
    }

    # 不同路径对应的限制策略
    PATH_TO_LIMIT = {
        r'^/api/v1/qrcode/verify/': 'verify_qrcode',
        r'^/api/v1/qrcode/generate-param/': 'generate_param',
        r'^/api/v1/public/submit-evaluation/': 'submit_evaluation'
    }

    # 白名单IP，不受限制
    WHITELIST_IPS = [
        '127.0.0.1',
        '::1',
        '************',  # 添加医院内部IP地址
        '*************',  # 示例IP
    ]

    def process_request(self, request):
        # 仅处理API请求
        if not any(re.match(path, request.path) for path in self.RATELIMIT_PATHS):
            return None

        # 获取客户端IP
        client_ip = self.get_client_ip(request)

        # 白名单IP不受限制
        if client_ip in self.WHITELIST_IPS:
            return None

        # 确定使用哪个限制策略
        limit_key = 'default'
        for path_pattern, limit_name in self.PATH_TO_LIMIT.items():
            if re.match(path_pattern, request.path):
                limit_key = limit_name
                break

        # 获取限制配置
        rate_limit = self.RATE_LIMITS.get(limit_key, self.RATE_LIMITS['default'])
        num_requests = rate_limit['num_requests']
        per_seconds = rate_limit['per_seconds']

        # 创建缓存键
        cache_key = f"ratelimit:{client_ip}:{limit_key}"

        # 检查是否超过限制
        request_history = cache.get(cache_key, [])

        # 清理历史记录，只保留时间窗口内的记录
        now = time.time()
        window_start = now - per_seconds
        request_history = [timestamp for timestamp in request_history if timestamp > window_start]

        # 检查是否超过最大请求数
        if len(request_history) >= num_requests:
            return JsonResponse({
                'status': 'error',
                'message': '请求频率过高，请稍后再试',
                'detail': f"超过速率限制：每{per_seconds}秒{num_requests}个请求"
            }, status=429)

        # 添加当前请求时间戳
        request_history.append(now)

        # 更新缓存
        cache.set(cache_key, request_history, per_seconds * 2)  # 设置缓存时间为窗口的两倍

        # 继续处理请求
        return None

    def get_client_ip(self, request):
        """获取客户端真实IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            # 如果有X-Forwarded-For头，取第一个IP
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            # 否则使用REMOTE_ADDR
            ip = request.META.get('REMOTE_ADDR', '')
        return ip

class QRCodeRateLimitMiddleware(MiddlewareMixin):
    """QR码验证访问频率限制中间件"""

    # 全局QR验证请求计数器的缓存键
    GLOBAL_COUNTER_KEY = 'qr_verify_global_counter'

    # 加密参数计数器的缓存键前缀
    QR_PARAM_COUNTER_PREFIX = 'qr_param_counter:'

    # QR验证API路径前缀 - 仅支持/q/格式
    QR_VERIFY_PATH_PREFIX = '/api/v1/qrcode/verify/q/'

    # 每分钟最大全局请求数（原为60）
    MAX_GLOBAL_REQUESTS_PER_MINUTE = 120

    # 每分钟每个二维码参数的最大请求数（原为3）
    MAX_PARAM_REQUESTS_PER_MINUTE = 20

    # 每个IP每分钟的最大请求数（原为10）
    MAX_IP_REQUESTS_PER_MINUTE = 30

    # 白名单IP列表，不受限制
    WHITELIST_IPS = [
        '127.0.0.1',
        '::1',
        '************',  # 添加医院内部IP
        '*************',  # 示例IP
    ]

    def process_request(self, request):
        """处理请求，限制访问频率"""
        # 忽略OPTIONS请求(CORS预检)
        if request.method == 'OPTIONS':
            return None

        # 仅限制QR验证API请求 - 只处理/q/格式的URL
        if not request.path.startswith(self.QR_VERIFY_PATH_PREFIX):
            return None

        # 1. 全局API限制 - 1分钟1000次
        if not self._check_global_rate_limit():
            return JsonResponse({
                'status': 'error',
                'message': '请求频率过高，请稍后再试',
                'error_code': 'TOO_MANY_REQUESTS'
            }, status=429)

        # 2. 提取加密参数
        param = self._extract_qr_param_from_url(request.path)
        if param:
            # 获取客户端IP
            client_ip = self._get_client_ip(request)

            # 3. 对每个加密参数限制 - 1分钟10次
            if not self._check_qr_param_rate_limit(param, client_ip):
                return JsonResponse({
                    'status': 'error',
                    'message': '验证频率过高，请稍后再试',
                    'error_code': 'TOO_MANY_VERIFICATIONS'
                }, status=429)

        return None

    def _extract_qr_param_from_url(self, path):
        """从URL路径中提取加密参数 - 只处理/q/格式"""
        if path.startswith(self.QR_VERIFY_PATH_PREFIX):
            # 移除前缀和可能的尾部斜杠
            param = path[len(self.QR_VERIFY_PATH_PREFIX):].rstrip('/')
            return param
        return None

    def _check_global_rate_limit(self):
        """
        检查全局QR验证请求频率限制
        每分钟最多允许MAX_GLOBAL_REQUESTS_PER_MINUTE个请求
        """
        # 获取当前计数器
        counter = cache.get(self.GLOBAL_COUNTER_KEY)

        # 如果计数器不存在，创建一个新的，有效期1分钟
        if counter is None:
            counter = 1
            cache.set(self.GLOBAL_COUNTER_KEY, counter, 60)
            return True

        # 如果计数器超过限制，返回False
        if counter >= self.MAX_GLOBAL_REQUESTS_PER_MINUTE:
            return False

        # 增加计数器并保持相同的过期时间
        cache.incr(self.GLOBAL_COUNTER_KEY)
        return True

    def _check_qr_param_rate_limit(self, qr_param, client_ip):
        """
        检查特定QR参数的请求频率限制
        每分钟每个参数最多允许MAX_PARAM_REQUESTS_PER_MINUTE个请求
        每分钟每个IP最多允许MAX_IP_REQUESTS_PER_MINUTE个请求
        """
        # 白名单IP不受限制
        if client_ip in self.WHITELIST_IPS:
            return True

        # 参数计数器缓存键
        param_key = f"{self.QR_PARAM_COUNTER_PREFIX}{qr_param}"
        ip_key = f"{self.QR_PARAM_COUNTER_PREFIX}{client_ip}"

        # 获取参数计数器
        param_counter = cache.get(param_key)
        ip_counter = cache.get(ip_key)

        # 如果参数计数器不存在，创建一个新的，有效期1分钟
        if param_counter is None:
            param_counter = 1
            cache.set(param_key, param_counter, 60)
        else:
            # 如果超过每参数限制，拒绝请求
            if param_counter >= self.MAX_PARAM_REQUESTS_PER_MINUTE:
                return False
            # 增加计数器
            cache.incr(param_key)

        # 如果IP计数器不存在，创建一个新的，有效期1分钟
        if ip_counter is None:
            ip_counter = 1
            cache.set(ip_key, ip_counter, 60)
        else:
            # 如果超过每IP限制，拒绝请求
            if ip_counter >= self.MAX_IP_REQUESTS_PER_MINUTE:
                return False
            # 增加计数器
            cache.incr(ip_key)

        return True

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip

class TempTokenSecurityMiddleware:
    """
    临时令牌安全中间件
    确保临时令牌只能用于提交评价的API接口
    """
    # 允许使用临时令牌的路径
    ALLOWED_PATHS = [
        # 不再有允许使用临时令牌的路径，因为submit-evaluation已改为直接验证加密参数
    ]

    def __init__(self, get_response):
        self.get_response = get_response

    def __call__(self, request):
        # 检查请求是否包含临时令牌
        temp_token = None

        # 从请求体中获取临时令牌 (POST请求)
        if request.method == 'POST' and request.content_type == 'application/json':
            try:
                body_data = json.loads(request.body.decode('utf-8'))
                temp_token = body_data.get('temp_token')
            except (json.JSONDecodeError, UnicodeDecodeError):
                pass

        # 如果包含临时令牌，检查是否访问的是允许的API
        if temp_token:
            path = request.path
            is_allowed = False

            # 检查路径是否在允许列表中
            for allowed_path in self.ALLOWED_PATHS:
                if re.match(allowed_path, path):
                    is_allowed = True
                    break

            # 如果路径不在允许列表中，则返回错误响应
            if not is_allowed:
                return JsonResponse({
                    'status': 'error',
                    'message': '临时令牌功能已停用，请使用加密参数直接验证'
                }, status=403)

        # 继续处理请求
        return self.get_response(request)