{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}打印模板管理{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col">
                            <h1 class="h3 mb-0">打印模板管理</h1>
                        </div>
                        <div class="col-md-4">
                            <div class="input-group">
                                <input type="text" id="searchInput" class="form-control" placeholder="搜索科室名称...">
                                <button class="btn btn-outline-secondary" type="button" id="searchButton">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </div>
                        <div class="col-auto">
                            <div class="btn-group">
                                <button type="button" class="btn btn-outline-secondary" id="viewToggle">
                                    <i class="fas fa-list me-2"></i>列表视图
                                </button>
                                {% if not public_template %}
                                <a href="{% url 'qrmanager:print_template_create' %}" class="btn btn-primary">
                                    <i class="fas fa-plus me-2"></i>创建公共模板
                                </a>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <!-- 公共模板部分 -->
                    <div class="mb-4">
                        <h5 class="border-bottom pb-2">公共模板</h5>
                        {% if public_template %}
                        <div class="row align-items-center">
                            <div class="col-md-4">
                                <div class="d-flex align-items-center">
                                    <div class="template-preview me-3">
                                        {% if public_template.background_image %}
                                        <img src="{{ public_template.background_image.url }}" alt="背景图片" class="img-thumbnail" style="max-width: 100px;">
                                        {% else %}
                                        <div class="no-image">无背景图</div>
                                        {% endif %}
                                    </div>
                                    <div>
                                        <h6 class="mb-1">{{ public_template.name }}</h6>
                                        <small class="text-muted">{{ public_template.print_width }} × {{ public_template.print_height }}毫米</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="template-info">
                                    <p class="mb-1"><small>二维码尺寸：{{ public_template.qr_size }}毫米</small></p>
                                    <p class="mb-0"><small>位置：({{ public_template.qr_position_x }}, {{ public_template.qr_position_y }})毫米</small></p>
                                </div>
                            </div>
                            <div class="col-md-4 text-end">
                                <div class="btn-group">
                                    <a href="{% url 'qrmanager:print_template_preview' public_template.id %}" class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-eye me-1"></i>预览
                                    </a>
                                    <a href="{% url 'qrmanager:print_template_update' public_template.id %}" class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit me-1"></i>编辑
                                    </a>
                                    <form action="{% url 'qrmanager:print_template_delete' public_template.id %}" method="post" style="display:inline;">
                                        {% csrf_token %}
                                        <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个模板吗？');">
                                            <i class="fas fa-trash me-1"></i>删除
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>尚未设置公共模板，未设置自定义模板的科室将无法使用打印功能
                        </div>
                        {% endif %}
                    </div>

                    <!-- 科室模板列表 -->
                    <h5 class="border-bottom pb-2">科室自定义模板</h5>
                    
                    <!-- 网格视图 -->
                    <div class="grid-view">
                        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 g-4">
                            {% for department in departments %}
                            <div class="col">
                                <div class="card h-100">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start mb-3">
                                            <h6 class="card-title mb-0">{{ department.name }}</h6>
                                            {% if department.print_template %}
                                                {% if department.print_template.is_active %}
                                                <span class="badge bg-success">已启用</span>
                                                {% else %}
                                                <span class="badge bg-warning">未启用</span>
                                                {% endif %}
                                            {% else %}
                                                {% if public_template %}
                                                <span class="badge bg-info">使用公共模板</span>
                                                {% else %}
                                                <span class="badge bg-secondary">未设置</span>
                                                {% endif %}
                                            {% endif %}
                                        </div>
                                        
                                        {% if department.print_template %}
                                        <div class="template-preview mb-3">
                                            {% if department.print_template.background_image %}
                                            <img src="{{ department.print_template.background_image.url }}" alt="背景图片" class="img-thumbnail w-100" style="max-height: 150px; object-fit: cover;">
                                            {% else %}
                                            <div class="no-image w-100 text-center py-4 bg-light">无背景图</div>
                                            {% endif %}
                                        </div>
                                        <div class="template-info small text-muted mb-3">
                                            <p class="mb-1">打印尺寸：{{ department.print_template.print_width }} × {{ department.print_template.print_height }}毫米</p>
                                            <p class="mb-1">二维码尺寸：{{ department.print_template.qr_size }}毫米</p>
                                            <p class="mb-0">二维码位置：({{ department.print_template.qr_position_x }}, {{ department.print_template.qr_position_y }})毫米</p>
                                        </div>
                                        <div class="d-flex justify-content-end gap-2">
                                            <a href="{% url 'qrmanager:print_template_preview' department.print_template.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>预览
                                            </a>
                                            <a href="{% url 'qrmanager:print_template_update' department.print_template.id %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit me-1"></i>编辑
                                            </a>
                                            <form action="{% url 'qrmanager:print_template_delete' department.print_template.id %}" method="post" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个模板吗？');">
                                                    <i class="fas fa-trash me-1"></i>删除
                                                </button>
                                            </form>
                                        </div>
                                        {% else %}
                                        <div class="text-center py-4">
                                            {% if public_template %}
                                            <div class="template-preview mb-3">
                                                {% if public_template.background_image %}
                                                <img src="{{ public_template.background_image.url }}" alt="背景图片" class="img-thumbnail w-100" style="max-height: 150px; object-fit: cover;">
                                                {% else %}
                                                <div class="no-image w-100 text-center py-4 bg-light">无背景图</div>
                                                {% endif %}
                                            </div>
                                            <div class="alert alert-info py-2 mb-3">
                                                <i class="fas fa-info-circle me-2"></i>使用公共模板
                                            </div>
                                            <div class="template-info small text-muted mb-3">
                                                <p class="mb-1">打印尺寸：{{ public_template.print_width }} × {{ public_template.print_height }}毫米</p>
                                                <p class="mb-1">二维码尺寸：{{ public_template.qr_size }}毫米</p>
                                                <p class="mb-0">二维码位置：({{ public_template.qr_position_x }}, {{ public_template.qr_position_y }})毫米</p>
                                            </div>
                                            <div class="d-flex justify-content-center gap-2">
                                                <a href="{% url 'qrmanager:print_template_preview' public_template.id %}" class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-eye me-1"></i>预览公共模板
                                                </a>
                                                <a href="{% url 'qrmanager:department_print_template_create' department.id %}" class="btn btn-outline-primary btn-sm">
                                                    <i class="fas fa-plus me-1"></i>创建自定义模板
                                                </a>
                                            </div>
                                            {% else %}
                                            <p class="text-muted mb-3">该科室尚未设置打印模板</p>
                                            <a href="{% url 'qrmanager:department_print_template_create' department.id %}" class="btn btn-primary btn-sm">
                                                <i class="fas fa-plus me-1"></i>创建模板
                                            </a>
                                            {% endif %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </div>
                    </div>

                    <!-- 列表视图 -->
                    <div class="table-responsive list-view" style="display: none;">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>科室</th>
                                    <th>模板状态</th>
                                    <th>打印尺寸</th>
                                    <th>二维码尺寸</th>
                                    <th>更新时间</th>
                                    <th class="text-end">操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for department in departments %}
                                <tr>
                                    <td>{{ department.name }}</td>
                                    <td>
                                        {% if department.print_template %}
                                            {% if department.print_template.is_active %}
                                            <span class="badge bg-success">已启用</span>
                                            {% else %}
                                            <span class="badge bg-danger">已禁用</span>
                                            {% endif %}
                                        {% else %}
                                            {% if public_template %}
                                            <span class="badge bg-info">使用公共模板</span>
                                            {% else %}
                                            <span class="badge bg-warning">未设置</span>
                                            {% endif %}
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if department.print_template %}
                                        {{ department.print_template.print_width }} × {{ department.print_template.print_height }}毫米
                                        {% elif public_template %}
                                        {{ public_template.print_width }} × {{ public_template.print_height }}毫米
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if department.print_template %}
                                        {{ department.print_template.qr_size }}毫米
                                        {% elif public_template %}
                                        {{ public_template.qr_size }}毫米
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if department.print_template %}
                                        {{ department.print_template.updated_at|date:"Y-m-d H:i" }}
                                        {% else %}
                                        -
                                        {% endif %}
                                    </td>
                                    <td class="text-end">
                                        <div class="btn-group">
                                            {% if department.print_template %}
                                            <a href="{% url 'qrmanager:print_template_preview' department.print_template.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>预览
                                            </a>
                                            <a href="{% url 'qrmanager:print_template_update' department.print_template.id %}" class="btn btn-sm btn-outline-secondary">
                                                <i class="fas fa-edit me-1"></i>编辑
                                            </a>
                                            <form action="{% url 'qrmanager:print_template_delete' department.print_template.id %}" method="post" style="display:inline;">
                                                {% csrf_token %}
                                                <button type="submit" class="btn btn-sm btn-outline-danger" onclick="return confirm('确定要删除这个模板吗？');">
                                                    <i class="fas fa-trash me-1"></i>删除
                                                </button>
                                            </form>
                                            {% else %}
                                            <a href="{% url 'qrmanager:department_print_template_create' department.id %}" class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-plus me-1"></i>创建模板
                                            </a>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.template-preview img {
    object-fit: cover;
    border-radius: 4px;
}
.no-image {
    background-color: #f8f9fa;
    border: 1px dashed #dee2e6;
    border-radius: 4px;
    color: #6c757d;
    padding: 1rem;
    text-align: center;
}
.card {
    transition: transform 0.2s ease-in-out;
}
.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.15);
}
</style>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const viewToggle = document.getElementById('viewToggle');
    const gridView = document.querySelector('.grid-view');
    const listView = document.querySelector('.list-view');
    const searchInput = document.getElementById('searchInput');
    const searchButton = document.getElementById('searchButton');
    let isListView = false;

    // 视图切换功能
    viewToggle.addEventListener('click', function() {
        isListView = !isListView;
        if (isListView) {
            gridView.style.display = 'none';
            listView.style.display = 'block';
            viewToggle.innerHTML = '<i class="fas fa-grid me-2"></i>网格视图';
        } else {
            gridView.style.display = 'block';
            listView.style.display = 'none';
            viewToggle.innerHTML = '<i class="fas fa-list me-2"></i>列表视图';
        }
    });

    // 搜索功能
    function performSearch() {
        const searchTerm = searchInput.value.toLowerCase();
        const cards = document.querySelectorAll('.grid-view .col');
        const rows = document.querySelectorAll('.list-view tbody tr');

        // 搜索网格视图
        cards.forEach(card => {
            const departmentName = card.querySelector('.card-title').textContent.toLowerCase();
            if (departmentName.includes(searchTerm)) {
                card.style.display = '';
            } else {
                card.style.display = 'none';
            }
        });

        // 搜索列表视图
        rows.forEach(row => {
            const departmentName = row.querySelector('td:first-child').textContent.toLowerCase();
            if (departmentName.includes(searchTerm)) {
                row.style.display = '';
            } else {
                row.style.display = 'none';
            }
        });
    }

    // 点击搜索按钮时搜索
    searchButton.addEventListener('click', performSearch);

    // 输入框按回车时搜索
    searchInput.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            performSearch();
        }
    });

    // 输入框内容变化时实时搜索
    searchInput.addEventListener('input', performSearch);
});
</script>
{% endblock %} 