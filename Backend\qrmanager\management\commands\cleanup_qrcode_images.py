import os
import shutil
from django.core.management.base import BaseCommand
from django.conf import settings

class Command(BaseCommand):
    help = '清理存储在media/qrcodes目录中的二维码图片文件，因为系统现在使用动态生成二维码'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='强制删除所有二维码图片，不进行确认',
        )

    def handle(self, *args, **options):
        qrcodes_dir = os.path.join(settings.MEDIA_ROOT, 'qrcodes')
        
        if not os.path.exists(qrcodes_dir):
            self.stdout.write(self.style.SUCCESS('二维码目录不存在，无需清理'))
            return
            
        # 计算文件数量
        file_count = len([name for name in os.listdir(qrcodes_dir) if os.path.isfile(os.path.join(qrcodes_dir, name))])
        
        if file_count == 0:
            self.stdout.write(self.style.SUCCESS('二维码目录为空，无需清理'))
            return
            
        # 确认删除
        if not options['force']:
            confirm = input(f'将删除 {file_count} 个二维码图片文件，确认操作? [y/N]: ')
            if confirm.lower() != 'y':
                self.stdout.write(self.style.WARNING('操作已取消'))
                return
        
        # 删除目录及其内容
        shutil.rmtree(qrcodes_dir)
        os.makedirs(qrcodes_dir, exist_ok=True)  # 重新创建空目录
        
        self.stdout.write(self.style.SUCCESS(f'成功清理 {file_count} 个二维码图片文件')) 