# 医院服务评价系统 - 技术文档索引

## 文档概述
本文档库包含医院服务评价系统的完整技术文档，按照软件工程标准编写，供开发团队参考使用。

## 文档结构

### 📋 系统设计文档
- [01-系统架构设计文档](./architecture/01-系统架构设计文档.md) ✅
- [02-数据库设计文档](./database/02-数据库设计文档.md) ✅
- [03-安全设计文档](./security/03-安全设计文档.md) 📝

### 🔧 后端技术文档
- [04-后端详细设计文档](./backend/04-后端详细设计文档.md) ✅
- [05-API接口规范文档](./backend/05-API接口规范文档.md) ✅
- [06-中间件设计文档](./backend/06-中间件设计文档.md) 📝
- [07-业务逻辑文档](./backend/07-业务逻辑文档.md) 📝

### 🎨 前端技术文档
- [08-前端详细设计文档](./frontend/08-前端详细设计文档.md) ✅
- [09-前端组件文档](./frontend/09-前端组件文档.md) 📝
- [10-前端状态管理文档](./frontend/10-前端状态管理文档.md) 📝

### 🔄 业务流程文档
- [11-核心业务流程文档](./business/11-核心业务流程文档.md) ✅
- [12-二维码生成流程文档](./business/12-二维码生成流程文档.md) 📝
- [13-评价提交流程文档](./business/13-评价提交流程文档.md) 📝

### 🚀 部署运维文档
- [14-部署指南文档](./deployment/14-部署指南文档.md) 📝
- [15-运维监控文档](./deployment/15-运维监控文档.md) 📝
- [16-故障排查文档](./deployment/16-故障排查文档.md) 📝

### 👨‍💻 开发者文档
- [17-开发环境搭建文档](./development/17-开发环境搭建文档.md) 📝
- [18-代码规范文档](./development/18-代码规范文档.md) 📝
- [19-测试指南文档](./development/19-测试指南文档.md) 📝

## 图例说明
- ✅ 已完成
- 🔄 进行中
- 📝 待创建

## 文档使用指南

### 新开发者入门
1. 阅读 [系统架构设计文档](./architecture/01-系统架构设计文档.md)
2. 阅读 [开发环境搭建文档](./development/17-开发环境搭建文档.md)
3. 阅读 [代码规范文档](./development/18-代码规范文档.md)

### 后端开发者
1. [后端详细设计文档](./backend/04-后端详细设计文档.md)
2. [API接口规范文档](./backend/05-API接口规范文档.md)
3. [数据库设计文档](./database/02-数据库设计文档.md)

### 前端开发者
1. [前端详细设计文档](./frontend/08-前端详细设计文档.md)
2. [前端组件文档](./frontend/09-前端组件文档.md)
3. [API接口规范文档](./backend/05-API接口规范文档.md)

### 运维人员
1. [部署指南文档](./deployment/14-部署指南文档.md)
2. [运维监控文档](./deployment/15-运维监控文档.md)
3. [故障排查文档](./deployment/16-故障排查文档.md)

## 文档维护

### 更新原则
- 代码变更时同步更新相关文档
- 重大架构变更时更新设计文档
- 新功能开发时补充相关文档

### 审核流程
1. 文档作者编写初稿
2. 技术负责人审核
3. 团队评审通过后发布

### 版本管理
- 文档版本与代码版本保持同步
- 重要变更记录在文档变更日志中

---

**文档维护**: 技术文档组  
**最后更新**: 2025-01-27  
**文档版本**: v1.0