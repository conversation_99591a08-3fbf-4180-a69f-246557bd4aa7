/* 
 * 本地图标库 - 用于替代Font Awesome
 * 使用Unicode字符和CSS样式模拟常用图标
 */

/* 基础图标样式 */
.fas {
  display: inline-block;
  font-style: normal;
  font-variant: normal;
  text-rendering: auto;
  line-height: 1;
  font-family: Arial, sans-serif;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
}

/* 添加图标前缀 */
.fas::before {
  display: inline-block;
  margin-right: 0.25em;
}

/* 医院用户图标 */
.fa-hospital-user::before {
  content: "🏥👤";
}

/* 用户图标 */
.fa-user-circle::before {
  content: "👤";
}

/* 星星图标 */
.fa-star::before {
  content: "⭐";
}

/* 医生图标 */
.fa-user-md::before {
  content: "👨‍⚕️";
}

/* 医院图标 */
.fa-hospital::before {
  content: "🏥";
}

/* 床位图标 */
.fa-bed::before {
  content: "🛏️";
}

/* 用户组图标 */
.fa-users::before {
  content: "👥";
}

/* 医疗评论图标 */
.fa-comment-medical::before {
  content: "💬";
}

/* 纸飞机/发送图标 */
.fa-paper-plane::before {
  content: "📤";
}

/* 勾选图标 */
.fa-check-circle::before {
  content: "✅";
}

/* 警告图标 */
.fa-exclamation-triangle::before {
  content: "⚠️";
}

/* 关闭/取消图标 */
.fa-times::before {
  content: "❌";
}

/* 医疗相关样式 */
.medical-icon::before {
  color: #0078d7;
}

/* 辅助样式 */
.text-center {
  text-align: center;
}

/* 图标大小控制 */
.fa-lg {
  font-size: 1.33em;
  line-height: 0.75em;
  vertical-align: -0.15em;
}

.fa-2x {
  font-size: 2em;
}

.fa-3x {
  font-size: 3em;
}

/* 动画效果 */
.fa-spin {
  animation: fa-spin 2s infinite linear;
}

@keyframes fa-spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 颜色辅助类 */
.text-success {
  color: #28a745;
}

.text-warning {
  color: #ffc107;
}

.text-danger {
  color: #dc3545;
}

/* 错误状态图标样式 */
.error-icon .fas {
  font-size: 2em;
  color: #dc3545;
  margin-bottom: 0.5em;
}

/* 成功状态图标样式 */
.success-icon .fas {
  font-size: 3em;
  color: #28a745;
  margin-bottom: 0.5em;
} 