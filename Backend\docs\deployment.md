# 部署指南

## 系统要求
- Python 3.8+
- SQLite3
- 2GB+ RAM
- 10GB+ 磁盘空间

## 最新更新部署说明

### 二维码管理页面优化 (v1.3.0)

最新版本对二维码管理页面进行了优化，主要变更包括：

1. 移除了不必要的"打印二维码"按钮
2. 移除了"批量打印"按钮和相关模态框
3. 保留了"按科室批量打印"功能

部署时需确保以下文件已更新：
- `qrmanager/templates/qrmanager/qrcode_list.html`
- `qrmanager/templates/qrmanager/department_print_modal.html`

## 部署步骤

### 1. 准备环境
```bash
# 安装 Python 3.8+
# 安装 virtualenv
pip install virtualenv
```

### 2. 克隆代码
```bash
git clone [项目地址]
cd [项目目录]
```

### 3. 创建并激活虚拟环境
```bash
# Windows
python -m venv .venv
.venv\Scripts\activate

# Linux/Mac
python3 -m venv .venv
source .venv/bin/activate
```

### 4. 安装依赖
```bash
pip install -r requirements.txt
```

### 5. 环境配置
1. 复制环境变量模板
   ```bash
   cp .env.example .env
   ```
2. 修改 .env 文件中的配置
   - SECRET_KEY
   - DEBUG
   - ALLOWED_HOSTS
   - DATABASE_URL（如果使用其他数据库）

### 6. 数据库迁移
```bash
python manage.py migrate
```

### 7. 创建超级用户
```bash
python manage.py createsuperuser
```

### 8. 收集静态文件
```bash
python manage.py collectstatic
```

### 9. 配置 Web 服务器
#### 使用 Nginx + Gunicorn

1. 安装 Gunicorn
   ```bash
   pip install gunicorn
   ```

2. Nginx 配置示例
   ```nginx
   server {
       listen 80;
       server_name example.com;

       location /static/ {
           alias /path/to/your/static/;
       }

       location /media/ {
           alias /path/to/your/media/;
       }

       location / {
           proxy_pass http://127.0.0.1:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

3. 启动 Gunicorn
   ```bash
   gunicorn HospitalQRCode.wsgi:application --bind 127.0.0.1:8000 --workers 3 --daemon
   ```

### 10. 配置 SSL（推荐）
使用 Let's Encrypt 获取免费的 SSL 证书：
```bash
sudo certbot --nginx -d example.com
```

## 维护指南

### 1. 数据库备份
```bash
# 创建备份
python manage.py dumpdata > backup.json

# 恢复备份
python manage.py loaddata backup.json
```

### 2. 日志管理
- 日志文件位置：/var/log/hospital_qr/
- 建议使用 logrotate 进行日志轮转

### 3. 系统监控
建议使用以下工具：
- Supervisor：进程管理
- Prometheus：性能监控
- Grafana：数据可视化

### 4. 更新部署
```bash
# 1. 拉取最新代码
git pull

# 2. 安装依赖
pip install -r requirements.txt

# 3. 数据库迁移
python manage.py migrate

# 4. 收集静态文件
python manage.py collectstatic

# 5. 重启服务
sudo systemctl restart gunicorn
```

## 故障排除

### 1. 静态文件无法访问
- 检查 STATIC_ROOT 配置
- 检查 Nginx 静态文件路径配置
- 确认已运行 collectstatic

### 2. 数据库连接错误
- 检查数据库配置
- 确认数据库服务运行状态
- 检查防火墙设置

### 3. 502 Bad Gateway
- 检查 Gunicorn 是否正在运行
- 检查 Nginx 配置
- 查看错误日志

### 4. 二维码打印问题
- **问题**: 按科室批量打印时没有反应
  - 确认已选择正确的科室和打印模板
  - 检查浏览器是否允许弹出窗口
  - 尝试使用不同的浏览器

- **问题**: 打印的二维码质量不佳
  - 确认打印机设置正确（建议使用300DPI或更高）
  - 检查打印模板设置
  - 尝试调整二维码大小参数

## 安全建议
1. 启用 HTTPS
2. 定期更新依赖包
3. 设置强密码策略
4. 配置防火墙
5. 启用 CSRF 保护
6. 限制上传文件大小
7. 配置跨域策略

## 性能优化
1. 启用缓存
2. 优化数据库查询
3. 使用 CDN
4. 配置适当的 worker 数量
5. 启用 Gzip 压缩

## 更新记录
- 2025-03-15: v1.3.0 - 二维码管理页面优化
- 2025-03-07: v1.2.0 - API管理界面优化
- 2024-02-20: v1.0.0 - 初始版本