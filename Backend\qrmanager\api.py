"""
医院服务评价系统API视图
实现RESTful API，支持前后端分离架构
"""

from django.http import JsonResponse
from django.db.models import Q
from django.core.paginator import Paginator
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.contrib.auth.decorators import login_required
import json
import datetime
from django.utils import timezone
from django.conf import settings
import os
from django.shortcuts import get_object_or_404
from .models import Department, Staff, Bed, QRCode, Evaluation, PrintTemplate, Dictionary, DictionaryItem, APIKey, APILog, StaffType, TempToken
from .utils import LoggerHelper
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework.pagination import PageNumberPagination
from rest_framework.permissions import IsAuthenticated
from .security import secure_qr_access, encrypt_qr_param, get_token_expiry
from rest_framework.authentication import SessionAuthentication
from .authentication import APIKeyAuthentication, APIPermissionMixin
from rest_framework import viewsets
from .serializers import (
    DepartmentSerializer, BedSerializer, StaffSerializer, 
    QRCodeSerializer, EvaluationSerializer, PrintTemplateSerializer
)
import base64
import logging
from datetime import datetime, timedelta
import uuid as uuid_module
import re
import hashlib
import hmac
import random
import urllib.parse
from django.db import transaction

from qrmanager.models import (
    QRCode, Department, Bed, Staff, StaffType, Evaluation, 
    OperationLog, APIKey, TempToken
)
from qrmanager.utils import LoggerHelper
from qrmanager.security import secure_qr_access, encrypt_qr_param


def api_response(status="success", data=None, message="", errors=None):
    """
    统一的API响应格式
    """
    if data is None:
        data = {}
    if errors is None:
        errors = {}
    
    response = JsonResponse({
        "status": status,
        "data": data,
        "message": message,
        "errors": errors
    })
    
    # 添加CORS头
    response["Access-Control-Allow-Origin"] = "*"  # 允许所有域名访问，生产环境中应限制为特定域名
    response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
    response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
    
    return response


class JSONResponseMixin:
    """用于处理JSON响应的Mixin类"""
    
    def render_to_json_response(self, context, **response_kwargs):
        response = JsonResponse(context, **response_kwargs)
        
        # 添加CORS头
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        
        return response
    
    def get_data(self, context):
        return context


class TokenAuthMixin:
    """API Token认证Mixin类"""
    allow_anonymous = False
    
    def dispatch(self, request, *args, **kwargs):
        # 处理CORS预检请求
        if request.method == 'OPTIONS':
            response = JsonResponse({})
            response["Access-Control-Allow-Origin"] = "*"
            response["Access-Control-Allow-Methods"] = "GET, POST, PUT, DELETE, OPTIONS"
            response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
            return response
        
        # 记录API调用开始时间
        start_time = timezone.now()
        
        # 检查是否允许匿名访问
        if not self.allow_anonymous:
            # 从请求头获取API密钥
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Token '):
                token = auth_header.split(' ')[1].strip()
            else:
                token = request.GET.get('token', '')
            
            # 验证API密钥
            try:
                api_key = APIKey.objects.get(key=token)
                # 检查密钥是否有效
                if not api_key.is_valid():
                    return api_response(status="error", message="API密钥已过期或无效", errors={"token": "Invalid or expired token"})
                
                # 更新最后使用时间
                api_key.last_used_at = timezone.now()
                api_key.save(update_fields=['last_used_at'])
                
                # 将API密钥附加到请求对象
                request.api_key = api_key
            except APIKey.DoesNotExist:
                return api_response(status="error", message="无效的API密钥", errors={"token": "Invalid token"})
        
        # 执行原始的请求处理
        try:
            response = super().dispatch(request, *args, **kwargs)
            status = 'success'
            status_code = response.status_code
        except Exception as e:
            status = 'error'
            error_message = str(e)
            status_code = 500
            # 创建错误响应
            response = api_response(status="error", message="处理请求时发生错误", errors={"detail": error_message})
        
        # 计算响应时间
        end_time = timezone.now()
        response_time = (end_time - start_time).total_seconds() * 1000  # 毫秒
        
        # 记录API调用日志
        try:
            # 提取请求数据
            if request.method in ['POST', 'PUT', 'PATCH']:
                try:
                    request_data = request.body.decode('utf-8')
                    if request_data and request_data.strip():
                        request_data = json.loads(request_data)
                    else:
                        request_data = {}
                except:
                    request_data = {}
            else:
                request_data = {}
            
            # 创建API日志记录
            APILog.objects.create(
                api_key=getattr(request, 'api_key', None),
                endpoint=request.path,
                method=request.method,
                status_code=status_code,
                status=status,
                response_time=response_time,
                remote_addr=request.META.get('REMOTE_ADDR'),
                user_agent=request.META.get('HTTP_USER_AGENT', ''),
                request_data=request_data,
                error_message=error_message if status == 'error' else '',
            )
        except Exception as log_error:
            # 记录日志失败不应影响API响应
            print(f"Failed to log API call: {log_error}")
        
        return response


@method_decorator(csrf_exempt, name='dispatch')
class BasePaginatedAPIView(JSONResponseMixin, TokenAuthMixin, View):
    """
    基础分页API视图
    提供通用的分页功能
    """
    model = None
    fields = []
    search_fields = []
    default_page_size = 20
    
    def get_queryset(self):
        return self.model.objects.all()
    
    def apply_filters(self, queryset, request):
        return queryset
    
    def apply_search(self, queryset, request):
        search_term = request.GET.get('search', '')
        if search_term and self.search_fields:
            q_objects = Q()
            for field in self.search_fields:
                q_objects |= Q(**{f"{field}__icontains": search_term})
            queryset = queryset.filter(q_objects)
        return queryset
    
    def serialize_object(self, obj):
        """将模型对象序列化为字典"""
        data = {}
        for field in self.fields:
            if hasattr(obj, field):
                data[field] = getattr(obj, field)
                # 处理外键字段
                if field.endswith('_id') and hasattr(obj, field[:-3]):
                    related_obj = getattr(obj, field[:-3])
                    if related_obj:
                        data[field[:-3]] = {
                            'id': related_obj.id,
                            'name': str(related_obj)
                        }
        return data
    
    def get(self, request, *args, **kwargs):
        # 获取查询集并应用过滤和搜索
        queryset = self.get_queryset()
        queryset = self.apply_filters(queryset, request)
        queryset = self.apply_search(queryset, request)
        
        # 获取分页参数
        page = int(request.GET.get('page', 1))
        page_size = int(request.GET.get('page_size', self.default_page_size))
        
        # 应用分页
        paginator = Paginator(queryset, page_size)
        page_obj = paginator.get_page(page)
        
        # 构造响应数据
        results = [self.serialize_object(obj) for obj in page_obj.object_list]
        data = {
            'results': results,
            'count': paginator.count,
            'next': f"?page={page+1}" if page_obj.has_next() else None,
            'previous': f"?page={page-1}" if page_obj.has_previous() else None
        }
        
        # 记录日志
        LoggerHelper.log_operation(
            user=request.user if request.user.is_authenticated else None,
            action=f"API查询{self.model.__name__}",
            description=f"通过API查询{self.model.__name__}列表",
            status='success'
        )
        
        return api_response(
            data=data,
            message=f"获取{self.model._meta.verbose_name}列表成功"
        )


@method_decorator(csrf_exempt, name='dispatch')
class BaseDetailAPIView(JSONResponseMixin, TokenAuthMixin, View):
    """
    基础详情API视图
    提供单个对象的查询、更新和删除功能
    """
    model = None
    fields = []
    
    def get_object(self, pk):
        return get_object_or_404(self.model, pk=pk)
    
    def serialize_object(self, obj):
        """将模型对象序列化为字典"""
        data = {}
        for field in self.fields:
            if hasattr(obj, field):
                data[field] = getattr(obj, field)
                # 处理外键字段
                if field.endswith('_id') and hasattr(obj, field[:-3]):
                    related_obj = getattr(obj, field[:-3])
                    if related_obj:
                        data[field[:-3]] = {
                            'id': related_obj.id,
                            'name': str(related_obj)
                        }
        return data
    
    def get(self, request, pk, *args, **kwargs):
        obj = self.get_object(pk)
        data = self.serialize_object(obj)
        
        # 记录日志
        LoggerHelper.log_operation(
            user=request.user if request.user.is_authenticated else None,
            action=f"API查询{self.model.__name__}详情",
            description=f"通过API查询{self.model.__name__} ID: {pk}的详情",
            status='success'
        )
        
        return api_response(
            data=data,
            message=f"获取{self.model._meta.verbose_name}详情成功"
        )
    
    def put(self, request, pk, *args, **kwargs):
        obj = self.get_object(pk)
        
        try:
            data = json.loads(request.body)
            
            # 更新字段
            for field in self.fields:
                if field in data and hasattr(obj, field):
                    setattr(obj, field, data[field])
            
            obj.save()
            
            # 记录日志
            LoggerHelper.log_operation(
                user=request.user if request.user.is_authenticated else None,
                action=f"API更新{self.model.__name__}",
                description=f"通过API更新{self.model.__name__} ID: {pk}",
                status='success'
            )
            
            return api_response(
                data=self.serialize_object(obj),
                message=f"更新{self.model._meta.verbose_name}成功"
            )
        except Exception as e:
            # 记录日志
            LoggerHelper.log_operation(
                user=request.user if request.user.is_authenticated else None,
                action=f"API更新{self.model.__name__}失败",
                description=f"通过API更新{self.model.__name__} ID: {pk}失败: {str(e)}",
                status='error'
            )
            
            return api_response(
                status="error",
                message=f"更新{self.model._meta.verbose_name}失败",
                errors={"detail": str(e)}
            )
    
    def delete(self, request, pk, *args, **kwargs):
        obj = self.get_object(pk)
        
        try:
            obj.delete()
            
            # 记录日志
            LoggerHelper.log_operation(
                user=request.user if request.user.is_authenticated else None,
                action=f"API删除{self.model.__name__}",
                description=f"通过API删除{self.model.__name__} ID: {pk}",
                status='success'
            )
            
            return api_response(
                message=f"删除{self.model._meta.verbose_name}成功"
            )
        except Exception as e:
            # 记录日志
            LoggerHelper.log_operation(
                user=request.user if request.user.is_authenticated else None,
                action=f"API删除{self.model.__name__}失败",
                description=f"通过API删除{self.model.__name__} ID: {pk}失败: {str(e)}",
                status='error'
            )
            
            return api_response(
                status="error",
                message=f"删除{self.model._meta.verbose_name}失败",
                errors={"detail": str(e)}
            )


class DepartmentListAPI(BasePaginatedAPIView):
    """科室列表API"""
    model = Department
    fields = ['id', 'code', 'name', 'remarks', 'created_at', 'updated_at']
    search_fields = ['code', 'name']


class DepartmentDetailAPI(BaseDetailAPIView):
    """科室详情API"""
    model = Department
    fields = ['id', 'code', 'name', 'remarks', 'created_at', 'updated_at']


class BedListAPI(BasePaginatedAPIView):
    """床位列表API"""
    model = Bed
    fields = ['id', 'number', 'department_id', 'area', 'is_active', 'staff_id']
    search_fields = ['number']
    
    def apply_filters(self, queryset, request):
        # 根据科室筛选
        department_id = request.GET.get('department')
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        
        # 根据状态筛选
        is_active = request.GET.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)
        
        return queryset


class BedDetailAPI(BaseDetailAPIView):
    """床位详情API"""
    model = Bed
    fields = ['id', 'number', 'department_id', 'area', 'is_active', 'staff_id']


class StaffListAPI(BasePaginatedAPIView):
    """工作人员列表API"""
    model = Staff
    fields = ['id', 'work_number', 'name', 'staff_type_id', 'title', 'department_id', 'photo']
    search_fields = ['work_number', 'name']
    
    def apply_filters(self, queryset, request):
        # 根据科室筛选
        department_id = request.GET.get('department')
        if department_id:
            queryset = queryset.filter(department_id=department_id)
        
        # 根据人员类型筛选
        staff_type = request.GET.get('staff_type')
        if staff_type:
            # 首先尝试通过StaffType模型查询
            try:
                # 尝试通过code字段查询StaffType
                staff_type_obj = StaffType.objects.filter(
                    code=staff_type,
                    is_active=True
                ).first()
                
                if staff_type_obj:
                    # 如果找到StaffType，使用它进行筛选
                    queryset = queryset.filter(staff_type=staff_type_obj)
                else:
                    # 如果找不到StaffType，尝试通过DictionaryItem查询
                    try:
                        # 尝试通过code字段查询DictionaryItem
                        staff_type_item = DictionaryItem.objects.get(
                            dictionary__code='staff_type',
                            code=staff_type,
                            is_active=True
                        )
                        # 使用DictionaryItem进行筛选
                        queryset = queryset.filter(staff_type=staff_type_item)
                    except DictionaryItem.DoesNotExist:
                        # 如果找不到对应的DictionaryItem，尝试通过ID查询
                        try:
                            staff_type_id = int(staff_type)
                            queryset = queryset.filter(staff_type_id=staff_type_id)
                        except ValueError:
                            # 如果无法转换为整数，则返回空查询集
                            queryset = queryset.none()
            except Exception:
                # 如果出现任何错误，尝试通过ID查询
                try:
                    staff_type_id = int(staff_type)
                    queryset = queryset.filter(staff_type_id=staff_type_id)
                except ValueError:
                    # 如果无法转换为整数，则返回空查询集
                    queryset = queryset.none()
        
        return queryset
    
    def serialize_object(self, obj):
        data = {}
        for field in self.fields:
            if field == 'photo':
                # 处理照片字段
                if obj.photo:
                    data['photo_url'] = obj.photo.url
                else:
                    data['photo_url'] = None
                continue
            
            if hasattr(obj, field):
                value = getattr(obj, field)
                # 处理外键字段
                if field.endswith('_id') and hasattr(obj, field[:-3]):
                    related_obj = getattr(obj, field[:-3])
                    if related_obj:
                        data[field[:-3]] = {
                            'id': related_obj.id,
                            'name': str(related_obj)
                        }
                else:
                    data[field] = value
        
        return data


class StaffDetailAPI(BaseDetailAPIView):
    """工作人员详情API"""
    model = Staff
    fields = ['id', 'work_number', 'name', 'staff_type_id', 'title', 'department_id', 'photo']
    
    def serialize_object(self, obj):
        data = super().serialize_object(obj)
        
        # 添加照片URL
        if obj.photo:
            data['photo_url'] = obj.photo.url
        else:
            data['photo_url'] = None
        
        return data


class QRCodeListAPI(BasePaginatedAPIView):
    """二维码列表API"""
    model = QRCode
    fields = ['id', 'name', 'description', 'code', 'bed_id', 'created_at', 'updated_at', 'is_active']
    search_fields = ['name', 'description']
    
    def apply_filters(self, queryset, request):
        # 根据科室筛选
        department_id = request.GET.get('department')
        if department_id:
            queryset = queryset.filter(bed__department_id=department_id)
        
        # 根据状态筛选
        is_active = request.GET.get('is_active')
        if is_active is not None:
            is_active = is_active.lower() == 'true'
            queryset = queryset.filter(is_active=is_active)
        
        return queryset
    
    def serialize_object(self, obj):
        data = super().serialize_object(obj)
        
        # 添加二维码图片URL和评价URL
        data['qr_image_url'] = obj.get_qr_image_url()
        data['evaluation_url'] = obj.get_evaluation_url()
        
        return data


class QRCodeDetailAPI(BaseDetailAPIView):
    """二维码详情API"""
    model = QRCode
    fields = ['id', 'name', 'description', 'code', 'bed_id', 'created_at', 'updated_at', 'is_active']
    
    def serialize_object(self, obj):
        data = super().serialize_object(obj)
        
        # 添加二维码图片URL和评价URL
        data['qr_image_url'] = obj.get_qr_image_url()
        data['evaluation_url'] = obj.get_evaluation_url()
        
        return data


class EvaluationListAPI(APIView):
    """评价列表API"""
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        """获取评价列表"""
        try:
            # 获取评价数据
            queryset = Evaluation.objects.select_related(
                'qr_code',
                'bed',  # 添加bed关联
                'bed__department',  # 修改为直接使用bed关联
                'staff'
            ).order_by('-created_at')
            
            # 应用筛选
            queryset = self.apply_filters(queryset, request)
            
            # 分页
            paginator = PageNumberPagination()
            paginator.page_size = 20
            result_page = paginator.paginate_queryset(queryset, request)
            
            # 序列化
            serializer = EvaluationSerializer(result_page, many=True)
            
            return paginator.get_paginated_response(serializer.data)
        except Exception as e:
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
    
    def apply_filters(self, queryset, request):
        # 根据科室筛选
        department_id = request.GET.get('department')
        if department_id:
            queryset = queryset.filter(bed__department_id=department_id)  # 修改为直接使用bed关联
        
        # 根据评分区间筛选
        rating_min = request.GET.get('rating_min')
        if rating_min:
            queryset = queryset.filter(rating__gte=int(rating_min))
        
        rating_max = request.GET.get('rating_max')
        if rating_max:
            queryset = queryset.filter(rating__lte=int(rating_max))
        
        # 根据情感倾向筛选
        sentiment = request.GET.get('sentiment')
        if sentiment:
            queryset = queryset.filter(sentiment=sentiment)
        
        # 根据日期区间筛选
        start_date = request.GET.get('start_date')
        if start_date:
            start_date = datetime.datetime.strptime(start_date, '%Y-%m-%d').replace(tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__gte=start_date)
        
        end_date = request.GET.get('end_date')
        if end_date:
            end_date = datetime.datetime.strptime(end_date, '%Y-%m-%d').replace(hour=23, minute=59, second=59, tzinfo=timezone.get_current_timezone())
            queryset = queryset.filter(created_at__lte=end_date)
        
        return queryset


class EvaluationDetailAPI(BaseDetailAPIView):
    """评价详情API"""
    model = Evaluation
    fields = ['id', 'qr_code_id', 'rating', 'comment', 'photo', 'sentiment', 'created_at', 'staff_id']
    
    def serialize_object(self, obj):
        data = super().serialize_object(obj)
        
        # 添加评分和情感倾向的显示值
        data['rating_display'] = obj.get_rating_display()
        data['sentiment_display'] = obj.get_sentiment_display()
        
        # 添加照片URL
        if obj.photo:
            data['photo_url'] = obj.photo.url
        else:
            data['photo_url'] = None
        
        return data


class PrintTemplateAPI(BaseDetailAPIView):
    """打印模板API"""
    model = PrintTemplate
    fields = [
        'id', 'department_id', 'name', 'background_image', 'print_width', 
        'print_height', 'qr_position_x', 'qr_position_y', 'qr_size', 
        'is_active', 'is_public'
    ]
    
    def serialize_object(self, obj):
        data = super().serialize_object(obj)
        
        # 添加背景图片URL
        if obj.background_image:
            data['background_image_url'] = obj.background_image.url
        else:
            data['background_image_url'] = None
        
        return data


@method_decorator(csrf_exempt, name='dispatch')
class PublicEvaluationSubmitAPI(JSONResponseMixin, View):
    """
    公开的评价提交API
    使用原始加密参数认证，不再需要临时令牌
    支持多工作人员评价（满意/不满意二元选择）
    支持对不同工作人员分别评价
    新增支持住院号和联系方式
    """
    # 添加工作人员评价数量限制常量
    MAX_SATISFIED = 3  # 最多3个满意的工作人员
    MAX_UNSATISFIED = 3  # 最多3个不满意的工作人员
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response["Access-Control-Allow-Origin"] = "*"
        response["Access-Control-Allow-Methods"] = "POST, OPTIONS"
        response["Access-Control-Allow-Headers"] = "Content-Type, Authorization"
        return response
    
    def post(self, request, *args, **kwargs):
        # 记录请求信息
        logger = logging.getLogger('api')
        client_ip = self._get_client_ip(request)
        logger.info(f"收到评价提交请求: IP:{client_ip}, UA:{request.META.get('HTTP_USER_AGENT')}")
        
        try:
            # 解析请求数据
            try:
                data = json.loads(request.body)
            except json.JSONDecodeError:
                return self.render_to_json_response({
                    "status": "error",
                    "message": "无效的JSON数据"
                }, status=400)
            
            # 验证必填字段 - 工作人员评价改为可选
            required_fields = ['qr_param', 'comment']
            missing_fields = [field for field in required_fields if field not in data]

            if missing_fields:
                return self.render_to_json_response({
                    "status": "error",
                    "message": f"缺少必填字段: {', '.join(missing_fields)}"
                }, status=400)
            
            # 获取加密参数
            qr_param = data.get('qr_param')
            
            # 验证加密参数并获取二维码UUID
            try:
                # 移除参数开头的等号（如果有）
                if qr_param.startswith('='):
                    logger.info(f"移除参数开头的等号: {qr_param[:10]}...")
                    qr_param = qr_param[1:]
                
                # 验证参数长度和格式
                if len(qr_param) < 20:
                    logger.warning(f"请求参数长度不足: {len(qr_param)}")
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "二维码参数格式无效"
                    }, status=400)
                
                # 使用安全访问函数解密并验证参数
                logger.info(f"开始解密参数: {qr_param[:20]}...")
                uuid = secure_qr_access(qr_param)
                
                # 验证解密后的数据是否有效
                if not uuid or not isinstance(uuid, str) or len(uuid) < 32:
                    logger.warning(f"解密结果无效: {uuid}")
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "二维码参数无效"
                    }, status=400)
                    
                logger.info(f"解密结果: {uuid}")
                
                # 获取二维码信息
                try:
                    qr_code = QRCode.objects.get(code=uuid, is_active=True)
                except QRCode.DoesNotExist:
                    logger.warning(f"找不到对应的二维码: {uuid}")
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "二维码无效或已过期"
                    }, status=404)
                
            except Exception as e:
                # 记录错误但不暴露详细信息
                logger.warning(f"二维码验证失败: {str(e)}, IP: {client_ip}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "二维码验证失败",
                    "detail": "无法解析二维码数据"
                }, status=400)
            
            # 获取科室和床位
            bed = qr_code.bed
            
            # 获取评论内容
            comment = data.get('comment', '')
            
            # 获取住院号和联系方式 (新增)
            hospital_number = data.get('hospital_number', '')
            phone_number = data.get('phone_number', '')

            # 获取医院整体评分 (1-5星)
            hospital_rating = data.get('hospital_rating')
            if hospital_rating is not None:
                try:
                    hospital_rating = int(hospital_rating)
                    if not (1 <= hospital_rating <= 5):
                        return self.render_to_json_response({
                            "status": "error",
                            "message": "医院评分必须在1-5之间"
                        }, status=400)
                except (ValueError, TypeError):
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "医院评分必须是有效的数字"
                    }, status=400)
            
            # 获取工作人员评价列表（可选）
            staff_evaluations = data.get('staff_evaluations', [])
            if not isinstance(staff_evaluations, list):
                return self.render_to_json_response({
                    "status": "error",
                    "message": "工作人员评价列表格式无效"
                }, status=400)

            # 允许空的工作人员评价列表
            if len(staff_evaluations) == 0:
                logger.info(f"用户选择不评价工作人员, IP: {client_ip}")
                # 创建一个默认的评价记录，不包含工作人员信息
                staff_evaluations = []
            
            # 验证每个工作人员评价的格式
            for staff_eval in staff_evaluations:
                if not isinstance(staff_eval, dict) or 'staff_id' not in staff_eval or 'is_satisfied' not in staff_eval:
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "工作人员评价格式无效，每项必须包含staff_id和is_satisfied字段"
                    }, status=400)
                
                # 验证is_satisfied为布尔值
                if not isinstance(staff_eval['is_satisfied'], bool):
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "满意度值必须是布尔值(true/false)"
                    }, status=400)
            
            # 添加工作人员评价数量限制验证
            satisfied_count = sum(1 for eval_data in staff_evaluations if eval_data['is_satisfied'])
            unsatisfied_count = sum(1 for eval_data in staff_evaluations if not eval_data['is_satisfied'])
            
            # 验证满意和不满意的工作人员数量不超过限制
            if satisfied_count > self.MAX_SATISFIED:
                logger.warning(f"满意工作人员数量超出限制: {satisfied_count}/{self.MAX_SATISFIED}, IP: {client_ip}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": f"满意评价超出限制，最多选择{self.MAX_SATISFIED}个"
                }, status=400)
                
            if unsatisfied_count > self.MAX_UNSATISFIED:
                logger.warning(f"不满意工作人员数量超出限制: {unsatisfied_count}/{self.MAX_UNSATISFIED}, IP: {client_ip}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": f"不满意评价超出限制，最多选择{self.MAX_UNSATISFIED}个"
                }, status=400)
            
            # 如果有工作人员评价，验证工作人员ID的有效性
            if len(staff_evaluations) > 0:
                staff_ids = [eval_data['staff_id'] for eval_data in staff_evaluations]
                valid_staff = Staff.objects.filter(id__in=staff_ids).count()

                if valid_staff == 0:
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "所有工作人员ID无效，无法创建评价"
                    }, status=400)
            
            # ===== 修改后的评价创建逻辑 =====
            # 直接在Evaluation中存储工作人员评价信息，不再使用StaffEvaluation表
            
            # 确定整体满意度
            # 如果有工作人员评价，基于工作人员评价确定满意度
            # 如果没有工作人员评价，根据医院评分或默认处理
            if len(staff_evaluations) > 0:
                is_any_satisfied = any(eval_data['is_satisfied'] for eval_data in staff_evaluations)
            else:
                # 没有工作人员评价时，如果有医院评分则根据评分确定，否则默认为中性（不影响情感倾向）
                if hospital_rating is not None:
                    is_any_satisfied = hospital_rating >= 4  # 4-5星为满意
                else:
                    is_any_satisfied = None  # 既没有评分也没有工作人员评价，设为None表示中性
                logger.info(f"没有工作人员评价，医院评分: {hospital_rating}, 设置整体满意度: {is_any_satisfied}, IP: {client_ip}")

            # 根据医院评分确定情感倾向
            def determine_sentiment(hospital_rating, is_any_satisfied):
                """根据医院评分和工作人员评价确定情感倾向"""
                if hospital_rating is not None:
                    # 优先使用医院评分
                    if hospital_rating <= 2:
                        return 'negative'  # 1-2星为负面
                    elif hospital_rating == 3:
                        return 'neutral'   # 3星为中性
                    else:  # 4-5星
                        return 'positive'  # 4-5星为正面
                else:
                    # 没有医院评分时，根据工作人员评价确定
                    if len(staff_evaluations) > 0:
                        return 'positive' if is_any_satisfied else 'negative'
                    else:
                        # 既没有医院评分也没有工作人员评价，不设置情感倾向
                        return None

            sentiment = determine_sentiment(hospital_rating, is_any_satisfied)
            logger.info(f"确定情感倾向: {sentiment}, 医院评分: {hospital_rating}, 工作人员满意: {is_any_satisfied}, IP: {client_ip}")

            # 创建主评价记录
            try:
                # 创建主评价记录
                # 处理is_any_satisfied为None的情况（既没有医院评分也没有工作人员评价）
                final_is_satisfied = is_any_satisfied if is_any_satisfied is not None else True

                evaluation = Evaluation(
                    is_satisfied=final_is_satisfied,  # 只要有一个满意，整体就是满意
                    comment=comment,
                    bed=bed,
                    qr_code=qr_code,
                    sentiment=sentiment,  # 使用新的情感倾向逻辑
                    hospital_number=hospital_number,
                    phone_number=phone_number,
                    hospital_rating=hospital_rating  # 添加医院整体评分
                )
                
                # 先保存评价记录
                evaluation.save()
                
                # 直接在Evaluation中添加工作人员评价信息
                successful_staff_evals = []
                failed_staff_ids = []

                # 处理工作人员评价（如果有的话）
                satisfied_index = 1
                unsatisfied_index = 1

                # 如果没有工作人员评价，记录日志并继续
                if len(staff_evaluations) == 0:
                    logger.info(f"用户选择不评价任何工作人员, 评价ID: {evaluation.id}, IP: {client_ip}")

                for staff_eval in staff_evaluations:
                    staff_id = staff_eval['staff_id']
                    is_satisfied = staff_eval['is_satisfied']
                    
                    try:
                        staff = Staff.objects.get(id=staff_id)
                        
                        # 获取员工类型ID和名称
                        staff_type_id = staff.staff_type_id if staff.staff_type else None
                        staff_type_name = getattr(staff.staff_type, 'name', '未知类型') if staff.staff_type else '未知类型'
                        
                        # 添加调试日志
                        logger.debug(f"工作人员ID: {staff.id}, 姓名: {staff.name}, 类型ID: {staff_type_id}, 类型名称: {staff_type_name}")
                        
                        # 根据满意度决定添加到哪个字段
                        if is_satisfied:
                            if satisfied_index <= 3:  # 最多3个满意
                                setattr(evaluation, f'satisfied_staff{satisfied_index}_id', staff.id)
                                setattr(evaluation, f'satisfied_staff{satisfied_index}_name', staff.name)
                                setattr(evaluation, f'satisfied_staff{satisfied_index}_type', staff_type_id)
                                setattr(evaluation, f'satisfied_staff{satisfied_index}_type_name', staff_type_name)
                                
                                # 添加调试日志
                                logger.debug(f"设置满意员工{satisfied_index}的类型名称: {staff_type_name}")
                                
                                satisfied_index += 1
                        else:
                            if unsatisfied_index <= 3:  # 最多3个不满意
                                setattr(evaluation, f'unsatisfied_staff{unsatisfied_index}_id', staff.id)
                                setattr(evaluation, f'unsatisfied_staff{unsatisfied_index}_name', staff.name)
                                setattr(evaluation, f'unsatisfied_staff{unsatisfied_index}_type', staff_type_id)
                                setattr(evaluation, f'unsatisfied_staff{unsatisfied_index}_type_name', staff_type_name)
                                
                                # 添加调试日志
                                logger.debug(f"设置不满意员工{unsatisfied_index}的类型名称: {staff_type_name}")
                                
                                unsatisfied_index += 1
                        
                        successful_staff_evals.append({
                            "id": staff.id,
                            "name": staff.name,
                            "is_satisfied": is_satisfied,
                            "type": staff_type_name
                        })
                        
                    except Staff.DoesNotExist:
                        failed_staff_ids.append(staff_id)
                
                # 保存更新后的评价记录
                evaluation.save()
                
                # 记录操作日志
                LoggerHelper.log_operation(
                    user=request.user if request.user.is_authenticated else None,
                    action="API提交评价",
                    description=f"通过API提交评价，床位ID: {bed.id if bed else 'N/A'}，评价ID: {evaluation.id}，关联工作人员数: {len(successful_staff_evals)}",
                    status='success',
                    extra_data={
                        "ip_address": client_ip,
                        "user_agent": request.META.get('HTTP_USER_AGENT'),
                        "qr_param": qr_param[:20] + "..." if len(qr_param) > 20 else qr_param,
                        "hospital_number": hospital_number[:3] + "***" if hospital_number else "N/A",
                        "phone_number": phone_number[:3] + "****" if phone_number else "N/A"
                    }
                )
                
                # 构建响应数据
                response_data = {
                    "status": "success",
                    "message": "评价提交成功",
                    "data": {
                        "evaluation_id": evaluation.id,
                        "successful_count": len(successful_staff_evals),
                        "failed_count": len(failed_staff_ids),
                        "failed_staff_ids": failed_staff_ids,
                        "staff_evaluations": successful_staff_evals,
                        "hospital_rating": hospital_rating,  # 返回医院评分
                    }
                }
                
                return self.render_to_json_response(response_data)
                
            except Exception as e:
                logger.error(f"创建评价记录时出错: {str(e)}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "创建评价记录时出错",
                    "detail": str(e)
                }, status=500)
            
        except Exception as e:
            # 记录未处理的异常
            logger.error(f"评价提交时发生未处理的异常: {str(e)}", exc_info=True)
            return self.render_to_json_response({
                "status": "error",
                "message": "服务器内部错误",
                "detail": str(e) if settings.DEBUG else "请联系管理员"
            }, status=500)
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class StaffTypeAPI(JSONResponseMixin, TokenAuthMixin, View):
    """工作人员类型API"""
    allow_anonymous = False
    
    def get(self, request, *args, **kwargs):
        try:
            # 首先尝试从StaffType模型获取数据
            staff_types_data = []
            
            # 尝试从StaffType模型获取数据
            try:
                staff_types = StaffType.objects.filter(is_active=True).order_by('display_order')
                
                if staff_types.exists():
                    # 如果StaffType模型有数据，使用它
                    staff_types_data = [{
                        'id': item.code,  # 使用code作为id，保持与旧API兼容
                        'name': item.name,
                        'icon': item.icon if hasattr(item, 'icon') else ''
                    } for item in staff_types]
                else:
                    # 如果StaffType模型没有数据，尝试从DictionaryItem获取
                    try:
                        staff_type_dict = Dictionary.objects.get(code='staff_type')
                        dict_items = DictionaryItem.objects.filter(
                            dictionary=staff_type_dict,
                            is_active=True
                        ).order_by('sort_order')
                        
                        staff_types_data = [{
                            'id': item.code,
                            'name': item.name
                        } for item in dict_items]
                    except (Dictionary.DoesNotExist, Exception):
                        # 如果字典也不存在，返回空列表
                        staff_types_data = []
            except Exception as e:
                # 如果StaffType模型不存在或出错，尝试从DictionaryItem获取
                try:
                    staff_type_dict = Dictionary.objects.get(code='staff_type')
                    dict_items = DictionaryItem.objects.filter(
                        dictionary=staff_type_dict,
                        is_active=True
                    ).order_by('sort_order')
                    
                    staff_types_data = [{
                        'id': item.code,
                        'name': item.name
                    } for item in dict_items]
                except (Dictionary.DoesNotExist, Exception):
                    # 如果字典也不存在，返回空列表
                    staff_types_data = []
            
            return JsonResponse(staff_types_data, safe=False)
            
        except Exception as e:
            # 记录错误但返回空列表
            print(f"获取工作人员类型时出错: {str(e)}")
            return JsonResponse([], safe=False)


class SecureQRCodeAPI(JSONResponseMixin, TokenAuthMixin, View):
    """安全的二维码API，需要API令牌认证"""
    
    def get(self, request, qr_code, *args, **kwargs):
        try:
            # 使用安全访问函数处理参数
            try:
                # 尝试解密参数
                uuid = secure_qr_access(qr_code)
            except ValueError as e:
                return JsonResponse({
                    'success': False,
                    'error': '二维码验证失败',
                    'message': str(e)
                }, status=400)
            
            # 查找二维码
            qrcode_obj = get_object_or_404(QRCode, code=uuid)
            
            # 获取关联的床位和科室信息
            bed = qrcode_obj.bed
            department = bed.department if bed else None
            staff = bed.staff if bed else None
            
            # 构建响应数据
            response_data = {
                'success': True,
                'qrcode': {
                    'id': qrcode_obj.id,
                    'code': qrcode_obj.code,
                    'created_at': qrcode_obj.created_at.isoformat(),
                },
                'bed': {
                    'id': bed.id,
                    'number': bed.number,
                    'area': bed.area,
                } if bed else None,
                'department': {
                    'id': department.id,
                    'name': department.name,
                    'code': department.code,
                } if department else None,
                'staff': {
                    'id': staff.id,
                    'name': staff.name,
                    'work_number': staff.work_number,
                    'staff_type': {
                        'id': staff.staff_type.code,
                        'name': staff.staff_type.name
                    } if staff and staff.staff_type else None,
                    'title': staff.title.name if staff and staff.title else None,
                    'photo_url': request.build_absolute_uri(staff.photo.url) if staff and staff.photo else None,
                } if staff else None,
            }
            
            # 记录API访问
            LoggerHelper.log_operation(
                user=None,
                action="secure_api_access",
                description=f"安全API访问: 获取二维码信息 {qr_code}",
                status='success',
                extra_data={
                    'qr_code': qr_code,
                    'uuid': uuid,
                    'ip': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT'),
                }
            )
            
            return JsonResponse(response_data)
        except Exception as e:
            # 记录错误
            LoggerHelper.log_operation(
                user=None,
                action="secure_api_access_failed",
                description=f"安全API访问失败: {str(e)}",
                status='error',
                extra_data={
                    'qr_code': qr_code,
                    'error': str(e),
                    'ip': request.META.get('REMOTE_ADDR'),
                    'user_agent': request.META.get('HTTP_USER_AGENT'),
                }
            )
            
            return JsonResponse({
                'success': False,
                'error': '无法获取二维码信息',
                'message': str(e)
            }, status=404)


@method_decorator(csrf_exempt, name='dispatch')
class QRCodeVerifyAPI(JSONResponseMixin, View):
    """
    二维码验证API
    验证二维码并返回完整数据包，以及一次性临时令牌用于后续提交数据
    仅支持 /q/<加密参数> 格式或POST请求
    允许匿名访问，无需API令牌
    """
    # 设置为允许匿名访问
    allow_anonymous = True
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'GET, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        return response
    
    def post(self, request, *args, **kwargs):
        """处理二维码验证请求"""
        try:
            # 记录请求信息
            logger = logging.getLogger('api')
            logger.info(f"收到二维码POST验证请求: IP:{request.META.get('REMOTE_ADDR')}, UA:{request.META.get('HTTP_USER_AGENT')}")
            
            # 记录所有请求头信息
            auth_header = request.META.get('HTTP_AUTHORIZATION', '')
            logger.info(f"Authorization头: {auth_header}")
            for header, value in request.META.items():
                if header.startswith('HTTP_'):
                    logger.info(f"请求头 {header}: {value}")
            
            # 获取请求数据
            try:
                data = json.loads(request.body)
                logger.info(f"请求数据: {data}")
            except json.JSONDecodeError as e:
                logger.error(f"解析请求数据失败: {str(e)}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "请求数据格式错误"
                }, status=400)
            
            # 获取加密参数，支持两种参数名
            encrypted_param = data.get('qr_param') or data.get('encrypted_param')
            
            if not encrypted_param:
                logger.warning("POST请求缺少加密参数")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "缺少必要参数 qr_param"
                }, status=400)
                
            # 验证参数长度和格式
            if len(encrypted_param) < 20:
                logger.warning(f"POST请求参数长度不足: {len(encrypted_param)}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "二维码参数格式无效"
                }, status=400)
            
            # 使用安全的二维码验证方法处理请求
            return self._process_qrcode_verification(request, encrypted_param)
            
        except Exception as e:
            # 记录错误但不暴露详细信息
            logger = logging.getLogger('api')
            logger.error(f"处理POST请求时发生异常: {str(e)}")
            return self.render_to_json_response({
                "status": "error",
                "message": "系统错误，请稍后再试"
            }, status=500)
    
    def _validate_qrcode_param(self, encrypted_param, logger):
        """
        验证二维码参数格式 - 只支持最新的56字符格式

        参数:
            encrypted_param (str): 加密的二维码参数
            logger (Logger): 日志记录器

        返回:
            tuple: (验证是否通过, 错误信息)
        """
        # 基本检查
        if not encrypted_param:
            logger.warning("二维码参数为空")
            return False, "二维码参数不能为空"

        # 🔒 最新格式验证：固定56字符Base64字符串
        logger.info(f"验证最新格式参数: {encrypted_param[:10]}...")

        # 长度检查 - 最新格式固定56字符
        if len(encrypted_param) != 56:
            logger.warning(f"参数长度错误: {len(encrypted_param)} != 56")
            return False, "二维码参数长度不正确"

        # 字符集检查 - 确保只包含Base64字符集
        if not re.match(r'^[A-Za-z0-9+/=]+$', encrypted_param):
            logger.warning(f"参数包含非法字符: {encrypted_param[:10]}...")
            return False, "二维码参数包含非法字符"

        # 🔒 拒绝旧格式
        if '.' in encrypted_param:
            logger.warning(f"检测到已废弃的旧格式: {encrypted_param[:10]}...")
            return False, "请使用最新版本的二维码"

        return True, ""

    def _process_qrcode_verification(self, request, encrypted_param):
        """
        处理二维码验证的核心逻辑，验证参数并返回相关信息
        
        参数:
            request (HttpRequest): 请求对象
            encrypted_param (str): 加密的二维码参数
            
        返回:
            JsonResponse: 包含验证结果的JSON响应
        """
        logger = logging.getLogger('api')
        client_ip = self._get_client_ip(request)
        
        try:
            # 移除参数开头的等号（如果有）
            if encrypted_param.startswith('='):
                logger.info(f"移除参数开头的等号: {encrypted_param[:10]}...")
                encrypted_param = encrypted_param[1:]
            
            # 严格验证参数格式
            is_valid, error_message = self._validate_qrcode_param(encrypted_param, logger)
            if not is_valid:
                return self.render_to_json_response({
                    "status": "error",
                    "message": error_message
                }, status=400)
            
            # 解密参数
            try:
                logger.info(f"开始解密参数: {encrypted_param[:20]}...")
                uuid = secure_qr_access(encrypted_param)
                
                # 验证解密后的数据是否有效
                if not uuid or not isinstance(uuid, str) or len(uuid) < 32:
                    logger.warning(f"解密结果无效: {uuid}")
                    return self.render_to_json_response({
                        "status": "error",
                        "message": "二维码参数无效"
                    }, status=400)
                    
                logger.info(f"解密结果: {uuid}")
            except Exception as e:
                # 记录错误但不暴露详细信息
                logger.warning(f"二维码验证失败: {str(e)}, IP: {client_ip}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "二维码验证失败",
                    "detail": "无法解析二维码数据"
                }, status=400)
            
            # 获取二维码信息
            try:
                qrcode_obj = QRCode.objects.get(code=uuid, is_active=True)
            except QRCode.DoesNotExist:
                logger.warning(f"找不到对应的二维码: {uuid}")
                return self.render_to_json_response({
                    "status": "error",
                    "message": "二维码无效或已过期"
                }, status=404)
            
            # 获取床位和科室信息
            bed = qrcode_obj.bed
            if not bed:
                return self.render_to_json_response({
                    "status": "error",
                    "message": "二维码未关联床位"
                }, status=400)
                
            department = bed.department
            if not department:
                return self.render_to_json_response({
                    "status": "error",
                    "message": "床位未关联科室"
                }, status=400)
            
            # 生成临时令牌
            temp_token = TempToken.generate_token(
                qr_param=encrypted_param,
                qr_code=qrcode_obj,
                ip_address=client_ip
            )
            
            logger.info(f"生成临时令牌: {temp_token.token[:8]}... 过期时间: {temp_token.expires_at}")
            
            # 获取工作人员类型和工作人员列表
            try:
                # 获取科室下的工作人员
                staff_list = Staff.objects.filter(
                    department=department,
                    is_active=True
                ).select_related('staff_type')
                
                # 获取工作人员类型 - 只包含当前科室员工的类型
                # 首先获取科室下的所有员工使用的员工类型ID（去重）
                staff_type_ids = set(
                    Staff.objects.filter(
                        department=department,
                        is_active=True,
                        staff_type__isnull=False  # 排除没有指定类型的员工
                    ).values_list('staff_type_id', flat=True)
                )

                # 然后获取这些ID对应的员工类型详情
                staff_types = StaffType.objects.filter(
                    id__in=staff_type_ids,
                    is_active=True
                ).order_by('display_order')
                
                staff_types_data = [
                    {
                        "id": st.id,
                        "name": st.name,
                        "code": st.code
                    } for st in staff_types
                ]
                
                staff_data = [
                    {
                        "id": staff.id,
                        "name": staff.name,
                        "title": staff.title,
                        "staff_type": staff.staff_type.id if staff.staff_type else None
                    } for staff in staff_list
                ]
            except Exception as e:
                logger.error(f"获取工作人员数据失败: {str(e)}")
                staff_types_data = []
                staff_data = []
            
            # 构建响应数据
            response_data = {
                "status": "success",
                "data": {
                    "temp_token": temp_token.token,  # 临时令牌
                    "expires_at": temp_token.expires_at.isoformat(),  # 过期时间
                    "qrcode": {},  # 空对象，保持API结构兼容性
                    "bed": {
                        "id": bed.id,
                        "number": bed.number,
                        "area": bed.get_area_display() if hasattr(bed, 'get_area_display') else bed.area
                    },
                    "department": {
                        "id": department.id,
                        "name": department.name,
                        "code": department.code
                    },
                    "staff_types": staff_types_data,
                    "staff": staff_data
                }
            }
            
            # 记录成功验证
            logger.info(f"二维码验证成功: QR:{qrcode_obj.id}, Dept:{department.id}, IP:{client_ip}")
            
            return self.render_to_json_response(response_data)
            
        except Exception as e:
            logger.error(f"处理二维码验证时发生错误: {str(e)}")
            return self.render_to_json_response({
                "status": "error",
                "message": "系统错误，请稍后再试"
            }, status=500)

    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class GenerateParamAPI(JSONResponseMixin, TokenAuthMixin, View):
    """
    生成加密参数API
    用于测试，接收UUID参数并返回加密后的字符串
    """
    allow_anonymous = True
    
    def options(self, request, *args, **kwargs):
        """处理CORS预检请求"""
        response = JsonResponse({})
        response['Access-Control-Allow-Origin'] = '*'
        response['Access-Control-Allow-Methods'] = 'POST, OPTIONS'
        response['Access-Control-Allow-Headers'] = 'Content-Type, Authorization'
        response['Access-Control-Max-Age'] = '86400'  # 24小时
        return response
    
    def post(self, request, *args, **kwargs):
        """处理生成加密参数请求"""
        try:
            # 获取请求数据
            data = json.loads(request.body)
            uuid = data.get('uuid')
            
            if not uuid:
                return self.render_to_json_response({
                    "status": "error",
                    "message": "缺少必要参数UUID"
                }, status=400)
            
            # 生成加密参数
            encrypted_param = encrypt_qr_param(uuid)
            
            # 检查数据库中是否存在此UUID
            qrcode_exists = QRCode.objects.filter(code=uuid, is_active=True).exists()
            
            # 返回响应
            response_data = {
                "status": "success",
                "data": {
                    "uuid": uuid,
                    "encrypted_param": encrypted_param,
                    "exists_in_db": qrcode_exists
                }
            }
            
            return self.render_to_json_response(response_data)
            
        except Exception as e:
            # 记录错误但不暴露详细信息
            logger = logging.getLogger('api')
            logger.error(f"生成加密参数异常: {str(e)}")
            return self.render_to_json_response({
                "status": "error",
                "message": "系统错误，请稍后再试"
            }, status=500)


class DepartmentViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """科室API视图集"""
    queryset = Department.objects.all()
    serializer_class = DepartmentSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'departments'


class BedViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """床位API视图集"""
    queryset = Bed.objects.all()
    serializer_class = BedSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'beds'


class StaffViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """工作人员API视图集"""
    queryset = Staff.objects.all()
    serializer_class = StaffSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'staff'


class QRCodeViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """二维码API视图集"""
    queryset = QRCode.objects.all()
    serializer_class = QRCodeSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'qrcodes'


class EvaluationViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """评价API视图集"""
    queryset = Evaluation.objects.all()
    serializer_class = EvaluationSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'evaluations'


class PrintTemplateViewSet(APIPermissionMixin, viewsets.ModelViewSet):
    """打印模板API视图集"""
    queryset = PrintTemplate.objects.all()
    serializer_class = PrintTemplateSerializer
    authentication_classes = [SessionAuthentication, APIKeyAuthentication]
    permission_classes = [IsAuthenticated]
    resource_name = 'print_templates' 