#!/usr/bin/env python
"""
测试正确的API路径
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
import uuid

def test_correct_api_paths():
    """测试正确的API路径"""
    print("=" * 80)
    print("🔧 测试正确的API路径")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    # 生成测试数据
    test_uuid = str(uuid.uuid4())
    encrypted_param = encrypt_qr_param(test_uuid)
    
    print(f"测试UUID: {test_uuid}")
    print(f"加密参数: {encrypted_param}")
    print()
    
    # 测试1: 正确的验证二维码API路径
    print("1. 测试正确的验证二维码API:")
    verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
    verify_data = {
        "qr_param": encrypted_param,
        "client_ip": "127.0.0.1"
    }
    
    print(f"   正确URL: {verify_url}")
    print(f"   请求数据: {verify_data}")
    
    try:
        response = requests.post(
            verify_url,
            json=verify_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"   响应数据: {response_data}")
            print(f"   验证API: ✅ 成功")
            verify_success = True
        else:
            print(f"   错误响应: {response.text}")
            print(f"   验证API: ❌ 失败")
            verify_success = False
            
    except Exception as e:
        print(f"   验证API: ❌ 异常 - {e}")
        verify_success = False
    
    print()
    
    # 测试2: 正确的提交评价API路径
    print("2. 测试正确的提交评价API:")
    submit_url = f"{BASE_URL}/api/v1/public/submit-evaluation/"
    submit_data = {
        "qr_param": encrypted_param,
        "comment": "测试评价",
        "staff_evaluations": [
            {"staff_id": 1, "is_satisfied": True}
        ],
        "hospital_number": "TEST123",
        "phone_number": "13800138000"
    }
    
    print(f"   正确URL: {submit_url}")
    print(f"   请求数据: qr_param={encrypted_param[:20]}..., comment='{submit_data['comment']}'")
    
    try:
        response = requests.post(
            submit_url,
            json=submit_data,
            headers={'Content-Type': 'application/json'},
            timeout=10
        )
        
        print(f"   响应状态: {response.status_code}")
        
        if response.status_code == 200:
            response_data = response.json()
            print(f"   响应数据: {response_data}")
            print(f"   提交API: ✅ 成功")
            submit_success = True
        else:
            print(f"   错误响应: {response.text}")
            print(f"   提交API: ❌ 失败")
            submit_success = False
            
    except Exception as e:
        print(f"   提交API: ❌ 异常 - {e}")
        submit_success = False
    
    print()
    
    # 测试3: 前端使用的错误路径（确认404）
    print("3. 确认前端使用的错误路径:")
    wrong_urls = [
        f"{BASE_URL}/service/resources/",
        f"{BASE_URL}/service/evaluation/"
    ]
    
    for wrong_url in wrong_urls:
        try:
            response = requests.post(
                wrong_url,
                json={"test": "data"},
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            print(f"   {wrong_url}: {response.status_code} ❌")
        except Exception as e:
            print(f"   {wrong_url}: 异常 - {e}")
    
    # 总结
    print("\n" + "=" * 80)
    print("📊 API路径测试结果:")
    print("=" * 80)
    
    if verify_success and submit_success:
        print("✅ 正确的API路径工作正常")
        print("❌ 前端使用的API路径不正确")
        print("\n🔧 解决方案:")
        print("需要修改前端API配置，使用正确的路径:")
        print(f"   验证API: /api/v1/public/qrcode/verify/")
        print(f"   提交API: /api/v1/public/submit-evaluation/")
    else:
        print("❌ API路径存在其他问题")
        if not verify_success:
            print("   - 验证API有问题")
        if not submit_success:
            print("   - 提交API有问题")

if __name__ == "__main__":
    test_correct_api_paths()
