"""
文件验证工具
用于验证上传文件的类型、大小和内容
"""

import os
import hashlib
from django.core.exceptions import ValidationError
from django.utils.translation import gettext as _
from django.conf import settings

# 允许的文件类型
ALLOWED_FILE_TYPES = {
    'image/jpeg': ['.jpg', '.jpeg'],
    'image/png': ['.png'],
    'image/gif': ['.gif'],
    'application/pdf': ['.pdf'],
    'application/vnd.ms-excel': ['.xls'],
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': ['.xlsx'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'text/plain': ['.txt'],
    'text/csv': ['.csv'],
}

# 最大文件大小（字节）
MAX_FILE_SIZE = 10 * 1024 * 1024  # 10MB

# 恶意文件签名（MD5哈希）
MALICIOUS_FILE_SIGNATURES = [
    # 示例恶意文件签名
    'e44fcd2a098af8c4f3d3e8a5d4e3f1a2',
    '5a8f1c6b2d3e4f5a6b7c8d9e0f1a2b3c',
]

def validate_file_type(file):
    """
    验证文件类型
    
    参数:
        file: 上传的文件对象
        
    异常:
        ValidationError: 文件类型不允许
    """
    # 获取文件扩展名
    file_name = file.name
    file_ext = os.path.splitext(file_name)[1].lower()
    
    # 检查文件扩展名是否在允许列表中
    allowed_extensions = []
    for extensions in ALLOWED_FILE_TYPES.values():
        allowed_extensions.extend(extensions)
    
    if file_ext not in allowed_extensions:
        raise ValidationError(
            _('不允许的文件扩展名：%(ext)s'),
            params={'ext': file_ext},
        )
    
    # 检查文件内容类型
    content_type = file.content_type
    
    # 检查内容类型是否在允许列表中
    if content_type not in ALLOWED_FILE_TYPES:
        raise ValidationError(
            _('不允许的文件类型：%(type)s'),
            params={'type': content_type},
        )
    
    # 检查文件扩展名是否与内容类型匹配
    if file_ext not in ALLOWED_FILE_TYPES[content_type]:
        raise ValidationError(
            _('文件扩展名(%(ext)s)与文件类型(%(type)s)不匹配'),
            params={'ext': file_ext, 'type': content_type},
        )

def validate_file_size(file):
    """
    验证文件大小
    
    参数:
        file: 上传的文件对象
        
    异常:
        ValidationError: 文件大小超过限制
    """
    if file.size > MAX_FILE_SIZE:
        raise ValidationError(
            _('文件大小不能超过%(size)s MB'),
            params={'size': MAX_FILE_SIZE / (1024 * 1024)},
        )

def validate_file_content(file):
    """
    验证文件内容
    检查文件是否包含恶意内容
    
    参数:
        file: 上传的文件对象
        
    异常:
        ValidationError: 文件包含恶意内容
    """
    # 计算文件MD5哈希
    md5_hash = hashlib.md5()
    for chunk in file.chunks():
        md5_hash.update(chunk)
    file_hash = md5_hash.hexdigest()
    
    # 检查文件哈希是否在恶意文件签名列表中
    if file_hash in MALICIOUS_FILE_SIGNATURES:
        raise ValidationError(
            _('文件包含恶意内容'),
        )
    
    # 重置文件指针
    file.seek(0)

def validate_file(file):
    """
    验证文件
    
    参数:
        file: 上传的文件对象
        
    异常:
        ValidationError: 文件验证失败
    """
    validate_file_type(file)
    validate_file_size(file)
    validate_file_content(file)