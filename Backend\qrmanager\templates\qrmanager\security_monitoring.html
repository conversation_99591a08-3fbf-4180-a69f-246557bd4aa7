{% extends 'qrmanager/base.html' %}
{% load static %}

{% block title %}安全监控 - 医院二维码管理系统{% endblock %}

{% block extra_css %}
<style>
.security-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.security-metric {
    text-align: center;
    padding: 15px;
    border-radius: 6px;
    margin: 10px 0;
}

.metric-value {
    font-size: 2em;
    font-weight: bold;
    margin-bottom: 5px;
}

.metric-label {
    color: #666;
    font-size: 0.9em;
}

.status-active { background-color: #d4edda; color: #155724; }
.status-warning { background-color: #fff3cd; color: #856404; }
.status-danger { background-color: #f8d7da; color: #721c24; }

.event-item {
    border-left: 4px solid #007bff;
    padding: 10px 15px;
    margin: 10px 0;
    background: #f8f9fa;
    border-radius: 0 4px 4px 0;
}

.event-time {
    font-size: 0.8em;
    color: #666;
    float: right;
}

.event-type {
    font-weight: bold;
    color: #007bff;
}

.qrcode-item, .ip-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    margin: 5px 0;
    background: #f8f9fa;
    border-radius: 4px;
    border-left: 3px solid #28a745;
}

.risk-high { border-left-color: #dc3545; }
.risk-medium { border-left-color: #ffc107; }
.risk-low { border-left-color: #28a745; }

.refresh-btn {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: #007bff;
    color: white;
    border: none;
    border-radius: 50%;
    width: 60px;
    height: 60px;
    font-size: 1.2em;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.3s;
}

.refresh-btn:hover {
    background: #0056b3;
    transform: scale(1.1);
}

.auto-refresh {
    background: #28a745;
    color: white;
    border: none;
    padding: 5px 15px;
    border-radius: 4px;
    margin-left: 10px;
}

.table-responsive {
    max-height: 400px;
    overflow-y: auto;
}

.badge {
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8em;
}

.badge-success { background: #28a745; color: white; }
.badge-warning { background: #ffc107; color: #212529; }
.badge-danger { background: #dc3545; color: white; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🔒 安全监控中心</h2>
                <div>
                    <span class="text-muted">最后更新: {{ security_overview.last_updated }}</span>
                    <button class="auto-refresh" onclick="toggleAutoRefresh()">自动刷新: <span id="auto-status">关闭</span></button>
                </div>
            </div>
        </div>
    </div>

    <!-- 安全概览 -->
    <div class="row">
        <div class="col-md-3">
            <div class="security-card">
                <div class="security-metric status-{{ security_overview.system_status }}">
                    <div class="metric-value">{{ security_overview.active_qrcode_limits }}</div>
                    <div class="metric-label">活跃二维码限制</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="security-card">
                <div class="security-metric status-active">
                    <div class="metric-value">{{ security_overview.security_events_count }}</div>
                    <div class="metric-label">安全事件数量</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="security-card">
                <div class="security-metric status-active">
                    <div class="metric-value">{{ rate_limit_statistics.total_active_limits }}</div>
                    <div class="metric-label">总活跃限制</div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="security-card">
                <div class="security-metric status-{% if security_overview.system_status == 'active' %}active{% else %}warning{% endif %}">
                    <div class="metric-value">{% if security_overview.system_status == 'active' %}正常{% else %}警告{% endif %}</div>
                    <div class="metric-label">系统状态</div>
                </div>
            </div>
        </div>
    </div>

    <!-- 详细统计 -->
    <div class="row">
        <!-- 最近安全事件 -->
        <div class="col-md-6">
            <div class="security-card">
                <h4>🚨 最近安全事件</h4>
                <div class="table-responsive">
                    {% if recent_security_events %}
                        {% for event in recent_security_events %}
                        <div class="event-item">
                            <div class="event-type">{{ event.type_display }}</div>
                            <div class="event-time">{{ event.formatted_time }}</div>
                            <div class="text-muted">
                                {% if event.data.ip %}IP: {{ event.data.ip }}{% endif %}
                                {% if event.data.uuid %}UUID: {{ event.data.uuid|slice:":8" }}...{% endif %}
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无安全事件</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 访问最多的二维码 -->
        <div class="col-md-6">
            <div class="security-card">
                <h4>📊 访问最多的二维码</h4>
                <div class="table-responsive">
                    {% if top_accessed_qrcodes %}
                        {% for qrcode in top_accessed_qrcodes %}
                        <div class="qrcode-item">
                            <div>
                                <strong>{{ qrcode.bed_info }}</strong><br>
                                <small class="text-muted">{{ qrcode.uuid|slice:":8" }}... | {{ qrcode.created_at }}</small>
                            </div>
                            <div>
                                <span class="badge badge-success">{{ qrcode.access_count }} 次访问</span>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无访问记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- IP统计和速率限制统计 -->
    <div class="row">
        <!-- IP访问统计 -->
        <div class="col-md-8">
            <div class="security-card">
                <h4>🌐 IP访问统计</h4>
                <div class="table-responsive">
                    <table class="table table-sm">
                        <thead>
                            <tr>
                                <th>IP地址</th>
                                <th>总请求数</th>
                                <th>访问二维码数</th>
                                <th>风险评分</th>
                                <th>状态</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% if ip_statistics %}
                                {% for ip in ip_statistics %}
                                <tr>
                                    <td><code>{{ ip.ip }}</code></td>
                                    <td>{{ ip.total_requests }}</td>
                                    <td>{{ ip.unique_qrcodes }}</td>
                                    <td>{{ ip.risk_score }}</td>
                                    <td>
                                        {% if ip.risk_score > 50 %}
                                            <span class="badge badge-danger">{{ ip.status }}</span>
                                        {% elif ip.risk_score > 20 %}
                                            <span class="badge badge-warning">{{ ip.status }}</span>
                                        {% else %}
                                            <span class="badge badge-success">{{ ip.status }}</span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            {% else %}
                                <tr>
                                    <td colspan="5" class="text-muted text-center">暂无IP访问记录</td>
                                </tr>
                            {% endif %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- 速率限制统计 -->
        <div class="col-md-4">
            <div class="security-card">
                <h4>🚫 速率限制统计</h4>
                <div class="row">
                    <div class="col-12">
                        <div class="security-metric status-active">
                            <div class="metric-value">{{ rate_limit_statistics.evaluation_limits }}</div>
                            <div class="metric-label">评价限制</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="security-metric status-active">
                            <div class="metric-value">{{ rate_limit_statistics.verification_limits }}</div>
                            <div class="metric-label">验证限制</div>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="security-metric status-active">
                            <div class="metric-value">{{ rate_limit_statistics.ip_limits }}</div>
                            <div class="metric-label">IP限制</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 刷新按钮 -->
<button class="refresh-btn" onclick="refreshPage()" title="刷新数据">
    <i class="fas fa-sync-alt"></i>
</button>
{% endblock %}

{% block extra_js %}
<script>
let autoRefreshInterval = null;
let autoRefreshEnabled = false;

function refreshPage() {
    location.reload();
}

function toggleAutoRefresh() {
    if (autoRefreshEnabled) {
        clearInterval(autoRefreshInterval);
        autoRefreshEnabled = false;
        document.getElementById('auto-status').textContent = '关闭';
    } else {
        autoRefreshInterval = setInterval(refreshPage, 30000); // 30秒刷新
        autoRefreshEnabled = true;
        document.getElementById('auto-status').textContent = '开启';
    }
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 可以在这里添加更多的初始化代码
    console.log('安全监控页面已加载');
});
</script>
{% endblock %}
