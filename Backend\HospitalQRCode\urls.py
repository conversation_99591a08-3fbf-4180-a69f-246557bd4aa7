from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.contrib.auth import views as auth_views
from django.views.static import serve
from django.http import JsonResponse
from qrmanager.views import CustomLogoutView, SafeLoginView
import os

# 会话刷新视图函数
def ping_view(request):
    """
    简单的视图函数，用于刷新会话。
    客户端可以定期调用此视图来保持会话活跃。
    """
    return JsonResponse({'status': 'ok', 'message': 'Session refreshed'})

# 前端文件路径
FRONTEND_DIR = os.path.join(os.path.dirname(settings.BASE_DIR), 'Frontend')

urlpatterns = [
    path('', include('qrmanager.urls')),  # 包含应用的 URLs
    path('login/', SafeLoginView.as_view(template_name='registration/login.html'), name='login'),
    path('logout/', CustomLogoutView.as_view(), name='logout'),
    path('password_change/', auth_views.PasswordChangeView.as_view(template_name='registration/password_change.html'), name='password_change'),
    path('password_change/done/', auth_views.PasswordChangeDoneView.as_view(template_name='registration/password_change_done.html'), name='password_change_done'),
    path('ping/', ping_view, name='ping'),  # 会话刷新端点

    # 专门为前端文件提供服务的URL
    path('js/<path:path>', serve, {'document_root': os.path.join(FRONTEND_DIR, 'js')}),
    path('styles.css', serve, {'document_root': FRONTEND_DIR, 'path': 'styles.css'}),
    path('index.html', serve, {'document_root': FRONTEND_DIR, 'path': 'index.html'}),
    path('test_load.html', serve, {'document_root': FRONTEND_DIR, 'path': 'test_load.html'}),
]

# 提供静态文件和媒体文件服务（在开发和生产环境中都生效）
urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 添加前端文件服务
urlpatterns += [
    path('images/<path:path>', serve, {'document_root': os.path.join(FRONTEND_DIR, 'images')}),
    path('fonts/<path:path>', serve, {'document_root': os.path.join(FRONTEND_DIR, 'fonts')}),
    path('assets/<path:path>', serve, {'document_root': os.path.join(FRONTEND_DIR, 'assets')}),
]

# 添加FontAwesome字体文件服务（解决生产环境中字体图标显示问题）
urlpatterns += [
    path('static/vendor/fontawesome/webfonts/<path:path>', serve, {
        'document_root': os.path.join(settings.STATIC_ROOT, 'vendor/fontawesome/webfonts')
    }),
]

# 添加媒体文件服务（解决生产环境中媒体文件显示问题）
urlpatterns += [
    path('media/<path:path>', serve, {
        'document_root': settings.MEDIA_ROOT
    }),
]

