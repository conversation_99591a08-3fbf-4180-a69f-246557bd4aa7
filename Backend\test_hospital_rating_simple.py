#!/usr/bin/env python3
"""
医院评分功能简化测试
"""

import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')

try:
    django.setup()
    print("✅ Django环境设置成功")
except Exception as e:
    print(f"❌ Django环境设置失败: {e}")
    sys.exit(1)

from qrmanager.models import Evaluation, QRCode, Bed, Department

def test_database_field():
    """测试数据库字段"""
    print("\n🔍 测试数据库字段")
    print("-" * 40)
    
    try:
        # 检查字段是否存在
        field_names = [field.name for field in Evaluation._meta.fields]
        
        if 'hospital_rating' in field_names:
            print("✅ hospital_rating字段存在")
            
            # 获取字段详情
            field = Evaluation._meta.get_field('hospital_rating')
            print(f"  类型: {type(field).__name__}")
            print(f"  允许空值: {field.null}")
            print(f"  选择项: {field.choices}")
            return True
        else:
            print("❌ hospital_rating字段不存在")
            return False
            
    except Exception as e:
        print(f"❌ 字段测试失败: {e}")
        return False

def test_create_evaluation():
    """测试创建带评分的评价"""
    print("\n🔍 测试创建评价")
    print("-" * 40)
    
    try:
        # 获取第一个床位
        bed = Bed.objects.first()
        if not bed:
            print("❌ 没有找到床位数据")
            return False
        
        print(f"使用床位: {bed.number} ({bed.department.name})")
        
        # 创建测试评价
        evaluation = Evaluation.objects.create(
            bed=bed,
            is_satisfied=True,
            comment="测试医院评分功能",
            hospital_rating=4,
            hospital_number="TEST001",
            phone_number="13800138000"
        )
        
        print(f"✅ 评价创建成功")
        print(f"  ID: {evaluation.id}")
        print(f"  医院评分: {evaluation.hospital_rating}星")
        print(f"  评价内容: {evaluation.comment}")
        
        # 验证保存
        saved = Evaluation.objects.get(id=evaluation.id)
        if saved.hospital_rating == 4:
            print("✅ 评分正确保存")
        else:
            print(f"❌ 评分保存错误: {saved.hospital_rating}")
        
        return evaluation.id
        
    except Exception as e:
        print(f"❌ 创建评价失败: {e}")
        return False

def test_query_evaluations():
    """测试查询评价"""
    print("\n🔍 测试查询评价")
    print("-" * 40)
    
    try:
        # 统计评价
        total = Evaluation.objects.count()
        with_rating = Evaluation.objects.filter(hospital_rating__isnull=False).count()
        without_rating = Evaluation.objects.filter(hospital_rating__isnull=True).count()
        
        print(f"评价统计:")
        print(f"  总数: {total}")
        print(f"  有评分: {with_rating}")
        print(f"  无评分: {without_rating}")
        
        # 显示评分分布
        print(f"\n评分分布:")
        for rating in range(1, 6):
            count = Evaluation.objects.filter(hospital_rating=rating).count()
            stars = "★" * rating + "☆" * (5-rating)
            print(f"  {stars} {rating}星: {count}条")
        
        # 显示最近的评价
        recent = Evaluation.objects.filter(hospital_rating__isnull=False).order_by('-created_at')[:3]
        if recent:
            print(f"\n最近的评分:")
            for eval in recent:
                print(f"  {eval.hospital_rating}星 - {eval.comment[:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ 查询评价失败: {e}")
        return False

def test_api_data_structure():
    """测试API数据结构"""
    print("\n🔍 测试API数据结构")
    print("-" * 40)
    
    try:
        # 模拟API数据
        api_data = {
            "qr_param": "test_param",
            "comment": "API测试",
            "hospital_rating": 5,
            "hospital_number": "API001",
            "phone_number": "13900139000",
            "staff_evaluations": []
        }
        
        print("API数据结构:")
        for key, value in api_data.items():
            print(f"  {key}: {value}")
        
        # 检查hospital_rating字段
        if 'hospital_rating' in api_data:
            rating = api_data['hospital_rating']
            if 1 <= rating <= 5:
                print(f"✅ hospital_rating字段有效: {rating}星")
            else:
                print(f"❌ hospital_rating字段无效: {rating}")
        
        return True
        
    except Exception as e:
        print(f"❌ API数据结构测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 医院评分功能简化测试")
    print("=" * 50)
    
    tests = [
        ("数据库字段", test_database_field),
        ("创建评价", test_create_evaluation),
        ("查询评价", test_query_evaluations),
        ("API数据结构", test_api_data_structure),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}测试异常: {e}")
            results.append((name, False))
    
    # 总结
    print("\n📋 测试结果")
    print("=" * 50)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！医院评分功能可用！")
    else:
        print("⚠️  部分测试失败，需要检查问题")

if __name__ == "__main__":
    main()
