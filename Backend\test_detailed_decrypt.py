#!/usr/bin/env python
"""
详细测试解密功能的每个步骤
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid
import base64
import hashlib

def test_detailed_decrypt():
    """详细测试解密过程的每一步"""
    print("=" * 60)
    print("详细解密测试")
    print("=" * 60)
    
    # 生成测试UUID
    test_uuid = str(uuid.uuid4())
    print(f"原始UUID: {test_uuid}")
    print()
    
    # 加密
    print("1. 加密过程:")
    encrypted = encrypt_qr_param(test_uuid)
    print(f"   加密结果: {encrypted}")
    print(f"   长度: {len(encrypted)} 字符")
    print()
    
    # 手动解密验证每一步
    print("2. 手动解密验证:")
    
    # 步骤1：添加Base64填充
    print("   步骤1: 添加Base64填充")
    padding_needed = len(encrypted) % 4
    if padding_needed != 0:
        padded = encrypted + '=' * (4 - padding_needed)
        print(f"   需要填充: {4 - padding_needed} 个等号")
        print(f"   填充后: {padded}")
    else:
        padded = encrypted
        print(f"   无需填充: {padded}")
    
    # 步骤2：Base64解码
    print("\n   步骤2: Base64解码")
    try:
        compact_data = base64.b64decode(padded).decode()
        print(f"   解码成功: {compact_data}")
        print(f"   解码长度: {len(compact_data)} 字符")
    except Exception as e:
        print(f"   解码失败: {e}")
        return
    
    # 步骤3：解析数据结构
    print("\n   步骤3: 解析数据结构")
    if len(compact_data) >= 42:
        salt = compact_data[:4]
        signature = compact_data[4:10]
        uuid_clean = compact_data[10:42]
        
        print(f"   盐值 (前4位): {salt}")
        print(f"   签名 (中6位): {signature}")
        print(f"   UUID (后32位): {uuid_clean}")
    else:
        print(f"   数据长度不足: {len(compact_data)} < 42")
        return
    
    # 步骤4：重构UUID
    print("\n   步骤4: 重构UUID")
    reconstructed_uuid = f"{uuid_clean[:8]}-{uuid_clean[8:12]}-{uuid_clean[12:16]}-{uuid_clean[16:20]}-{uuid_clean[20:32]}"
    print(f"   重构UUID: {reconstructed_uuid}")
    print(f"   原始UUID: {test_uuid}")
    print(f"   UUID匹配: {'✓' if reconstructed_uuid == test_uuid else '✗'}")
    
    # 步骤5：验证签名
    print("\n   步骤5: 验证签名")
    from qrmanager.security import SECURITY_CONFIG
    signature_data = f"{reconstructed_uuid}|{salt}|{SECURITY_CONFIG['encryption_key']}"
    expected_signature = hashlib.md5(signature_data.encode()).hexdigest()[:6]
    
    print(f"   签名数据: {signature_data}")
    print(f"   期望签名: {expected_signature}")
    print(f"   实际签名: {signature}")
    print(f"   签名验证: {'✓' if signature == expected_signature else '✗'}")
    
    # 使用官方解密函数验证
    print("\n3. 官方解密函数验证:")
    try:
        result = decrypt_qr_param(encrypted)
        print(f"   解密结果: {result}")
        print(f"   UUID匹配: {'✓' if result['uuid'] == test_uuid else '✗'}")
        print(f"   格式类型: {result['format']}")
    except Exception as e:
        print(f"   解密失败: {e}")

if __name__ == "__main__":
    test_detailed_decrypt()
    print("\n" + "=" * 60)
    print("详细测试完成！")
    print("=" * 60)
