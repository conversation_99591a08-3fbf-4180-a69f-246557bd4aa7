# Generated by Django 4.2.7 on 2025-03-23 14:12

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0043_add_evaluation_patient_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='StaffEvaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_satisfied', models.BooleanField(choices=[(True, '满意'), (False, '不满意')], verbose_name='是否满意')),
                ('evaluation', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='staff_evaluations', to='qrmanager.evaluation', verbose_name='评价')),
                ('staff', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluation_links', to='qrmanager.staff', verbose_name='工作人员')),
            ],
            options={
                'verbose_name': '员工评价关联',
                'verbose_name_plural': '员工评价关联',
                'ordering': ['evaluation', '-is_satisfied'],
                'unique_together': {('evaluation', 'staff')},
            },
        ),
    ]
