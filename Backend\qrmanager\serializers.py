from rest_framework import serializers
from .models import Department, Bed, Staff, QRCode, Evaluation, PrintTemplate, APIKey, APILog

class DepartmentSerializer(serializers.ModelSerializer):
    """科室序列化器"""
    class Meta:
        model = Department
        fields = '__all__'


class BedSerializer(serializers.ModelSerializer):
    """床位序列化器"""
    department_name = serializers.ReadOnlyField(source='department.name')
    
    class Meta:
        model = Bed
        fields = '__all__'


class StaffSerializer(serializers.ModelSerializer):
    """工作人员序列化器"""
    department_name = serializers.ReadOnlyField(source='department.name')
    
    class Meta:
        model = Staff
        fields = '__all__'


class QRCodeSerializer(serializers.ModelSerializer):
    """二维码序列化器"""
    bed_name = serializers.ReadOnlyField(source='bed.name')
    department_name = serializers.ReadOnlyField(source='bed.department.name')
    
    class Meta:
        model = QRCode
        fields = '__all__'
        read_only_fields = ('qr_image', 'created_at', 'updated_at')


class EvaluationSerializer(serializers.ModelSerializer):
    """评价序列化器"""
    qrcode_id = serializers.ReadOnlyField(source='qrcode.id')
    bed_name = serializers.ReadOnlyField(source='bed.number')
    department_name = serializers.ReadOnlyField(source='bed.department.name')
    staff_name = serializers.ReadOnlyField(source='staff.name')
    satisfaction_display = serializers.SerializerMethodField()
    
    def get_satisfaction_display(self, obj):
        return '满意' if obj.is_satisfied else '不满意'
    
    class Meta:
        model = Evaluation
        fields = '__all__'
        read_only_fields = ('created_at', 'satisfaction_display')


class PrintTemplateSerializer(serializers.ModelSerializer):
    """打印模板序列化器"""
    class Meta:
        model = PrintTemplate
        fields = '__all__'


class APIKeySerializer(serializers.ModelSerializer):
    """API密钥序列化器"""
    created_by_username = serializers.ReadOnlyField(source='created_by.username')
    
    class Meta:
        model = APIKey
        fields = '__all__'
        read_only_fields = ('key', 'created_at')
        extra_kwargs = {
            'key': {'write_only': True}
        }


class APILogSerializer(serializers.ModelSerializer):
    """API日志序列化器"""
    api_key_name = serializers.ReadOnlyField(source='api_key.name')
    
    class Meta:
        model = APILog
        fields = '__all__'
        read_only_fields = ('timestamp',) 