{% extends "qrmanager/base.html" %}
{% load static %}
{% block title %}评价管理工作台 - 医院服务评价系统{% endblock %}

{% block extra_head %}
<link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/bootstrap-icons.css" rel="stylesheet">
<style>
    /* 整体样式 - 苹果风格现代设计 */
    :root {
        /* 主色调 - 采用苹果风格蓝色系 */
        --primary-color: #0071e3;
        --primary-hover: #0077ed;
        --secondary-color: #5ac8fa;
        
        /* 功能色 */
        --success-color: #34c759;
        --warning-color: #ff9500;
        --danger-color: #ff3b30;
        --info-color: #5ac8fa;
        
        /* 中性色 */
        --light-color: #f5f5f7;
        --dark-color: #1d1d1f;
        --bg-color: #fbfbfd;
        --card-bg: #ffffff;
        --border-color: #d2d2d7;
        
        /* 处理状态颜色 */
        --pending-color: #ff9500;
        --processing-color: #5ac8fa;
        --processed-color: #34c759;
        --flagged-color: #ff3b30;
        
        /* 圆角和阴影 */
        --border-radius: 12px;
        --card-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
        --card-hover-shadow: 0 8px 24px rgba(0, 0, 0, 0.12);
        --button-shadow: 0 2px 6px rgba(0, 0, 0, 0.08);
        
        /* 新增渐变色 */
        --gradient-primary: linear-gradient(135deg, #0071e3, #42a5f5);
        --gradient-success: linear-gradient(135deg, #34c759, #4cd964);
        --gradient-warning: linear-gradient(135deg, #ff9500, #ffcc00);
        --gradient-danger: linear-gradient(135deg, #ff3b30, #ff6b6b);
        
        /* 新增视图切换按钮颜色 */
        --card-view-color: #7c5cfc;
        --card-view-gradient: linear-gradient(135deg, #7c5cfc, #a78bfa);
        --list-view-color: #00c7be;
        --list-view-gradient: linear-gradient(135deg, #00c7be, #0ecfb5);
        --table-view-color: #ff6d3f;
        --table-view-gradient: linear-gradient(135deg, #ff6d3f, #ff9a66);
        
        /* 新增炫彩霓虹特效 */
        --neon-glow: 0 0 10px rgba(122, 122, 255, 0.5), 0 0 20px rgba(122, 122, 255, 0.3);
        --neon-shadow: 0 0 15px rgba(0, 113, 227, 0.4);
        
        /* 新增3D效果 */
        --3d-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        --3d-transform: translateY(-2px);
    }
    
    body {
        background-color: var(--bg-color);
        color: var(--dark-color);
        font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, sans-serif;
    }
    
    /* 评价管理工作台容器 */
    .evaluation-dashboard {
        padding: 20px 0;
        background: linear-gradient(135deg, var(--bg-color) 0%, #f0f2f5 100%);
        min-height: calc(100vh - 56px);
        position: relative;
    }
    
    .evaluation-dashboard:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%230071e3' fill-opacity='0.02'%3E%3Cpath d='M36 34v-4h-2v4h-4v2h4v4h2v-4h4v-2h-4zm0-30V0h-2v4h-4v2h4v4h2V6h4V4h-4zM6 34v-4H4v4H0v2h4v4h2v-4h4v-2H6zM6 4V0H4v4H0v2h4v4h2V6h4V4H6z'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E");
        pointer-events: none;
        z-index: 0;
    }
    
    /* 页面标题区域 */
    .page-title {
        margin-bottom: 28px;
        padding: 16px 0;
        border-bottom: 1px solid rgba(0,0,0,0.05);
        position: relative;
    }
    
    .page-title:after {
        content: '';
        position: absolute;
        bottom: -1px;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(90deg, 
            var(--primary-color), 
            var(--secondary-color), 
            var(--success-color), 
            var(--warning-color), 
            var(--danger-color));
        opacity: 0.3;
    }
    
    .page-title h2 {
        font-weight: 600;
        color: var(--dark-color);
        font-size: 30px;
        margin-bottom: 6px;
        display: flex;
        align-items: center;
        position: relative;
    }
    
    .page-title h2 i {
        color: var(--primary-color);
        font-size: 32px;
        margin-right: 15px;
        background: var(--gradient-primary);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        border-radius: 50%;
        padding: 10px;
        box-shadow: 0 3px 15px rgba(0, 113, 227, 0.3);
        background-color: rgba(0, 113, 227, 0.05);
        animation: glow 2s infinite;
    }
    
    .page-title .text-muted {
        color: #86868b !important;
        font-size: 16px;
        letter-spacing: 0.2px;
        display: flex;
        align-items: center;
    }
    
    .page-title .text-muted i {
        margin-right: 8px;
        color: var(--primary-color);
    }
    
    /* 卡片通用样式增强 */
    .card {
    border: none;
        box-shadow: var(--card-shadow);
        border-radius: var(--border-radius);
        transition: all 0.35s cubic-bezier(0.165, 0.84, 0.44, 1);
        background-color: var(--card-bg);
        overflow: hidden;
        margin-bottom: 20px;
        position: relative;
        will-change: transform;
        transform-style: preserve-3d;
        backface-visibility: hidden;
    }
    
    .card:hover {
        transform: translateY(-6px) scale(1.02);
        box-shadow: var(--card-hover-shadow), 0 15px 35px rgba(0, 0, 0, 0.08);
        z-index: 2;
    }
    
    .card:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(145deg, rgba(255,255,255,0.05) 0%, rgba(255,255,255,0) 100%);
        pointer-events: none;
        border-radius: var(--border-radius);
    }
    
    .card:hover:after {
        opacity: 0.8;
    }
    
    .card-header {
        background-color: var(--card-bg);
        border-bottom: 1px solid rgba(0,0,0,0.05);
        padding: 16px 20px;
        font-weight: 500;
        position: relative;
        overflow: hidden;
    }
    
    .card-header:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(90deg, rgba(0,0,0,0) 0%, rgba(0,0,0,0.05) 50%, rgba(0,0,0,0) 100%);
    }
    
    /* 评价卡片样式 */
    .evaluation-card {
        position: relative;
        height: 100%;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
    }
    
    .evaluation-card .card-body {
        padding: 20px;
        transition: all 0.3s ease;
    }
    
    .evaluation-card:hover .card-body {
        background-color: rgba(245, 245, 247, 0.3);
    }
    
    /* 处理状态样式增强 */
    .status-pending {
        border-left: 4px solid var(--pending-color) !important;
        box-shadow: 0 4px 12px rgba(255, 149, 0, 0.1);
        position: relative;
    }
    
    .status-pending:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-warning);
        z-index: 1;
        box-shadow: 0 0 8px rgba(255, 149, 0, 0.4);
    }
    
    .status-processing {
        border-left: 4px solid var(--processing-color) !important;
        box-shadow: 0 4px 12px rgba(90, 200, 250, 0.1);
        position: relative;
    }
    
    .status-processing:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-primary);
        z-index: 1;
        box-shadow: 0 0 8px rgba(90, 200, 250, 0.4);
        animation: statusPulse 2s infinite;
    }
    
    .status-processed {
        border-left: 4px solid var(--processed-color) !important;
        box-shadow: 0 4px 12px rgba(52, 199, 89, 0.1);
        position: relative;
    }
    
    .status-processed:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-success);
        z-index: 1;
        box-shadow: 0 0 8px rgba(52, 199, 89, 0.4);
    }
    
    .status-flagged {
        border-left: 4px solid var(--flagged-color) !important;
        box-shadow: 0 4px 12px rgba(255, 59, 48, 0.1);
        position: relative;
    }
    
    .status-flagged:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 4px;
        height: 100%;
        background: var(--gradient-danger);
        z-index: 1;
        box-shadow: 0 0 8px rgba(255, 59, 48, 0.4);
        animation: statusDanger 1.5s infinite;
    }
    
    @keyframes statusPulse {
        0% {
            opacity: 0.7;
        }
        50% {
            opacity: 1;
        }
        100% {
            opacity: 0.7;
        }
    }
    
    @keyframes statusDanger {
        0% {
            opacity: 0.7;
            box-shadow: 0 0 8px rgba(255, 59, 48, 0.4);
        }
        50% {
            opacity: 1;
            box-shadow: 0 0 15px rgba(255, 59, 48, 0.6);
        }
        100% {
            opacity: 0.7;
            box-shadow: 0 0 8px rgba(255, 59, 48, 0.4);
        }
    }
    
    /* 状态徽章样式 */
    .badge {
        padding: 0.35em 0.65em;
        font-weight: 500;
        border-radius: 8px;
        font-size: 0.85em;
        display: inline-flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.08);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .badge i {
        margin-right: 4px;
        font-size: 1rem;
        animation: pulse 2s infinite;
        position: relative;
        z-index: 2;
    }
    
    .badge:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(45deg, rgba(255, 255, 255, 0.15), rgba(255, 255, 255, 0));
        z-index: 1;
    }
    
    @keyframes pulse {
        0% {
            transform: scale(1);
        }
        50% {
            transform: scale(1.15);
        }
        100% {
            transform: scale(1);
        }
    }
    
    .badge:hover {
        transform: translateY(-3px);
        box-shadow: 0 4px 10px rgba(0,0,0,0.15);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
    
    /* 动画增强 */
    @keyframes float {
        0% {
            transform: translateY(0px);
        }
        50% {
            transform: translateY(-5px);
        }
        100% {
            transform: translateY(0px);
        }
    }
    
    @keyframes glow {
        0% {
            box-shadow: 0 0 5px rgba(0, 113, 227, 0.2);
        }
        50% {
            box-shadow: 0 0 20px rgba(0, 113, 227, 0.4);
        }
        100% {
            box-shadow: 0 0 5px rgba(0, 113, 227, 0.2);
        }
    }
    
    /* 添加平滑滚动效果 */
    html {
        scroll-behavior: smooth;
    }
    
    /* 添加页面载入动画 */
    @keyframes fadeIn {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    .row-cols-1, .row-cols-md-2, .row-cols-lg-3, .row-cols-xl-4 {
        animation: fadeIn 0.5s ease-out forwards;
    }
    
    /* 为评价卡片逐个添加延迟动画 */
    .col:nth-child(1) .card { animation-delay: 0.05s; }
    .col:nth-child(2) .card { animation-delay: 0.1s; }
    .col:nth-child(3) .card { animation-delay: 0.15s; }
    .col:nth-child(4) .card { animation-delay: 0.2s; }
    .col:nth-child(5) .card { animation-delay: 0.25s; }
    .col:nth-child(6) .card { animation-delay: 0.3s; }
    .col:nth-child(7) .card { animation-delay: 0.35s; }
    .col:nth-child(8) .card { animation-delay: 0.4s; }
    
    /* 评分星星动画增强 */
    .ratings .bi-star-fill {
        color: #ff9500 !important;
        position: relative;
        display: inline-block;
        animation: starShine 3s infinite;
        animation-delay: calc(0.3s * var(--star-index, 0));
    }
    
    @keyframes starShine {
        0% {
            transform: scale(1);
            filter: drop-shadow(0 0 0 rgba(255, 149, 0, 0));
        }
        40% {
            transform: scale(1);
            filter: drop-shadow(0 0 0 rgba(255, 149, 0, 0));
        }
        50% {
            transform: scale(1.1);
            filter: drop-shadow(0 0 3px rgba(255, 149, 0, 0.5));
        }
        60% {
            transform: scale(1);
            filter: drop-shadow(0 0 0 rgba(255, 149, 0, 0));
        }
        100% {
            transform: scale(1);
            filter: drop-shadow(0 0 0 rgba(255, 149, 0, 0));
        }
    }
    
    /* 为每个星星设置不同的动画延迟 */
    .ratings .bi-star-fill:nth-child(1) { --star-index: 1; }
    .ratings .bi-star-fill:nth-child(2) { --star-index: 2; }
    .ratings .bi-star-fill:nth-child(3) { --star-index: 3; }
    .ratings .bi-star-fill:nth-child(4) { --star-index: 4; }
    .ratings .bi-star-fill:nth-child(5) { --star-index: 5; }
    
    .bg-warning {
        background: var(--gradient-warning) !important;
    }
    
    .bg-info {
        background: var(--gradient-primary) !important;
    }
    
    .bg-success {
        background: var(--gradient-success) !important;
    }
    
    .bg-danger {
        background: var(--gradient-danger) !important;
    }
    
    /* 功能按钮样式 */
    .btn {
        border-radius: 10px;
        font-weight: 500;
        transition: all 0.25s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        display: inline-flex;
        align-items: center;
        justify-content: center;
        padding: 0.5rem 1rem;
        position: relative;
        overflow: hidden;
        z-index: 1;
        transform: translateZ(0);
        box-shadow: var(--button-shadow);
        border-width: 1.5px;
    }
    
    .btn:after {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(rgba(255,255,255,0.1), rgba(255,255,255,0));
        opacity: 0;
        transition: opacity 0.2s ease;
        z-index: -1;
    }
    
    .btn:hover:after {
        opacity: 1;
    }
    
    .btn i {
        margin-right: 6px;
        transition: all 0.3s ease;
        display: inline-block;
    }
    
    .btn:hover i {
        transform: translateY(-2px) scale(1.1) rotate(-5deg);
    }
    
    .btn-primary {
        background: var(--gradient-primary);
        border-color: var(--primary-color);
        box-shadow: var(--button-shadow);
    }
    
    .btn-primary:hover {
        background: linear-gradient(135deg, #0077ed, #4dabf5);
        border-color: var(--primary-hover);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 113, 227, 0.3);
    }
    
    .btn-outline-primary {
        color: var(--primary-color);
        border-color: var(--primary-color);
        background: rgba(0, 113, 227, 0.05);
    }
    
    .btn-outline-primary:hover {
        background: var(--gradient-primary);
        color: white;
        border-color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(0, 113, 227, 0.2);
    }
    
    .btn-outline-info {
        color: var(--info-color);
        border-color: var(--info-color);
        position: relative;
        overflow: hidden;
        z-index: 1;
        background: rgba(90, 200, 250, 0.05);
    }
    
    .btn-outline-info:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: var(--gradient-primary);
        transition: all 0.3s ease;
        z-index: -1;
        border-radius: 8px;
    }
    
    .btn-outline-info:hover {
        color: white;
        border-color: var(--info-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(90, 200, 250, 0.2);
    }
    
    .btn-outline-info:hover:before {
        width: 100%;
    }
    
    .btn-outline-success {
        color: var(--success-color);
        border-color: var(--success-color);
        background-color: rgba(52, 199, 89, 0.05);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .btn-outline-success:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: var(--gradient-success);
        transition: all 0.3s ease;
        z-index: -1;
        border-radius: 8px;
    }
    
    .btn-outline-success:hover {
        color: white;
        border-color: var(--success-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(52, 199, 89, 0.2);
    }
    
    .btn-outline-success:hover:before {
        width: 100%;
    }
    
    .btn-outline-warning {
        color: var(--warning-color);
        border-color: var(--warning-color);
        background-color: rgba(255, 149, 0, 0.05);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .btn-outline-warning:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: var(--gradient-warning);
        transition: all 0.3s ease;
        z-index: -1;
        border-radius: 8px;
    }
    
    .btn-outline-warning:hover {
        color: white;
        border-color: var(--warning-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 149, 0, 0.2);
    }
    
    .btn-outline-warning:hover:before {
        width: 100%;
    }
    
    .btn-outline-danger {
        color: var(--danger-color);
        border-color: var(--danger-color);
        background-color: rgba(255, 59, 48, 0.05);
        position: relative;
        overflow: hidden;
        z-index: 1;
    }
    
    .btn-outline-danger:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 0;
        height: 100%;
        background: var(--gradient-danger);
        transition: all 0.3s ease;
        z-index: -1;
        border-radius: 8px;
    }
    
    .btn-outline-danger:hover {
        color: white;
        border-color: var(--danger-color);
        transform: translateY(-2px);
        box-shadow: 0 5px 15px rgba(255, 59, 48, 0.2);
    }
    
    .btn-outline-danger:hover:before {
        width: 100%;
    }
    
    /* 视图切换样式 */
    .view-mode-selector {
        margin-bottom: 24px;
        background-color: rgba(0,0,0,0.03);
        border-radius: 16px;
        padding: 10px;
        box-shadow: inset 0 2px 8px rgba(0,0,0,0.05), 0 1px 1px rgba(255,255,255,0.8);
        position: relative;
        overflow: hidden;
    }
    
    .view-mode-selector:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(to bottom, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0) 100%);
        pointer-events: none;
    }
    
    .view-mode-selector .btn-group {
        width: 100%;
        display: flex;
        gap: 12px;
        padding: 5px;
    }
    
    .view-mode-selector .btn {
        padding: 14px 20px;
        font-size: 1rem;
        border-radius: 12px;
        border-width: 2px;
        position: relative;
        overflow: hidden;
        transition: all 0.35s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        flex: 1;
        box-shadow: 0 3px 10px rgba(0,0,0,0.08);
    font-weight: 600;
        letter-spacing: 0.5px;
        text-transform: uppercase;
        font-size: 0.9rem;
    }
    
    .view-mode-selector .btn i {
        font-size: 1.3rem;
        margin-right: 10px;
        transition: all 0.3s ease;
        vertical-align: -0.15em;
    }
    
    .view-mode-selector .btn:hover i {
        transform: scale(1.2) rotate(-8deg);
    }
    
    .view-mode-selector .btn:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255,255,255,0.2) 0%, rgba(255,255,255,0) 60%);
        z-index: 1;
        transition: opacity 0.3s ease;
        opacity: 0.6;
        pointer-events: none;
    }
    
    .view-mode-selector .btn:hover:before {
        opacity: 1;
    }
    
    /* 卡片视图按钮样式 - 使用!important确保样式生效 */
    .view-mode-selector .btn-card-view {
        color: #ffffff !important;
        border-color: #7c5cfc !important;
        background-color: #7c5cfc !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
        transform: translateZ(0);
        will-change: transform, box-shadow;
    }
    
    .view-mode-selector .btn-card-view:hover {
        background: var(--card-view-gradient) !important;
        background-color: #8f6bff !important;
        box-shadow: 0 7px 20px rgba(124, 92, 252, 0.4) !important;
        transform: translateY(-3px) !important;
    }
    
    .view-mode-selector .btn-check:checked + .btn-card-view {
        background: var(--card-view-gradient) !important;
        background-color: #8f6bff !important;
        color: white !important;
        box-shadow: 0 7px 20px rgba(124, 92, 252, 0.5) !important;
        transform: translateY(-2px) scale(1.05) !important;
    }
    
    /* 列表视图按钮样式 - 使用!important确保样式生效 */
    .view-mode-selector .btn-list-view {
        color: #ffffff !important;
        border-color: #00c7be !important;
        background-color: #00c7be !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
        transform: translateZ(0);
        will-change: transform, box-shadow;
    }
    
    .view-mode-selector .btn-list-view:hover {
        background: var(--list-view-gradient) !important;
        background-color: #02ddca !important;
        box-shadow: 0 7px 20px rgba(0, 199, 190, 0.4) !important;
        transform: translateY(-3px) !important;
    }
    
    .view-mode-selector .btn-check:checked + .btn-list-view {
        background: var(--list-view-gradient) !important;
        background-color: #02ddca !important;
        color: white !important;
        box-shadow: 0 7px 20px rgba(0, 199, 190, 0.5) !important;
        transform: translateY(-2px) scale(1.05) !important;
    }
    
    /* 表格视图按钮样式 - 使用!important确保样式生效 */
    .view-mode-selector .btn-table-view {
        color: #ffffff !important;
        border-color: #ff6d3f !important;
        background-color: #ff6d3f !important;
        text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
        transform: translateZ(0);
        will-change: transform, box-shadow;
    }
    
    .view-mode-selector .btn-table-view:hover {
        background: var(--table-view-gradient) !important;
        background-color: #ff7a4b !important;
        box-shadow: 0 7px 20px rgba(255, 109, 63, 0.4) !important;
        transform: translateY(-3px) !important;
    }
    
    .view-mode-selector .btn-check:checked + .btn-table-view {
        background: var(--table-view-gradient) !important;
        background-color: #ff7a4b !important;
        color: white !important;
        box-shadow: 0 7px 20px rgba(255, 109, 63, 0.5) !important;
        transform: translateY(-2px) scale(1.05) !important;
    }
    
    /* 操作按钮样式 */
    .action-buttons .btn {
        height: auto;
        width: auto;
        padding: 0.4rem 0.7rem;
        margin-left: 5px;
        font-size: 0.8rem;
        display: inline-flex;
        align-items: center;
    justify-content: center;
        border-width: 1.5px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    }
    
    .action-buttons .btn:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    }
    
    .action-buttons .btn i {
        margin-right: 4px;
        font-size: 0.9rem;
    }
    
    /* 情感标签样式 */
    .sentiment-badge {
        display: inline-flex;
        align-items: center;
        padding: 4px 10px;
        border-radius: 6px;
        font-size: 0.85rem;
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .sentiment-badge i {
        margin-right: 4px;
        font-size: 1rem;
    }
    
    .sentiment-positive {
        background-color: rgba(52, 199, 89, 0.1);
        color: var(--success-color);
        border: 1px solid rgba(52, 199, 89, 0.3);
    }
    
    .sentiment-negative {
        background-color: rgba(255, 59, 48, 0.1);
        color: var(--danger-color);
        border: 1px solid rgba(255, 59, 48, 0.3);
    }
    
    .sentiment-neutral {
        background-color: rgba(255, 149, 0, 0.1);
        color: var(--warning-color);
        border: 1px solid rgba(255, 149, 0, 0.3);
    }
    
    /* 评价内容显示 */
    .comment-text {
        color: #424245;
        font-size: 0.95rem;
        max-height: 3.6em;
        overflow: hidden;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        line-height: 1.5;
        margin-bottom: 0.5rem;
        position: relative;
        transition: all 0.3s ease;
    }
    
    .comment-text:after {
        content: '';
        position: absolute;
        bottom: 0;
        right: 0;
        width: 30%;
        height: 1.5em;
        background: linear-gradient(to right, rgba(255,255,255,0), rgba(255,255,255,1));
        pointer-events: none;
    }
    
    .evaluation-card:hover .comment-text {
        color: #1d1d1f;
    }
    
    /* 评分星星 */
    .ratings .bi-star-fill {
        color: #ff9500 !important;
    }
    
    .ratings .bi-star {
        color: #d2d2d7 !important;
    }
    
    /* 评分显示增强 */
    .ratings {
        display: inline-flex;
        align-items: center;
        background: rgba(255, 149, 0, 0.05);
        padding: 3px 8px;
        border-radius: 6px;
        border: 1px solid rgba(255, 149, 0, 0.1);
        transition: all 0.3s ease;
    }
    
    .ratings:hover {
        background: rgba(255, 149, 0, 0.1);
        transform: translateY(-2px);
        box-shadow: 0 3px 10px rgba(255, 149, 0, 0.15);
    }
    
    /* 列表和表格视图样式 */
    #list-view-container .list-group-item {
        border-left: 3px solid var(--list-view-color) !important;
        transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
        border-radius: 10px;
        margin-bottom: 10px;
        box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        overflow: hidden;
        position: relative;
    }
    
    #list-view-container .list-group-item:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 3px;
        height: 100%;
        background: var(--list-view-gradient);
        z-index: 1;
    }
    
    #list-view-container .list-group-item:hover {
        box-shadow: 0 10px 25px rgba(0, 199, 190, 0.15);
        transform: translateY(-3px) scale(1.01);
    }
    
    #table-view-container .table {
        border-radius: var(--border-radius);
        overflow: hidden;
        box-shadow: var(--card-shadow);
        border-top: 3px solid var(--table-view-color);
        position: relative;
    }
    
    #table-view-container .table:after {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 3px;
        background: var(--table-view-gradient);
        z-index: 1;
    }
    
    #table-view-container th {
        background-color: rgba(255, 109, 63, 0.05);
        color: #6e6e73;
        font-weight: 600;
        border-top: none;
        padding: 16px 18px;
        white-space: nowrap;
        position: relative;
        overflow: hidden;
    }
    
    #table-view-container th:after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 1px;
        background: linear-gradient(90deg, rgba(255, 109, 63, 0.1), rgba(255, 109, 63, 0.3), rgba(255, 109, 63, 0.1));
    }
    
    #table-view-container td {
        padding: 14px 16px;
        vertical-align: middle;
        transition: all 0.2s ease;
    }
    
    #table-view-container tr:hover td {
        background-color: rgba(255, 109, 63, 0.03);
    }
    
    /* 筛选面板样式 */
    .filter-form {
        padding: 15px;
    }
    
    .filter-form label {
        font-weight: 500;
        color: #6e6e73;
        margin-bottom: 8px;
        font-size: 0.9rem;
        display: flex;
        align-items: center;
    }
    
    .filter-form label i {
        margin-right: 6px;
        color: var(--primary-color);
        font-size: 1.1rem;
    }
    
    .filter-form .form-control,
    .filter-form .form-select {
        border: 1.5px solid var(--border-color);
        border-radius: 10px;
        padding: 12px 16px;
        font-size: 0.95rem;
        box-shadow: none;
        transition: all 0.3s ease;
        background-color: rgba(255,255,255,0.8);
    }
    
    .filter-form .form-control:focus,
    .filter-form .form-select:focus {
        border-color: var(--primary-color);
        box-shadow: 0 0 0 3px rgba(0, 113, 227, 0.1);
        background-color: #ffffff;
        transform: translateY(-2px);
    }
    
    .filter-form .form-control:hover,
    .filter-form .form-select:hover {
        border-color: var(--primary-hover);
        background-color: #ffffff;
    }
    
    .filter-form .btn {
        padding: 12px 20px;
        font-weight: 500;
        margin-top: 5px;
    }
    
    /* 分页控件样式 */
    .pagination-container {
        margin-top: 40px;
        padding: 20px 0;
        background: linear-gradient(180deg, rgba(245,245,247,0) 0%, rgba(245,245,247,0.5) 100%);
        border-radius: 20px;
    }
    
    .page-link {
        color: var(--primary-color);
        border: none;
        margin: 0 4px;
        border-radius: 8px;
        padding: 10px 15px;
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        font-weight: 500;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05);
    }
    
    .page-link:hover {
        background-color: rgba(0, 113, 227, 0.08);
        color: var(--primary-color);
        transform: translateY(-2px);
        box-shadow: 0 3px 10px rgba(0, 113, 227, 0.1);
    }
    
    .page-item.active .page-link {
        background: var(--gradient-primary);
        box-shadow: 0 5px 15px rgba(0, 113, 227, 0.3);
        transform: translateY(-2px) scale(1.05);
    }
    
    /* 情感标签样式增强 */
    .sentiment-badge {
        display: inline-flex;
        align-items: center;
        padding: 5px 12px;
        border-radius: 8px;
        font-size: 0.85rem;
        font-weight: 500;
        box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
        position: relative;
        overflow: hidden;
    }
    
    .sentiment-badge:before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0));
        z-index: 1;
        pointer-events: none;
    }
    
    .sentiment-badge i {
        margin-right: 5px;
        font-size: 1rem;
        position: relative;
        z-index: 2;
    }
    
    .sentiment-badge:hover {
        transform: translateY(-3px);
        box-shadow: 0 5px 15px rgba(0,0,0,0.15);
    }
    
    .sentiment-positive {
        background: linear-gradient(135deg, rgba(52, 199, 89, 0.1), rgba(52, 199, 89, 0.2));
        color: var(--success-color);
        border: 1px solid rgba(52, 199, 89, 0.3);
    }
    
    .sentiment-positive:hover {
        background: linear-gradient(135deg, rgba(52, 199, 89, 0.15), rgba(52, 199, 89, 0.25));
        box-shadow: 0 5px 15px rgba(52, 199, 89, 0.2);
    }
    
    .sentiment-negative {
        background: linear-gradient(135deg, rgba(255, 59, 48, 0.1), rgba(255, 59, 48, 0.2));
        color: var(--danger-color);
        border: 1px solid rgba(255, 59, 48, 0.3);
    }
    
    .sentiment-negative:hover {
        background: linear-gradient(135deg, rgba(255, 59, 48, 0.15), rgba(255, 59, 48, 0.25));
        box-shadow: 0 5px 15px rgba(255, 59, 48, 0.2);
    }
    
    .sentiment-neutral {
        background: linear-gradient(135deg, rgba(255, 149, 0, 0.1), rgba(255, 149, 0, 0.2));
        color: var(--warning-color);
        border: 1px solid rgba(255, 149, 0, 0.3);
    }
    
    .sentiment-neutral:hover {
        background: linear-gradient(135deg, rgba(255, 149, 0, 0.15), rgba(255, 149, 0, 0.25));
        box-shadow: 0 5px 15px rgba(255, 149, 0, 0.2);
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .evaluation-dashboard {
            padding: 15px;
        }
        
        .card-header {
            padding: 12px 16px;
        }
        
        .page-title h2 {
            font-size: 24px;
        }
        
        .view-mode-selector .btn {
            padding: 10px 12px;
            font-size: 0.8rem;
        }
        
        .view-mode-selector .btn i {
            font-size: 1.1rem;
            margin-right: 6px;
        }
    }
    
    /* 页面滚动条美化 */
    /* 响应式调整 */
    @media (max-width: 768px) {
        .evaluation-dashboard {
            padding: 15px;
        }
        
        .card-header {
            padding: 12px 16px;
        }
        
        .page-title h2 {
            font-size: 24px;
        }
    }
    
    /* 页面滚动条美化 */
    ::-webkit-scrollbar {
        width: 8px;
        height: 8px;
    }

    ::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 10px;
    }

    ::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 10px;
        transition: all 0.3s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
        background: #a1a1a1;
    }

    /* 卡片容器美化 */
    #card-view-container, #list-view-container, #table-view-container {
        position: relative;
        z-index: 1;
    }

    /* 增强页面过渡效果 */
    #card-view-container, 
    #list-view-container, 
    #table-view-container {
        transition: opacity 0.5s ease, transform 0.5s ease;
    }

    #card-view-container:not(:visible), 
    #list-view-container:not(:visible), 
    #table-view-container:not(:visible) {
        opacity: 0;
        transform: translateY(20px);
    }

    #card-view-container:visible, 
    #list-view-container:visible, 
    #table-view-container:visible {
        opacity: 1;
        transform: translateY(0);
    }

    /* 增强筛选面板动画 */
    #filterCollapse.collapsing {
        transition: height 0.35s cubic-bezier(0.34, 1.56, 0.64, 1);
    }

    #filterCollapse.collapse.show {
        animation: filterFadeIn 0.5s ease forwards;
    }

    @keyframes filterFadeIn {
        from {
            opacity: 0;
            transform: translateY(-10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid evaluation-dashboard" style="background: #f9fafb; min-height: 100vh; padding-top: 20px;">
    <div class="row page-title" style="border-bottom: 1px solid #e9ecef; margin-bottom: 24px; padding-bottom: 12px;">
        <div class="col-md-6">
            <h2 style="color: #212529; font-size: 28px; font-weight: 600;"><i class="bi bi-clipboard2-check me-2" style="color: #0071e3;"></i>评价管理工作台</h2>
            <p class="text-muted" style="font-size: 15px;"><i class="bi bi-info-circle me-1"></i>管理和处理所有患者评价</p>
                </div>
        <div class="col-md-6 text-md-end d-flex justify-content-md-end align-items-center mt-3 mt-md-0">
            <a href="{% url 'qrmanager:sentiment_analysis' %}" class="btn me-2" style="background-color: #5ac8fa; color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(90, 200, 250, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(90, 200, 250, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(90, 200, 250, 0.25)';">
                <i class="bi bi-graph-up-arrow me-1"></i>数据分析
            </a>
            <button type="button" class="btn me-2" data-bs-toggle="modal" data-bs-target="#exportModal" style="background-color: #34c759; color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(52, 199, 89, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(52, 199, 89, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(52, 199, 89, 0.25)';">
                <i class="bi bi-file-earmark-excel me-1"></i>导出Excel
            </button>
            <button type="button" class="btn me-2" data-bs-toggle="modal" data-bs-target="#bulkDeleteModal" style="background-color: #ff3b30; color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(255, 59, 48, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(255, 59, 48, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(255, 59, 48, 0.25)';">
                <i class="bi bi-trash me-1"></i>批量删除
            </button>
            <button class="btn" type="button" data-bs-toggle="collapse" data-bs-target="#filterCollapse" style="background-color: #ff9500; color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(255, 149, 0, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(255, 149, 0, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(255, 149, 0, 0.25)';">
                <i class="bi bi-funnel-fill me-1"></i>筛选选项
            </button>
            </div>
        </div>
        
    <!-- 筛选面板 -->
    <div class="collapse mb-4" id="filterCollapse">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="bi bi-sliders me-2 text-primary"></i>评价筛选</h5>
                <button type="button" class="btn btn-sm btn-link text-muted" data-bs-toggle="collapse" data-bs-target="#filterCollapse">
                    <i class="bi bi-x-circle"></i>
                </button>
            </div>
            <div class="card-body">
                <form method="get" class="row g-3 filter-form">
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-building me-1"></i>科室</label>
                        <select class="form-select" name="department">
                            <option value="">全部科室</option>
                            {% for dept in departments %}
                            <option value="{{ dept.id }}" {% if department == dept.id|stringformat:"i" %}selected{% endif %}>{{ dept.name }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-emoji-smile me-1"></i>满意度</label>
                        <select class="form-select" name="is_satisfied">
                            <option value="">全部</option>
                            {% for satisfaction_value, satisfaction_label in satisfaction_choices %}
                            <option value="{{ satisfaction_value }}" {% if is_satisfied == satisfaction_value|stringformat:"s" %}selected{% endif %}>{{ satisfaction_label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-emoji-smile me-1"></i>情感倾向</label>
                        <select class="form-select" name="sentiment">
                            <option value="">全部情感</option>
                            {% for sentiment_value, sentiment_label in sentiment_choices %}
                            <option value="{{ sentiment_value }}" {% if sentiment == sentiment_value %}selected{% endif %}>{{ sentiment_label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-calendar-event me-1"></i>开始日期</label>
                        <input type="date" class="form-control" name="date_from" value="{{ date_from }}">
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-calendar-event me-1"></i>结束日期</label>
                        <input type="date" class="form-control" name="date_to" value="{{ date_to }}">
                    </div>
                    
                    <div class="col-md-4">
                        <label class="form-label"><i class="bi bi-check-circle me-1"></i>处理状态</label>
                        <select class="form-select" name="status">
                            <option value="">全部状态</option>
                            {% for status_value, status_label in process_status_choices %}
                            <option value="{{ status_value }}" {% if status == status_value %}selected{% endif %}>{{ status_label }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="col-md-8">
                        <label class="form-label"><i class="bi bi-search me-1"></i>关键词搜索</label>
                        <input type="text" class="form-control" name="search" placeholder="搜索评价内容..." value="{{ search }}">
                    </div>
                    
                    <div class="col-md-4 d-flex align-items-end">
                        <button type="submit" class="btn me-2" style="background: var(--gradient-primary); color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(0, 113, 227, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(0, 113, 227, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(0, 113, 227, 0.25)';">
                            <i class="bi bi-funnel-fill me-1"></i>应用筛选
                        </button>
                        <a href="{% url 'qrmanager:evaluation_list' %}" class="btn" style="background-color: #ff9500; color: white; font-weight: 600; border: none; box-shadow: 0 3px 10px rgba(255, 149, 0, 0.25); transition: all 0.3s ease; border-radius: 10px;" onmouseover="this.style.transform='translateY(-3px)'; this.style.boxShadow='0 5px 15px rgba(255, 149, 0, 0.35)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 10px rgba(255, 149, 0, 0.25)';">
                            <i class="bi bi-arrow-counterclockwise me-1"></i>清除筛选
                        </a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- 视图切换 -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="view-mode-selector" style="background: #ffffff; border-radius: 16px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08); padding: 15px; border: none;">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="bi bi-layout-text-window me-2 text-primary"></i>数据展示方式</h5>
                    <div class="btn-group" role="group">
                        <input type="radio" class="btn-check" name="view-mode" id="card-view" autocomplete="off" checked>
                        <label class="btn" for="card-view" style="background: linear-gradient(135deg, #7c5cfc, #a78bfa); color: #ffffff; font-weight: 600; border-radius: 12px; margin-right: 10px; border: none; box-shadow: 0 4px 10px rgba(124, 92, 252, 0.25); transform: translateZ(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-3px) scale(1.03)'; this.style.boxShadow='0 6px 15px rgba(124, 92, 252, 0.35)';" onmouseout="this.style.transform='translateZ(0)'; this.style.boxShadow='0 4px 10px rgba(124, 92, 252, 0.25)';">
                            <i class="bi bi-grid-3x3-gap-fill me-2"></i>卡片视图
                        </label>
                        
                        <input type="radio" class="btn-check" name="view-mode" id="list-view" autocomplete="off">
                        <label class="btn" for="list-view" style="background: linear-gradient(135deg, #00c7be, #0ecfb5); color: #ffffff; font-weight: 600; border-radius: 12px; margin-right: 10px; border: none; box-shadow: 0 4px 10px rgba(0, 199, 190, 0.25); transform: translateZ(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-3px) scale(1.03)'; this.style.boxShadow='0 6px 15px rgba(0, 199, 190, 0.35)';" onmouseout="this.style.transform='translateZ(0)'; this.style.boxShadow='0 4px 10px rgba(0, 199, 190, 0.25)';">
                            <i class="bi bi-list-task me-2"></i>列表视图
                        </label>
                        
                        <input type="radio" class="btn-check" name="view-mode" id="table-view" autocomplete="off">
                        <label class="btn" for="table-view" style="background: linear-gradient(135deg, #ff6d3f, #ff9a66); color: #ffffff; font-weight: 600; border-radius: 12px; border: none; box-shadow: 0 4px 10px rgba(255, 109, 63, 0.25); transform: translateZ(0); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-3px) scale(1.03)'; this.style.boxShadow='0 6px 15px rgba(255, 109, 63, 0.35)';" onmouseout="this.style.transform='translateZ(0)'; this.style.boxShadow='0 4px 10px rgba(255, 109, 63, 0.25)';">
                            <i class="bi bi-table me-2"></i>表格视图
                        </label>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 卡片视图 -->
    <div id="card-view-container">
        <div class="row row-cols-1 row-cols-md-2 row-cols-lg-3 row-cols-xl-4 g-4">
            {% for evaluation in evaluations %}
            <div class="col">
                <div class="card h-100 evaluation-card status-{{ evaluation.process_status }}" style="border-radius: 16px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-8px)'; this.style.boxShadow='0 12px 24px rgba(0, 0, 0, 0.12)';" onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='0 5px 15px rgba(0, 0, 0, 0.05)';">
                    <div class="card-header py-3 d-flex justify-content-between align-items-center" style="background-color: #fafafa; border-top-left-radius: 16px; border-top-right-radius: 16px; border-bottom: 1px solid #f0f0f0;">
                        <span class="badge 
                            {% if evaluation.process_status == 'pending' %}bg-warning text-dark
                            {% elif evaluation.process_status == 'processing' %}bg-info
                            {% elif evaluation.process_status == 'processed' %}bg-success
                            {% else %}bg-danger{% endif %}" 
                            style="font-size: 0.85rem; font-weight: 600; box-shadow: 0 3px 6px rgba(0,0,0,0.1); padding: 0.5em 0.9em; border-radius: 10px;">
                            {% if evaluation.process_status == 'pending' %}
                                <i class="bi bi-hourglass"></i>
                            {% elif evaluation.process_status == 'processing' %}
                                <i class="bi bi-gear-wide-connected"></i>
                            {% elif evaluation.process_status == 'processed' %}
                                <i class="bi bi-check2-all"></i>
                            {% else %}
                                <i class="bi bi-exclamation-triangle"></i>
                            {% endif %}
                            {{ evaluation.get_process_status_display }}
                        </span>
                        <div>
                            <span class="badge bg-primary" style="font-size: 0.85rem; padding: 0.5em 0.9em; border-radius: 10px; box-shadow: 0 3px 6px rgba(0,0,0,0.08);">{{ evaluation.qr_code.bed.department.name }}</span>
                            <small class="text-muted ms-1" style="font-style: italic;">{{ evaluation.created_at|date:"Y-m-d" }}</small>
                        </div>
                    </div>
                    <div class="card-body p-4">
                        <!-- 患者信息区块 -->
                        <div class="patient-info mb-3 p-3" style="background-color: #f9fbff; border-radius: 12px; border-left: 3px solid #0071e3;">
                            <h6 class="mb-2" style="color: #0071e3;"><i class="bi bi-person-vcard me-2"></i>患者信息</h6>
                            <div class="d-flex flex-column">
                                <small class="mb-1"><i class="bi bi-hospital me-2"></i>住院号: <span class="fw-bold">{{ evaluation.hospital_number|default:"未填写" }}</span></small>
                                <small class="mb-1"><i class="bi bi-telephone me-2"></i>联系方式: <span class="fw-bold">{{ evaluation.phone_number|default:"未填写" }}</span></small>
                                <small><i class="bi bi-door-open me-2"></i>床号: <span class="fw-bold">{{ evaluation.bed.number|default:"未知" }}</span></small>
                            </div>
                        </div>
                        
                        <!-- 工作人员评价区块 -->
                        <div class="staff-evaluations mb-3">
                            <h6 class="mb-2"><i class="bi bi-people me-2"></i>工作人员评价</h6>
                            
                            <!-- 满意的工作人员 -->
                            <div class="satisfied-staff mb-2 p-2" style="background-color: #f2fff8; border-radius: 10px;">
                                <small class="d-block mb-1 text-success"><i class="bi bi-emoji-smile"></i> 满意的工作人员：</small>
                                <div>
                                {% if evaluation.satisfied_staff1_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff1_name }}
                                    {% if evaluation.satisfied_staff1_title %}({{ evaluation.satisfied_staff1_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.satisfied_staff2_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff2_name }}
                                    {% if evaluation.satisfied_staff2_title %}({{ evaluation.satisfied_staff2_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.satisfied_staff3_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff3_name }}
                                    {% if evaluation.satisfied_staff3_title %}({{ evaluation.satisfied_staff3_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if not evaluation.satisfied_staff1_name and not evaluation.satisfied_staff2_name and not evaluation.satisfied_staff3_name %}
                                <span class="text-muted">无满意工作人员</span>
                                {% endif %}
                                </div>
                            </div>
                            
                            <!-- 不满意的工作人员 -->
                            <div class="unsatisfied-staff p-2" style="background-color: #fff8f8; border-radius: 10px;">
                                <small class="d-block mb-1 text-danger"><i class="bi bi-emoji-frown"></i> 不满意的工作人员：</small>
                                <div>
                                {% if evaluation.unsatisfied_staff1_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff1_name }}
                                    {% if evaluation.unsatisfied_staff1_title %}({{ evaluation.unsatisfied_staff1_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.unsatisfied_staff2_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff2_name }}
                                    {% if evaluation.unsatisfied_staff2_title %}({{ evaluation.unsatisfied_staff2_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.unsatisfied_staff3_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.85rem; padding: 0.5em 0.8em; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.05);">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff3_name }}
                                    {% if evaluation.unsatisfied_staff3_title %}({{ evaluation.unsatisfied_staff3_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if not evaluation.unsatisfied_staff1_name and not evaluation.unsatisfied_staff2_name and not evaluation.unsatisfied_staff3_name %}
                                <span class="text-muted">无不满意工作人员</span>
                                {% endif %}
                                </div>
                            </div>
                        </div>
                        
                        <!-- 评价内容 -->
                        <div class="comment-section p-3 mb-3" style="background-color: #f9f9f9; border-radius: 12px; position: relative;">
                            <h6 class="mb-2"><i class="bi bi-chat-left-text me-2"></i>评价内容</h6>
                            <p class="card-text mb-0" style="line-height: 1.5; color: #333;">
                                {{ evaluation.comment|default:"(无评价内容)"|truncatechars:100 }}
                            </p>
                            <!-- 情感标记 -->
                            <div class="sentiment-tag position-absolute" style="top: 10px; right: 10px;">
                                <span class="sentiment-badge 
                                    {% if evaluation.sentiment == 'positive' %}sentiment-positive
                                    {% elif evaluation.sentiment == 'negative' %}sentiment-negative
                                    {% else %}sentiment-neutral{% endif %}"
                                    style="font-size: 0.75rem; padding: 0.3em 0.6em; border-radius: 6px;">
                                    {% if evaluation.sentiment == 'positive' %}
                                        <i class="bi bi-emoji-smile"></i> 正面
                                    {% elif evaluation.sentiment == 'negative' %}
                                        <i class="bi bi-emoji-frown"></i> 负面
                                    {% else %}
                                        <i class="bi bi-emoji-neutral"></i> 中性
                                    {% endif %}
                                </span>
                            </div>
                        </div>
                        
                        <!-- 操作按钮区 -->
                        <div class="action-buttons">
                            {% if evaluation.process_status != 'processed' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=processed&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm me-2" style="background: linear-gradient(135deg, #34c759, #4cd964); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(52, 199, 89, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(52, 199, 89, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(52, 199, 89, 0.2)';">
                                <i class="bi bi-check-circle me-1"></i>已处理
                            </a>
                            {% endif %}
                            
                            {% if evaluation.process_status != 'flagged' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=flagged&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm me-2" style="background: linear-gradient(135deg, #ff9500, #ffcc00); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(255, 149, 0, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(255, 149, 0, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(255, 149, 0, 0.2)';">
                                <i class="bi bi-flag me-1"></i>需关注
                            </a>
                            {% endif %}
                            
                            {% if evaluation.process_status != 'pending' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=pending&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm" style="background: linear-gradient(135deg, #8e8e93, #aeaeb2); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(142, 142, 147, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(142, 142, 147, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(142, 142, 147, 0.2)';">
                                <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- 列表视图 -->
    <div id="list-view-container" style="display:none;">
        <div class="list-group" style="box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); border-radius: 16px; overflow: hidden;">
            {% for evaluation in evaluations %}
            <div class="list-group-item list-group-item-action p-4 status-{{ evaluation.process_status }}" style="border-left-width: 4px; transition: all 0.3s ease; position: relative;" onmouseover="this.style.backgroundColor='#f9fbff';" onmouseout="this.style.backgroundColor='';">
                <div class="row">
                    <div class="col-md-6">
                        <div class="d-flex align-items-center mb-2">
                            <span class="badge 
                                {% if evaluation.process_status == 'pending' %}bg-warning text-dark
                                {% elif evaluation.process_status == 'processing' %}bg-info
                                {% elif evaluation.process_status == 'processed' %}bg-success
                                {% else %}bg-danger{% endif %}" 
                                style="font-size: 0.85rem; padding: 0.5em 0.9em; border-radius: 10px; box-shadow: 0 3px 6px rgba(0,0,0,0.08);">
                                {% if evaluation.process_status == 'pending' %}
                                    <i class="bi bi-hourglass"></i>
                                {% elif evaluation.process_status == 'processing' %}
                                    <i class="bi bi-gear-wide-connected"></i>
                                {% elif evaluation.process_status == 'processed' %}
                                    <i class="bi bi-check2-all"></i>
                                {% else %}
                                    <i class="bi bi-exclamation-triangle"></i>
                                {% endif %}
                                {{ evaluation.get_process_status_display }}
                            </span>
                            <span class="ms-2 badge bg-primary" style="font-size: 0.85rem; padding: 0.5em 0.9em; border-radius: 10px; box-shadow: 0 3px 6px rgba(0,0,0,0.08);">{{ evaluation.qr_code.bed.department.name }}</span>
                            <span class="sentiment-badge ms-2
                                {% if evaluation.sentiment == 'positive' %}sentiment-positive
                                {% elif evaluation.sentiment == 'negative' %}sentiment-negative
                                {% else %}sentiment-neutral{% endif %}"
                                style="font-size: 0.75rem; padding: 0.3em 0.6em; border-radius: 6px;">
                                {% if evaluation.sentiment == 'positive' %}
                                    <i class="bi bi-emoji-smile"></i> 正面
                                {% elif evaluation.sentiment == 'negative' %}
                                    <i class="bi bi-emoji-frown"></i> 负面
                                {% else %}
                                    <i class="bi bi-emoji-neutral"></i> 中性
                                {% endif %}
                            </span>
                        </div>
                        
                        <!-- 患者信息 -->
                        <div class="patient-info mb-2">
                            <div class="d-flex flex-wrap mb-1">
                                <span class="me-3" style="color: #666;"><i class="bi bi-hospital me-1"></i>住院号: <span class="fw-bold">{{ evaluation.hospital_number|default:"未填写" }}</span></span>
                                <span class="me-3" style="color: #666;"><i class="bi bi-telephone me-1"></i>联系方式: <span class="fw-bold">{{ evaluation.phone_number|default:"未填写" }}</span></span>
                                <span style="color: #666;"><i class="bi bi-door-open me-1"></i>床号: <span class="fw-bold">{{ evaluation.bed.number|default:"未知" }}</span></span>
                            </div>
                            
                            <div class="evaluation-date">
                                <span style="color: #888; font-style: italic;"><i class="bi bi-calendar-date me-1"></i>{{ evaluation.created_at|date:"Y-m-d H:i" }}</span>
                            </div>
                        </div>
                        
                        <!-- 评价内容 -->
                        <div class="comment-section mt-2 mb-2 p-2" style="background-color: #f9f9f9; border-radius: 8px;">
                            <p class="mb-0" style="line-height: 1.5;">{{ evaluation.comment|default:"(无评价内容)"|truncatechars:100 }}</p>
                        </div>
                    </div>
                    
                    <div class="col-md-4">
                        <!-- 工作人员评价 -->
                        <div class="staff-evaluations">
                            <div style="font-size: 0.9rem; font-weight: 600; margin-bottom: 8px;"><i class="bi bi-people me-1"></i>工作人员评价</div>
                            
                            <!-- 满意的工作人员 -->
                            <div class="mb-2">
                                <small class="text-success d-block mb-1"><i class="bi bi-emoji-smile"></i> 满意的工作人员：</small>
                                <div>
                                {% if evaluation.satisfied_staff1_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff1_name }}
                                    {% if evaluation.satisfied_staff1_title %}({{ evaluation.satisfied_staff1_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.satisfied_staff2_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff2_name }}
                                    {% if evaluation.satisfied_staff2_title %}({{ evaluation.satisfied_staff2_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.satisfied_staff3_name %}
                                <span class="badge bg-light text-success me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-check me-1"></i>{{ evaluation.satisfied_staff3_name }}
                                    {% if evaluation.satisfied_staff3_title %}({{ evaluation.satisfied_staff3_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if not evaluation.satisfied_staff1_name and not evaluation.satisfied_staff2_name and not evaluation.satisfied_staff3_name %}
                                <span class="text-muted">无满意工作人员</span>
                                {% endif %}
                                </div>
                            </div>
                            
                            <!-- 不满意的工作人员 -->
                            <div>
                                <small class="text-danger d-block mb-1"><i class="bi bi-emoji-frown"></i> 不满意的工作人员：</small>
                                <div>
                                {% if evaluation.unsatisfied_staff1_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff1_name }}
                                    {% if evaluation.unsatisfied_staff1_title %}({{ evaluation.unsatisfied_staff1_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.unsatisfied_staff2_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff2_name }}
                                    {% if evaluation.unsatisfied_staff2_title %}({{ evaluation.unsatisfied_staff2_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if evaluation.unsatisfied_staff3_name %}
                                <span class="badge bg-light text-danger me-1 mb-1" style="font-size: 0.8rem; padding: 0.5em 0.7em; border-radius: 6px;">
                                    <i class="bi bi-person-x me-1"></i>{{ evaluation.unsatisfied_staff3_name }}
                                    {% if evaluation.unsatisfied_staff3_title %}({{ evaluation.unsatisfied_staff3_title }}){% endif %}
                                </span>
                                {% endif %}
                                
                                {% if not evaluation.unsatisfied_staff1_name and not evaluation.unsatisfied_staff2_name and not evaluation.unsatisfied_staff3_name %}
                                <span class="text-muted">无不满意工作人员</span>
                                {% endif %}
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="col-md-2">
                        <!-- 操作按钮 -->
                        <div class="action-buttons d-flex flex-column align-items-stretch">
                            {% if evaluation.process_status != 'processed' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=processed&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm mb-2" style="background: linear-gradient(135deg, #34c759, #4cd964); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(52, 199, 89, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(52, 199, 89, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(52, 199, 89, 0.2)';">
                                <i class="bi bi-check-circle me-1"></i>已处理
                            </a>
                            {% endif %}
                            
                            {% if evaluation.process_status != 'flagged' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=flagged&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm mb-2" style="background: linear-gradient(135deg, #ff9500, #ffcc00); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(255, 149, 0, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(255, 149, 0, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(255, 149, 0, 0.2)';">
                                <i class="bi bi-flag me-1"></i>需关注
                            </a>
                            {% endif %}
                            
                            {% if evaluation.process_status != 'pending' %}
                            <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=pending&next={{ request.get_full_path|urlencode }}" 
                               class="btn btn-sm" style="background: linear-gradient(135deg, #8e8e93, #aeaeb2); color: white; border: none; border-radius: 10px; box-shadow: 0 3px 6px rgba(142, 142, 147, 0.2); transition: all 0.3s ease;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 5px 12px rgba(142, 142, 147, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 3px 6px rgba(142, 142, 147, 0.2)';">
                                <i class="bi bi-arrow-counterclockwise me-1"></i>重置
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    
    <!-- 表格视图 -->
    <div id="table-view-container" style="display:none;">
        <div class="card" style="border-radius: 16px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05); overflow: hidden; border: none;">
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-hover mb-0" style="border-collapse: separate; border-spacing: 0;">
                        <thead style="background: linear-gradient(135deg, #f0f5ff, #ffffff);">
                            <tr>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">状态</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">科室</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">床号</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">住院号</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">联系方式</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">日期</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">工作人员评价</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">内容</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">情感</th>
                                <th class="py-3 px-4" style="font-weight: 600; color: #444; border-bottom: 2px solid #eaeaea;">操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for evaluation in evaluations %}
                            <tr class="status-{{ evaluation.process_status }}" style="transition: all 0.3s ease;" onmouseover="this.style.backgroundColor='#f9fbff';" onmouseout="this.style.backgroundColor='';">
                                <td class="py-3 px-4">
                                    <span class="badge 
                                        {% if evaluation.process_status == 'pending' %}bg-warning text-dark
                                        {% elif evaluation.process_status == 'processing' %}bg-info
                                        {% elif evaluation.process_status == 'processed' %}bg-success
                                        {% else %}bg-danger{% endif %}" 
                                        style="font-size: 0.85rem; padding: 0.5em 0.9em; border-radius: 10px; box-shadow: 0 3px 6px rgba(0,0,0,0.08);">
                                        {% if evaluation.process_status == 'pending' %}
                                            <i class="bi bi-hourglass"></i>
                                        {% elif evaluation.process_status == 'processing' %}
                                            <i class="bi bi-gear-wide-connected"></i>
                                        {% elif evaluation.process_status == 'processed' %}
                                            <i class="bi bi-check2-all"></i>
                                        {% else %}
                                            <i class="bi bi-exclamation-triangle"></i>
                                        {% endif %}
                                        {{ evaluation.get_process_status_display }}
                                    </span>
                                </td>
                                <td class="py-3 px-4" style="font-weight: 500;">{{ evaluation.qr_code.bed.department.name }}</td>
                                <td class="py-3 px-4">{{ evaluation.bed.number|default:"未知" }}</td>
                                <td class="py-3 px-4">{{ evaluation.hospital_number|default:"未填写" }}</td>
                                <td class="py-3 px-4">{{ evaluation.phone_number|default:"未填写" }}</td>
                                <td class="py-3 px-4">{{ evaluation.created_at|date:"Y-m-d" }}</td>
                                <td class="py-3 px-4">
                                    <div class="staff-evaluations">
                                        <!-- 满意的工作人员 -->
                                        <div class="mb-1">
                                            {% if evaluation.satisfied_staff1_name %}
                                            <span class="badge bg-light text-success me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-check"></i> {{ evaluation.satisfied_staff1_name }}
                                            </span>
                                            {% endif %}
                                            
                                            {% if evaluation.satisfied_staff2_name %}
                                            <span class="badge bg-light text-success me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-check"></i> {{ evaluation.satisfied_staff2_name }}
                                            </span>
                                            {% endif %}
                                            
                                            {% if evaluation.satisfied_staff3_name %}
                                            <span class="badge bg-light text-success me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-check"></i> {{ evaluation.satisfied_staff3_name }}
                                            </span>
                                            {% endif %}
                                        </div>
                                        
                                        <!-- 不满意的工作人员 -->
                                        <div>
                                            {% if evaluation.unsatisfied_staff1_name %}
                                            <span class="badge bg-light text-danger me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-x"></i> {{ evaluation.unsatisfied_staff1_name }}
                                            </span>
                                            {% endif %}
                                            
                                            {% if evaluation.unsatisfied_staff2_name %}
                                            <span class="badge bg-light text-danger me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-x"></i> {{ evaluation.unsatisfied_staff2_name }}
                                            </span>
                                            {% endif %}
                                            
                                            {% if evaluation.unsatisfied_staff3_name %}
                                            <span class="badge bg-light text-danger me-1" style="font-size: 0.8rem; padding: 0.4em 0.6em; border-radius: 6px;">
                                                <i class="bi bi-person-x"></i> {{ evaluation.unsatisfied_staff3_name }}
                                            </span>
                                            {% endif %}
                                            
                                            {% if not evaluation.satisfied_staff1_name and not evaluation.satisfied_staff2_name and not evaluation.satisfied_staff3_name and not evaluation.unsatisfied_staff1_name and not evaluation.unsatisfied_staff2_name and not evaluation.unsatisfied_staff3_name %}
                                            <span class="text-muted">无工作人员</span>
                                            {% endif %}
                                        </div>
                                    </div>
                                </td>
                                <td class="py-3 px-4">{{ evaluation.comment|default:"(无评价内容)"|truncatechars:50 }}</td>
                                <td class="py-3 px-4">
                                    <span class="sentiment-badge
                                        {% if evaluation.sentiment == 'positive' %}sentiment-positive
                                        {% elif evaluation.sentiment == 'negative' %}sentiment-negative
                                        {% else %}sentiment-neutral{% endif %}"
                                        style="font-size: 0.75rem; padding: 0.3em 0.6em; border-radius: 6px;">
                                        {% if evaluation.sentiment == 'positive' %}
                                            <i class="bi bi-emoji-smile"></i> 正面
                                        {% elif evaluation.sentiment == 'negative' %}
                                            <i class="bi bi-emoji-frown"></i> 负面
                                        {% else %}
                                            <i class="bi bi-emoji-neutral"></i> 中性
                                        {% endif %}
                                    </span>
                                </td>
                                <td class="py-3 px-4">
                                    <div class="action-buttons">
                                        {% if evaluation.process_status != 'processed' %}
                                        <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=processed&next={{ request.get_full_path|urlencode }}" 
                                           class="btn btn-sm mb-1" style="background: linear-gradient(135deg, #34c759, #4cd964); color: white; border: none; border-radius: 8px; box-shadow: 0 2px 4px rgba(52, 199, 89, 0.2); transition: all 0.3s ease; padding: 0.3em 0.6em; font-size: 0.75rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(52, 199, 89, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 2px 4px rgba(52, 199, 89, 0.2)';">
                                            <i class="bi bi-check-circle"></i>
                                        </a>
                                        {% endif %}
                                        
                                        {% if evaluation.process_status != 'flagged' %}
                                        <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=flagged&next={{ request.get_full_path|urlencode }}" 
                                           class="btn btn-sm mb-1" style="background: linear-gradient(135deg, #ff9500, #ffcc00); color: white; border: none; border-radius: 8px; box-shadow: 0 2px 4px rgba(255, 149, 0, 0.2); transition: all 0.3s ease; padding: 0.3em 0.6em; font-size: 0.75rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(255, 149, 0, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 2px 4px rgba(255, 149, 0, 0.2)';">
                                            <i class="bi bi-flag"></i>
                                        </a>
                                        {% endif %}
                                        
                                        {% if evaluation.process_status != 'pending' %}
                                        <a href="{% url 'qrmanager:process_evaluation' evaluation.id %}?status=pending&next={{ request.get_full_path|urlencode }}" 
                                           class="btn btn-sm" style="background: linear-gradient(135deg, #8e8e93, #aeaeb2); color: white; border: none; border-radius: 8px; box-shadow: 0 2px 4px rgba(142, 142, 147, 0.2); transition: all 0.3s ease; padding: 0.3em 0.6em; font-size: 0.75rem;" onmouseover="this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 8px rgba(142, 142, 147, 0.3)';" onmouseout="this.style.transform='none'; this.style.boxShadow='0 2px 4px rgba(142, 142, 147, 0.2)';">
                                            <i class="bi bi-arrow-counterclockwise"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
    
    <!-- 分页 -->
    <div class="mt-4">
        {% if is_paginated %}
        <nav aria-label="评价分页">
            <ul class="pagination justify-content-center">
                {% if page_obj.has_previous %}
                <li class="page-item">
                    <a class="page-link" href="?page=1{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="首页">
                        <span aria-hidden="true">&laquo;&laquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="上一页">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
                {% endif %}
                
                {% for i in page_obj.paginator.page_range %}
                    {% if i == page_obj.number %}
                    <li class="page-item active">
                        <span class="page-link">{{ i }}</span>
                    </li>
                    {% elif i > page_obj.number|add:"-3" and i < page_obj.number|add:"3" %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ i }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a>
                    </li>
                    {% endif %}
                {% endfor %}
                
                {% if page_obj.has_next %}
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="下一页">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
                <li class="page-item">
                    <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="末页">
                        <span aria-hidden="true">&raquo;&raquo;</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </nav>
        {% endif %}
    </div>
</div>

<!-- 导出Excel模态弹窗 -->
<div class="modal fade" id="exportModal" tabindex="-1" aria-labelledby="exportModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border-radius: 16px; border: none; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background-color: #f8f9fa; border-bottom: 1px solid #f0f0f0; border-top-left-radius: 16px; border-top-right-radius: 16px;">
                <h5 class="modal-title" id="exportModalLabel" style="color: #333; font-weight: 600;">
                    <i class="bi bi-file-earmark-excel me-2" style="color: #34c759;"></i>导出评价数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form id="exportForm" action="{% url 'qrmanager:export_evaluations' %}" method="get">
                    <!-- 添加现有的筛选参数 -->
                    {% for key, value in request.GET.items %}
                    {% if key != 'date_from' and key != 'date_to' %}
                    <input type="hidden" name="{{ key }}" value="{{ value }}">
                    {% endif %}
                    {% endfor %}
                    
                    <div class="mb-4 text-center">
                        <p style="color: #666;">请选择要导出的评价时间范围</p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="date_from" class="form-label">
                                <i class="bi bi-calendar-event me-1"></i>开始日期
                            </label>
                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                        <div class="col-md-6">
                            <label for="date_to" class="form-label">
                                <i class="bi bi-calendar-event me-1"></i>结束日期
                            </label>
                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="time_from" class="form-label">
                                <i class="bi bi-clock me-1"></i>开始时间
                            </label>
                            <input type="time" class="form-control" id="time_from" name="time_from" value="{{ time_from|default:'00:00:00' }}" step="1" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                        <div class="col-md-6">
                            <label for="time_to" class="form-label">
                                <i class="bi bi-clock me-1"></i>结束时间
                            </label>
                            <input type="time" class="form-control" id="time_to" name="time_to" value="{{ time_to|default:'23:59:59' }}" step="1" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border-top: 1px solid #f0f0f0; padding: 1rem;">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #f1f3f5; color: #495057; font-weight: 500; border-radius: 10px; padding: 0.5rem 1.25rem; transition: all 0.3s ease; border: none;">
                    取消
                </button>
                <button type="button" id="downloadBtn" class="btn" style="background-color: #34c759; color: white; font-weight: 500; border-radius: 10px; padding: 0.5rem 1.25rem; transition: all 0.3s ease; border: none;">
                    <i class="bi bi-download me-1"></i>下载Excel
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 提示框 -->
<div class="position-fixed bottom-0 end-0 p-3" style="z-index: 9999;">
    <div id="downloadToast" class="toast hide" role="alert" aria-live="assertive" aria-atomic="true" data-bs-delay="3000" style="background-color: white; border-radius: 12px; border: none; box-shadow: 0 5px 25px rgba(0, 0, 0, 0.12);">
        <div class="toast-header" style="background-color: #f8f9fa; border-bottom: 1px solid #f0f0f0; border-top-left-radius: 12px; border-top-right-radius: 12px;">
            <i class="bi bi-check-circle-fill me-2" style="color: #34c759;"></i>
            <strong class="me-auto">导出成功</strong>
            <small>刚刚</small>
            <button type="button" class="btn-close" data-bs-dismiss="toast" aria-label="Close"></button>
        </div>
        <div class="toast-body">
            评价数据已成功导出为Excel文件，请查看下载文件夹。
        </div>
    </div>
</div>

<!-- 批量删除模态弹窗 -->
<div class="modal fade" id="bulkDeleteModal" tabindex="-1" aria-labelledby="bulkDeleteModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content" style="border-radius: 16px; border: none; box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);">
            <div class="modal-header" style="background-color: #f8f9fa; border-bottom: 1px solid #f0f0f0; border-top-left-radius: 16px; border-top-right-radius: 16px;">
                <h5 class="modal-title" id="bulkDeleteModalLabel" style="color: #333; font-weight: 600;">
                    <i class="bi bi-trash me-2" style="color: #ff3b30;"></i>批量删除评价数据
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body p-4">
                <form id="bulkDeleteForm" action="{% url 'qrmanager:evaluation_bulk_delete' %}" method="post">
                    {% csrf_token %}
                    
                    <div class="alert alert-danger mb-4">
                        <i class="bi bi-exclamation-triangle-fill me-2"></i>
                        <strong>警告：</strong>此操作将永久删除选定时间范围内的所有评价数据，且无法恢复。请谨慎操作！
                    </div>
                    
                    <div class="mb-4 text-center">
                        <p style="color: #666;">请选择要删除的评价时间范围</p>
                    </div>
                    
                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="delete_date_from" class="form-label">
                                <i class="bi bi-calendar-event me-1"></i>开始日期
                            </label>
                            <input type="date" class="form-control" id="delete_date_from" name="date_from" required style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                        <div class="col-md-6">
                            <label for="delete_date_to" class="form-label">
                                <i class="bi bi-calendar-event me-1"></i>结束日期
                            </label>
                            <input type="date" class="form-control" id="delete_date_to" name="date_to" required style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                    </div>

                    <div class="row mb-3">
                        <div class="col-md-6">
                            <label for="delete_time_from" class="form-label">
                                <i class="bi bi-clock me-1"></i>开始时间
                            </label>
                            <input type="time" class="form-control" id="delete_time_from" name="time_from" value="00:00:00" step="1" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                        <div class="col-md-6">
                            <label for="delete_time_to" class="form-label">
                                <i class="bi bi-clock me-1"></i>结束时间
                            </label>
                            <input type="time" class="form-control" id="delete_time_to" name="time_to" value="23:59:59" step="1" style="border-radius: 10px; border: 1px solid #ced4da;">
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="current_password" class="form-label">
                            <i class="bi bi-key me-1"></i>当前密码
                        </label>
                        <input type="password" class="form-control" id="current_password" name="current_password" required style="border-radius: 10px; border: 1px solid #ced4da;" placeholder="请输入当前密码以确认操作">
                        <small class="text-muted">为确保安全，请输入您当前的登录密码以确认此操作。</small>
                    </div>
                    
                    <!-- 添加模态框内的错误消息区域 -->
                    <div id="modalErrorMessage" class="alert alert-danger mt-3" style="display: none;">
                        <i class="bi bi-exclamation-circle-fill me-2"></i>
                        <span id="errorText"></span>
                    </div>
                    
                    <div id="deleteCountPreview" class="alert alert-info mt-3" style="display: none;">
                        <i class="bi bi-info-circle me-2"></i>
                        符合条件的评价数据：<span id="deleteCount">0</span> 条
                    </div>
                </form>
            </div>
            <div class="modal-footer" style="border-top: 1px solid #f0f0f0; padding: 1rem;">
                <button type="button" class="btn" data-bs-dismiss="modal" style="background-color: #f1f3f5; color: #495057; font-weight: 500; border-radius: 10px; padding: 0.5rem 1.25rem; transition: all 0.3s ease; border: none;">
                    取消
                </button>
                <button type="button" id="previewDeleteBtn" class="btn me-2" style="background-color: #5ac8fa; color: white; font-weight: 500; border-radius: 10px; padding: 0.5rem 1.25rem; transition: all 0.3s ease; border: none;">
                    <i class="bi bi-search me-1"></i>预览
                </button>
                <button type="button" id="confirmDeleteBtn" class="btn" style="background-color: #ff3b30; color: white; font-weight: 500; border-radius: 10px; padding: 0.5rem 1.25rem; transition: all 0.3s ease; border: none; display: none;">
                    <i class="bi bi-trash me-1"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 视图切换功能
        $('input[name="view-mode"]').on('change', function() {
            var viewMode = $(this).attr('id');
            
            // 隐藏所有视图容器
            $('#card-view-container, #list-view-container, #table-view-container').hide();
            
            // 显示选中的视图
            $('#' + viewMode + '-container').show();
            
            // 存储用户偏好
            localStorage.setItem('evaluation-view-mode', viewMode);
        });
        
        // 加载用户偏好的视图
        var savedViewMode = localStorage.getItem('evaluation-view-mode');
        if (savedViewMode) {
            $('#' + savedViewMode).prop('checked', true).trigger('change');
        } else {
            $('#card-view').prop('checked', true).trigger('change');
        }
        
        // 显示消息提示
        function showMessages(messages) {
            if (messages && messages.length > 0) {
                messages.forEach(function(message) {
                    var alertDiv = $('<div class="alert alert-' + message.tags + ' alert-dismissible fade show" role="alert">' + 
                                   message.text + 
                                   '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' + 
                                   '</div>');
                    
                    $('.evaluation-dashboard').prepend(alertDiv);
                    
                    // 5秒后自动关闭
                    setTimeout(function() {
                        alertDiv.alert('close');
                    }, 5000);
                });
            }
        }
        
        // 如果有Django传递的消息，转换为JavaScript对象并显示
        var djangoMessages = [];
        {% if messages %}
            {% for message in messages %}
                djangoMessages.push({
                    tags: "{{ message.tags }}",
                    text: "{{ message }}"
                });
            {% endfor %}
        {% endif %}
        
        // 显示消息
        showMessages(djangoMessages);
        
        // 处理下载按钮点击
        $('#downloadBtn').on('click', function() {
            // 提交表单
            $('#exportForm').submit();
            
            // 关闭模态窗口
            var exportModal = bootstrap.Modal.getInstance(document.getElementById('exportModal'));
            exportModal.hide();
            
            // 显示下载成功提示
            setTimeout(function() {
                var toast = new bootstrap.Toast(document.getElementById('downloadToast'));
                toast.show();
            }, 1000);
        });
        
        // 批量删除相关代码
        // 初始化日期选择器默认值为当天
        var today = new Date();
        var dateString = today.toISOString().split('T')[0];
        $('#delete_date_from, #delete_date_to').val(dateString);
        
        // 预览删除效果
        $('#previewDeleteBtn').on('click', function() {
            // 清除之前的错误和预览结果
            $('#modalErrorMessage').hide();
            $('#deleteCountPreview').hide();
            
            var formData = new FormData($('#bulkDeleteForm')[0]);
            formData.append('preview', 'true');
            
            // 显示加载状态
            $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat me-1 spin"></i>正在加载...');
            
            $.ajax({
                url: $('#bulkDeleteForm').attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.status === 'success') {
                        // 显示符合条件的评价数量
                        $('#deleteCount').text(response.count);
                        $('#deleteCountPreview').show();
                        
                        // 显示确认删除按钮
                        if (response.count > 0) {
                            $('#confirmDeleteBtn').show();
                        } else {
                            $('#confirmDeleteBtn').hide();
                        }
                    } else {
                        // 在模态框内显示错误
                        $('#errorText').text(response.message || '预览失败，请重试。');
                        $('#modalErrorMessage').show();
                    }
                },
                error: function(xhr) {
                    var errorMsg = '预览请求失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    // 在模态框内显示错误
                    $('#errorText').text(errorMsg);
                    $('#modalErrorMessage').show();
                },
                complete: function() {
                    // 恢复按钮状态
                    $('#previewDeleteBtn').prop('disabled', false).html('<i class="bi bi-search me-1"></i>预览');
                }
            });
        });
        
        // 确认删除
        $('#confirmDeleteBtn').on('click', function() {
            // 清除之前的错误
            $('#modalErrorMessage').hide();
            
            // 再次确认
            if (!confirm('确定要删除这些评价数据吗？此操作不可恢复！')) {
                return;
            }
            
            // 显示加载状态
            $(this).prop('disabled', true).html('<i class="bi bi-arrow-repeat me-1 spin"></i>正在删除...');
            
            // 提交表单
            var formData = new FormData($('#bulkDeleteForm')[0]);
            
            $.ajax({
                url: $('#bulkDeleteForm').attr('action'),
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    if (response.status === 'success') {
                        // 关闭模态框
                        var bulkDeleteModal = bootstrap.Modal.getInstance(document.getElementById('bulkDeleteModal'));
                        bulkDeleteModal.hide();
                        
                        // 显示成功消息
                        showAlert('success', response.message || '评价数据已成功删除');
                        
                        // 刷新页面以更新评价列表
                        setTimeout(function() {
                            window.location.reload();
                        }, 1500);
                    } else {
                        // 在模态框内显示错误
                        $('#errorText').text(response.message || '删除失败，请重试。');
                        $('#modalErrorMessage').show();
                    }
                },
                error: function(xhr) {
                    var errorMsg = '删除请求失败';
                    if (xhr.responseJSON && xhr.responseJSON.message) {
                        errorMsg = xhr.responseJSON.message;
                    }
                    // 在模态框内显示错误
                    $('#errorText').text(errorMsg);
                    $('#modalErrorMessage').show();
                },
                complete: function() {
                    // 恢复按钮状态
                    $('#confirmDeleteBtn').prop('disabled', false).html('<i class="bi bi-trash me-1"></i>确认删除');
                }
            });
        });
        
        // 辅助函数：显示警告消息
        function showAlert(type, message) {
            var alertDiv = $('<div class="alert alert-' + type + ' alert-dismissible fade show" role="alert">' + 
                           message + 
                           '<button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>' + 
                           '</div>');
            
            $('.evaluation-dashboard').prepend(alertDiv);
            
            // 5秒后自动关闭
            setTimeout(function() {
                alertDiv.alert('close');
            }, 5000);
        }
        
        // 添加CSS动画
        $('<style>.spin { animation: spin 1s linear infinite; } @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }</style>').appendTo('head');
    });
</script>
{% endblock %} 