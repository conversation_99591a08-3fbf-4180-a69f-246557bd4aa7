# Generated by Django 4.2.7 on 2025-02-06 07:42

from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='Bed',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('bed_number', models.CharField(max_length=10, unique=True, verbose_name='床位编号')),
            ],
        ),
        migrations.CreateModel(
            name='QRCode',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('code', models.UUIDField(default=uuid.uuid4, editable=False, verbose_name='唯一编码')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '二维码',
                'verbose_name_plural': '二维码',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Evaluation',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, '非常不满意'), (2, '不满意'), (3, '一般'), (4, '满意'), (5, '非常满意')], verbose_name='评分')),
                ('comment', models.TextField(blank=True, verbose_name='评价内容')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='评价时间')),
                ('qr_code', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='qrmanager.qrcode')),
            ],
            options={
                'verbose_name': '评价',
                'verbose_name_plural': '评价',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DeviceFingerprint',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('fingerprint', models.CharField(max_length=200, verbose_name='指纹信息')),
                ('evaluation', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='fingerprint_info', to='qrmanager.evaluation', verbose_name='评价')),
            ],
        ),
    ]
