#!/usr/bin/env python
"""
测试二维码加密简化功能
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid

def test_encryption_comparison():
    """测试简化加密功能"""
    print("=" * 60)
    print("二维码简化加密测试")
    print("=" * 60)

    # 生成测试UUID
    test_uuid = str(uuid.uuid4())
    print(f"测试UUID: {test_uuid}")
    print(f"UUID长度: {len(test_uuid)} 字符")
    print()

    # 测试简化加密
    print("1. 简化加密测试:")
    try:
        encrypted = encrypt_qr_param(test_uuid)
        print(f"   加密结果: {encrypted}")
        print(f"   字符串长度: {len(encrypted)} 字符")

        # 测试解密
        decrypted_data = decrypt_qr_param(encrypted)
        print(f"   解密UUID: {decrypted_data['uuid']}")
        print(f"   解密成功: {'✓' if decrypted_data['uuid'] == test_uuid else '✗'}")
        print(f"   格式类型: {decrypted_data['format']}")

        # 计算压缩效果（与原始UUID比较）
        print()
        print("2. 压缩效果:")
        print(f"   原始UUID长度: {len(test_uuid)} 字符")
        print(f"   加密后长度: {len(encrypted)} 字符")
        increase = (len(encrypted) - len(test_uuid)) / len(test_uuid) * 100
        print(f"   长度增加: {increase:.1f}%")
        print(f"   增加字符: {len(encrypted) - len(test_uuid)} 个")

    except Exception as e:
        print(f"   加密失败: {e}")

def test_multiple_uuids():
    """测试多个UUID的加密效果"""
    print("\n" + "=" * 60)
    print("多UUID加密测试")
    print("=" * 60)

    test_uuids = [str(uuid.uuid4()) for _ in range(5)]

    total_encrypted_length = 0
    total_uuid_length = 0

    for i, test_uuid in enumerate(test_uuids, 1):
        print(f"\n测试 {i}:")
        print(f"UUID: {test_uuid}")

        try:
            # 加密测试
            encrypted = encrypt_qr_param(test_uuid)

            print(f"加密结果: {encrypted} ({len(encrypted)} 字符)")

            total_encrypted_length += len(encrypted)
            total_uuid_length += len(test_uuid)

            # 验证解密
            decrypted = decrypt_qr_param(encrypted)

            print(f"解密成功: {'✓' if decrypted['uuid'] == test_uuid else '✗'}")

        except Exception as e:
            print(f"错误: {e}")

    print(f"\n总体统计:")
    print(f"平均UUID长度: {total_uuid_length / len(test_uuids):.1f} 字符")
    print(f"平均加密长度: {total_encrypted_length / len(test_uuids):.1f} 字符")
    avg_increase = (total_encrypted_length - total_uuid_length) / total_uuid_length * 100
    print(f"平均长度增加: {avg_increase:.1f}%")

if __name__ == "__main__":
    test_encryption_comparison()
    test_multiple_uuids()
    print("\n" + "=" * 60)
    print("测试完成！")
    print("=" * 60)
