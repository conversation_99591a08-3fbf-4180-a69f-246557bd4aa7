{% extends "qrmanager/base.html" %}
{% load django_bootstrap5 %}

{% block title %}{% if form.instance.pk %}编辑二维码{% else %}创建二维码{% endif %}{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center">
                        {% if form.instance.pk %}
                            编辑二维码
                        {% else %}
                            创建新二维码
                        {% endif %}
                    </h1>
                    <form method="post" class="mt-4">
                        {% csrf_token %}
                        {% bootstrap_form form %}
                        <div class="text-center">
                            <button type="submit" class="btn btn-primary">保存</button>
                            <a href="{% url 'qrmanager:qr_list' %}" class="btn btn-secondary">返回</a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 