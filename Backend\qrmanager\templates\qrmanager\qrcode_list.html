{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}二维码管理{% endblock %}

{% block extra_js %}
<script src="{% static 'qrmanager/js/qrcode-unified.js' %}"></script>
{% endblock %}

{% block content %}
<div class="container-fluid p-4">
    <!-- 页面标题和操作按钮 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 fw-bold text-primary">二维码管理</h1>
        </div>
        <div class="d-flex">
            <button type="button" class="btn btn-success me-2" onclick="openDepartmentPrintModal()">
                <i class="fas fa-hospital me-1"></i>按科室批量打印
            </button>
            <a href="{% url 'qrmanager:qrcode_history' %}" class="btn btn-outline-primary me-2">
                <i class="fas fa-history me-1"></i>二维码历史记录
            </a>
            <button type="button" class="btn btn-info me-2" data-bs-toggle="modal" data-bs-target="#urlSettingsModal">
                <i class="fas fa-cog me-1"></i>URL设置
            </button>
            <!-- 移除新增二维码按钮 -->
            <div class="alert alert-info mb-0 d-flex align-items-center">
                <i class="fas fa-info-circle me-2"></i>
                <span>二维码由床位自动生成，无需手动创建</span>
            </div>
        </div>
    </div>

    <!-- 在二维码列表页面的适当位置添加说明 -->
    <!-- 例如在页面顶部的说明区域 -->

    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i> 
        <strong>提示：</strong> 系统生成的二维码默认<strong>永不过期</strong>，适合打印后长期使用。如需更换二维码，可使用"重新生成"功能。
        {% if qrcode_expiry and qrcode_expiry != '0' %}
            <br>当前系统设置的二维码有效期为：{{ qrcode_expiry|floatformat:"0" }} 秒
            （约 {{ qrcode_expiry|floatformat:"0"|divisibleby:"86400" }} 天）。
        {% endif %}
    </div>

    <!-- 扁平化统计卡片 -->
    <div class="row g-3 mb-4">
        <div class="col-xl-3 col-md-6">
            <div class="stat-card-flat bg-blue text-white">
                <div class="stat-content">
                    <div class="stat-label">二维码总数</div>
                    <div class="stat-value">{{ total_qrcodes }}</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-qrcode"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card-flat bg-green text-white">
                <div class="stat-content">
                    <div class="stat-label">总评价数</div>
                    <div class="stat-value">{{ total_evaluations }}</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-star"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card-flat bg-cyan text-white">
                <div class="stat-content">
                    <div class="stat-label">平均评分</div>
                    <div class="stat-value">{{ avg_rating|floatformat:1 }} <i class="fas fa-star fa-xs"></i></div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-chart-line"></i>
                </div>
            </div>
        </div>
        <div class="col-xl-3 col-md-6">
            <div class="stat-card-flat bg-amber text-white">
                <div class="stat-content">
                    <div class="stat-label">待处理评价</div>
                    <div class="stat-value">{{ pending_evaluations }}</div>
                </div>
                <div class="stat-icon">
                    <i class="fas fa-clock"></i>
                </div>
            </div>
        </div>
    </div>

    <!-- 内容区 -->
    <div class="content-panel">
        <!-- 搜索和筛选 -->
        <div class="filter-bar bg-white p-3 border-bottom">
            <form method="get" action="{% url 'qrmanager:qrcode_list' %}">
            <div class="row g-2">
                <div class="col-lg-3 col-md-6">
                    <div class="input-group">
                        <span class="input-group-text bg-transparent border-end-0">
                            <i class="fas fa-search text-muted"></i>
                        </span>
                        <input type="text" class="form-control border-start-0" name="search" 
                               placeholder="搜索床位号、科室名称..." 
                               value="{{ request.GET.search }}"
                               id="searchInput"
                               oninput="hybridSearch(this.value)">
                        {% if request.GET.search %}
                        <button type="button" class="btn btn-outline-secondary" onclick="clearSearch()">
                            <i class="fas fa-times"></i>
                        </button>
                        {% endif %}
                    </div>
                </div>
                <div class="col-lg-3 col-md-6">
                    <select name="department" class="form-select" onchange="this.form.submit()">
                        <option value="">全部科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"s" %}selected{% endif %}>
                            {{ dept.name }}
                        </option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-lg-2 col-md-6">
                    <select name="area" class="form-select" onchange="this.form.submit()">
                        <option value="">全部区域</option>
                        <option value="A" {% if request.GET.area == 'A' %}selected{% endif %}>A区</option>
                        <option value="B" {% if request.GET.area == 'B' %}selected{% endif %}>B区</option>
                    </select>
                </div>
                <div class="col-lg-4 col-md-6 d-flex">
                        <button type="submit" class="btn btn-primary me-2">
                            <i class="fas fa-search me-1"></i>搜索
                        </button>
                    <div class="btn-group me-2">
                        <button type="button" class="btn btn-outline-secondary" id="viewModeToggle" title="切换视图模式">
                            <i class="fas fa-th-large" id="viewModeIcon"></i>
                        </button>
                    </div>
                    
                    {% if department and department.print_template %}
                    <div class="dropdown d-inline-block me-2">
                        <button class="btn btn-outline-primary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                            <i class="fas fa-cog me-1"></i>模板设置
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{% url 'qrmanager:print_template_update' department.print_template.pk %}">
                                    <i class="fas fa-edit me-2"></i>编辑打印模板
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'qrmanager:print_template_preview' department.print_template.pk %}">
                                    <i class="fas fa-eye me-2"></i>预览打印效果
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item text-danger delete-template" 
                                   href="#" 
                                   data-template-id="{{ department.print_template.pk }}"
                                   data-department-name="{{ department.name }}">
                                    <i class="fas fa-trash me-2"></i>删除打印模板
                                </a>
                            </li>
                        </ul>
                    </div>
                    {% elif department %}
                        <a href="{% url 'qrmanager:department_print_template_create' department.id %}" class="btn btn-outline-primary me-2">
                        <i class="fas fa-plus me-1"></i>设置打印模板
                    </a>
                    {% endif %}
                    
                    <!-- 批量打印按钮已移除 -->
                </div>
            </div>
            </form>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar p-2 bg-light border-bottom">
            <div class="d-flex flex-wrap justify-content-between align-items-center">
                <div class="btn-toolbar">
                    <!-- 移除批量删除按钮 -->
                    <!-- 打印二维码按钮已移除 -->
                    <div class="btn-group">
                        <button type="button" class="btn btn-sm btn-outline-success dropdown-toggle" data-bs-toggle="dropdown" id="exportDropdownBtn" disabled>
                            <i class="fas fa-file-export me-1"></i>导出
                        </button>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="bulkExport(); return false;"><i class="fas fa-file-export me-2"></i>导出选中</a></li>
                            <li><a class="dropdown-item" href="#" onclick="exportQRCodes(); return false;"><i class="fas fa-file-export me-2"></i>导出全部</a></li>
                            <!-- 移除导入二维码选项 -->
                        </ul>
                    </div>
                    <!-- 删除清除无科室数据按钮 -->
                </div>
                <div class="d-flex align-items-center">
                    <span class="badge bg-primary me-2" id="selectionCounter" style="display: none;">
                        已选择 <span id="selectedCount">0</span> 项
                    </span>
                    <span class="text-muted small">选择要操作的二维码，点击相应按钮执行批量操作</span>
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        {% if qrcodes %}
        <form id="bulkForm" method="POST">
            {% csrf_token %}
            <input type="hidden" name="action" id="bulkAction" value="">
            <div class="table-responsive">
                <table class="table table-hover table-striped align-middle mb-0">
                    <thead class="table-light">
                        <tr>
                            <th class="ps-3" width="40">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input" id="selectAll">
                                </div>
                            </th>
                            <th>床位号</th>
                            <th>科室名称</th>
                            <th>区域</th>
                            <th class="text-center">评价数</th>
                            <th class="text-center">平均评分</th>
                            <th>创建时间</th>
                            <th>更新时间</th>
                            <th class="text-end pe-3">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for qrcode in qrcodes %}
                        <tr>
                            <td class="ps-3">
                                <div class="form-check">
                                    <input type="checkbox" class="form-check-input qrcode-checkbox" 
                                           name="selected_qrcodes" value="{{ qrcode.id }}"
                                           id="qrcode-{{ qrcode.id }}">
                                </div>
                            </td>
                            <td class="fw-bold">
                                {{ qrcode.bed.number }}
                            </td>
                            <td>
                                <span class="text-primary"><i class="fas fa-hospital-alt me-1"></i>{{ qrcode.bed.department.name }}</span>
                            </td>
                            <td>
                                {% if qrcode.bed.area %}
                                <span class="badge bg-info text-white">{{ qrcode.bed.get_area_display }}</span>
                                {% else %}
                                <span class="text-muted">-</span>
                                {% endif %}
                            </td>
                            <td class="text-center">
                                <a href="{% url 'qrmanager:evaluation_list' %}?department={{ qrcode.bed.department.id }}" class="text-decoration-none">
                                    {% if qrcode.evaluation_count == 0 %}
                                    <span class="badge bg-secondary">
                                        <i class="fas fa-comment me-1"></i>{{ qrcode.evaluation_count }}
                                    </span>
                                    {% elif qrcode.evaluation_count <= 3 %}
                                    <span class="badge bg-info text-white">
                                        <i class="fas fa-comment me-1"></i>{{ qrcode.evaluation_count }}
                                    </span>
                                    {% elif qrcode.evaluation_count <= 10 %}
                                    <span class="badge bg-success">
                                        <i class="fas fa-comment me-1"></i>{{ qrcode.evaluation_count }}
                                    </span>
                                    {% else %}
                                    <span class="badge bg-warning text-dark">
                                        <i class="fas fa-comment me-1"></i>{{ qrcode.evaluation_count }}
                                    </span>
                                    {% endif %}
                                </a>
                            </td>
                            <td class="text-center">
                                {% if qrcode.avg_rating > 0 %}
                                <span class="badge bg-warning text-dark">
                                    <i class="fas fa-star me-1"></i>{{ qrcode.avg_rating|floatformat:1 }}
                                </span>
                                {% else %}
                                <span class="text-muted">暂无评分</span>
                                {% endif %}
                            </td>
                            <td>
                                <span class="text-secondary"><i class="far fa-clock me-1"></i>{{ qrcode.created_at|date:"Y-m-d H:i" }}</span>
                            </td>
                            <td>
                                <span class="text-secondary"><i class="far fa-edit me-1"></i>{{ qrcode.updated_at|date:"Y-m-d H:i" }}</span>
                            </td>
                            <td class="text-end pe-3">
                                <div class="btn-group btn-group-sm">
                                    <a href="javascript:void(0)" class="btn btn-outline-primary view-qrcode-btn"
                                       data-bs-toggle="tooltip" title="查看二维码" data-qrcode-id="{{ qrcode.id }}"
                                       onclick="showQRCodePreview('{{ qrcode.id }}'); return false;">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{% url 'qrmanager:qrcode_update' qrcode.id %}" class="btn btn-outline-info"
                                       data-bs-toggle="tooltip" title="编辑二维码">
                                        <i class="fas fa-pencil-alt"></i>
                                    </a>
                                    <button class="btn btn-outline-warning btn-regenerate-qrcode"
                                       data-qrcode-id="{{ qrcode.id }}" data-qrcode-name="{{ qrcode.name }}"
                                       data-bs-toggle="tooltip" title="重新生成二维码">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                    <!-- 移除删除按钮 -->
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </form>
        {% else %}
        <div class="text-center py-5">
            <img src="{% static 'images/empty.svg' %}" alt="暂无数据" class="mb-3" style="width: 100px;">
            <h5 class="text-muted mb-3">暂无二维码信息</h5>
            <div class="alert alert-info d-inline-block">
                <i class="fas fa-info-circle me-2"></i>
                <span>二维码由床位自动生成，请先创建床位</span>
            </div>
        </div>
        {% endif %}
        
        <!-- 分页导航 -->
        {% if is_paginated %}
        <div class="d-flex justify-content-between align-items-center p-3 border-top">
            <div class="small text-muted">
                显示 {{ page_obj.start_index }} - {{ page_obj.end_index }} 条，共 {{ paginator.count }} 条记录
            </div>
            <nav aria-label="分页导航">
                <ul class="pagination mb-0">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.area %}area={{ request.GET.area }}&{% endif %}{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}page=1" aria-label="首页">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.area %}area={{ request.GET.area }}&{% endif %}{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}page={{ page_obj.previous_page_number }}" aria-label="上一页">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="首页">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="上一页">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for i in paginator.page_range %}
                        {% if page_obj.number == i %}
                        <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                        {% elif i > page_obj.number|add:'-3' and i < page_obj.number|add:'3' %}
                        <li class="page-item">
                            <a class="page-link" href="?{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.area %}area={{ request.GET.area }}&{% endif %}{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}page={{ i }}">{{ i }}</a>
                        </li>
                        {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.area %}area={{ request.GET.area }}&{% endif %}{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}page={{ page_obj.next_page_number }}" aria-label="下一页">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?{% if request.GET.department %}department={{ request.GET.department }}&{% endif %}{% if request.GET.area %}area={{ request.GET.area }}&{% endif %}{% if request.GET.search %}search={{ request.GET.search }}&{% endif %}page={{ paginator.num_pages }}" aria-label="末页">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% else %}
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="下一页">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item disabled">
                        <a class="page-link" href="#" aria-label="末页">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            <div class="d-flex align-items-center">
                <span class="me-2">每页显示:</span>
                <select class="form-select form-select-sm" style="width: 70px;" onchange="changePageSize(this.value)">
                    <option value="20" {% if paginator.per_page == 20 %}selected{% endif %}>20</option>
                    <option value="50" {% if paginator.per_page == 50 %}selected{% endif %}>50</option>
                    <option value="100" {% if paginator.per_page == 100 %}selected{% endif %}>100</option>
                </select>
            </div>
        </div>
        {% endif %}
    </div>
</div>

<!-- 二维码重新生成模态框 -->
<div class="modal fade" id="regenerateQRCodeModal" tabindex="-1" aria-labelledby="regenerateQRCodeModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="regenerateQRCodeModalLabel">重新生成二维码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="regenerateQRCodeForm" method="post">
                {% csrf_token %}
            <div class="modal-body">
                    <div class="mb-3">
                        <label for="regenerateReason" class="form-label">更换原因</label>
                        <textarea class="form-control" id="regenerateReason" name="reason" rows="3" placeholder="请输入更换二维码的原因"></textarea>
                    </div>
                    <div class="form-check mb-3">
                        <input class="form-check-input" type="checkbox" id="isSecurityIssue" name="is_security_issue">
                        <label class="form-check-label" for="isSecurityIssue">
                            标记为安全问题
                        </label>
                        <div class="form-text text-danger">标记为安全问题的旧二维码将被禁用，无法用于评价</div>
                    </div>
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>重新生成二维码后，原有二维码将失效，需要重新打印新的二维码。
                    </div>
                        </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-danger">确认重新生成</button>
                </div>
            </form>
                        </div>
                    </div>
                </div>
                
<!-- 二维码预览模态框 -->
<div class="modal fade" id="qrcodePreviewModal" tabindex="-1" aria-labelledby="qrcodePreviewModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-primary text-white">
                <h5 class="modal-title" id="qrcodePreviewModalLabel">二维码预览</h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close" onclick="setTimeout(fixModalBackdropIssue, 300)"></button>
            </div>
            <div class="modal-body p-0">
                <!-- 内容将由JavaScript动态填充 -->
                <div class="d-flex justify-content-center p-5">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-outline-secondary" data-bs-dismiss="modal" onclick="setTimeout(fixModalBackdropIssue, 300)">
                    <i class="fas fa-times me-1"></i>关闭
                </button>
                <button type="button" class="btn btn-outline-primary" onclick="downloadQRCode()">
                    <i class="fas fa-download me-1"></i>下载二维码
                </button>
                <button type="button" class="btn btn-primary" onclick="printPreviewQRCode()">
                    <i class="fas fa-print me-1"></i>打印
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Toast消息容器 -->
<div id="toastContainer" class="toast-container position-fixed bottom-0 end-0 p-3" style="z-index: 9999;"></div>

<style>
/* 轻量化的面包屑 */
.breadcrumb-simple {
    font-size: 0.85rem;
    color: #6c757d;
}

/* 扁平化统计卡片样式 */
.stat-card-flat {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1.25rem;
    border-radius: 8px;
    height: 100%;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    cursor: pointer;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.stat-card-flat:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 15px rgba(0, 0, 0, 0.1);
}

.stat-content {
    display: flex;
    flex-direction: column;
}

.stat-label {
    font-size: 0.85rem;
    opacity: 0.9;
    margin-bottom: 0.25rem;
}

.stat-value {
    font-size: 1.75rem;
    font-weight: 600;
}

.stat-icon {
    font-size: 1.75rem;
    opacity: 0.8;
}

/* 背景颜色 */
.bg-blue {
    background-color: #4A90E2;
}

.bg-green {
    background-color: #2ECC71;
}

.bg-cyan {
    background-color: #00BCD4;
}

.bg-amber {
    background-color: #FFC107;
}

/* 内容面板 */
.content-panel {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

/* 工具栏 */
.toolbar {
    background-color: #f8f9fa;
}

/* 表格样式优化 */
.table th {
    font-weight: 600;
    color: #333;
    border-bottom-width: 2px;
    background-color: #f5f5f5;
}

.table-striped > tbody > tr:nth-of-type(odd) {
    background-color: rgba(0, 0, 0, 0.02);
}

.table-hover > tbody > tr:hover {
    background-color: rgba(0, 123, 255, 0.05);
}

/* 床位号列样式 */
.table td.fw-bold {
    font-size: 1.1rem;
    color: #333;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .stat-card-flat {
        padding: 1rem;
    }
    
    .stat-value {
        font-size: 1.5rem;
    }
    
    .stat-icon {
        font-size: 1.5rem;
    }
}

/* 批量打印预览样式 */
.qr-print-preview {
    background-color: #f8f8f8;
    border-radius: 4px;
}

.print-page {
    background-color: white;
    width: 210mm;
    height: 297mm;
    margin: 0 auto 15px;
    padding: 10mm;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    position: relative;
}

.print-qr-grid {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-gap: 5mm;
}

.print-qr-item {
    border: 1px solid #eee;
    padding: 5mm;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.print-qr-image {
    width: 30mm;
    height: 30mm;
    margin-bottom: 2mm;
}

.print-qr-info {
    font-size: 12px;
    line-height: 1.2;
}

.print-qr-bed {
    font-weight: bold;
    font-size: 14px;
}

@media print {
    body * {
        visibility: hidden;
    }
    #printPreviewContainer, #printPreviewContainer * {
        visibility: visible;
    }
    #printPreviewContainer {
        position: absolute;
        left: 0;
        top: 0;
        width: 100%;
    }
    .print-page {
        box-shadow: none;
        margin: 0;
        padding: 0;
    }
}

/* 基础拖放区域样式 */
.drop-zone {
    border: 2px dashed #d2d2d7;
    border-radius: 8px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    background-color: #f8f9fa;
    cursor: pointer;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.drop-zone:hover {
    border-color: #0071e3;
    background-color: rgba(0, 113, 227, 0.05);
}

/* 拖放区域激活状态 */
.drop-zone.drop-zone-active {
    border-color: #0071e3;
    border-width: 2px;
    background-color: rgba(0, 113, 227, 0.05);
}

/* 拖放区域错误状态 */
.drop-zone.drop-zone-error {
    border-color: #ff3b30;
    background-color: rgba(255, 59, 48, 0.05);
}

/* 文件选择后的样式 */
.drop-zone-file-selected {
    border-color: #34c759;
    background-color: rgba(52, 199, 89, 0.05);
}

.drop-zone-file-selected .drop-zone-prompt i {
    color: #34c759;
}

/* 定义动画 */
@keyframes shake {
    0%, 100% {transform: translateX(0);}
    10%, 30%, 50%, 70%, 90% {transform: translateX(-5px);}
    20%, 40%, 60%, 80% {transform: translateX(5px);}
}

/* 应用动画效果 */
.drop-zone.drop-zone-error {
    animation: shake 0.6s ease-in-out;
}

.drop-zone-prompt i {
    font-size: 2.5rem;
    color: #6c757d;
    transition: all 0.3s ease;
    margin-bottom: 1rem;
}

.drop-zone:hover .drop-zone-prompt i {
    color: #0071e3;
    transform: translateY(-5px);
}

/* 二维码预览模态框样式 */
#qrcodePreviewModal .modal-body {
    padding: 0;
    overflow: hidden;
}

.preview-container {
    display: flex;
    min-height: 500px;
}

.preview-sidebar {
    width: 300px;
    background-color: #f8f9fa;
    border-right: 1px solid #dee2e6;
    padding: 1rem;
    overflow-y: auto;
    max-height: 600px;
}

.preview-main {
    flex: 1;
    padding: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    overflow: hidden;
}

.preview-section {
    margin-bottom: 1.5rem;
    border-bottom: 1px solid #eee;
    padding-bottom: 1rem;
}

.preview-section:last-child {
    border-bottom: none;
}

.preview-section-title {
    font-size: 0.9rem;
    font-weight: 600;
    margin-bottom: 0.75rem;
    color: #333;
}

.preview-info-item {
    display: flex;
    margin-bottom: 0.5rem;
    font-size: 0.85rem;
}

.preview-label {
    width: 80px;
    color: #666;
}

.preview-value {
    flex: 1;
    font-weight: 500;
}

.preview-value.code {
    font-family: monospace;
    font-size: 0.8rem;
    color: #0d6efd;
    word-break: break-all;
}

.template-preview-container {
    position: relative;
    width: 100%;
    max-width: 500px;
    margin: 0 auto;
}

.template-preview {
    position: relative;
    width: 100%;
    overflow: hidden;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

.template-background {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.qrcode-overlay {
    position: absolute;
    z-index: 10;
}

.qrcode-image {
    width: 100%;
    height: 100%;
}

.qrcode-large {
    max-width: 300px;
    height: auto;
    border: 1px solid #eee;
    padding: 10px;
    background-color: white;
}

.qrcode-notice {
    margin-top: 1rem;
    text-align: center;
    color: #666;
}

.preview-url-container {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 0.5rem;
}

.preview-url-actions {
    display: flex;
    gap: 0.5rem;
    margin-top: 0.5rem;
}

/* 确保不显示不必要的提示信息 */
.template-info-label {
    position: absolute;
    top: 10px;
    right: 10px;
    font-size: 12px;
    color: #666;
    background-color: rgba(255,255,255,0.9);
    padding: 6px 10px;
    border-radius: 4px;
    z-index: 30;
    max-width: 90%;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

/* 隐藏可能的额外提示信息 */
.template-preview::after {
    display: none !important;
}

/* 确保统计卡片内容正确显示 */
.row.g-3 .col-xl-3 .stat-card-flat {
    display: flex !important; 
    visibility: visible !important;
}
</style>

<script>
// 获取CSRF令牌
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 清除搜索
function clearSearch() {
    // 获取当前URL参数
    const urlParams = new URLSearchParams(window.location.search);
    // 删除search参数
    urlParams.delete('search');
    
    // 创建一个临时表单
    const form = document.createElement('form');
    form.method = 'get';
    form.action = window.location.pathname;
    
    // 添加其他参数到表单
    urlParams.forEach((value, key) => {
        const input = document.createElement('input');
        input.type = 'hidden';
        input.name = key;
        input.value = value;
        form.appendChild(input);
    });
    
    // 提交表单
    document.body.appendChild(form);
    form.submit();
    document.body.removeChild(form);
}

// 清除客户端搜索
function clearClientSearch() {
    document.getElementById('searchInput').value = '';
    clientSearch('');
}

// 客户端搜索功能
function clientSearch(query) {
    query = query.toLowerCase().trim();
    
    // 获取所有表格行
    const rows = document.querySelectorAll('table tbody tr');
    let visibleCount = 0;
    
    // 遍历每一行进行过滤
    rows.forEach(row => {
        // 获取床位号和科室名称
        const bedNumber = row.querySelector('td:nth-child(2)')?.textContent.toLowerCase() || '';
        const departmentName = row.querySelector('td:nth-child(3)')?.textContent.toLowerCase() || '';
        const area = row.querySelector('td:nth-child(4)')?.textContent.toLowerCase() || '';
        
        // 如果查询字符串为空或者匹配床位号或科室名称，则显示该行
        if (query === '' || 
            bedNumber.includes(query) || 
            departmentName.includes(query) || 
            area.includes(query)) {
            row.style.display = '';
            visibleCount++;
        } else {
            row.style.display = 'none';
        }
    });
    
    // 更新搜索结果计数
    updateSearchResultCount(visibleCount, rows.length);
    
    // 如果没有匹配的结果，显示提示信息
    const tableBody = document.querySelector('table tbody');
    if (visibleCount === 0 && tableBody) {
        // 检查是否已经有"无匹配结果"的行
        if (!document.getElementById('noResultsRow')) {
            const noResultsRow = document.createElement('tr');
            noResultsRow.id = 'noResultsRow';
            noResultsRow.innerHTML = `
                <td colspan="9" class="text-center py-4">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>当前页面没有找到匹配的二维码记录</span>
                        <p class="mt-2 mb-0">
                            <button type="button" class="btn btn-primary btn-sm" onclick="document.querySelector('form').submit()">
                                <i class="fas fa-search me-1"></i>搜索所有页面
                            </button>
                        </p>
                    </div>
                </td>
            `;
            tableBody.appendChild(noResultsRow);
        }
    } else {
        // 移除"无匹配结果"的行
        const noResultsRow = document.getElementById('noResultsRow');
        if (noResultsRow) {
            noResultsRow.remove();
        }
    }
}

// 更新搜索结果计数
function updateSearchResultCount(visibleCount, totalCount) {
    const searchResultInfo = document.querySelector('.small.text-muted');
    if (searchResultInfo) {
        searchResultInfo.textContent = `显示 ${visibleCount} 条，共 ${totalCount} 条记录`;
    }
}

// 批量导出
function bulkExport() {
    const selectedQRCodes = getSelectedQRCodes();
    if (selectedQRCodes.length === 0) {
        showToast('请先选择要导出的二维码', 'warning');
            return;
        }
        
    document.getElementById('bulkAction').value = 'export';
    document.getElementById('bulkForm').submit();
}

// 导出全部
function exportQRCodes() {
    window.location.href = "{% url 'qrmanager:qrcode_export' %}";
}

// 打印二维码
function printQRCodes() {
    const selectedQRCodes = getSelectedQRCodes();
    if (selectedQRCodes.length === 0) {
        showToast('请先选择要打印的二维码', 'warning');
        return;
    }
    
    document.getElementById('bulkAction').value = 'print';
    document.getElementById('bulkForm').submit();
}

// 获取选中的二维码
function getSelectedQRCodes() {
    const checkboxes = document.querySelectorAll('.qrcode-checkbox:checked');
    return Array.from(checkboxes).map(cb => cb.value);
}

// 显示批量打印模态框
function openBatchPrintModal() {
    const modal = new bootstrap.Modal(document.getElementById('batchPrintModal'));
    modal.show();
}

// 删除打印模板
function deleteTemplate(templateId) {
    if (confirm('确定要删除此打印模板吗？删除后将无法恢复。')) {
        window.location.href = `/print-templates/${templateId}/delete/`;
    }
}

// 显示提示消息
function showToast(message, type = 'info') {
    try {
        const toast = document.createElement('div');
        toast.className = `toast align-items-center text-white bg-${type} border-0`;
        toast.setAttribute('role', 'alert');
        toast.setAttribute('aria-live', 'assertive');
        toast.setAttribute('aria-atomic', 'true');
        
        toast.innerHTML = `
            <div class="d-flex">
                <div class="toast-body">
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        `;
        
        const container = document.getElementById('toastContainer');
        if (!container) {
            console.warn('Toast container not found, creating one');
            const newContainer = document.createElement('div');
            newContainer.id = 'toastContainer';
            newContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            newContainer.style.zIndex = '9999';
            document.body.appendChild(newContainer);
            newContainer.appendChild(toast);
        } else {
            container.appendChild(toast);
        }
        
        const bsToast = new bootstrap.Toast(toast, {
            autohide: true,
            delay: 3000
        });
        
        bsToast.show();
        
        // 自动移除
        toast.addEventListener('hidden.bs.toast', function() {
            const parent = toast.parentNode;
            if (parent) parent.removeChild(toast);
        });
    } catch (error) {
        console.error('显示Toast消息失败:', error);
        // 失败时使用alert作为备选方案
        if (type === 'danger') {
            alert('错误: ' + message);
        }
    }
}

// 显示二维码预览弹窗
function showQRCodePreview(qrcodeId) {
    // 先清理可能存在的遮罩层
    fixModalBackdropIssue();
    
    // 显示模态框
    const modalElement = document.getElementById('qrcodePreviewModal');
    const modalBody = modalElement.querySelector('.modal-body');
    
    // 显示加载状态
    modalBody.innerHTML = `
        <div class="d-flex justify-content-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
            <span class="ms-3">正在加载二维码数据...</span>
        </div>
    `;
    
    const modal = new bootstrap.Modal(modalElement);
    modal.show();
    
    // 监听模态框关闭事件
    modalElement.addEventListener('hidden.bs.modal', function onHidden() {
        // 移除事件监听器，避免重复绑定
        modalElement.removeEventListener('hidden.bs.modal', onHidden);
        // 清理背景遮罩
        setTimeout(fixModalBackdropIssue, 300);
    });
    
    // 记录查看开始时间，用于检测请求耗时
    const startTime = Date.now();
    
    // 加载二维码预览数据
    fetch(`/qrcodes/${qrcodeId}/preview/?format=json&timestamp=${Date.now()}`, {
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'Cache-Control': 'no-cache, no-store, must-revalidate'
        }
    })
    .then(response => {
        if (!response.ok) {
            throw new Error(`网络请求失败: ${response.status} ${response.statusText}`);
        }
        return response.json();
    })
    .then(data => {
        console.log(`获取二维码数据成功，耗时: ${Date.now() - startTime}ms`, data);
        
        // 保存原始数据
        window.currentQRCodeData = data;
        
        // 保存当前二维码UUID（用于后续验证）
        window.currentQRCodeUUID = data.code;
        
        // 填充模态框内容
        fillQRCodePreviewModal(data);
        
        // 检查并记录UUID变化
        if (window.previousQRCodeUUID && window.previousQRCodeUUID !== data.code) {
            console.warn('警告: 二维码UUID在查看期间发生了变化!', {
                previous: window.previousQRCodeUUID,
                current: data.code
            });
        }
        
        // 更新前一次UUID记录
        window.previousQRCodeUUID = data.code;
    })
    .catch(error => {
        console.error('获取二维码数据失败:', error);
        modalBody.innerHTML = `
            <div class="alert alert-danger m-4">
                <h5 class="alert-heading">
                    <i class="fas fa-exclamation-circle me-2"></i>
                    加载二维码数据失败
                </h5>
                <hr>
                <p>${error.message}</p>
                <div class="mt-3">
                    <button class="btn btn-outline-primary btn-sm" onclick="window.location.reload()">
                        <i class="fas fa-sync-alt me-1"></i>刷新页面
                    </button>
                </div>
            </div>`;
    });
}

// 填充二维码预览模态框内容
function fillQRCodePreviewModal(data) {
    const modalBody = document.querySelector('#qrcodePreviewModal .modal-body');
    
    // 创建内容
    let content = `
    <div class="preview-container">
        <!-- 左侧信息栏 -->
        <div class="preview-sidebar">
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-qrcode text-primary me-2"></i>二维码信息
                </h6>
                <div class="preview-info-item">
                    <span class="preview-label">名称</span>
                    <span class="preview-value">${data.name}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">创建时间</span>
                    <span class="preview-value">${data.created_at}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">编号</span>
                    <span class="preview-value code">${data.code}</span>
                </div>
            </div>
            
            ${data.bed ? `
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-bed text-primary me-2"></i>床位信息
                </h6>
                <div class="preview-info-item">
                    <span class="preview-label">床位号</span>
                    <span class="preview-value">${data.bed.number}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">科室</span>
                    <span class="preview-value">${data.bed.department ? data.bed.department.name : '-'}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">区域</span>
                    <span class="preview-value">${data.bed.area_display || '-'}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">负责人</span>
                    <span class="preview-value">${data.bed.staff ? data.bed.staff.name : '-'}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">评价次数</span>
                    <span class="preview-value">
                        <span class="badge bg-secondary">${data.evaluation_count}</span>
                    </span>
                </div>
            </div>
            ` : `
            <div class="preview-section">
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>此二维码未关联床位信息</span>
                </div>
            </div>
            `}
            
            ${data.template_data ? `
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-file-alt text-primary me-2"></i>模板信息
                </h6>
                <div class="preview-info-item">
                    <span class="preview-label">模板名称</span>
                    <span class="preview-value">${data.template_data.template_name || '未命名模板'}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">类型</span>
                    <span class="preview-value">${data.template_data.is_public ? '公共模板' : '科室专属模板'}</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">打印尺寸</span>
                    <span class="preview-value">${data.template_data.print_width}mm × ${data.template_data.print_height}mm</span>
                </div>
                <div class="preview-info-item">
                    <span class="preview-label">二维码尺寸</span>
                    <span class="preview-value">${data.template_data.original_qr_size}mm</span>
                </div>
            </div>
            ` : ''}
            
            ${data.bed ? `
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-star text-primary me-2"></i>床位评价
                </h6>
                <div class="preview-url-container">
                    <p class="text-muted mb-2" style="font-size: 12px;">
                        <i class="fas fa-info-circle me-1"></i>
                        患者可通过以下加密链接对该床位的服务进行评价
                    </p>
                    <input type="text" class="form-control form-control-sm" value="${data.evaluation_url}" id="evaluationUrlInput" readonly>
                    <div class="preview-url-actions">
                        <button class="btn btn-sm btn-outline-primary" type="button" onclick="copyEvaluationUrl()" title="复制链接">
                            <i class="fas fa-copy"></i>
                        </button>
                        <a href="${data.evaluation_url}" target="_blank" class="btn btn-sm btn-primary" title="访问链接">
                            <i class="fas fa-external-link-alt"></i>
                        </a>
                    </div>
                    <small class="text-muted mt-1 d-block">
                        <i class="fas fa-shield-alt me-1"></i>
                        此链接使用加密参数，确保评价数据安全
                    </small>
                </div>
            </div>
            ` : `
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-star text-primary me-2"></i>床位评价
                </h6>
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>此二维码未关联床位，无法进行评价</span>
                </div>
            </div>
            `}
            
            ${data.bed && data.bed.department && data.template_data ? `
            <div class="preview-section text-center">
                <a href="{% url 'qrmanager:print_template_update' 0 %}".replace('0', data.template_data.template_id)
                    class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="fas fa-edit me-1"></i>编辑打印模板
                </a>
            </div>
            ` : data.bed && data.bed.department ? `
            <div class="preview-section text-center">
                <a href="{% url 'qrmanager:department_print_template_create' 0 %}".replace('0', data.bed.department.id)
                    class="btn btn-sm btn-outline-primary" target="_blank">
                    <i class="fas fa-plus me-1"></i>设置打印模板
                </a>
            </div>
            ` : ''}

            <!-- 二维码参数部分 -->
            <div class="preview-section">
                <h6 class="preview-section-title">
                    <i class="fas fa-code text-success me-2"></i>二维码参数
                </h6>
                <div class="mb-2">
                    <label class="form-label small fw-bold">UUID:</label>
                    <input type="text" class="form-control form-control-sm" value="${data.code}" readonly>
                </div>
                <div>
                    <label class="form-label small fw-bold">加密参数:</label>
                    <div class="input-group">
                        <input type="text" class="form-control form-control-sm" id="encryptedParamInput" 
                               value="${data.secure_url ? data.secure_url.split('qr_code=')[1] : '未加密'}" readonly>
                        <button class="btn btn-sm btn-outline-secondary" type="button" onclick="copyEncryptedParam()" title="复制加密参数">
                            <i class="fas fa-copy"></i>
                        </button>
                    </div>
                    <small class="text-muted">加密参数可用于安全验证，在链接中使用 ?qr_code= 参数</small>
                </div>
            </div>
        </div>
        
        <!-- 右侧预览区 -->
        <div class="preview-main">
            ${data.template_data ? `
            <div class="template-preview-container">
                <!-- 强制预览容器与模板保持相同的比例 -->
                <div class="template-preview" style="
                    width: 100%; 
                    max-width: 500px;
                    position: relative; 
                    overflow: hidden;
                    aspect-ratio: ${data.template_data.print_width} / ${data.template_data.print_height};
                    box-shadow: 0 0 10px rgba(0,0,0,0.1);
                    background-color: white;
                ">
                    ${data.template_data.background_image ? 
                      `<img src="${data.template_data.background_image}" class="template-background" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; z-index: 1; object-fit: contain;">` : 
                      `<div class="template-no-background" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                         <span>无背景图片</span>
                       </div>`
                    }
                    
                    <!-- 使用绝对毫米值转换为精确百分比 -->
                    <div class="qrcode-overlay" style="
                        position: absolute;
                        left: calc(${data.template_data.original_qr_position_x} / ${data.template_data.print_width} * 100%);
                        top: calc(${data.template_data.original_qr_position_y} / ${data.template_data.print_height} * 100%);
                        width: calc(${data.template_data.original_qr_size} / ${data.template_data.print_width} * 100%); 
                        height: calc(${data.template_data.original_qr_size} / ${data.template_data.print_height} * 100%);
                        z-index: 2;
                        transform: translate(-50%, -50%); /* 确保与其他页面一致，使用中心点定位 */
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    ">
                        <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-image" style="width: 100%; height: 100%; border: none; box-shadow: none; background: transparent;">
                    </div>
                </div>
                <div class="template-info-label">
                    预览: 使用${data.template_data.is_public ? '公共模板' : '科室专属模板'} "${data.template_data.template_name || '未命名模板'}"
                    <div class="mt-1">模板尺寸: ${data.template_data.print_width} × ${data.template_data.print_height}mm | 二维码尺寸: ${data.template_data.original_qr_size}mm</div>
                    <div class="mt-1">二维码位置: X=${data.template_data.original_qr_position_x}mm, Y=${data.template_data.original_qr_position_y}mm</div>
                </div>
            </div>
            ` : data.bed ? `
            <div class="qrcode-only-preview">
                <div class="qrcode-container">
                    <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-large">
                </div>
                <div class="qrcode-notice">
                    <i class="fas fa-info-circle me-2"></i>
                    <span>该科室尚未设置打印模板</span>
                </div>
            </div>
            ` : `
            <div class="qrcode-only-preview">
                <div class="qrcode-container">
                    <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-large">
                </div>
                <div class="qrcode-notice alert alert-warning">
                    <i class="fas fa-exclamation-triangle me-2"></i>
                    <span>此二维码未关联床位，请先关联床位后再使用</span>
                </div>
            </div>
            `}
        </div>
    </div>`;
    
    // 更新模态框标题和内容
    document.getElementById('qrcodePreviewModalLabel').textContent = data.bed ? 
        `${data.bed.department ? data.bed.department.name : ''} - ${data.bed.number}号床位二维码` : 
        `二维码预览: ${data.name}`;
    modalBody.innerHTML = content;
    
    // 确保不显示额外的提示信息
    setTimeout(() => {
        const extraInfoElements = modalBody.querySelectorAll('.template-preview::after');
        extraInfoElements.forEach(el => {
            if(el) el.style.display = 'none';
        });
    }, 100);
}

// 复制评价链接到剪贴板
function copyEvaluationUrl() {
    const urlInput = document.getElementById('evaluationUrlInput');
    urlInput.select();
    document.execCommand('copy');
    showToast('评价链接已复制到剪贴板');
}

// 复制加密参数到剪贴板
function copyEncryptedParam() {
    const paramInput = document.getElementById('encryptedParamInput');
    paramInput.select();
    document.execCommand('copy');
    showToast('加密参数已复制到剪贴板');
}

// 打印预览二维码
function printPreviewQRCode() {
    if (!window.currentQRCodeData) return;
    
    const data = window.currentQRCodeData;
    const printWindow = window.open('', '_blank');
    
    let template = '';
    if (data.template_data) {
        template = `
            <html>
            <head>
                <title>打印二维码</title>
                <style>
                    @page {
                        size: ${data.template_data.print_width}mm ${data.template_data.print_height}mm;
                        margin: 0;
                    }
                    body {
                        margin: 0;
                        padding: 0;
                        width: ${data.template_data.print_width}mm;
                        height: ${data.template_data.print_height}mm;
                        position: relative;
                        background-color: white;
                    }
                    .background-image {
                        position: absolute;
                        top: 0;
                        left: 0;
                        width: 100%;
                        height: 100%;
                        z-index: 1;
                        object-fit: contain;
                    }
                    .qrcode-image {
                        position: absolute;
                        left: ${data.template_data.original_qr_position_x}mm;
                        top: ${data.template_data.original_qr_position_y}mm;
                        width: ${data.template_data.original_qr_size}mm;
                        height: ${data.template_data.original_qr_size}mm;
                        transform: translate(-50%, -50%); /* 确保与预览一致，使用中心点定位 */
                        z-index: 2;
                    }
                    /* 移除底部提示信息 */
                </style>
            </head>
            <body>
                ${data.template_data.background_image ? 
                  `<img src="${data.template_data.background_image}" class="background-image">` : ''}
                <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-image">
            </body>
            </html>
        `;
    } else {
        // 如果没有模板数据，则使用简单布局
        template = `
            <html>
            <head>
                <title>打印二维码</title>
                <style>
                    @page {
                        size: 100mm 100mm;
                        margin: 0;
                    }
                    body {
                        margin: 0;
                        padding: 20px;
                        display: flex;
                        flex-direction: column;
                        align-items: center;
                        justify-content: center;
                        background-color: white;
                    }
                    .qrcode-container {
                        text-align: center;
                    }
                    .qrcode-image {
                        width: 80mm;
                        height: 80mm;
                    }
                    .qrcode-title {
                        margin-top: 10px;
                        font-family: Arial, sans-serif;
                        font-size: 14px;
                        color: #333;
                    }
                </style>
            </head>
            <body>
                <div class="qrcode-container">
                    <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-image">
                    <div class="qrcode-title">${data.name}</div>
                </div>
            </body>
            </html>
        `;
    }
    
    printWindow.document.open();
    printWindow.document.write(template);
    printWindow.document.close();
    
    // 等待图片加载完成后打印
    printWindow.onload = function() {
        printWindow.focus();
        printWindow.print();
        // 打印后自动关闭窗口
        printWindow.onafterprint = function() {
            printWindow.close();
        };
    };
}

// 下载二维码图片
function downloadQRCode() {
    try {
        console.log('开始下载二维码...');
        if (!window.currentQRCodeData) {
            console.error('没有可用的二维码数据');
            showToast('没有可用的二维码数据，请重新打开预览', 'danger');
        return;
    }
    
        const data = window.currentQRCodeData;
        console.log('二维码数据:', data);
        
        // 如果有模板数据，创建一个包含模板的图片
        if (data.template_data) {
            console.log('使用模板数据生成图片');
            try {
                // 创建一个临时的canvas元素
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                
                // 设置canvas尺寸为模板尺寸（以像素为单位，使用更高的分辨率）
                // 使用高DPI比例以确保清晰度
                const pxRatio = 20; // 提高分辨率，1mm = 20px，确保超清晰
                const templateWidth = data.template_data.print_width;
                const templateHeight = data.template_data.print_height;
                
                canvas.width = templateWidth * pxRatio;
                canvas.height = templateHeight * pxRatio;
                
                // 绘制白色背景
                ctx.fillStyle = 'white';
                ctx.fillRect(0, 0, canvas.width, canvas.height);
                
                // 显示处理状态
                showToast('正在生成高质量图片...', 'info');
                
                // 使用原始二维码图片，不进行裁剪
                const qrImg = new Image();
                qrImg.onload = function() {
                    console.log('二维码图片加载成功');
                    // 如果有背景图片，先加载并绘制背景
                    if (data.template_data.background_image) {
                        const bgImg = new Image();
                        bgImg.crossOrigin = 'Anonymous';
                        bgImg.onload = function() {
                            console.log('背景图片加载成功');
                            // 绘制背景图片 - 保持比例且居中显示
                            drawImageProp(ctx, bgImg, 0, 0, canvas.width, canvas.height);
                            
                            // 计算二维码参数
                            const qrSize = data.template_data.original_qr_size * pxRatio;
                            const qrX = data.template_data.original_qr_position_x * pxRatio;
                            const qrY = data.template_data.original_qr_position_y * pxRatio;
                            
                            // 计算左上角坐标，模拟transform: translate(-50%, -50%)的效果
                            const position = centerToTopLeft(qrX, qrY, qrSize);
                            const qrLeft = position.left;
                            const qrTop = position.top;
                            
                            // 使用左上角坐标绘制
                            ctx.drawImage(qrImg, qrLeft, qrTop, qrSize, qrSize);
                            
                            // 转换为图片并下载（使用最高质量）
                            downloadCanvasAsImage(canvas);
                        };
                        bgImg.onerror = function(error) {
                            console.error('背景图片加载失败:', error);
                            showToast('背景图片加载失败，仅生成二维码', 'warning');
                            
                            // 仍然绘制二维码
                            const qrSize = data.template_data.original_qr_size * pxRatio;
                            const qrX = data.template_data.original_qr_position_x * pxRatio;
                            const qrY = data.template_data.original_qr_position_y * pxRatio;
                            
                            // 计算左上角坐标，模拟transform: translate(-50%, -50%)的效果
                            const position = centerToTopLeft(qrX, qrY, qrSize);
                            const qrLeft = position.left;
                            const qrTop = position.top;
                            
                            // 使用左上角坐标绘制
                            ctx.drawImage(qrImg, qrLeft, qrTop, qrSize, qrSize);
                            
                            downloadCanvasAsImage(canvas);
                        };
                        console.log('开始加载背景图片:', data.template_data.background_image);
                        bgImg.src = data.template_data.background_image;
            } else {
                        console.log('没有背景图片，只绘制二维码');
                        // 如果没有背景图片，只绘制二维码
                        const qrSize = data.template_data.original_qr_size * pxRatio;
                        const qrX = data.template_data.original_qr_position_x * pxRatio;
                        const qrY = data.template_data.original_qr_position_y * pxRatio;
                        
                        // 计算左上角坐标，模拟transform: translate(-50%, -50%)的效果
                        const position = centerToTopLeft(qrX, qrY, qrSize);
                        const qrLeft = position.left;
                        const qrTop = position.top;
                        
                        // 使用左上角坐标绘制
                        ctx.drawImage(qrImg, qrLeft, qrTop, qrSize, qrSize);
                        downloadCanvasAsImage(canvas);
                    }
                };
                qrImg.onerror = function(error) {
                    console.error('二维码图片加载失败:', error);
                    showToast('二维码图片加载失败，请重试', 'danger');
                    // 尝试直接下载原始二维码
                    directDownloadQRCode(data);
                };
                console.log('开始加载二维码图片');
                qrImg.src = `data:image/png;base64,${data.qr_image_base64}`;
            } catch (error) {
                console.error('Canvas处理失败:', error);
                showToast('图片处理失败，尝试直接下载', 'warning');
                // 出错时尝试直接下载
                directDownloadQRCode(data);
            }
        } else {
            console.log('没有模板数据，直接下载原始二维码');
            // 如果没有模板数据，直接下载原始二维码
            directDownloadQRCode(data);
        }
    } catch (error) {
        console.error('下载过程中发生错误:', error);
        alert('下载失败，请重试。错误: ' + error.message);
    }
}

// 直接下载二维码（无需Canvas处理）
function directDownloadQRCode(data) {
    try {
        console.log('直接下载二维码');
        const link = document.createElement('a');
        link.href = `data:image/png;base64,${data.qr_image_base64}`;
        
        // 生成更有意义的文件名
        let fileName = '';
        
        // 如果有床位信息，使用科室名称和床位号
        if (data.bed && data.bed.department) {
            const deptName = data.bed.department.name || '';
            const bedNumber = data.bed.number || '';
            fileName = `${deptName}-${bedNumber}号床位-二维码-${data.code}.png`;
        } else {
            // 否则使用二维码名称和编号
            fileName = `二维码-${data.name}-${data.code}.png`;
        }
        
        // 设置下载文件名
        link.download = fileName;
        
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        console.log('下载完成:', fileName);
        showToast(`二维码已下载: ${fileName}`, 'success');
    } catch (error) {
        console.error('直接下载失败:', error);
        alert('下载失败，请重试。错误: ' + error.message);
    }
}

// 辅助函数：将图像以正确的比例绘制到Canvas上（保持比例且居中）
function drawImageProp(ctx, img, x, y, w, h) {
    if (w === undefined) w = img.width;
    if (h === undefined) h = img.height;
    
    // 默认填充整个canvas
    if (x === undefined) x = 0;
    if (y === undefined) y = 0;
    
    // 保持原始比例
    var iw = img.width;
    var ih = img.height;
    var r = Math.min(w / iw, h / ih);
    var nw = iw * r;   // 新宽度
    var nh = ih * r;   // 新高度
    var cx, cy, cw, ch;
    
    // 决定图像在x和y方向上的开始位置
    cx = x + (w - nw) * 0.5;
    cy = y + (h - nh) * 0.5;
    cw = nw;
    ch = nh;
    
    // 绘制图像
    ctx.drawImage(img, 0, 0, iw, ih, cx, cy, cw, ch);
}

// 将canvas转换为图片并下载
function downloadCanvasAsImage(canvas) {
    try {
        console.log('开始从Canvas生成图片...');
        const data = window.currentQRCodeData;
        
        // 转换canvas为图片URL，使用最高质量(1.0)
        const imgURL = canvas.toDataURL('image/png', 1.0);
        console.log('Canvas转换为图片URL成功');
        
        // 创建下载链接
        const link = document.createElement('a');
        link.href = imgURL;
        
        // 生成更有意义的文件名
        let fileName = '';
        
        // 如果有床位信息，使用科室名称和床位号
        if (data.bed && data.bed.department) {
            const deptName = data.bed.department.name || '';
            const bedNumber = data.bed.number || '';
            fileName = `${deptName}-${bedNumber}号床位-二维码-${data.code}.png`;
            } else {
            // 否则使用二维码名称和编号
            fileName = `二维码-${data.name}-${data.code}.png`;
        }
        
        // 加上模板信息，让文件名更完整
        if (data.template_data && data.template_data.template_name) {
            fileName = fileName.replace('.png', `-模板[${data.template_data.template_name}].png`);
        }
        
        // 设置下载文件名
        link.download = fileName;
        
        // 添加到DOM并触发点击
        document.body.appendChild(link);
        console.log('开始下载文件:', fileName);
        link.click();
        document.body.removeChild(link);
        
        // 显示成功提示
        console.log('下载完成');
        showToast(`二维码已下载: ${fileName}`, 'success');
    } catch (error) {
        console.error('从Canvas下载图片失败:', error);
        showToast('下载图片失败，尝试直接下载', 'warning');
        
        // 如果Canvas下载失败，尝试直接下载原始二维码
        if (window.currentQRCodeData) {
            directDownloadQRCode(window.currentQRCodeData);
        } else {
            alert('下载失败，请重试');
        }
    }
}

// 切换每页显示数量
function changePageSize(size) {
    // 获取当前URL参数
    const urlParams = new URLSearchParams(window.location.search);
    // 设置page_size参数
    urlParams.set('page_size', size);
    // 重置页码为1
    urlParams.set('page', '1');
    // 跳转到新URL
    window.location.href = `${window.location.pathname}?${urlParams.toString()}`;
}

// 初始化页面
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });
    
    // 全选/取消全选
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.querySelectorAll('.qrcode-checkbox');
            checkboxes.forEach(cb => {
                cb.checked = selectAllCheckbox.checked;
            });
            updateSelectionCounter();
            updateBulkButtons();
        });
    }
    
    // 单个复选框变化
    const checkboxes = document.querySelectorAll('.qrcode-checkbox');
    checkboxes.forEach(cb => {
        cb.addEventListener('change', function() {
            updateSelectionCounter();
            updateBulkButtons();
            
            // 更新全选框状态
            if (selectAllCheckbox) {
                const allChecked = document.querySelectorAll('.qrcode-checkbox:checked').length === checkboxes.length;
                selectAllCheckbox.checked = allChecked;
            }
        });
    });
    
    // 获取所有重新生成按钮
    const regenerateButtons = document.querySelectorAll('.btn-regenerate-qrcode');
    
    // 为每个按钮添加点击事件
    regenerateButtons.forEach(button => {
        button.addEventListener('click', function(e) {
        e.preventDefault();
            const qrcodeId = this.getAttribute('data-qrcode-id');
            const qrcodeName = this.getAttribute('data-qrcode-name');
            
            // 设置模态框标题
            document.getElementById('regenerateQRCodeModalLabel').textContent = `重新生成二维码: ${qrcodeName}`;
            
            // 设置表单提交地址
            const form = document.getElementById('regenerateQRCodeForm');
            form.action = `/qrcodes/${qrcodeId}/regenerate/`;
            
            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('regenerateQRCodeModal'));
            modal.show();
        });
    });

    // 为查看按钮添加点击事件
    const viewButtons = document.querySelectorAll('.view-qrcode-btn');
    console.log('找到查看按钮数量:', viewButtons.length);
    viewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const qrcodeId = this.getAttribute('data-qrcode-id');
            console.log('点击查看按钮，二维码ID:', qrcodeId);
            if (qrcodeId) {
                showQRCodePreview(qrcodeId);
            }
        });
    });
    
    // 搜索输入框回车提交
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                e.preventDefault();
                // 直接提交表单进行服务器搜索
                this.form.submit();
            }
        });
        
        // 添加输入延迟提交功能
        let submitTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(submitTimeout);
            
            // 如果输入框为空，不设置延迟提交
            if (this.value.trim() === '') {
                return;
            }
            
            // 设置2秒后自动提交表单
            submitTimeout = setTimeout(() => {
                // 如果用户停止输入2秒，自动提交表单
                this.form.submit();
            }, 2000);
        });
        
        // 如果页面加载时已有搜索词，立即执行客户端搜索
        if (searchInput.value.trim() !== '') {
            clientSearch(searchInput.value);
        }
    }

    // 添加预览模态框的样式
    const style = document.createElement('style');
    style.textContent = `
        /* 二维码预览模态框样式 */
        .preview-container {
            display: flex;
            height: 600px;
        }
        
        .preview-sidebar {
            width: 280px;
            background-color: #f8f9fa;
            border-right: 1px solid #e9ecef;
            overflow-y: auto;
            padding: 0;
        }
        
        .preview-main {
            flex: 1;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #fff;
            overflow: hidden;
            position: relative;
        }
        
        .preview-section {
            padding: 15px;
            border-bottom: 1px solid #e9ecef;
        }
        
        .preview-section:last-child {
            border-bottom: none;
        }
        
        .preview-section-title {
            font-size: 14px;
            font-weight: 600;
            margin-bottom: 12px;
            color: #212529;
        }
        
        .preview-info-item {
            display: flex;
            margin-bottom: 8px;
            font-size: 13px;
        }
        
        .preview-info-item:last-child {
            margin-bottom: 0;
        }
        
        .preview-label {
            color: #6c757d;
            width: 80px;
            flex-shrink: 0;
        }
        
        .preview-value {
            color: #212529;
            flex: 1;
            font-weight: 500;
        }
        
        .preview-value.code {
            font-family: monospace;
            background-color: #f1f3f5;
            padding: 2px 4px;
            border-radius: 3px;
            font-size: 12px;
        }
        
        .preview-url-container {
            position: relative;
        }
        
        .preview-url-actions {
            display: flex;
            margin-top: 8px;
            gap: 8px;
        }
        
        .template-preview-container {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .template-preview {
            position: relative;
            width: 100%;
            height: 100%;
            max-width: 500px;
            max-height: 560px;
            box-shadow: 0 0 20px rgba(0,0,0,0.1);
        }
        
        .template-background {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }
        
        .template-no-background {
            width: 100%;
            height: 100%;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f8f9fa;
            color: #adb5bd;
        }
        
        .qrcode-overlay {
            position: absolute;
            background-color: transparent;
            box-shadow: none;
            border-radius: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
        }
        
        .qrcode-image {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border: none;
            box-shadow: none;
        }
        
        .qrcode-only-preview {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 30px;
            width: 100%;
            height: 100%;
        }
        
        .qrcode-container {
            background-color: transparent;
            padding: 0;
            border-radius: 0;
            box-shadow: none;
            margin-bottom: 20px;
        }
        
        .qrcode-large {
            width: 200px;
            height: 200px;
            border: none;
            box-shadow: none;
        }
        
        .qrcode-notice {
            color: #6c757d;
            font-size: 14px;
            background-color: #f8f9fa;
            padding: 8px 16px;
            border-radius: 4px;
        }
    `;
    document.head.appendChild(style);
});

// 更新选择计数器
function updateSelectionCounter() {
    const selectedCount = document.querySelectorAll('.qrcode-checkbox:checked').length;
    const counter = document.getElementById('selectionCounter');
    const countDisplay = document.getElementById('selectedCount');
    
    if (counter && countDisplay) {
        countDisplay.textContent = selectedCount;
        counter.style.display = selectedCount > 0 ? 'inline-block' : 'none';
    }
}

// 更新批量操作按钮状态
function updateBulkButtons() {
    const selectedCount = document.querySelectorAll('.qrcode-checkbox:checked').length;
    const printBtn = document.getElementById('printBtn');
    const exportDropdownBtn = document.getElementById('exportDropdownBtn');
    
    if (printBtn) printBtn.disabled = selectedCount === 0;
    if (exportDropdownBtn) exportDropdownBtn.disabled = selectedCount === 0;
}

// 混合搜索功能（客户端+服务器）
function hybridSearch(query) {
    // 执行客户端搜索，提供即时反馈
    clientSearch(query);
    
    // 如果搜索框为空，清除搜索结果
    if (query.trim() === '') {
        // 如果URL中有search参数，则刷新页面以显示所有结果
        if (window.location.search.includes('search=')) {
            clearSearch();
        }
    }
}

// 修复模态框背景遮罩问题
function fixModalBackdropIssue() {
    console.log('修复模态框背景问题');
    $('.modal-backdrop').remove();
    $('body').removeClass('modal-open');
    $('body').css('overflow', '');
    $('body').css('padding-right', '');
}

// 监听模态框关闭事件
document.addEventListener('hidden.bs.modal', function(event) {
    // 延迟执行，确保模态框完全关闭
    setTimeout(function() {
        // 检查是否所有模态框都已关闭但仍有背景遮罩
        if ($('.modal.show').length === 0 && $('.modal-backdrop').length > 0) {
            fixModalBackdropIssue();
        }
    }, 300);
});

// 监听点击事件，检测是否需要修复灰色背景问题
$(document).on('click', function(e) {
    // 如果所有模态框都已关闭但仍有模态框背景，则清理
    if ($('.modal.show').length === 0 && $('.modal-backdrop').length > 0) {
        fixModalBackdropIssue();
    }
});

// 在JS部分加载QRCode统一模块
// 这里应该删除错误的script标签
// 修改 drawQRCodes 函数，使用统一的二维码绘制方法
function drawQRCodes(qrCodes) {
    // ... existing code ...
    
    // 绘制二维码
    qrImg.onload = function() {
        // 使用统一的方法绘制二维码
        QRCodeUnified.drawQRCodeOnCanvas(
            ctx,
            qrImg,
            qrX,
            qrY,
            qrSize
        );
        
        // 绘制其他元素
        // ... existing code ...
    };
    
    // ... existing code ...
}

// 修改生成图片处理函数，使用统一方法
function processQRCode(data) {
    // ... existing code ...
    
    // 设置尺寸和比例
    // ... existing code ...
    
    qrImg.onload = function() {
        // 使用统一的方法绘制二维码
        QRCodeUnified.drawQRCodeOnCanvas(
            ctx,
            qrImg,
            data.template_data.original_qr_position_x * pxRatio,
            data.template_data.original_qr_position_y * pxRatio,
            qrSize
        );
        
        // 完成绘制和下载
        // ... existing code ...
    };
    
    // ... existing code ...
}

// 修改批量下载处理函数
function processBatchDownload(qrCodes) {
    // ... existing code ...
    
    qrImg.onload = function() {
        // 使用统一的方法绘制二维码
        QRCodeUnified.drawQRCodeOnCanvas(
            ctx,
            qrImg,
            qrX,
            qrY,
            qrSize
        );
        
        // 完成绘制和下载
        // ... existing code ...
    };
    
    // ... existing code ...
}

// 添加统一的二维码坐标转换函数
function centerToTopLeft(centerX, centerY, size) {
    return {
        left: centerX - (size / 2),
        top: centerY - (size / 2)
    };
}

/* 删除无科室二维码功能已不再需要 */
</script>

<!-- URL设置模态框 -->
<div class="modal fade" id="urlSettingsModal" tabindex="-1" aria-labelledby="urlSettingsModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-info text-white">
                <h5 class="modal-title" id="urlSettingsModalLabel">URL设置</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="urlSettingsForm" method="post" action="{% url 'qrmanager:update_url_settings' %}">
                {% csrf_token %}
                <div class="modal-body">
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle me-2"></i>
                        <span>这里可以设置前端URL，用于生成二维码和重定向。请确保设置正确的URL，否则可能导致二维码无法正常使用。</span>
                    </div>
                    
                    <div class="mb-3">
                        <label for="frontendUrl" class="form-label">前端URL</label>
                        <input type="text" class="form-control" id="frontendUrl" name="frontend_url" 
                               value="{{ frontend_url }}" placeholder="例如: http://************:8000 或 https://hospital.example.com">
                        <div class="form-text">用于生成二维码URL和重定向到前端页面，可以是IP地址或域名。</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="submit" class="btn btn-primary">保存设置</button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- 引入科室批量打印模态窗口 -->
{% include "qrmanager/department_print_modal.html" %}

<script>
$(document).ready(function() {
    // URL设置表单提交前验证
    $('#urlSettingsForm').on('submit', function(e) {
        var frontendUrl = $('#frontendUrl').val();
        
        // 验证URL格式
        if (!frontendUrl.startsWith('http://') && !frontendUrl.startsWith('https://')) {
            e.preventDefault(); // 阻止表单提交
            toastr.error('前端URL必须以http://或https://开头');
            return false;
        }
        
        return true; // 允许表单提交
    });
});
</script>

<!-- 批量打印模态框已移除 -->

{% endblock %}