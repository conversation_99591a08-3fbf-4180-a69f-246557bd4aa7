# Generated by Django 4.2.7 on 2025-03-16 13:27

from django.db import migrations, models
import django.db.models.deletion


def copy_staff_type_values(apps, schema_editor):
    """将new_staff_type的值复制到staff_type"""
    try:
        Staff = apps.get_model('qrmanager', 'Staff')
        
        # 遍历所有Staff记录
        for staff in Staff.objects.all():
            if hasattr(staff, 'new_staff_type') and staff.new_staff_type:
                staff.staff_type = staff.new_staff_type
                staff.save()
    except Exception as e:
        # 如果出现任何错误，只记录错误但不中断迁移
        print(f"复制工作人员类型值时出错: {str(e)}")


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0032_stafftype_alter_staff_staff_type'),
    ]

    operations = [
        # 第一步：将staff_type字段设置为可空
        migrations.AlterField(
            model_name='staff',
            name='staff_type',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, related_name='staffs', to='qrmanager.stafftype', verbose_name='人员类型'),
        ),
        
        # 第二步：将new_staff_type的值复制到staff_type
        migrations.RunPython(copy_staff_type_values),
        
        # 第三步：删除临时字段
        migrations.RemoveField(
            model_name='staff',
            name='new_staff_type',
        ),
    ]
