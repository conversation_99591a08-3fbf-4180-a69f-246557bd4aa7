# 开发规范

## 代码规范
1. **Python 编码规范**
   - 遵循 PEP 8 规范
   - 使用 4 个空格缩进
   - 行长度限制在 120 字符以内
   - 使用有意义的变量和函数名

2. **Django 最佳实践**
   - 使用 Django 4.2+ 的新特性
   - 遵循 app 模块化原则
   - 使用 Django ORM 而不是原生 SQL
   - 视图尽量使用类视图（CBV）

3. **文档规范**
   - 所有函数必须有文档字符串
   - 复杂逻辑必须有注释说明
   - 更新代码时同步更新相关文档
   - 使用 Markdown 格式编写文档

4. **Git 提交规范**
   ```
   <type>(<scope>): <subject>

   <body>

   <footer>
   ```
   类型（type）：
   - feat: 新功能
   - fix: 修复bug
   - docs: 文档更新
   - style: 代码格式调整
   - refactor: 重构
   - test: 测试相关
   - chore: 构建过程或辅助工具的变动

## 自动化流程

### 1. 代码提交前检查
```bash
# 运行自动化检查脚本
./scripts/pre-commit-check.sh
```

### 2. 文档自动更新
```bash
# 运行文档更新脚本
./scripts/update-docs.sh
```

### 3. 测试流程
```bash
# 运行测试套件
./scripts/run-tests.sh
```

## 开发流程

### 1. 功能开发
1. 创建功能分支
   ```bash
   git checkout -b feature/feature-name
   ```
2. 编写代码和测试
3. 更新相关文档
4. 提交代码
5. 创建合并请求

### 2. Bug修复
1. 创建修复分支
   ```bash
   git checkout -b fix/bug-name
   ```
2. 修复bug并添加测试
3. 更新相关文档
4. 提交代码
5. 创建合并请求

### 3. 文档更新
1. 创建文档分支
   ```bash
   git checkout -b docs/doc-name
   ```
2. 更新文档
3. 提交更新
4. 创建合并请求

## 自动化脚本

### pre-commit-check.sh
```bash
#!/bin/bash

# 运行代码格式化
black .

# 运行代码检查
flake8 .

# 运行测试
python manage.py test

# 检查文档更新
./scripts/check-docs.sh
```

### update-docs.sh
```bash
#!/bin/bash

# 更新API文档
python manage.py generate_swagger

# 更新数据库文档
python scripts/update_db_docs.py

# 更新测试覆盖率报告
coverage run manage.py test
coverage html
```

## 代码审查清单
- [ ] 代码是否符合PEP 8规范
- [ ] 是否包含必要的测试
- [ ] 文档是否同步更新
- [ ] 是否有不必要的代码
- [ ] 是否有潜在的安全问题
- [ ] 是否考虑了性能影响
- [ ] 提交信息是否规范

## 更新记录
- 2024-02-20: 初始版本 