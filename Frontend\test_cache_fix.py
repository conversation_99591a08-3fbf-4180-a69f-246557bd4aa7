#!/usr/bin/env python3
"""
缓存修复效果测试脚本
验证手机扫码后能否自动加载最新前端版本
"""

import requests
import time
from datetime import datetime
import urllib3

# 禁用SSL警告
urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)

def test_cache_headers(url, file_type):
    """测试缓存头部设置"""
    print(f"🔍 测试 {file_type} 缓存头部")
    print(f"URL: {url}")
    
    try:
        response = requests.head(url, verify=False, timeout=10)
        
        print(f"状态码: {response.status_code}")
        
        # 检查关键缓存头部
        cache_control = response.headers.get('Cache-Control', '未设置')
        pragma = response.headers.get('Pragma', '未设置')
        expires = response.headers.get('Expires', '未设置')
        
        print(f"Cache-Control: {cache_control}")
        print(f"Pragma: {pragma}")
        print(f"Expires: {expires}")
        
        # 判断缓存策略
        if 'no-cache' in cache_control and 'no-store' in cache_control:
            print("✅ 缓存已禁用 - 将自动加载最新版本")
            return True
        elif 'max-age=0' in cache_control:
            print("✅ 缓存时间为0 - 将自动加载最新版本")
            return True
        elif 'max-age=3600' in cache_control:
            print("⚠️  缓存1小时 - 图片文件正常")
            return True
        else:
            print("❌ 缓存策略可能有问题")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_file_modification_detection():
    """测试文件修改检测"""
    print("\n🔍 测试文件修改检测")
    print("-" * 50)
    
    # 测试主页HTML
    html_url = "https://zg120pj.cn/index.html"
    print(f"\n📄 测试HTML文件:")
    html_result = test_cache_headers(html_url, "HTML")
    
    # 测试JS文件
    js_url = "https://zg120pj.cn/js/main.js"
    print(f"\n📜 测试JS文件:")
    js_result = test_cache_headers(js_url, "JavaScript")
    
    # 测试CSS文件
    css_url = "https://zg120pj.cn/styles.css"
    print(f"\n🎨 测试CSS文件:")
    css_result = test_cache_headers(css_url, "CSS")
    
    return html_result and js_result and css_result

def simulate_mobile_access():
    """模拟手机访问"""
    print("\n📱 模拟手机访问测试")
    print("-" * 50)
    
    # 模拟手机User-Agent
    mobile_headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Version/14.1.2 Mobile/15E148 Safari/604.1'
    }
    
    try:
        url = "https://zg120pj.cn/"
        print(f"访问URL: {url}")
        
        start_time = time.time()
        response = requests.get(url, headers=mobile_headers, verify=False, timeout=15)
        end_time = time.time()
        
        print(f"✅ 访问成功")
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        print(f"内容长度: {len(response.content)} 字节")
        
        # 检查缓存头部
        cache_control = response.headers.get('Cache-Control', '未设置')
        print(f"Cache-Control: {cache_control}")
        
        # 检查是否包含最新版本号
        content = response.text
        if 'v=202506181354' in content:
            print("✅ 检测到最新版本号")
        else:
            print("⚠️  未检测到最新版本号")
        
        # 检查是否包含医院整体评价必填标识
        if '*必填' in content:
            print("✅ 检测到最新修改内容（医院整体评价必填）")
        else:
            print("⚠️  未检测到最新修改内容")
        
        return True
        
    except Exception as e:
        print(f"❌ 手机访问测试失败: {e}")
        return False

def test_qr_scan_simulation():
    """模拟二维码扫描"""
    print("\n📲 模拟二维码扫描测试")
    print("-" * 50)
    
    # 模拟微信内置浏览器
    wechat_headers = {
        'User-Agent': 'Mozilla/5.0 (iPhone; CPU iPhone OS 14_7_1 like Mac OS X) AppleWebKit/605.1.15 (KHTML, like Gecko) Mobile/15E148 MicroMessenger/8.0.20(0x18001428) NetType/WIFI Language/zh_CN'
    }
    
    try:
        # 模拟扫码访问（带二维码参数）
        qr_url = "https://zg120pj.cn/?qr=test123"
        print(f"扫码URL: {qr_url}")
        
        start_time = time.time()
        response = requests.get(qr_url, headers=wechat_headers, verify=False, timeout=15)
        end_time = time.time()
        
        print(f"✅ 扫码访问成功")
        print(f"状态码: {response.status_code}")
        print(f"响应时间: {end_time - start_time:.2f}秒")
        
        # 检查缓存策略
        cache_control = response.headers.get('Cache-Control', '未设置')
        if 'no-cache' in cache_control:
            print("✅ 页面禁用缓存 - 扫码将看到最新版本")
        else:
            print(f"⚠️  缓存策略: {cache_control}")
        
        return True
        
    except Exception as e:
        print(f"❌ 二维码扫描测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔧 缓存修复效果测试")
    print("=" * 80)
    print(f"测试时间: {datetime.now()}")
    print("目标：验证手机扫码后能否自动加载最新前端版本")
    print()
    
    tests = [
        ("文件修改检测", test_file_modification_detection),
        ("手机访问模拟", simulate_mobile_access),
        ("二维码扫描模拟", test_qr_scan_simulation),
    ]
    
    results = []
    
    for name, test_func in tests:
        try:
            print(f"执行: {name}")
            result = test_func()
            results.append((name, result))
            
            if result:
                print(f"✅ {name}: 通过\n")
            else:
                print(f"❌ {name}: 失败\n")
                
        except Exception as e:
            print(f"❌ {name}执行异常: {e}\n")
            results.append((name, False))
    
    # 总结
    print("📋 测试结果总结")
    print("=" * 80)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n测试通过率: {passed}/{len(results)}")
    
    if passed == len(results):
        print("🎉 所有测试通过！")
        print("✅ 缓存策略已正确配置")
        print("📱 手机扫码将自动加载最新前端版本")
        
    elif passed >= len(results) * 0.7:
        print("⚠️  大部分测试通过，可能需要微调")
        
    else:
        print("❌ 多个测试失败，需要检查配置")
    
    print(f"\n💡 修复说明:")
    print("1. HTML文件：完全禁用缓存")
    print("2. CSS/JS文件：完全禁用缓存")
    print("3. 图片文件：1小时缓存")
    print("4. 手机扫码：立即看到最新版本")
    
    print(f"\n🎯 下一步:")
    print("1. 重启Nginx服务使配置生效")
    print("2. 修改前端代码测试效果")
    print("3. 手机扫码验证是否自动更新")

if __name__ == "__main__":
    main()
