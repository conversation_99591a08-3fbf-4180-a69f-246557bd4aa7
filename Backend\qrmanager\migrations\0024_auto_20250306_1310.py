# Generated by Django 4.2.7 on 2025-03-06 05:10

from django.db import migrations, models
import django.db.models.deletion
from django.conf import settings


def initialize_process_status(apps, schema_editor):
    """初始化所有现有评价的处理状态为'pending'（未处理）"""
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    for evaluation in Evaluation.objects.all():
        evaluation.process_status = 'pending'
        evaluation.save(update_fields=['process_status'])


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('qrmanager', '0023_printtemplate_is_public_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='process_status',
            field=models.CharField(
                choices=[
                    ('pending', '未处理'),
                    ('processing', '处理中'),
                    ('processed', '已处理'),
                    ('flagged', '需关注'),
                ],
                default='pending',
                max_length=15,
                verbose_name='处理状态'
            ),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='processed_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='处理时间'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='processed_by',
            field=models.ForeignKey(
                blank=True,
                null=True,
                on_delete=django.db.models.deletion.SET_NULL,
                related_name='processed_evaluations',
                to=settings.AUTH_USER_MODEL,
                verbose_name='处理人'
            ),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='process_notes',
            field=models.TextField(blank=True, verbose_name='处理备注'),
        ),
        migrations.RunPython(initialize_process_status),
    ]
