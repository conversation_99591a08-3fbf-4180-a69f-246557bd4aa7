/**
 * 日志模块 - 提供集中式日志管理和错误处理
 * 2025-03-24
 */

(function() {
    // 日志级别定义
    const LOG_LEVELS = {
        ERROR: 0,
        WARN: 1,
        INFO: 2,
        DEBUG: 3,
        TRACE: 4
    };

    // 日志级别名称映射
    const LOG_LEVEL_NAMES = {
        0: 'ERROR',
        1: 'WARN',
        2: 'INFO',
        3: 'DEBUG',
        4: 'TRACE'
    };

    // 检测是否为生产环境
    const isProduction = function() {
        // 检查URL中是否包含debug参数
        const hasDebugParam = window.location.search.includes('debug=true');
        
        // 检查localStorage中是否设置了debugMode
        const hasDebugMode = localStorage.getItem('debugMode') === 'true';
        
        // 如果URL中有debug参数或localStorage中设置了debugMode，则不是生产环境
        return !(hasDebugParam || hasDebugMode);
    };

    // 默认日志级别：生产环境为WARN，开发环境为DEBUG
    let currentLogLevel = isProduction() ? LOG_LEVELS.WARN : LOG_LEVELS.DEBUG;

    // 日志模块
    const logger = {
        // 获取当前日志级别
        getLogLevel: function() {
            return currentLogLevel;
        },

        // 设置日志级别
        setLogLevel: function(level) {
            if (LOG_LEVELS[level] !== undefined) {
                currentLogLevel = LOG_LEVELS[level];
            } else if (typeof level === 'number' && level >= 0 && level <= 4) {
                currentLogLevel = level;
            } else {
                this.warn('无效的日志级别:', level);
            }
        },

        // 获取当前日志级别名称
        getLogLevelName: function() {
            return LOG_LEVEL_NAMES[currentLogLevel];
        },

        // 检查是否应该记录指定级别的日志
        shouldLog: function(level) {
            return level <= currentLogLevel;
        },

        // 格式化日志消息
        formatLogMessage: function(level, args) {
            const timestamp = new Date().toISOString();
            const levelName = LOG_LEVEL_NAMES[level];
            const prefix = `[${levelName}] [${timestamp}]`;
            
            // 将所有参数转换为字符串
            const messages = Array.from(args).map(arg => {
                if (typeof arg === 'object') {
                    try {
                        return JSON.stringify(arg);
                    } catch (e) {
                        return String(arg);
                    }
                }
                return String(arg);
            });
            
            return `${prefix} ${messages.join(' ')}`;
        },

        // 错误日志
        error: function() {
            if (this.shouldLog(LOG_LEVELS.ERROR)) {
                const message = this.formatLogMessage(LOG_LEVELS.ERROR, arguments);
                console.error(message);
            }
        },

        // 警告日志
        warn: function() {
            if (this.shouldLog(LOG_LEVELS.WARN)) {
                const message = this.formatLogMessage(LOG_LEVELS.WARN, arguments);
                console.warn(message);
            }
        },

        // 信息日志
        info: function() {
            if (this.shouldLog(LOG_LEVELS.INFO)) {
                const message = this.formatLogMessage(LOG_LEVELS.INFO, arguments);
                console.info(message);
            }
        },

        // 调试日志
        debug: function() {
            if (this.shouldLog(LOG_LEVELS.DEBUG)) {
                const message = this.formatLogMessage(LOG_LEVELS.DEBUG, arguments);
                console.debug(message);
            }
        },

        // 跟踪日志
        trace: function() {
            if (this.shouldLog(LOG_LEVELS.TRACE)) {
                const message = this.formatLogMessage(LOG_LEVELS.TRACE, arguments);
                console.log(message);
                if (console.trace) {
                    console.trace();
                }
            }
        },

        // 集中式错误处理
        handleError: function(error, userFriendlyMessage) {
            // 记录错误到日志
            this.error('错误:', error);
            
            // 保存错误信息到全局变量，用于调试
            window.appData = window.appData || {};
            window.appData.lastError = {
                message: error.message,
                stack: error.stack,
                timestamp: new Date().toISOString()
            };
            
            // 显示用户友好的错误消息
            const message = userFriendlyMessage || '操作失败，请稍后重试';
            
            // 如果存在UI模块的showError方法，使用它显示错误
            if (window.ui && typeof window.ui.showError === 'function') {
                window.ui.showError(message);
            } else {
                // 否则使用alert显示错误
                alert(message);
            }
            
            return message;
        }
    };

    // 导出为全局对象
    window.logger = logger;
    
    // 初始化日志级别
    logger.info('日志模块初始化完成，当前日志级别:', logger.getLogLevelName());
})();
