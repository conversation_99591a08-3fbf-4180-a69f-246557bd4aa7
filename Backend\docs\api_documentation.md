# 医院服务评价系统API文档

## 概述

本文档详细描述了医院服务评价系统的API接口，可用于前后端分离应用或第三方系统集成。
所有API返回JSON格式的数据，响应中包含状态码和结果数据。

## 最新更新 (v1.3.0)

### 二维码管理API优化

在最新版本中，我们对二维码管理相关的API进行了优化：

1. 移除了单个二维码打印API和批量打印API
2. 保留并优化了按科室批量打印API
3. 增强了二维码导出功能
4. 新增API密钥认证机制
5. 优化了评价提交API，支持多工作人员评价

## 认证机制

系统支持多种认证方式，根据API类型选择合适的认证方法：

### 1. Session认证
- **适用范围**：管理后台API
- **使用方式**：通过Django的会话系统进行认证
- **CSRF保护**：需要在请求头中携带CSRF Token
- **示例**：
  ```
  X-CSRFToken: your-csrf-token
  Cookie: sessionid=your-session-id
  ```

### 2. API密钥认证
- **适用范围**：RESTful API (v1)
- **使用方式**：通过HTTP头部提供API密钥
- **权限控制**：支持读取、写入、删除权限控制
- **IP限制**：支持IP地址白名单
- **示例**：
  ```
  X-API-Key: your-api-key
  ```
  或
  ```
  Authorization: Token your-api-key
  ```

### 3. 无认证
- **适用范围**：公开API（评价提交、二维码验证）
- **使用方式**：直接访问，无需认证
- **安全机制**：通过加密参数和设备指纹防护

## 通用响应格式

所有API响应都遵循以下格式：

```json
{
  "status": "success",  // 或 "error"
  "data": {},           // 成功时返回的数据
  "message": "",        // 消息说明
  "errors": {}          // 错误详情（仅在status为error时）
}
```

## API端点分类

系统API按照功能和认证要求分为以下几类：

### 1. 公开API（无需认证）
- 评价提交API
- 二维码验证API

### 2. 管理后台API（需要Session认证）
- 科室管理API
- 工作人员管理API
- 床位管理API
- 二维码管理API
- 评价管理API

### 3. RESTful API v1（需要API密钥认证）
- 标准CRUD操作
- 数据导出API
- 统计分析API

## 公开API接口

### 1. 二维码验证API

**接口地址**：`POST /api/v1/public/qrcode/verify/`

**功能说明**：验证二维码参数并返回相关信息

**请求参数**：
```json
{
  "qr_param": "加密的二维码参数"
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "bed": {
      "id": 1,
      "number": "101",
      "department": {
        "id": 1,
        "name": "内科"
      }
    },
    "staff": [
      {
        "id": 1,
        "name": "张医生",
        "type_id": 1,
        "staff_type": "医生"
      }
    ],
    "staff_types": [
      {
        "id": 1,
        "name": "医生",
        "code": "doctor"
      }
    ]
  },
  "message": "验证成功"
}
```

### 2. 评价提交API

**接口地址**：`POST /api/v1/public/submit-evaluation/`

**功能说明**：提交患者评价信息

**请求参数**：
```json
{
  "qr_param": "加密的二维码参数",
  "comment": "评价内容",
  "staff_evaluations": [
    {
      "staff_id": 1,
      "is_satisfied": true
    },
    {
      "staff_id": 2,
      "is_satisfied": false
    }
  ],
  "hospital_number": "住院号（可选）",
  "phone_number": "联系方式（可选）",
  "hospital_rating": 4
}
```

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "evaluation_id": 123
  },
  "message": "评价提交成功"
}
```

**限制说明**：
- 最多3个满意的工作人员评价
- 最多3个不满意的工作人员评价
- 评价内容最少5个字符
- 支持设备指纹防刷机制

## RESTful API v1接口

### 认证要求
所有v1 API都需要API密钥认证，请在请求头中包含：
```
X-API-Key: your-api-key
```
或
```
Authorization: Token your-api-key
```

### 1. 科室管理API

#### 获取科室列表
**接口地址**：`GET /api/v1/departments/`

**功能说明**：获取所有科室信息

**查询参数**：
- `page`: 页码（可选）
- `page_size`: 每页数量（可选）
- `search`: 搜索关键词（可选）

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "count": 10,
    "next": null,
    "previous": null,
    "results": [
      {
        "id": 1,
        "name": "内科",
        "description": "内科科室",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 获取科室详情
**接口地址**：`GET /api/v1/departments/{id}/`

**功能说明**：获取指定科室的详细信息

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "id": 1,
    "name": "内科",
    "description": "内科科室",
    "staff_count": 5,
    "bed_count": 20,
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 创建科室
**接口地址**：`POST /api/v1/departments/`

**权限要求**：写入权限

**请求参数**：
```json
{
  "name": "科室名称",
  "description": "科室描述"
}
```

#### 更新科室
**接口地址**：`PUT /api/v1/departments/{id}/`

**权限要求**：写入权限

#### 删除科室
**接口地址**：`DELETE /api/v1/departments/{id}/`

**权限要求**：删除权限

### 2. 工作人员管理API

#### 获取工作人员列表
**接口地址**：`GET /api/v1/staff/`

**查询参数**：
- `department`: 科室ID筛选
- `staff_type`: 工作人员类型筛选
- `search`: 搜索关键词

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "name": "张医生",
        "work_number": "WN12345678",
        "department_name": "内科",
        "staff_type": "医生",
        "phone": "13800138000",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ]
  }
}
```

#### 获取工作人员类型
**接口地址**：`GET /api/v1/staff-types/`

**功能说明**：获取所有工作人员类型

**响应示例**：
```json
{
  "status": "success",
  "data": [
    {
      "id": 1,
      "name": "医生",
      "code": "doctor"
    },
    {
      "id": 2,
      "name": "护士",
      "code": "nurse"
    }
  ]
}
```

### 3. 床位管理API

#### 获取床位列表
**接口地址**：`GET /api/v1/beds/`

**查询参数**：
- `department`: 科室ID筛选
- `page`: 页码（可选）
- `page_size`: 每页数量（可选）

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "number": "101",
        "department_name": "内科",
        "staff_name": "张医生",
        "created_at": "2024-01-01T00:00:00Z",
        "updated_at": "2024-01-01T00:00:00Z"
      }
    ],
    "count": 50,
    "next": null,
    "previous": null
  }
}
```

### 4. 二维码管理API

#### 获取二维码列表
**接口地址**：`GET /api/v1/qrcodes/`

**查询参数**：
- `department`: 科室ID筛选
- `page`: 页码（可选）
- `page_size`: 每页数量（可选）

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "name": "床位101二维码",
        "description": "内科床位101",
        "code": "QR123456",
        "bed_id": 1,
        "qr_image_url": "/media/qrcodes/QR123456.png",
        "evaluation_url": "/evaluation/QR123456/",
        "created_at": "2024-01-01T00:00:00Z",
        "is_active": true
      }
    ]
  }
}
```

### 5. 评价管理API

#### 获取评价列表
**接口地址**：`GET /api/v1/evaluations/`

**查询参数**：
- `department`: 科室ID筛选
- `start_date`: 开始日期
- `end_date`: 结束日期
- `page`: 页码（可选）

**响应示例**：
```json
{
  "status": "success",
  "data": {
    "results": [
      {
        "id": 1,
        "comment": "服务很好",
        "hospital_rating": 5,
        "created_at": "2024-01-01T00:00:00Z",
        "staff_evaluations": [
          {
            "staff_id": 1,
            "staff_name": "张医生",
            "is_satisfied": true
          }
        ]
      }
    ]
  }
}
```

## 管理后台API接口

### 认证要求
管理后台API需要Session认证，请先登录系统获取会话。

### 1. 科室管理
- `GET /admin/api/departments/` - 获取科室列表
- `GET /admin/api/departments/{id}/` - 获取科室详情
- `GET /admin/api/department/{id}/staff/` - 获取科室工作人员

### 2. 床位管理
- `GET /admin/api/beds/` - 获取床位列表

### 3. 工作人员管理
- `GET /admin/api/staff/` - 获取工作人员列表

### 4. 二维码管理
- `GET /admin/api/qrcodes/` - 获取二维码列表

### 5. 评价管理
- `GET /admin/api/evaluations/` - 获取评价列表

## 错误处理

### 错误响应格式
```json
{
  "status": "error",
  "message": "错误描述",
  "errors": {
    "field_name": ["具体错误信息"]
  }
}
```

### 常见错误码
| HTTP状态码 | 错误类型 | 描述 |
|-----------|---------|------|
| 400 | Bad Request | 请求参数错误 |
| 401 | Unauthorized | 未认证或认证失败 |
| 403 | Forbidden | 权限不足 |
| 404 | Not Found | 资源不存在 |
| 429 | Too Many Requests | 请求过于频繁 |
| 500 | Internal Server Error | 服务器内部错误 |

## 使用示例

### JavaScript示例
```javascript
// 验证二维码
async function verifyQRCode(qrParam) {
  const response = await fetch('/api/v1/public/qrcode/verify/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({
      qr_param: qrParam
    })
  });

  const data = await response.json();
  if (data.status === 'success') {
    return data.data;
  } else {
    throw new Error(data.message);
  }
}

// 提交评价
async function submitEvaluation(evaluationData) {
  const response = await fetch('/api/v1/public/submit-evaluation/', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify(evaluationData)
  });

  return await response.json();
}
```

### Python示例
```python
import requests

# 获取科室列表（需要API密钥）
def get_departments(api_key):
    headers = {
        'X-API-Key': api_key,
        'Content-Type': 'application/json'
    }

    response = requests.get(
        'http://example.com/api/v1/departments/',
        headers=headers
    )

    data = response.json()
    if data['status'] == 'success':
        return data['data']['results']
    else:
        raise Exception(data['message'])
```

## 版本历史

### v1.3.0 (2025-03-15)
- 新增API密钥认证机制
- 优化评价提交API，支持多工作人员评价
- 移除了单个二维码打印API和批量打印API
- 保留并优化了按科室批量打印API
- 增强了二维码导出功能

### v1.2.0 (2025-03-07)
- 添加了API管理接口
- 增强了API日志记录功能
- 添加了API性能监控端点

### v1.0.0 (2024-02-20)
- 初始版本发布
- 基础API功能实现

## 注意事项

1. **安全性**：
   - 生产环境中请使用HTTPS
   - 妥善保管API密钥
   - 定期更换API密钥

2. **性能**：
   - 合理使用分页参数
   - 避免频繁请求
   - 注意请求频率限制

3. **兼容性**：
   - API版本向后兼容
   - 新功能会在新版本中添加
   - 废弃功能会提前通知