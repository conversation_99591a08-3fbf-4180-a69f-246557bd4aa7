{% extends "qrmanager/base.html" %}

{% block title %}二维码管理{% endblock title %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <!-- 面包屑导航 -->
            <nav aria-label="breadcrumb" class="mb-3">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="{% url 'qrmanager:dashboard' %}" class="text-decoration-none">
                            <i class="fas fa-home"></i> 首页
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'qrmanager:department_list' %}" class="text-decoration-none">
                            <i class="fas fa-hospital"></i> 科室管理
                        </a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="{% url 'qrmanager:bed_list' %}" class="text-decoration-none">
                            <i class="fas fa-bed"></i> 床位管理
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <i class="fas fa-qrcode"></i> 二维码管理
                    </li>
                </ol>
            </nav>

            <div class="card fade-in shadow-sm">
                <div class="card-header bg-white py-3">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-0 text-gray-800">二维码管理</h1>
                            <p class="text-muted mb-0 mt-1">管理医院各科室的二维码信息</p>
                        </div>
                        <div class="d-flex gap-2">
                            <a href="{% url 'qrmanager:bed_list' %}" class="btn btn-outline-secondary">
                                <i class="fas fa-arrow-left me-2"></i>返回床位管理
                            </a>
                            {% if department %}
                                {% if department.print_template %}
                                <a href="{% url 'qrmanager:print_template_update' department.print_template.pk %}" 
                                   class="btn btn-outline-info">
                                    <i class="fas fa-edit me-2"></i>编辑打印模板
                                </a>
                                <a href="{% url 'qrmanager:print_template_preview' department.print_template.pk %}" 
                                   class="btn btn-outline-success">
                                    <i class="fas fa-print me-2"></i>打印预览
                                </a>
                                {% else %}
                                <a href="{% url 'qrmanager:print_template_create' department.id %}" 
                                   class="btn btn-outline-secondary">
                                    <i class="fas fa-plus me-2"></i>设置打印模板
                                </a>
                                {% endif %}
                            {% endif %}
                            <div class="dropdown">
                                <button class="btn btn-outline-primary dropdown-toggle" type="button" id="bulkActionDropdown" data-bs-toggle="dropdown" aria-expanded="false" disabled>
                                    <i class="fas fa-tasks me-2"></i>批量操作
                                </button>
                                <ul class="dropdown-menu" aria-labelledby="bulkActionDropdown">
                                    <li>
                                        <button class="dropdown-item text-warning bulk-action" data-action="regenerate">
                                            <i class="fas fa-sync-alt me-2"></i>重新生成二维码
                                        </button>
                                    </li>
                                    <li>
                                        <button class="dropdown-item text-danger bulk-action" data-action="delete">
                                            <i class="fas fa-trash me-2"></i>删除选中二维码
                                        </button>
                                    </li>
                                </ul>
                            </div>
                            <form method="post" class="d-inline" onsubmit="return confirm('确定要删除所有未关联床位的二维码吗？此操作不可恢复。');">
                                {% csrf_token %}
                                <input type="hidden" name="action" value="clean_unlinked">
                                <button type="submit" class="btn btn-outline-danger">
                                    <i class="fas fa-broom me-2"></i>清理未关联二维码
                                </button>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- 筛选表单 -->
                    <form method="get" class="mb-4">
                        <div class="row g-3">
                            <div class="col-md-3">
                                <select name="department" class="form-select">
                                    <option value="">选择科室</option>
                                    {% for dept in departments %}
                                    <option value="{{ dept.id }}" {% if request.GET.department == dept.id|stringformat:"s" %}selected{% endif %}>
                                        {{ dept.name }}
                                    </option>
                                    {% endfor %}
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select name="area" class="form-select">
                                    <option value="">选择区域</option>
                                    <option value="A" {% if request.GET.area == 'A' %}selected{% endif %}>A区</option>
                                    <option value="B" {% if request.GET.area == 'B' %}selected{% endif %}>B区</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <input type="text" name="search" class="form-control" placeholder="搜索床位号/科室..." value="{{ request.GET.search }}">
                            </div>
                            <div class="col-md-2">
                                <button type="submit" class="btn btn-secondary w-100">
                                    <i class="fas fa-search me-2"></i>筛选
                                </button>
                            </div>
                        </div>
                    </form>

                    <!-- 统计卡片 -->
                    <div class="row mb-4">
                        <div class="col-md-3">
                            <div class="card bg-primary text-white h-100">
                                <div class="card-body">
                                    <h5 class="card-title">总二维码数</h5>
                                    <p class="h2 mb-0">{{ total_qrcodes }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-success text-white h-100">
                                <div class="card-body">
                                    <h5 class="card-title">总评价数</h5>
                                    <p class="h2 mb-0">{{ total_evaluations }}</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-info text-white h-100">
                                <div class="card-body">
                                    <h5 class="card-title">平均评分</h5>
                                    <p class="h2 mb-0">{{ avg_rating|floatformat:1 }}★</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card bg-warning text-white h-100">
                                <div class="card-body">
                                    <h5 class="card-title">待处理评价</h5>
                                    <p class="h2 mb-0">{{ pending_evaluations }}</p>
                                </div>
                            </div>
                        </div>
                    </div>

                    {% if qrcodes %}
                    <form id="bulkActionForm" method="post">
                        {% csrf_token %}
                        <input type="hidden" name="bulk_action" id="bulkActionInput">
                        <div class="table-responsive">
                            <table class="table table-hover align-middle">
                                <thead class="table-light">
                                    <tr>
                                        <th>
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" id="selectAll">
                                                <label class="form-check-label" for="selectAll"></label>
                                            </div>
                                        </th>
                                        <th>床位信息</th>
                                        <th>科室</th>
                                        <th>区域</th>
                                        <th>二维码</th>
                                        <th>评价统计</th>
                                        <th>最近评价</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for qr in qrcodes %}
                                    <tr>
                                        <td class="text-center" style="width: 40px;">
                                            <div class="form-check">
                                                <input class="form-check-input qr-checkbox" type="checkbox" name="selected_qrs" value="{{ qr.id }}" id="qr{{ qr.id }}">
                                            </div>
                                        </td>
                                        <td style="width: 200px;">
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-primary rounded-pill me-2">{{ qr.bed.number }}</span>
                                                <small class="text-muted">{{ qr.code|truncatechars:12 }}</small>
                                            </div>
                                        </td>
                                        <td style="width: 150px;">
                                            {% if qr.bed.department %}
                                            <span class="badge bg-info">{{ qr.bed.department.name }}</span>
                                            {% else %}
                                            <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-center" style="width: 80px;">
                                            {% if qr.bed.area %}
                                            <span class="badge bg-secondary">{{ qr.bed.get_area_display }}</span>
                                            {% else %}
                                            <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td style="width: 120px;">
                                            {% if qr.bed.staff %}
                                            <small class="text-primary">
                                                <i class="fas fa-user-md me-1"></i>{{ qr.bed.staff.name }}
                                            </small>
                                            {% else %}
                                            <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td style="width: 150px;">
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-info me-2">{{ qr.evaluation_count|default:"0" }}</span>
                                                {% if qr.avg_rating %}
                                                <div class="rating-stars">
                                                    {% with ''|center:qr.avg_rating|make_list as stars %}
                                                    {% for _ in stars %}
                                                    <i class="fas fa-star text-warning"></i>
                                                    {% endfor %}
                                                    {% endwith %}
                                                </div>
                                                {% else %}
                                                <small class="text-muted">暂无评分</small>
                                                {% endif %}
                                            </div>
                                        </td>
                                        <td style="width: 200px;">
                                            {% if qr.latest_evaluation %}
                                            <div class="d-flex align-items-center">
                                                <span class="badge bg-{{ qr.latest_evaluation.sentiment }} me-2">{{ qr.latest_evaluation.get_sentiment_display }}</span>
                                                <small class="text-truncate" style="max-width: 120px;">{{ qr.latest_evaluation.comment|default:"-"|truncatechars:15 }}</small>
                                            </div>
                                            {% else %}
                                            <span class="text-muted">-</span>
                                            {% endif %}
                                        </td>
                                        <td class="text-end" style="width: 150px;">
                                            <div class="btn-group">
                                                {% if qr.bed %}
                                                <a href="{% url 'qrmanager:bed_qrcode_preview' qr.bed.pk %}" 
                                                   class="btn btn-sm btn-outline-primary" 
                                                   title="打印二维码">
                                                    <i class="fas fa-print"></i>
                                                </a>
                                                {% endif %}
                                                <a href="{% url 'qrmanager:evaluation_list' %}?qr_code={{ qr.id }}" 
                                                   class="btn btn-sm btn-outline-info" 
                                                   title="查看评价">
                                                    <i class="fas fa-star"></i>
                                                </a>
                                                {% if qr.bed %}
                                                <a href="{% url 'qrmanager:bed_update' qr.bed.pk %}" 
                                                   class="btn btn-sm btn-outline-secondary" 
                                                   title="编辑床位">
                                                    <i class="fas fa-edit"></i>
                                                </a>
                                                {% endif %}
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>

                    <!-- 分页 -->
                    {% if is_paginated %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if page_obj.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}

                            {% for num in page_obj.paginator.page_range %}
                            {% if page_obj.number == num %}
                            <li class="page-item active"><span class="page-link">{{ num }}</span></li>
                            {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ num }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a>
                            </li>
                            {% endif %}
                            {% endfor %}

                            {% if page_obj.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key,value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}

                    {% else %}
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle me-2"></i>暂无二维码数据，请先在床位管理中添加床位。
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

{% endblock content %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    const qrCheckboxes = document.querySelectorAll('.qr-checkbox');
    const bulkActionDropdown = document.getElementById('bulkActionDropdown');
    const bulkActionForm = document.getElementById('bulkActionForm');
    const bulkActionInput = document.getElementById('bulkActionInput');

    // 更新批量操作按钮状态
    function updateBulkActionButton() {
        const checkedBoxes = document.querySelectorAll('.qr-checkbox:checked');
        bulkActionDropdown.disabled = checkedBoxes.length === 0;
    }

    // 全选/取消全选
    selectAllCheckbox?.addEventListener('change', function() {
        qrCheckboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
        updateBulkActionButton();
    });

    // 单个复选框变化时更新全选状态和批量操作按钮
    qrCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(qrCheckboxes).every(cb => cb.checked);
            const anyChecked = Array.from(qrCheckboxes).some(cb => cb.checked);
            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = anyChecked && !allChecked;
            }
            updateBulkActionButton();
        });
    });

    // 批量操作按钮点击事件
    document.querySelectorAll('.bulk-action').forEach(button => {
        button.addEventListener('click', function() {
            const action = this.dataset.action;
            const selectedCount = document.querySelectorAll('.qr-checkbox:checked').length;
            
            if (selectedCount === 0) {
                alert('请至少选择一个二维码');
                return;
            }

            let confirmMessage = '';
            if (action === 'regenerate') {
                confirmMessage = `确定要重新生成选中的 ${selectedCount} 个二维码吗？`;
            } else if (action === 'delete') {
                confirmMessage = `确定要删除选中的 ${selectedCount} 个二维码吗？此操作不可恢复。`;
            }

            if (confirm(confirmMessage)) {
                bulkActionInput.value = action;
                bulkActionForm.submit();
            }
        });
    });

    // 初始化工具提示
    const tooltipTriggerList = [].slice.call(document.querySelectorAll('[title]'));
    tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });

    // 自动提交筛选表单
    const filterForm = document.querySelector('form[method="get"]');
    const selects = filterForm?.querySelectorAll('select');
    selects?.forEach(select => {
        select.addEventListener('change', () => filterForm.submit());
    });
});
</script>

<style>
/* 基础表格样式 */
.table {
    white-space: nowrap;
    table-layout: fixed;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
}

.table th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    text-transform: uppercase;
    font-size: 0.85rem;
    color: #495057;
    padding: 0.75rem 0.5rem;
}

.table td {
    vertical-align: middle;
    border-bottom: 1px solid #f2f2f2;
    padding: 0.5rem;
}

/* 徽章样式 */
.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.8em;
    font-weight: 500;
    letter-spacing: 0.3px;
}

.badge.bg-primary { background-color: #0d6efd !important; }
.badge.bg-info { background-color: #0dcaf0 !important; }
.badge.bg-warning { 
    background-color: #ffc107 !important; 
    color: #000;
}
.badge.bg-secondary { background-color: #6c757d !important; }
.bg-positive { background-color: #28a745 !important; color: white; }
.bg-neutral { background-color: #6c757d !important; color: white; }
.bg-negative { background-color: #dc3545 !important; color: white; }

/* 按钮组样式 */
.btn-group .btn {
    padding: 0.25rem 0.5rem;
}

.btn-group .btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* 评分星星样式 */
.rating-stars {
    color: #ffc107;
    font-size: 0.8rem;
}

/* 文本截断 */
.text-truncate {
    max-width: 120px;
    display: inline-block;
}

/* 复选框样式 */
.form-check-input {
    cursor: pointer;
    width: 1.2rem;
    height: 1.2rem;
    margin-top: 0.25rem;
}

/* 工具提示 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    max-width: 200px;
    padding: 0.5rem 1rem;
    background-color: rgba(0,0,0,0.9);
}

/* 容器和卡片样式 */
.container-fluid {
    padding: 1.5rem;
    max-width: 100%;
}

.card-body {
    padding: 1.5rem;
}

/* 统计卡片样式 */
.row.mb-4 .card {
    transition: all 0.3s ease;
    border: none;
}

.row.mb-4 .card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.row.mb-4 .card .card-title {
    font-size: 1rem;
    font-weight: 500;
    margin-bottom: 0.5rem;
}

.row.mb-4 .card .h2 {
    font-size: 2rem;
    font-weight: 600;
}

/* 表单元素样式 */
.form-select, .form-control {
    padding: 0.5rem 1rem;
    height: 42px;
}

.btn {
    padding: 0.5rem 1rem;
    height: 42px;
    display: inline-flex;
    align-items: center;
    justify-content: center;
}

/* 批量操作样式 */
.bulk-action {
    cursor: pointer;
}

.bulk-action:hover {
    background-color: #f8f9fa;
}

.dropdown-item.text-danger:hover {
    background-color: #dc3545;
    color: white !important;
}

.dropdown-item.text-warning:hover {
    background-color: #ffc107;
    color: black !important;
}

/* 面包屑导航样式 */
.breadcrumb {
    background-color: #fff;
    padding: 0.75rem 1rem;
    border-radius: 0.25rem;
    box-shadow: 0 2px 4px rgba(0,0,0,0.05);
    margin-bottom: 1.5rem;
}

.breadcrumb-item a {
    color: #6c757d;
    transition: color 0.2s ease-in-out;
}

.breadcrumb-item a:hover {
    color: #0d6efd;
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #495057;
    font-weight: 500;
}

.breadcrumb-item + .breadcrumb-item::before {
    content: "›";
    font-size: 1.2rem;
    line-height: 1;
    color: #6c757d;
}

/* 下拉菜单样式 */
.dropdown-menu {
    padding: 0.5rem 0;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border: none;
}

.dropdown-item {
    padding: 0.5rem 1rem;
    transition: all 0.2s ease-in-out;
}

.dropdown-item:hover {
    background-color: #f8f9fa;
}

.dropdown-item i {
    width: 1.5rem;
    text-align: center;
}

/* 响应式优化 */
@media (max-width: 1400px) {
    .table td {
        padding: 0.4rem;
    }
    
    .badge {
        font-size: 0.7rem;
        padding: 0.3em 0.6em;
    }
    
    .btn-group .btn {
        padding: 0.2rem 0.4rem;
    }
}

@media (max-width: 768px) {
    .container-fluid {
        padding: 1rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table th, .table td {
        padding: 0.75rem;
    }
}
</style>
{% endblock extra_js %} 