# 医院服务评价系统 - 完整功能清单和部署指南

## 1. 项目完整功能清单

### 1.1 前端用户功能 (患者端)

#### 1.1.1 二维码扫描评价
- **URL格式**: `https://zg120pj.cn/q/{二维码参数}/`
- **验证机制**: 安全加密参数验证
- **响应式设计**: 支持手机、平板、PC访问
- **离线检测**: 网络状态监控和提示

#### 1.1.2 工作人员选择评价
- **分类显示**: 按工作人员类型分类 (医生、护士、技师等)
- **多选限制**: 最多3名满意，3名不满意
- **实时计数**: 动态显示选择数量
- **防重复**: 同一工作人员不能重复选择

#### 1.1.3 评价提交
- **必填验证**: 至少选择一名工作人员
- **可选评论**: 文字评论功能
- **医院评分**: 1-5星整体评分
- **防重复提交**: 会话级别的重复提交防护

#### 1.1.4 成功反馈
- **提交确认**: 美观的成功页面
- **评价汇总**: 显示提交的评价内容
- **感谢信息**: 个性化感谢消息

### 1.2 后端管理功能 (管理员端)

#### 1.2.1 科室管理
- **CRUD操作**: 创建、查看、编辑、删除科室
- **科室统计**: 床位数、工作人员数、评价数统计
- **批量操作**: 批量导入导出科室信息
- **关联检查**: 删除前检查关联数据

#### 1.2.2 床位管理
- **床位信息**: 床位号、科室、区域、责任护士
- **自动二维码**: 创建床位时自动生成二维码
- **智能排序**: 自然排序算法 (1,2,10而非1,10,2)
- **批量操作**: 批量导入导出床位信息

#### 1.2.3 工作人员管理
- **完整档案**: 工号、姓名、科室、职称、照片
- **类型管理**: 医生、护士、技师等类型分类
- **批量导入**: Excel模板批量导入功能
- **照片上传**: 支持工作人员照片上传

#### 1.2.4 二维码管理
- **自动生成**: 床位创建时自动生成二维码
- **批量打印**: PDF格式批量打印二维码
- **预览功能**: 二维码预览和模板自定义
- **安全编码**: 加密参数防篡改

#### 1.2.5 评价数据管理
- **列表查看**: 分页显示所有评价数据
- **多维筛选**: 按科室、时间、满意度、情感筛选
- **详情查看**: 查看评价详细信息
- **数据导出**: CSV/Excel/PDF格式导出
- **备注功能**: 为评价添加处理备注

#### 1.2.6 统计分析功能
- **满意度统计**: 整体和分科室满意度统计
- **趋势分析**: 时间趋势图表显示
- **工作人员排名**: 表扬和批评排行榜
- **科室对比**: 科室间服务质量对比
- **情感分析**: AI情感倾向分析

#### 1.2.7 用户权限管理
- **用户创建**: 创建管理员账户
- **权限分配**: 细粒度权限控制
- **密码策略**: 复杂密码要求和定期更换
- **会话管理**: 会话超时和安全控制

#### 1.2.8 系统配置管理
- **基础配置**: 系统参数配置
- **打印模板**: 自定义二维码打印模板
- **数据字典**: 工作人员类型等字典管理
- **日志配置**: 操作日志记录配置

#### 1.2.9 安全监控
- **操作日志**: 完整的用户操作记录
- **安全事件**: 异常访问和攻击监控
- **IP管理**: IP黑白名单管理
- **速率限制**: API访问频率限制

### 1.3 API接口功能

#### 1.3.1 公开API (无需认证)
- **二维码验证**: `POST /api/v1/public/qrcode/verify/`
- **评价提交**: `POST /api/v1/public/submit-evaluation/`

#### 1.3.2 管理API (需要认证)
- **科室管理**: 完整的CRUD API
- **床位管理**: 完整的CRUD API  
- **工作人员管理**: 完整的CRUD API
- **二维码管理**: 完整的CRUD API
- **评价管理**: 查询和统计API

#### 1.3.3 内部API (管理后台)
- **数据导入导出**: 批量操作API
- **统计分析**: 数据分析API
- **文件上传**: 文件处理API
- **进度查询**: 长时间任务进度API

## 2. 技术架构详情

### 2.1 后端技术栈
- **Django 4.2+**: Web框架
- **Python 3.8+**: 编程语言
- **SQLite/MySQL**: 数据库
- **Gunicorn**: WSGI服务器
- **Nginx**: 反向代理服务器

### 2.2 前端技术栈
- **HTML5**: 页面结构
- **CSS3**: 样式设计，响应式布局
- **JavaScript ES6+**: 交互逻辑
- **原生JS**: 无框架依赖，轻量级

### 2.3 安全机制
- **HTTPS**: 全站SSL加密
- **CSRF保护**: Django内置CSRF防护
- **XSS防护**: 模板自动转义
- **SQL注入防护**: ORM自动防护
- **操作审计**: 完整的操作日志

### 2.4 性能优化
- **数据库优化**: 索引优化，查询优化
- **缓存机制**: 文件缓存，数据缓存
- **静态文件**: CDN和缓存策略
- **异步处理**: 非阻塞数据加载

## 3. 部署环境要求

### 3.1 服务器要求
- **操作系统**: Linux (Ubuntu 20.04+) 或 Windows Server
- **CPU**: 2核心以上
- **内存**: 4GB以上
- **存储**: 50GB以上SSD
- **网络**: 公网IP，域名解析

### 3.2 软件环境
- **Python**: 3.8+
- **Nginx**: 1.20+
- **SSL证书**: 有效的HTTPS证书
- **数据库**: SQLite (开发) / MySQL (生产)

### 3.3 域名和证书
- **主域名**: zg120pj.cn
- **SSL证书**: 配置在Nginx中
- **端口配置**: 
  - 443 (前端HTTPS)
  - 8000 (后端管理HTTPS)
  - 8001 (Django应用HTTP)

## 4. 部署步骤

### 4.1 环境准备
```bash
# 1. 创建项目目录
sudo mkdir -p /var/www/hospital-qr
cd /var/www/hospital-qr

# 2. 克隆项目代码
git clone <repository-url> .

# 3. 创建Python虚拟环境
python3 -m venv venv
source venv/bin/activate

# 4. 安装依赖
pip install -r requirements.txt
```

### 4.2 数据库配置
```bash
# 1. 数据库迁移
cd Backend
python manage.py makemigrations
python manage.py migrate

# 2. 创建超级用户
python manage.py createsuperuser

# 3. 收集静态文件
python manage.py collectstatic
```

### 4.3 Nginx配置
```bash
# 1. 复制Nginx配置
sudo cp nginx-1.27.5/conf/nginx.conf /etc/nginx/nginx.conf

# 2. 配置SSL证书
sudo mkdir -p /etc/nginx/ssl
sudo cp ssl/*.crt /etc/nginx/ssl/
sudo cp ssl/*.key /etc/nginx/ssl/

# 3. 测试配置
sudo nginx -t

# 4. 重启Nginx
sudo systemctl restart nginx
```

### 4.4 Systemd服务配置
```bash
# 1. 复制服务文件
sudo cp hospital-qr.service /etc/systemd/system/

# 2. 重载systemd
sudo systemctl daemon-reload

# 3. 启用并启动服务
sudo systemctl enable hospital-qr
sudo systemctl start hospital-qr

# 4. 检查状态
sudo systemctl status hospital-qr
```

### 4.5 防火墙配置
```bash
# 开放必要端口
sudo ufw allow 80
sudo ufw allow 443
sudo ufw allow 8000
sudo ufw enable
```

## 5. 运维管理

### 5.1 日志查看
```bash
# Django应用日志
tail -f /var/www/hospital-qr/Backend/logs/debug.log

# Systemd服务日志
sudo journalctl -u hospital-qr -f

# Nginx日志
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log
```

### 5.2 备份策略
```bash
# 数据库备份
python manage.py dumpdata > backup_$(date +%Y%m%d).json

# 媒体文件备份
tar -czf media_backup_$(date +%Y%m%d).tar.gz media/

# 配置文件备份
cp -r /etc/nginx/ nginx_backup_$(date +%Y%m%d)/
```

### 5.3 更新部署
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 更新依赖
pip install -r requirements.txt

# 3. 数据库迁移
python manage.py migrate

# 4. 收集静态文件
python manage.py collectstatic --noinput

# 5. 重启服务
sudo systemctl restart hospital-qr
sudo systemctl reload nginx
```

该系统已完成开发和部署，功能完整，性能稳定，安全可靠。
