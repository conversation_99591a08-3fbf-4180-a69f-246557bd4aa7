# Generated by Django 4.2.7 on 2025-02-07 07:09

import django.core.validators
from django.db import migrations, models
import django.db.models.deletion
import qrmanager.models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0011_alter_staff_staff_type_alter_staff_title_and_more'),
    ]

    operations = [
        migrations.AlterModelOptions(
            name='staff',
            options={'ordering': ['work_number'], 'verbose_name': '工作人员', 'verbose_name_plural': '工作人员'},
        ),
        migrations.AlterField(
            model_name='staff',
            name='photo',
            field=models.ImageField(blank=True, help_text='请上传300x400像素以内的照片,大小不超过2MB,仅支持JPG/PNG格式', null=True, upload_to=qrmanager.models.staff_photo_path, validators=[django.core.validators.FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']), qrmanager.models.validate_image_size], verbose_name='照片'),
        ),
        migrations.AlterField(
            model_name='staff',
            name='staff_type',
            field=models.ForeignKey(
                limit_choices_to={'dictionary__code': 'staff_type'},
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_types',
                to='qrmanager.dictionaryitem',
                verbose_name='人员类型'
            ),
        ),
        migrations.AlterField(
            model_name='staff',
            name='title',
            field=models.ForeignKey(
                limit_choices_to={'dictionary__code': 'staff_title'},
                on_delete=django.db.models.deletion.PROTECT,
                related_name='staff_titles',
                to='qrmanager.dictionaryitem',
                verbose_name='职称'
            ),
        ),
        migrations.AlterField(
            model_name='staff',
            name='work_number',
            field=models.CharField(max_length=20, unique=True, verbose_name='工号'),
        ),
    ]
