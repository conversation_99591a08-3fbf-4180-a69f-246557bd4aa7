import json
import base64
import random
import string
import hashlib

# 安全配置
SECURITY_CONFIG = {
    # 加密密钥 - 使用固定值，不再从环境变量获取
    'encryption_key': 'HospitalQRSecureKey2025',
    # 令牌有效期（秒）- 默认值，0表示永不过期
    'token_expiry': 0,  # 默认永不过期
    # 是否启用加密
    'enable_encryption': True,  # 确保加密功能已启用
    'check_timestamp': True,
    'timestamp_max_age': 300000  # 5 minutes
}

# 尝试从系统配置中读取过期时间
def get_token_expiry():
    """从系统配置中获取令牌过期时间，如果不存在则使用默认值"""
    try:
        from .models import SystemConfig
        # 尝试获取配置值，转换为整数
        expiry_str = SystemConfig.get_value('qrcode_token_expiry')
        if expiry_str:
            return int(expiry_str)
    except (ImportError, ValueError):
        pass
    # 返回默认值
    return SECURITY_CONFIG['token_expiry']

def generate_random_string(length=8):
    """
    生成指定长度的随机字符串

    参数:
        length (int): 字符串长度，默认8

    返回:
        str: 随机字符串
    """
    chars = string.ascii_letters + string.digits
    return ''.join(random.choice(chars) for _ in range(length))



def encrypt_qr_param(uuid):
    """
    加密二维码参数 - 医院级超级稳定版本

    参数:
        uuid (str|UUID): 标准UUID格式字符串或UUID对象

    返回:
        str: 加密后的参数（固定56字符长度）

    异常:
        ValueError: 输入格式无效时抛出
    """
    try:
        import hashlib

        # 🔒 处理UUID对象类型：转换为字符串
        if hasattr(uuid, '__str__'):
            uuid_str = str(uuid)
        else:
            uuid_str = uuid

        # 🔒 严格验证：只接受标准UUID格式
        if not uuid_str or not is_valid_uuid(uuid_str):
            raise ValueError(f"输入必须是标准UUID格式: {uuid_str}")

        # 🔒 标准化UUID：去除连字符，确保32字符长度
        uuid_clean = uuid_str.replace('-', '').lower()
        if len(uuid_clean) != 32:
            raise ValueError(f"UUID长度无效: {len(uuid_clean)} != 32")

        # 🔒 固定盐值生成：使用UUID的前4位
        salt = uuid_clean[:4]

        # 🔒 固定签名生成：使用MD5前6位
        signature_data = f"{uuid_clean}|{salt}|{SECURITY_CONFIG['encryption_key']}"
        signature = hashlib.md5(signature_data.encode()).hexdigest()[:6]

        # 🔒 固定格式：盐值(4位)+签名(6位)+UUID(32位) = 42字符
        compact_data = f"{salt}{signature}{uuid_clean}"

        # 🔒 严格验证数据长度
        if len(compact_data) != 42:
            raise ValueError(f"内部错误：数据长度异常 {len(compact_data)} != 42")

        # 🔒 固定编码：Base64编码
        encoded = base64.b64encode(compact_data.encode()).decode().rstrip('=')

        # 🔒 严格验证输出长度
        if len(encoded) != 56:
            raise ValueError(f"内部错误：输出长度异常 {len(encoded)} != 56")

        return encoded
    except Exception as e:
        print(f"🚨 加密失败: {e}")
        raise ValueError(f"加密失败: {e}")



def decrypt_qr_param(encrypted_param):
    """
    解密二维码参数 - 医院级超级稳定版本

    参数:
        encrypted_param (str): 加密的参数

    返回:
        dict: 解密后的数据对象

    异常:
        ValueError: 解密失败时抛出
    """
    try:
        import hashlib

        # 🔒 严格验证输入长度
        if not encrypted_param or len(encrypted_param) != 56:
            raise ValueError(f"加密参数长度无效: {len(encrypted_param) if encrypted_param else 0} != 56")

        # 🔒 添加Base64填充
        padding_needed = len(encrypted_param) % 4
        if padding_needed != 0:
            encrypted_param += '=' * (4 - padding_needed)

        # 🔒 Base64解码
        compact_data = base64.b64decode(encrypted_param).decode()

        # 🔒 严格验证解码后数据长度
        if len(compact_data) != 42:  # 4+6+32=42
            raise ValueError(f"解码数据长度无效: {len(compact_data)} != 42")

        # 🔒 解析固定格式：盐值(4位)+签名(6位)+UUID(32位)
        salt = compact_data[:4]
        signature = compact_data[4:10]
        uuid_clean = compact_data[10:42]

        # 🔒 验证签名
        signature_data = f"{uuid_clean}|{salt}|{SECURITY_CONFIG['encryption_key']}"
        expected_signature = hashlib.md5(signature_data.encode()).hexdigest()[:6]

        if signature != expected_signature:
            raise ValueError("签名验证失败")

        # 🔒 重构标准UUID格式（添加连字符）
        uuid = f"{uuid_clean[:8]}-{uuid_clean[8:12]}-{uuid_clean[12:16]}-{uuid_clean[16:20]}-{uuid_clean[20:32]}"

        return {
            'uuid': uuid,  # 标准UUID格式
            'salt': salt,
            'signature': signature,
            'format': 'ultra_stable'
        }

    except Exception as e:
        raise ValueError(f"解密失败: {str(e)}")





def is_encrypted_param(param):
    """
    检查参数是否为最新加密格式

    参数:
        param (str): 要检查的参数

    返回:
        bool: 是否为最新加密格式（56字符）
    """
    if not param or not isinstance(param, str):
        return False

    # 🔒 只支持最新格式：固定56字符长度
    if len(param) != 56:
        return False

    # 🔒 拒绝旧格式（包含点号分隔符）
    if '.' in param:
        return False

    # 🔒 检查字符集：只允许Base64字符
    import re
    return bool(re.match(r'^[A-Za-z0-9+/=]+$', param))

def secure_qr_access(qr_param):
    """
    安全访问二维码 - 只支持最新加密格式

    参数:
        qr_param (str): 二维码参数（最新56字符加密格式）

    返回:
        str: 解密后的UUID

    异常:
        ValueError: 参数无效或解密失败时抛出
    """
    import logging
    logger = logging.getLogger('security')
    logger.info(f"安全访问二维码参数: {qr_param[:20]}..." if len(qr_param) > 20 else f"安全访问二维码参数: {qr_param}")

    # 参数基本验证
    if not qr_param:
        logger.warning("二维码参数为空")
        raise ValueError("参数不能为空")

    try:
        # 移除URL可能带来的等号前缀
        if qr_param.startswith('='):
            qr_param = qr_param[1:]
            logger.info("已移除参数前缀等号")

        # 🔒 只支持最新加密格式
        if not is_encrypted_param(qr_param):
            logger.warning(f"参数不是最新加密格式: {qr_param[:20]}...")
            raise ValueError("请使用最新版本的二维码")

        # 解密参数
        logger.info("参数为最新加密格式，开始解密")
        try:
            data = decrypt_qr_param(qr_param)
            uuid = data['uuid']

            # 验证UUID格式
            if not is_valid_uuid(uuid):
                logger.warning(f"解密后的UUID格式无效: {uuid}")
                raise ValueError("无效的UUID格式")

            logger.info(f"解密成功，UUID: {uuid}")
            return uuid
        except Exception as e:
            logger.error(f"解密失败: {str(e)}")
            raise ValueError(f"无法解密参数: {str(e)}")

    except Exception as e:
        # 捕获所有异常，确保函数不会意外崩溃
        logger.error(f"处理二维码参数时发生异常: {str(e)}")
        raise ValueError(f"无法处理二维码参数: {str(e)}")

def is_valid_uuid(uuid_string):
    """
    验证字符串是否是有效的UUID格式

    参数:
        uuid_string (str): 待验证的字符串

    返回:
        bool: 是否是有效的UUID
    """
    import re

    # 检查字符串长度，最短的UUID格式应该至少有32个字符
    if not uuid_string or len(uuid_string) < 32:
        return False

    # 标准UUID格式的正则表达式
    uuid_pattern = re.compile(
        r'^[0-9a-f]{8}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{4}-?[0-9a-f]{12}$',
        re.IGNORECASE
    )

    # 如果不是标准格式，检查是否是32位十六进制字符
    hex_pattern = re.compile(r'^[0-9a-f]{32}$', re.IGNORECASE)

    return bool(uuid_pattern.match(uuid_string) or hex_pattern.match(uuid_string))