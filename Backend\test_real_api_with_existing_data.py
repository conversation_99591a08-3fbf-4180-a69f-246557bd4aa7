#!/usr/bin/env python
"""
使用数据库中现有的二维码测试真实API
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
from qrmanager.models import QRCode

def test_real_api_with_existing_qrcode():
    """使用现有二维码测试真实API"""
    print("=" * 80)
    print("🔧 使用现有二维码测试真实API")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    try:
        # 获取数据库中的第一个二维码
        qrcode_obj = QRCode.objects.first()
        
        if not qrcode_obj:
            print("❌ 数据库中没有二维码")
            return
        
        print(f"使用现有二维码:")
        print(f"  UUID: {qrcode_obj.code}")
        print(f"  床位: {qrcode_obj.bed}")
        print()
        
        # 使用新算法加密现有UUID
        encrypted_param = encrypt_qr_param(qrcode_obj.code)
        print(f"新算法加密结果: {encrypted_param}")
        print(f"加密长度: {len(encrypted_param)} 字符")
        print()
        
        # 测试正确的验证API路径
        print("1. 测试验证二维码API:")
        verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
        verify_data = {
            "qr_param": encrypted_param,
            "client_ip": "127.0.0.1"
        }
        
        print(f"   URL: {verify_url}")
        print(f"   数据: qr_param={encrypted_param[:20]}...")
        
        try:
            response = requests.post(
                verify_url,
                json=verify_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"   响应数据: {response_data}")
                print(f"   验证API: ✅ 成功")
                verify_success = True
            else:
                print(f"   错误响应: {response.text}")
                print(f"   验证API: ❌ 失败")
                verify_success = False
                
        except Exception as e:
            print(f"   验证API: ❌ 异常 - {e}")
            verify_success = False
        
        print()
        
        # 测试提交评价API（如果验证成功）
        if verify_success:
            print("2. 测试提交评价API:")
            submit_url = f"{BASE_URL}/api/v1/public/submit-evaluation/"
            submit_data = {
                "qr_param": encrypted_param,
                "comment": "使用新加密算法的测试评价",
                "staff_evaluations": [],
                "hospital_number": "TEST123",
                "phone_number": "13800138000"
            }
            
            print(f"   URL: {submit_url}")
            print(f"   数据: comment='{submit_data['comment']}'")
            
            try:
                response = requests.post(
                    submit_url,
                    json=submit_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=10
                )
                
                print(f"   响应状态: {response.status_code}")
                
                if response.status_code == 200:
                    response_data = response.json()
                    print(f"   响应数据: {response_data}")
                    print(f"   提交API: ✅ 成功")
                    submit_success = True
                else:
                    print(f"   错误响应: {response.text}")
                    print(f"   提交API: ❌ 失败")
                    submit_success = False
                    
            except Exception as e:
                print(f"   提交API: ❌ 异常 - {e}")
                submit_success = False
        else:
            print("2. 跳过提交评价API测试（验证失败）")
            submit_success = False
        
        # 总结
        print("\n" + "=" * 80)
        print("📊 真实API测试结果:")
        print("=" * 80)
        
        if verify_success and submit_success:
            print("🎉 所有API测试成功！")
            print("✅ 新加密算法与API完全兼容")
            print("✅ 前端可以正常使用新的加密参数")
        elif verify_success:
            print("⚠️  验证API成功，提交API失败")
            print("✅ 新加密算法基本兼容")
            print("❌ 提交API需要进一步调试")
        else:
            print("❌ API测试失败")
            print("❌ 需要进一步调试API兼容性问题")
        
        return verify_success and submit_success
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        return False

def test_frontend_url_paths():
    """测试前端可能使用的URL路径"""
    print("\n" + "=" * 80)
    print("🌐 测试前端URL路径")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    # 前端可能使用的路径
    frontend_paths = [
        "/service/resources/",
        "/service/evaluation/",
        "/api/service/resources/",
        "/api/service/evaluation/",
    ]
    
    print("检查前端可能使用的URL路径:")
    for path in frontend_paths:
        full_url = BASE_URL + path
        try:
            response = requests.post(
                full_url,
                json={"test": "data"},
                headers={'Content-Type': 'application/json'},
                timeout=3
            )
            print(f"   {path}: {response.status_code}")
        except Exception as e:
            print(f"   {path}: 连接失败")
    
    print("\n正确的API路径:")
    print("   ✅ /api/v1/public/qrcode/verify/")
    print("   ✅ /api/v1/public/submit-evaluation/")

if __name__ == "__main__":
    # 测试真实API
    api_success = test_real_api_with_existing_qrcode()
    
    # 测试URL路径
    test_frontend_url_paths()
    
    print("\n" + "=" * 80)
    print("🏁 最终结论")
    print("=" * 80)
    
    if api_success:
        print("🎉 新加密算法与后端API完全兼容！")
        print("✅ 主要问题：前端使用的API路径不正确")
        print("🔧 解决方案：")
        print("   1. 修改前端API配置，使用正确路径:")
        print("      - 验证: /api/v1/public/qrcode/verify/")
        print("      - 提交: /api/v1/public/submit-evaluation/")
        print("   2. 或者在后端添加URL重定向规则")
    else:
        print("❌ 仍存在API兼容性问题")
        print("🔧 需要进一步调试")
