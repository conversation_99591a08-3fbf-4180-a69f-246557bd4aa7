# Generated by Django 4.2.7 on 2025-02-22 16:48

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0019_alter_bed_department_alter_staff_department'),
    ]

    operations = [
        migrations.AddField(
            model_name='printtemplate',
            name='print_height',
            field=models.DecimalField(decimal_places=1, default=29.7, help_text='单位：厘米', max_digits=5, verbose_name='打印高度'),
        ),
        migrations.AddField(
            model_name='printtemplate',
            name='print_width',
            field=models.DecimalField(decimal_places=1, default=21.0, help_text='单位：厘米', max_digits=5, verbose_name='打印宽度'),
        ),
    ]
