#!/usr/bin/env python
"""
测试修复后的安全监控页面
"""
import os
import sys
import django
import requests
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param
from qrmanager.models import QRCode
from django.core.cache import cache

def generate_test_data():
    """生成测试数据"""
    print("🧪 生成测试数据...")
    
    # 获取测试二维码
    qrcode_obj = QRCode.objects.first()
    if not qrcode_obj:
        print("❌ 数据库中没有二维码")
        return False
    
    encrypted_param = encrypt_qr_param(qrcode_obj.code)
    
    # 发送一些API请求来生成安全记录
    base_url = "http://127.0.0.1:8000"
    verify_url = f"{base_url}/api/v1/public/qrcode/verify/"
    
    print("   发送API请求生成安全数据...")
    success_count = 0
    limited_count = 0
    
    for i in range(15):  # 发送15次请求，应该会触发速率限制
        try:
            response = requests.post(
                verify_url,
                json={"qr_param": encrypted_param, "client_ip": "127.0.0.1"},
                headers={'Content-Type': 'application/json'},
                timeout=3
            )
            
            if response.status_code == 200:
                success_count += 1
                print(f"     请求 {i+1}: ✅ 成功")
            elif response.status_code == 429:
                limited_count += 1
                print(f"     请求 {i+1}: 🚫 速率限制")
            else:
                print(f"     请求 {i+1}: ❌ 错误 ({response.status_code})")
            
        except Exception as e:
            print(f"     请求 {i+1}: ❌ 异常 - {e}")
    
    print(f"   结果: 成功 {success_count}, 限制 {limited_count}")
    return True

def test_security_monitoring_data():
    """测试安全监控数据获取"""
    print("\n🔍 测试安全监控数据获取...")
    
    try:
        from qrmanager.security_monitoring_views import SecurityMonitoringView
        
        # 创建视图实例
        view = SecurityMonitoringView()
        
        # 测试安全概览
        print("   测试安全概览...")
        overview = view.get_security_overview()
        print(f"     活跃限制: {overview['active_qrcode_limits']}")
        print(f"     安全事件: {overview['security_events_count']}")
        print(f"     系统状态: {overview['system_status']}")
        
        # 测试安全事件
        print("   测试安全事件...")
        events = view.get_recent_security_events()
        print(f"     找到 {len(events)} 个安全事件")
        for event in events[:3]:
            print(f"       {event.get('type_display', 'Unknown')}: {event.get('formatted_time', 'Unknown')}")
        
        # 测试二维码统计
        print("   测试二维码统计...")
        qrcodes = view.get_top_accessed_qrcodes()
        print(f"     找到 {len(qrcodes)} 个二维码记录")
        for qrcode in qrcodes[:3]:
            print(f"       {qrcode['bed_info']}: {qrcode['access_count']} 次访问")
        
        # 测试IP统计
        print("   测试IP统计...")
        ips = view.get_ip_statistics()
        print(f"     找到 {len(ips)} 个IP记录")
        for ip in ips[:3]:
            print(f"       {ip['ip']}: {ip['total_requests']} 次请求, 风险: {ip['risk_score']}")
        
        # 测试速率限制统计
        print("   测试速率限制统计...")
        rate_stats = view.get_rate_limit_statistics()
        print(f"     评价限制: {rate_stats['evaluation_limits']}")
        print(f"     验证限制: {rate_stats['verification_limits']}")
        print(f"     总限制: {rate_stats['total_active_limits']}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_page_access():
    """测试页面访问"""
    print("\n🌐 测试安全监控页面访问...")
    
    try:
        from django.test import Client
        from django.contrib.auth.models import User
        
        # 创建测试客户端
        client = Client()
        
        # 确保有管理员用户
        try:
            admin_user = User.objects.get(username='admin')
        except User.DoesNotExist:
            admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
            print("   创建了管理员用户")
        
        # 登录
        login_success = client.login(username='admin', password='admin123')
        if not login_success:
            print("   ❌ 登录失败")
            return False
        
        print("   ✅ 登录成功")
        
        # 访问安全监控页面
        response = client.get('/security/monitoring/')
        
        if response.status_code == 200:
            print(f"   ✅ 页面访问成功 (状态码: {response.status_code})")
            
            # 检查页面内容
            content = response.content.decode('utf-8')
            
            key_elements = [
                '安全监控中心',
                '活跃二维码限制',
                '安全事件数量',
                '最近安全事件',
                'IP访问统计'
            ]
            
            found_elements = 0
            for element in key_elements:
                if element in content:
                    found_elements += 1
                    print(f"     ✅ 找到: {element}")
                else:
                    print(f"     ❌ 缺少: {element}")
            
            print(f"   页面元素完整性: {found_elements}/{len(key_elements)}")
            return found_elements >= len(key_elements) * 0.8  # 80%以上元素存在
            
        else:
            print(f"   ❌ 页面访问失败 (状态码: {response.status_code})")
            return False
            
    except Exception as e:
        print(f"   ❌ 页面测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 测试修复后的安全监控系统")
    print("=" * 60)
    
    # 生成测试数据
    data_generated = generate_test_data()
    
    # 测试数据获取
    data_test_passed = test_security_monitoring_data()
    
    # 测试页面访问
    page_test_passed = test_page_access()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    results = [
        ("数据生成", data_generated),
        ("数据获取", data_test_passed),
        ("页面访问", page_test_passed)
    ]
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过！安全监控系统修复成功！")
        print("🔗 访问地址: http://127.0.0.1:8000/security/monitoring/")
    else:
        print("⚠️  部分测试失败，需要进一步调试")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🔒 安全监控系统已就绪！")
    else:
        print("\n🚨 需要进一步修复！")
