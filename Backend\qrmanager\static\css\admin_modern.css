/* 重置默认样式 */
#header {
    background: #ffffff !important;
    color: #333 !important;
    box-shadow: 0 2px 4px rgba(0,0,0,.04);
    height: 60px !important;
    padding: 0 24px !important;
}

#branding h1 {
    font-size: 20px;
    color: #1a73e8;
    font-weight: 500;
}

div.breadcrumbs {
    background: #f8f9fa;
    padding: 15px 24px;
    border-bottom: 1px solid #eee;
    color: #666;
    font-size: 14px;
}

/* 侧边栏样式 */
#container {
    background: #f8f9fa;
    min-height: 100vh;
}

#content {
    padding: 24px;
    background: transparent;
}

.module {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
    margin-bottom: 24px;
    overflow: hidden;
}

.module h2 {
    background: #fff;
    color: #333;
    font-size: 16px;
    font-weight: 500;
    padding: 16px 20px;
    border-bottom: 1px solid #eee;
}

/* 表格样式 */
#changelist table {
    border: none;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
}

#changelist table thead th {
    background: #f8f9fa;
    padding: 12px 16px;
    font-weight: 500;
}

#changelist table tbody td {
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
}

/* 按钮样式 */
.button, input[type=submit], input[type=button], .submit-row input {
    background: #1a73e8;
    color: #fff;
    border: none;
    border-radius: 4px;
    padding: 8px 16px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.2s;
}

.button:hover, input[type=submit]:hover, input[type=button]:hover, .submit-row input:hover {
    background: #1557b0;
}

.button.default, input[type=submit].default {
    background: #1a73e8;
}

/* 表单样式 */
.form-row {
    padding: 16px;
    border-bottom: 1px solid #eee;
}

.form-row label {
    font-size: 14px;
    color: #666;
    display: block;
    margin-bottom: 8px;
}

.form-row input, .form-row textarea, .form-row select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 14px;
    width: 100%;
    box-sizing: border-box;
}

/* 用户工具栏 */
#user-tools {
    font-size: 14px;
    position: absolute;
    right: 24px;
    top: 50%;
    transform: translateY(-50%);
}

#user-tools a {
    color: #666;
    text-decoration: none;
    margin-left: 16px;
}

#user-tools a:hover {
    color: #1a73e8;
}

/* 列表过滤器 */
#changelist-filter {
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
    margin-left: 24px;
}

#changelist-filter h2 {
    background: #f8f9fa;
    padding: 12px 16px;
    font-size: 14px;
    font-weight: 500;
    margin: 0;
    border-bottom: 1px solid #eee;
}

#changelist-filter ul {
    margin: 0;
    padding: 12px 16px;
}

#changelist-filter li {
    list-style: none;
    margin: 8px 0;
}

/* 分页样式 */
.paginator {
    font-size: 14px;
    padding: 16px;
    background: #fff;
    border-radius: 8px;
    box-shadow: 0 1px 3px rgba(0,0,0,.1);
    margin-top: 24px;
}

.paginator a {
    padding: 4px 8px;
    border-radius: 4px;
    color: #1a73e8;
    text-decoration: none;
}

.paginator a:hover {
    background: #f0f4fe;
}

/* 消息提示 */
.messagelist {
    padding: 0;
    margin: 0 0 24px 0;
}

.messagelist li {
    padding: 12px 16px;
    margin: 0;
    border-radius: 4px;
    font-size: 14px;
}

.messagelist li.success {
    background: #e6f4ea;
    color: #1e4620;
}

.messagelist li.error {
    background: #fce8e6;
    color: #c5221f;
} 