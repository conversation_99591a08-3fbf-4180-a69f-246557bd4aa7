/* 全局样式 */
:root {
    --primary-color: #4A90E2;
    --secondary-color: #F5A623;
    --text-color: #333;
    --light-bg: rgba(255, 255, 255, 0.95);
    --hover-brightness: 0.9;
}

body {
    min-height: 100vh;
    font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: var(--text-color);
    display: flex;
    flex-direction: column;
}

/* 导航栏样式 */
.navbar {
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.navbar-brand {
    font-weight: bold;
    font-size: 1.5rem;
    color: var(--primary-color) !important;
}

.nav-link {
    position: relative;
    transition: color 0.3s ease;
}

.nav-link::after {
    content: '';
    position: absolute;
    width: 0;
    height: 2px;
    bottom: 0;
    left: 0;
    background-color: var(--primary-color);
    transition: width 0.3s ease;
}

.nav-link:hover::after {
    width: 100%;
}

/* 主要内容区域 */
main {
    flex: 1;
    padding: 2rem 0;
}

.container {
    background: var(--light-bg);
    border-radius: 15px;
    padding: 2rem;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

/* 卡片样式 */
.card {
    transition: all 0.3s ease;
    border: none;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.15);
}

/* 按钮样式 */
.btn-primary {
    background-color: var(--primary-color);
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    color: white;
}

.btn-primary:hover {
    filter: brightness(var(--hover-brightness));
    transform: scale(1.05);
}

.btn-secondary {
    background-color: var(--secondary-color);
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    color: white;
}

.btn-secondary:hover {
    filter: brightness(var(--hover-brightness));
    transform: scale(1.05);
}

/* 页脚样式 */
.footer {
    background-color: var(--light-bg);
    padding: 1rem 0;
    margin-top: auto;
}

.footer a {
    color: var(--primary-color);
    text-decoration: none;
    transition: all 0.3s ease;
}

.footer a:hover {
    filter: brightness(var(--hover-brightness));
    text-decoration: underline;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 1rem;
    }
    
    .navbar-brand {
        font-size: 1.2rem;
    }

    .table-responsive {
        border-radius: 10px;
        overflow: hidden;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in {
    animation: fadeIn 0.5s ease-in;
}

/* 表格样式 */
.table {
    background-color: white;
    border-radius: 10px;
    overflow: hidden;
}

.table th {
    background-color: #f8f9fa;
    border-top: none;
}

.table td {
    vertical-align: middle;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ddd;
    padding: 0.75rem;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(74, 144, 226, 0.25);
}

/* 图片上传预览 */
.image-preview {
    max-width: 200px;
    border-radius: 8px;
    margin-top: 10px;
}

/* 评分星级样式 */
.rating-stars {
    color: #ffc107;
    font-size: 1.2rem;
}

/* 情感分析标签样式 */
.sentiment-badge {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
}

.sentiment-positive {
    background-color: #28a745;
}

.sentiment-neutral {
    background-color: #ffc107;
}

.sentiment-negative {
    background-color: #dc3545;
}

/* 卡片渐入动画 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 图表容器样式 */
.chart-container {
    width: 100%;
    height: 300px;
    margin: 1rem 0;
}

/* 数据卡片样式 */
.stat-card {
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-card .card-title {
    color: #6c757d;
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.stat-card .display-4 {
    font-size: 2.5rem;
    font-weight: bold;
    margin-bottom: 0.5rem;
}

.stat-card .text-muted {
    font-size: 0.875rem;
}

/* 筛选表单样式 */
.filter-form .form-label {
    font-size: 0.875rem;
    color: #6c757d;
}

.filter-form .form-select {
    border-radius: 0.5rem;
}

.filter-form .btn {
    border-radius: 0.5rem;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .sentiment-badge {
        width: 50px;
        height: 50px;
        font-size: 1.2rem;
    }

    .stat-card .display-4 {
        font-size: 2rem;
    }

    .chart-container {
        height: 250px;
    }
} 