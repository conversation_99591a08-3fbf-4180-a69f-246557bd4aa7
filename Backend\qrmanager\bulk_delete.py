from django.http import JsonResponse
from django.contrib.auth.decorators import login_required
from django.db.models import Count
from datetime import datetime

from .models import Evaluation
from .utils import LoggerHelper

@login_required
def evaluation_bulk_delete(request):
    """批量删除评价数据"""
    # 只允许POST请求和超级管理员
    if request.method != 'POST':
        return JsonResponse({'status': 'error', 'message': '不支持的请求方法'}, status=405)
    
    if not request.user.is_superuser:
        return JsonResponse({'status': 'error', 'message': '您没有权限执行此操作'}, status=403)
    
    # 获取请求参数
    date_from = request.POST.get('date_from')
    date_to = request.POST.get('date_to')
    time_from = request.POST.get('time_from', '00:00:00')
    time_to = request.POST.get('time_to', '23:59:59')
    password = request.POST.get('current_password')
    is_preview = request.POST.get('preview') == 'true'
    
    # 参数验证
    if not all([date_from, date_to, password]):
        return JsonResponse({'status': 'error', 'message': '请提供所有必需参数'}, status=400)
    
    # 密码验证（预览模式下也需要验证密码）
    if not request.user.check_password(password):
        return JsonResponse({
            'status': 'error', 
            'message': '密码验证失败，请确认您输入了正确的当前密码'
        }, status=403)
    
    # 构建完整的日期时间范围
    try:
        datetime_from = datetime.combine(
            datetime.strptime(date_from, '%Y-%m-%d').date(),
            datetime.strptime(time_from, '%H:%M:%S').time()
        )
        datetime_to = datetime.combine(
            datetime.strptime(date_to, '%Y-%m-%d').date(),
            datetime.strptime(time_to, '%H:%M:%S').time()
        )
    except ValueError:
        return JsonResponse({
            'status': 'error',
            'message': '日期或时间格式不正确，请使用正确的格式'
        }, status=400)
    
    # 检查日期范围是否有效
    if datetime_from > datetime_to:
        return JsonResponse({
            'status': 'error', 
            'message': '开始时间不能晚于结束时间，请重新选择'
        }, status=400)
    
    # 查询符合条件的评价
    evaluations = Evaluation.objects.filter(
        created_at__gte=datetime_from,
        created_at__lte=datetime_to
    )
    
    # 如果是预览，则只返回符合条件的评价数量
    if is_preview:
        count = evaluations.count()
        return JsonResponse({
            'status': 'success',
            'count': count,
            'message': f'找到{count}条符合条件的评价数据'
        })
    
    # 执行删除操作前，获取要删除的评价信息摘要
    count = evaluations.count()
    if count == 0:
        return JsonResponse({'status': 'success', 'count': 0, 'message': '没有找到符合条件的评价数据'})
    
    # 记录要删除的评价摘要信息
    evaluation_summary = {
        'count': count,
        'date_range': f"{date_from} {time_from} - {date_to} {time_to}",
        'departments': list(evaluations.values('bed__department__name').annotate(count=Count('id')).order_by('-count')),
        'earliest': evaluations.earliest('created_at').created_at.isoformat(),
        'latest': evaluations.latest('created_at').created_at.isoformat()
    }
    
    try:
        # 执行批量删除
        deleted_count, deleted_detail = evaluations.delete()
        
        # 记录操作日志
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="bulk_delete_evaluations",
            description=f"批量删除了{deleted_count}条评价数据",
            status="success",
            extra_data={
                'evaluation_summary': evaluation_summary,
                'deleted_detail': deleted_detail
            }
        )
        
        return JsonResponse({
            'status': 'success',
            'count': deleted_count,
            'message': f'成功删除了{deleted_count}条评价数据'
        })
        
    except Exception as e:
        # 记录错误
        LoggerHelper.log_operation(
            user=request.user,
            request=request,
            action="bulk_delete_evaluations_failed",
            description=f"批量删除评价数据失败: {str(e)}",
            status="error",
            extra_data={
                'error': str(e),
                'evaluation_summary': evaluation_summary
            }
        )
        
        return JsonResponse({
            'status': 'error',
            'message': f'删除操作失败: {str(e)}'
        }, status=500) 