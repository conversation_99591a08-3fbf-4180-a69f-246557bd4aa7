@echo off
echo 🚀 前端版本更新工具
echo ================================================

echo.
echo 📋 功能说明：
echo   - 自动更新HTML和JS文件的版本号
echo   - 解决手机扫码缓存问题
echo   - 确保用户看到最新修改

echo.
echo 🔍 检查Python环境...

python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Python未安装或未添加到PATH
    echo 请安装Python或手动更新版本号
    pause
    exit /b 1
)

echo ✅ Python环境正常

echo.
echo 🔧 开始更新版本号...

cd /d "%~dp0"
python update_version.py

if %errorlevel% equ 0 (
    echo.
    echo 🎉 版本更新成功！
    echo.
    echo 📱 用户缓存清理方法：
    echo   1. Android Chrome: 设置 → 隐私设置 → 清除浏览数据
    echo   2. iPhone Safari: 设置 → Safari → 清除历史记录与网站数据
    echo   3. 微信浏览器: 退出微信重新扫码
    echo.
    echo 🔄 强制刷新方法：
    echo   - 电脑: Ctrl+F5 或 Ctrl+Shift+R
    echo   - 手机: 长按刷新按钮选择"硬性重新加载"
    echo.
    echo 💡 建议：
    echo   每次修改前端代码后运行此工具
    echo   确保用户能看到最新版本
    
) else (
    echo ❌ 版本更新失败
    echo 请检查错误信息并手动处理
)

echo.
echo 按任意键退出...
pause >nul
