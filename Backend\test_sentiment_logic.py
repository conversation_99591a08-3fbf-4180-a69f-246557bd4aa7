#!/usr/bin/env python3
"""
测试基于医院评分的情感倾向逻辑
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.http import HttpRequest
from django.contrib.auth.models import AnonymousUser
from qrmanager.api import PublicEvaluationSubmitAPI
from qrmanager.models import Evaluation, QRCode, Bed
from qrmanager.security import encrypt_qr_param

def create_mock_request(data):
    """创建模拟请求"""
    request = HttpRequest()
    request.method = 'POST'
    request.META['CONTENT_TYPE'] = 'application/json'
    request._body = json.dumps(data).encode('utf-8')
    request.user = AnonymousUser()
    return request

def test_sentiment_by_hospital_rating():
    """测试基于医院评分的情感倾向"""
    print("🔍 测试基于医院评分的情感倾向")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试二维码")
            return False
        
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试不同评分对应的情感倾向
        test_cases = [
            (1, 'negative', '1星 → 负面'),
            (2, 'negative', '2星 → 负面'),
            (3, 'neutral', '3星 → 中性'),
            (4, 'positive', '4星 → 正面'),
            (5, 'positive', '5星 → 正面'),
        ]
        
        results = []
        
        for rating, expected_sentiment, description in test_cases:
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"测试{rating}星评分的情感倾向",
                "hospital_rating": rating,
                "hospital_number": f"TEST_{rating}STAR",
                "phone_number": "13800138000",
                "staff_evaluations": []
            }
            
            request = create_mock_request(test_data)
            api_view = PublicEvaluationSubmitAPI()
            response = api_view.post(request)
            
            if response.status_code == 200:
                response_data = json.loads(response.content.decode())
                evaluation_id = response_data.get('data', {}).get('evaluation_id')
                
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    actual_sentiment = evaluation.sentiment
                    
                    if actual_sentiment == expected_sentiment:
                        print(f"✅ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                        results.append(True)
                    else:
                        print(f"❌ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                        results.append(False)
                else:
                    print(f"❌ {description}: 没有返回evaluation_id")
                    results.append(False)
            else:
                print(f"❌ {description}: API请求失败 {response.status_code}")
                results.append(False)
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_sentiment_without_rating():
    """测试没有医院评分时的情感倾向"""
    print(f"\n🔍 测试没有医院评分时的情感倾向")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试没有医院评分，只有工作人员评价的情况
        test_cases = [
            (True, 'positive', '工作人员满意 → 正面'),
            (False, 'negative', '工作人员不满意 → 负面'),
        ]
        
        results = []
        
        for is_satisfied, expected_sentiment, description in test_cases:
            # 获取一个工作人员ID
            bed = qr_code.bed
            if bed and bed.department:
                from qrmanager.models import Staff
                staff = Staff.objects.filter(department=bed.department, is_active=True).first()
                if not staff:
                    print("⚠️  没有找到测试用的工作人员，跳过工作人员评价测试")
                    continue
                
                test_data = {
                    "qr_param": encrypted_param,
                    "comment": f"测试工作人员{'满意' if is_satisfied else '不满意'}的情感倾向",
                    # 注意：没有hospital_rating字段
                    "hospital_number": f"TEST_STAFF_{is_satisfied}",
                    "phone_number": "13800138000",
                    "staff_evaluations": [{
                        "staff_id": staff.id,
                        "is_satisfied": is_satisfied
                    }]
                }
                
                request = create_mock_request(test_data)
                api_view = PublicEvaluationSubmitAPI()
                response = api_view.post(request)
                
                if response.status_code == 200:
                    response_data = json.loads(response.content.decode())
                    evaluation_id = response_data.get('data', {}).get('evaluation_id')
                    
                    if evaluation_id:
                        evaluation = Evaluation.objects.get(id=evaluation_id)
                        actual_sentiment = evaluation.sentiment
                        
                        if actual_sentiment == expected_sentiment:
                            print(f"✅ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                            results.append(True)
                        else:
                            print(f"❌ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                            results.append(False)
                    else:
                        print(f"❌ {description}: 没有返回evaluation_id")
                        results.append(False)
                else:
                    print(f"❌ {description}: API请求失败 {response.status_code}")
                    results.append(False)
        
        return all(results) if results else True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_sentiment_no_rating_no_staff():
    """测试既没有医院评分也没有工作人员评价的情况"""
    print(f"\n🔍 测试既没有评分也没有工作人员评价的情况")
    print("=" * 60)
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        test_data = {
            "qr_param": encrypted_param,
            "comment": "测试既没有医院评分也没有工作人员评价",
            # 注意：没有hospital_rating字段
            "hospital_number": "TEST_NEUTRAL",
            "phone_number": "13800138000",
            "staff_evaluations": []  # 空的工作人员评价
        }
        
        request = create_mock_request(test_data)
        api_view = PublicEvaluationSubmitAPI()
        response = api_view.post(request)
        
        if response.status_code == 200:
            response_data = json.loads(response.content.decode())
            evaluation_id = response_data.get('data', {}).get('evaluation_id')
            
            if evaluation_id:
                evaluation = Evaluation.objects.get(id=evaluation_id)
                actual_sentiment = evaluation.sentiment
                
                # 根据新逻辑，没有评分和工作人员评价时应该是neutral（中性）
                expected_sentiment = 'neutral'
                
                if actual_sentiment == expected_sentiment:
                    print(f"✅ 无评分无工作人员评价: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                    return True
                else:
                    print(f"❌ 无评分无工作人员评价: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                    return False
            else:
                print(f"❌ 没有返回evaluation_id")
                return False
        else:
            print(f"❌ API请求失败 {response.status_code}")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_model_save_sentiment():
    """测试模型保存时的情感倾向逻辑"""
    print(f"\n🔍 测试模型保存时的情感倾向逻辑")
    print("=" * 60)
    
    try:
        bed = Bed.objects.first()
        if not bed:
            print("❌ 没有找到测试床位")
            return False
        
        # 测试不同医院评分的模型保存
        test_cases = [
            (1, 'negative', '模型保存: 1星 → 负面'),
            (2, 'negative', '模型保存: 2星 → 负面'),
            (3, 'neutral', '模型保存: 3星 → 中性'),
            (4, 'positive', '模型保存: 4星 → 正面'),
            (5, 'positive', '模型保存: 5星 → 正面'),
        ]
        
        results = []
        
        for rating, expected_sentiment, description in test_cases:
            evaluation = Evaluation.objects.create(
                bed=bed,
                is_satisfied=True,
                comment=f"模型测试{rating}星评分",
                hospital_rating=rating,
                hospital_number=f"MODEL_TEST_{rating}",
                phone_number="13900139000"
            )
            
            # 重新从数据库获取，确保save方法被调用
            evaluation.refresh_from_db()
            actual_sentiment = evaluation.sentiment
            
            if actual_sentiment == expected_sentiment:
                print(f"✅ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                results.append(True)
            else:
                print(f"❌ {description}: 期望 {expected_sentiment}, 实际 {actual_sentiment}")
                results.append(False)
            
            # 清理测试数据
            evaluation.delete()
        
        return all(results)
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def analyze_existing_evaluations():
    """分析现有评价的情感倾向分布"""
    print(f"\n📊 分析现有评价的情感倾向分布")
    print("=" * 60)
    
    try:
        # 统计现有评价
        total = Evaluation.objects.count()
        with_rating = Evaluation.objects.filter(hospital_rating__isnull=False).count()
        without_rating = Evaluation.objects.filter(hospital_rating__isnull=True).count()
        
        print(f"评价总数: {total}")
        print(f"有医院评分: {with_rating}")
        print(f"无医院评分: {without_rating}")
        
        # 情感倾向分布
        sentiment_stats = {}
        for sentiment in ['positive', 'negative', 'neutral']:
            count = Evaluation.objects.filter(sentiment=sentiment).count()
            percentage = (count / total * 100) if total > 0 else 0
            sentiment_stats[sentiment] = {'count': count, 'percentage': percentage}
        
        print(f"\n情感倾向分布:")
        print(f"  正面: {sentiment_stats['positive']['count']}条 ({sentiment_stats['positive']['percentage']:.1f}%)")
        print(f"  负面: {sentiment_stats['negative']['count']}条 ({sentiment_stats['negative']['percentage']:.1f}%)")
        print(f"  中性: {sentiment_stats['neutral']['count']}条 ({sentiment_stats['neutral']['percentage']:.1f}%)")
        
        # 医院评分与情感倾向的对应关系
        print(f"\n医院评分与情感倾向对应关系:")
        for rating in range(1, 6):
            evaluations = Evaluation.objects.filter(hospital_rating=rating)
            if evaluations.exists():
                sentiment_dist = {}
                for sentiment in ['positive', 'negative', 'neutral']:
                    count = evaluations.filter(sentiment=sentiment).count()
                    sentiment_dist[sentiment] = count
                
                print(f"  {rating}星: 正面{sentiment_dist['positive']}条, 负面{sentiment_dist['negative']}条, 中性{sentiment_dist['neutral']}条")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 医院评分情感倾向逻辑测试")
    print("=" * 80)
    
    tests = [
        ("医院评分情感倾向", test_sentiment_by_hospital_rating),
        ("无评分工作人员评价", test_sentiment_without_rating),
        ("无评分无工作人员评价", test_sentiment_no_rating_no_staff),
        ("模型保存情感倾向", test_model_save_sentiment),
        ("现有评价分析", analyze_existing_evaluations),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}执行异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"\n📋 测试结果总结")
    print("=" * 80)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 所有测试通过！情感倾向逻辑正确实现！")
        print("\n✅ 功能确认:")
        print("  - 1-2星医院评分 → 负面情感")
        print("  - 3星医院评分 → 中性情感")
        print("  - 4-5星医院评分 → 正面情感")
        print("  - 医院评分优先于工作人员评价")
        print("  - 无评分时根据工作人员评价确定")
        print("  - 管理界面正确显示评分来源")
    else:
        print("⚠️  部分功能存在问题，需要修复")

if __name__ == "__main__":
    main()
