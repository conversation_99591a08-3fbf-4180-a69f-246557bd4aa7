import os
import django
import sys

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.models import StaffType, Staff, DictionaryItem
from django.db import connection

def create_staff_types():
    """创建工作人员类型记录"""
    print("开始创建工作人员类型记录...")
    
    # 获取现有的字典项
    try:
        staff_type_items = DictionaryItem.objects.filter(dictionary__code='staff_type')
        print(f"找到 {staff_type_items.count()} 个工作人员类型字典项")
        
        # 创建工作人员类型记录
        for item in staff_type_items:
            staff_type, created = StaffType.objects.get_or_create(
                code=item.code,
                defaults={
                    'name': item.name,
                    'display_order': item.sort_order,
                    'is_active': item.is_active
                }
            )
            if created:
                print(f"创建工作人员类型: {staff_type.name}")
            else:
                print(f"工作人员类型已存在: {staff_type.name}")
        
        # 如果没有找到任何字典项，创建默认类型
        if staff_type_items.count() == 0:
            default_types = [
                {'code': 'doctor', 'name': '医生', 'display_order': 1},
                {'code': 'nurse', 'name': '护士', 'display_order': 2},
                {'code': 'technician', 'name': '技师', 'display_order': 3},
                {'code': 'other', 'name': '其他', 'display_order': 99}
            ]
            
            for type_data in default_types:
                staff_type, created = StaffType.objects.get_or_create(
                    code=type_data['code'],
                    defaults={
                        'name': type_data['name'],
                        'display_order': type_data['display_order'],
                        'is_active': True
                    }
                )
                if created:
                    print(f"创建默认工作人员类型: {staff_type.name}")
        
        print("工作人员类型记录创建完成")
        return True
    except Exception as e:
        print(f"创建工作人员类型记录时出错: {str(e)}")
        return False

def update_staff_references():
    """更新工作人员记录的外键引用"""
    print("开始更新工作人员记录的外键引用...")
    
    try:
        # 获取所有工作人员记录
        staff_records = Staff.objects.all()
        print(f"找到 {staff_records.count()} 个工作人员记录")
        
        # 获取默认工作人员类型
        default_type = StaffType.objects.first()
        if not default_type:
            print("未找到默认工作人员类型，无法继续")
            return False
        
        # 更新工作人员记录
        for staff in staff_records:
            # 获取原始类型ID
            original_type_id = staff.staff_type_id
            
            # 尝试查找对应的StaffType
            try:
                # 先尝试通过原始字典项查找
                if isinstance(original_type_id, int):
                    try:
                        dict_item = DictionaryItem.objects.get(id=original_type_id)
                        staff_type = StaffType.objects.filter(code=dict_item.code).first()
                    except DictionaryItem.DoesNotExist:
                        staff_type = None
                else:
                    staff_type = None
                
                # 如果找不到，使用默认类型
                if not staff_type:
                    staff_type = default_type
                
                # 更新工作人员记录
                staff.staff_type = staff_type
                staff.save()
                print(f"更新工作人员 {staff.name} 的类型为 {staff_type.name}")
            except Exception as e:
                print(f"更新工作人员 {staff.name} 时出错: {str(e)}")
        
        print("工作人员记录更新完成")
        return True
    except Exception as e:
        print(f"更新工作人员记录时出错: {str(e)}")
        return False

def fix_database_directly():
    """直接修复数据库"""
    print("尝试直接修复数据库...")
    
    try:
        # 获取默认工作人员类型
        default_type = StaffType.objects.first()
        if not default_type:
            print("未找到默认工作人员类型，无法继续")
            return False
        
        # 直接执行SQL更新语句
        with connection.cursor() as cursor:
            cursor.execute(
                "UPDATE qrmanager_staff SET staff_type_id = %s",
                [default_type.id]
            )
            print(f"已将所有工作人员的类型更新为 {default_type.name}")
        
        print("数据库直接修复完成")
        return True
    except Exception as e:
        print(f"直接修复数据库时出错: {str(e)}")
        return False

if __name__ == "__main__":
    print("开始修复工作人员类型数据...")
    
    # 创建工作人员类型记录
    if not create_staff_types():
        print("创建工作人员类型记录失败，退出")
        sys.exit(1)
    
    # 尝试更新工作人员记录
    if not update_staff_references():
        print("更新工作人员记录失败，尝试直接修复数据库")
        
        # 尝试直接修复数据库
        if not fix_database_directly():
            print("直接修复数据库失败，退出")
            sys.exit(1)
    
    print("修复完成，现在可以运行迁移了") 