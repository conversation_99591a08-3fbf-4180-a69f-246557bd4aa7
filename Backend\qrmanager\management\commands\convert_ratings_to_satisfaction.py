from django.core.management.base import BaseCommand
from qrmanager.models import Evaluation
from django.utils import timezone
from django.db.models import Q
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = '将现有的5级评分转换为二元满意度（满意/不满意）'

    def add_arguments(self, parser):
        parser.add_argument(
            '--threshold', 
            type=int, 
            default=4, 
            help='满意度阈值，大于等于此值视为满意，默认为4（4和5视为满意，1-3视为不满意）'
        )
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='仅显示将进行的更改，不实际执行',
        )

    def handle(self, *args, **options):
        threshold = options['threshold']
        dry_run = options['dry_run']
        
        self.stdout.write(f"使用阈值 {threshold} (>= {threshold} 视为满意)")
        
        # 查找所有有rating字段的评价
        try:
            # 检查rating字段是否存在
            evaluations = Evaluation.objects.all()
            
            # 尝试访问第一条记录的rating字段
            if evaluations.exists():
                first_eval = evaluations.first()
                if not hasattr(first_eval, 'rating'):
                    self.stdout.write(self.style.WARNING("评价模型已经没有rating字段，可能已经完成迁移"))
                    return
            
            count = evaluations.count()
            
            if count == 0:
                self.stdout.write(self.style.SUCCESS("没有评价记录需要转换"))
                return
                
            # 统计转换情况
            satisfied = 0
            unsatisfied = 0
            
            # 记录开始时间
            start_time = timezone.now()
            
            for eval in evaluations:
                # 根据评分确定满意度
                is_satisfied = eval.rating >= threshold
                
                if is_satisfied:
                    satisfied += 1
                else:
                    unsatisfied = unsatisfied + 1
                    
                if not dry_run:
                    # 设置满意度
                    eval.is_satisfied = is_satisfied
                    
                    # 确保工作人员关联
                    if not eval.staff:
                        # 尝试从床位获取工作人员
                        if eval.bed and eval.bed.staff:
                            eval.staff = eval.bed.staff
                        else:
                            # 如果没有关联的工作人员，记录警告
                            self.stdout.write(self.style.WARNING(f"评价ID {eval.id} 没有关联的工作人员，无法转换"))
                            continue
                    
                    # 保存更改
                    eval.save(update_fields=['is_satisfied'])
            
            # 记录结束时间
            end_time = timezone.now()
            duration = (end_time - start_time).total_seconds()
            
            # 输出汇总信息
            self.stdout.write("\n转换汇总:")
            self.stdout.write(f"  总计评价: {count}")
            self.stdout.write(f"  满意: {satisfied} ({satisfied/count*100:.2f}%)")
            self.stdout.write(f"  不满意: {unsatisfied} ({unsatisfied/count*100:.2f}%)")
            self.stdout.write(f"  耗时: {duration:.2f} 秒")
            
            if dry_run:
                self.stdout.write(self.style.WARNING("\n这是演习模式，未实际执行转换。如需执行转换，请移除 --dry-run 参数"))
            else:
                self.stdout.write(self.style.SUCCESS("\n转换完成!"))
                
            # 记录日志
            logger.info(f"评价转换完成: 总计 {count} 条, 满意 {satisfied} 条, 不满意 {unsatisfied} 条, 耗时 {duration:.2f} 秒")
            
        except Exception as e:
            self.stdout.write(self.style.ERROR(f"转换过程中发生错误: {str(e)}"))
            logger.error(f"评价转换失败: {str(e)}", exc_info=True) 