/**
 * 人员模块 - 处理工作人员显示和选择
 * 2025-03-24 重构版
 */

// 全局变量
window.staffList = [];
window.staffTypes = [];
window.selectedStaff = [];
window.MAX_SATISFIED = 3;
window.MAX_UNSATISFIED = 3;

// DOM元素引用
window.elements = {
    staffContainer: null,
    staffTypeSelect: null,
    noStaffMessage: null,
    satisfiedCounter: null,
    unsatisfiedCounter: null,
    limitMessage: null,
    selectedStaffDisplay: null
};

// 刷新DOM元素引用
function refreshElementRefs() {
    window.elements = {
        staffContainer: document.getElementById('staffListContainer'),
        staffTypeSelect: document.getElementById('staffTypeSelect'),
        noStaffMessage: document.getElementById('noStaffMessage'),
        satisfiedCounter: document.getElementById('satisfiedCounter'),
        unsatisfiedCounter: document.getElementById('unsatisfiedCounter'),
        limitMessage: document.getElementById('evaluationLimitMessage'),
        selectedStaffDisplay: document.getElementById('selectedStaffDisplay')
    };
    return window.elements.staffTypeSelect !== null && window.elements.staffContainer !== null;
}

// 根据员工类型ID获取类型名称
function getStaffTypeName(staff) {
    // 直接返回类型名称（如果有）
    if (staff.type) {
        return staff.type;
    }

    // 否则尝试通过类型ID获取名称
    const typeId = staff.type_id || staff.staff_type;
    if (typeId && window.staffTypes) {
        const staffType = window.staffTypes.find(type => String(type.id) === String(typeId));
        return staffType ? staffType.name : '未知类型';
    }

    return '未知类型';
}

// 更新计数器
function updateCounters() {
    refreshElementRefs();

    // 使用评价管理器获取数据
    const satisfiedCount = window.evaluationManager.getSatisfiedEvaluations().length;
    const unsatisfiedCount = window.evaluationManager.getUnsatisfiedEvaluations().length;

    if (window.elements.satisfiedCounter) {
        window.elements.satisfiedCounter.textContent = `${satisfiedCount}/${window.evaluationManager.MAX_SATISFIED}`;
    }

    if (window.elements.unsatisfiedCounter) {
        window.elements.unsatisfiedCounter.textContent = `${unsatisfiedCount}/${window.evaluationManager.MAX_UNSATISFIED}`;
    }

    if (window.elements.limitMessage) {
        if (satisfiedCount >= window.evaluationManager.MAX_SATISFIED || unsatisfiedCount >= window.evaluationManager.MAX_UNSATISFIED) {
            window.elements.limitMessage.classList.remove('hidden');
        } else {
            window.elements.limitMessage.classList.add('hidden');
        }
    }
}

// 评价管理类
class EvaluationManager {
    constructor() {
        this.satisfiedStaff = new Set();
        this.unsatisfiedStaff = new Set();
        this.MAX_SATISFIED = 3;
        this.MAX_UNSATISFIED = 3;

        // 从localStorage恢复数据
        this.loadFromStorage();
    }

    // 从localStorage加载数据
    loadFromStorage() {
        try {
            const savedStaff = localStorage.getItem('selectedStaff');
            if (savedStaff) {
                const staffList = JSON.parse(savedStaff);
                staffList.forEach(staff => {
                    if (staff.rating === 'satisfied') {
                        this.satisfiedStaff.add(staff);
                    } else if (staff.rating === 'unsatisfied') {
                        this.unsatisfiedStaff.add(staff);
                    }
                });
            }
        } catch (error) {
            this.satisfiedStaff.clear();
            this.unsatisfiedStaff.clear();
        }
    }

    // 保存到localStorage
    saveToStorage() {
        try {
            const allStaff = [...this.satisfiedStaff, ...this.unsatisfiedStaff];
            localStorage.setItem('selectedStaff', JSON.stringify(allStaff));
        } catch (error) {
        }
    }

    // 获取所有评价
    getAllEvaluations() {
        return [...this.satisfiedStaff, ...this.unsatisfiedStaff];
    }

    // 获取满意评价
    getSatisfiedEvaluations() {
        return [...this.satisfiedStaff];
    }

    // 获取不满意评价
    getUnsatisfiedEvaluations() {
        return [...this.unsatisfiedStaff];
    }

    // 检查是否可以添加评价
    canAddEvaluation(rating) {
        if (rating === 'satisfied') {
            return this.satisfiedStaff.size < this.MAX_SATISFIED;
        } else if (rating === 'unsatisfied') {
            return this.unsatisfiedStaff.size < this.MAX_UNSATISFIED;
        }
        return false;
    }

    // 获取评价状态
    getEvaluationStatus(staffId) {
        const satisfiedItem = [...this.satisfiedStaff].find(s => String(s.id) === String(staffId));
        const unsatisfiedItem = [...this.unsatisfiedStaff].find(s => String(s.id) === String(staffId));

        if (satisfiedItem) return 'satisfied';
        if (unsatisfiedItem) return 'unsatisfied';
        return null;
    }

    // 切换评价状态
    toggleEvaluation(staff, rating) {
        const staffId = String(staff.id);
        const currentStatus = this.getEvaluationStatus(staffId);

        // 如果已有相同评价，则移除
        if (currentStatus === rating) {
            if (rating === 'satisfied') {
                const itemToRemove = [...this.satisfiedStaff].find(s => String(s.id) === staffId);
                if (itemToRemove) {
                    this.satisfiedStaff.delete(itemToRemove);
                }
            } else {
                const itemToRemove = [...this.unsatisfiedStaff].find(s => String(s.id) === staffId);
                if (itemToRemove) {
                    this.unsatisfiedStaff.delete(itemToRemove);
                }
            }
            this.saveToStorage();
            return true;
        }

        // 如果已有不同评价，先移除旧评价
        if (currentStatus) {
            if (currentStatus === 'satisfied') {
                const itemToRemove = [...this.satisfiedStaff].find(s => String(s.id) === staffId);
                if (itemToRemove) {
                    this.satisfiedStaff.delete(itemToRemove);
                }
            } else {
                const itemToRemove = [...this.unsatisfiedStaff].find(s => String(s.id) === staffId);
                if (itemToRemove) {
                    this.unsatisfiedStaff.delete(itemToRemove);
                }
            }
        }

        // 检查是否可以添加新评价
        if (!this.canAddEvaluation(rating)) {
            return false;
        }

        // 添加新评价
        const staffType = getStaffTypeName(staff);
        const typeId = staff.type_id || staff.staff_type;

        const evaluationData = {
            id: staffId,
            name: staff.name,
            type: staffType,
            type_id: typeId,
            rating: rating,
            timestamp: new Date().toISOString()
        };

        // 记录详细的评价数据用于调试
        console.log(`添加${rating}评价:`, {
            id: staffId,
            name: staff.name,
            type: staffType,
            type_id: typeId
        });

        // 添加到对应集合
        if (rating === 'satisfied') {
            this.satisfiedStaff.add(evaluationData);
        } else {
            this.unsatisfiedStaff.add(evaluationData);
        }

        // 保存到本地存储
        this.saveToStorage();
        return true;
    }

    // 移除评价
    removeEvaluation(staffId) {
        const currentStatus = this.getEvaluationStatus(staffId);
        if (!currentStatus) return false;

        if (currentStatus === 'satisfied') {
            const itemToRemove = [...this.satisfiedStaff].find(s => String(s.id) === String(staffId));
            if (itemToRemove) {
                this.satisfiedStaff.delete(itemToRemove);
            }
        } else {
            const itemToRemove = [...this.unsatisfiedStaff].find(s => String(s.id) === String(staffId));
            if (itemToRemove) {
                this.unsatisfiedStaff.delete(itemToRemove);
            }
        }

        this.saveToStorage();
        return true;
    }

    // 清空所有评价
    clearAll() {
        this.satisfiedStaff.clear();
        this.unsatisfiedStaff.clear();
        localStorage.removeItem('selectedStaff');
    }

    // 验证评价数据
    validateEvaluations() {
        const errors = [];

        if (this.satisfiedStaff.size > this.MAX_SATISFIED) {
            errors.push(`满意评价超出限制，最多选择${this.MAX_SATISFIED}个`);
        }

        if (this.unsatisfiedStaff.size > this.MAX_UNSATISFIED) {
            errors.push(`不满意评价超出限制，最多选择${this.MAX_UNSATISFIED}个`);
        }

        return {
            isValid: errors.length === 0,
            errors: errors
        };
    }

    // 获取提交数据
    getSubmitData() {
        const staffEvaluations = [];

        // 添加满意评价
        this.satisfiedStaff.forEach(staff => {
            staffEvaluations.push({
                staff_id: parseInt(staff.id),
                is_satisfied: true
            });
        });

        // 添加不满意评价
        this.unsatisfiedStaff.forEach(staff => {
            staffEvaluations.push({
                staff_id: parseInt(staff.id),
                is_satisfied: false
            });
        });

        return staffEvaluations;
    }
}

// 创建评价管理器实例
window.evaluationManager = new EvaluationManager();

// 工作人员评价模块
window.staffModule = {
    // 初始化状态标志
    isInitialized: false,
    isInitializing: false,

    // 初始化方法
    init: function(staff, types) {
        if (this.isInitialized) {
            return;
        }

        if (this.isInitializing) {
            return;
        }

        this.isInitializing = true;

        // 记录初始化时的appData状态
        console.log('staffModule初始化时appData状态:', window.appData);
        if (window.appData && window.appData.qrParam) {
            console.log('二维码参数:', window.appData.qrParam);
        }

        // 刷新DOM元素引用
        if (!refreshElementRefs()) {
            console.warn('DOM元素引用刷新失败，延迟初始化');
            setTimeout(() => {
                if (refreshElementRefs()) {
                    console.log('DOM元素引用刷新成功，继续初始化');
                    this.init(staff, types);
                } else {
                    console.error('DOM元素引用刷新失败，无法初始化staffModule');
                }
            }, 500);
            this.isInitializing = false;
            return;
        }

        // 清除上一次的本地存储数据
        window.evaluationManager.clearAll();

        // 设置人员列表和类型
        try {
            // 优先使用传入的参数
            if (Array.isArray(staff) && staff.length > 0) {
                window.staffList = staff.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
            }
            // 其次使用window.appData
            else if (window.appData && Array.isArray(window.appData.staffList)) {
                window.staffList = window.appData.staffList.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
            }
            else {
                window.staffList = [];
                console.warn('初始化时staffList不存在或为空');
            }

            // 类型数据处理
            if (Array.isArray(types) && types.length > 0) {
                window.staffTypes = types.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
                console.log('使用传入的types初始化staffTypes:', types.length);
            }
            else if (window.appData && Array.isArray(window.appData.staffTypes)) {
                window.staffTypes = window.appData.staffTypes.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
                console.log('使用appData.staffTypes初始化staffTypes:', window.appData.staffTypes.length);
            }
            else {
                window.staffTypes = [];
                console.warn('初始化时staffTypes不存在或为空');
            }
        } catch (error) {
            console.error('初始化staffList和staffTypes时出错:', error);
            window.staffList = [];
            window.staffTypes = [];
        }

        // 移除旧的事件监听器
        this.removeEventListeners();

        // 填充工作人员类型下拉框
        this.populateStaffTypes();

        // 注册事件监听器
        this.registerEventListeners();

        // 显示选择提示
        if (window.elements.staffContainer) {
            window.elements.staffContainer.innerHTML = `
                <div class="staff-selection-message">
                    请选择您想评价的工作人员类型
                </div>
            `;
        }

        // 初始化计数器
        updateCounters();

        // 初始化选中工作人员展示区
        this.updateSelectedStaffDisplay();

        // 标记初始化完成
        this.isInitialized = true;
        this.isInitializing = false;
        console.log('staffModule初始化完成');
    },

    // 移除事件监听器
    removeEventListeners: function() {
        if (window.elements.staffTypeSelect) {
            const oldSelect = window.elements.staffTypeSelect;
            const newSelect = oldSelect.cloneNode(true);
            oldSelect.parentNode.replaceChild(newSelect, oldSelect);
            window.elements.staffTypeSelect = newSelect;
        }

        if (window.elements.staffContainer) {
            const oldContainer = window.elements.staffContainer;
            const newContainer = oldContainer.cloneNode(true);
            oldContainer.parentNode.replaceChild(newContainer, oldContainer);
            window.elements.staffContainer = newContainer;
        }
    },

    // 填充工作人员类型下拉框
    populateStaffTypes: function() {
        if (!window.elements.staffTypeSelect) {
            console.error('staffTypeSelect元素不存在，无法填充工作人员类型');
            return;
        }

        // 清空现有选项
        window.elements.staffTypeSelect.innerHTML = '<option value="">-- 请选择评价对象 --</option>';

        // 添加类型选项
        if (!Array.isArray(window.staffTypes) || window.staffTypes.length === 0) {
            console.warn('window.staffTypes不是数组或为空，无法填充工作人员类型');

            // 尝试从appData中获取
            if (window.appData && Array.isArray(window.appData.staffTypes) && window.appData.staffTypes.length > 0) {
                console.log('从appData中获取staffTypes:', window.appData.staffTypes);
                window.staffTypes = window.appData.staffTypes.map(item => ({
                    ...item,
                    id: String(item.id)
                }));
            } else {
                console.error('无法获取工作人员类型数据');
                return;
            }
        }

        console.log('填充工作人员类型下拉框:', window.staffTypes);
        window.staffTypes.forEach(type => {
            const option = document.createElement('option');
            option.value = type.id;
            option.textContent = type.name;
            window.elements.staffTypeSelect.appendChild(option);
        });

        console.log('工作人员类型下拉框填充完成，选项数量:', window.staffTypes.length);
    },

    // 注册事件监听器
    registerEventListeners: function() {
        // 保存this的引用
        const self = this;

        // 刷新DOM元素引用
        refreshElementRefs();

        // 类型选择事件
        if (window.elements.staffTypeSelect) {
            window.elements.staffTypeSelect.addEventListener('change', function(e) {
                const selectedType = e.target.value;
                console.log('工作人员类型选择变化:', selectedType);

                if (!selectedType) {
                    window.elements.staffContainer.innerHTML = `
                        <div class="staff-selection-message">
                            请选择您想评价的工作人员类型
                        </div>
                    `;
                    return;
                }

                console.log('过滤工作人员列表，类型ID:', selectedType);
                console.log('当前staffList:', window.staffList);

                const filteredStaff = window.staffList.filter(staff => {
                    const staffTypeMatches = String(staff.type_id) === String(selectedType) ||
                                           String(staff.staff_type) === String(selectedType);

                    if (staffTypeMatches) {
                        console.log('匹配的工作人员:', staff.name, '类型:', staff.staff_type || staff.type_id);
                    }

                    return staffTypeMatches;
                });

                console.log('过滤后的工作人员数量:', filteredStaff.length);
                self.renderStaffList(filteredStaff);
            });

            // 不自动选择工作人员类型，让用户手动选择
            console.log('初始化完成，等待用户选择工作人员类型');
        }

        // 评价按钮点击事件
        if (window.elements.staffContainer) {
            window.elements.staffContainer.addEventListener('click', function(e) {
                const ratingBtn = e.target.closest('.rating-btn');

                if (!ratingBtn || ratingBtn.disabled) {
                    return;
                }

                // 阻止默认行为和事件冒泡
                e.preventDefault();
                e.stopPropagation();

                const staffRow = ratingBtn.closest('.staff-row');
                if (!staffRow) {
                    return;
                }

                const staffId = staffRow.dataset.staffId;
                const rating = ratingBtn.dataset.rating;

                const staff = window.staffList.find(s => String(s.id) === String(staffId));
                if (!staff) {
                    return;
                }

                self.toggleRating(staff, rating);
            });
        }
    },

    // 渲染工作人员列表
    renderStaffList: function(staffToRender) {
        console.log('开始渲染工作人员列表...');

        // 检查DOM元素是否存在
        if (!window.elements.staffContainer) {
            console.error('staffContainer元素不存在，无法渲染工作人员列表');

            // 尝试重新获取DOM元素
            refreshElementRefs();
            if (!window.elements.staffContainer) {
                console.error('重新获取DOM元素后staffContainer仍然不存在');
                alert('页面加载错误，请刷新页面重试');
                return;
            }
        }

        // 检查工作人员列表是否为空
        if (!staffToRender || !staffToRender.length) {
            console.warn('工作人员列表为空，显示提示信息');
            window.elements.staffContainer.innerHTML = '<div class="staff-selection-message">暂无可选择的工作人员</div>';
            return;
        }

        // 获取当前评价数量
        const satisfiedCount = window.evaluationManager.getSatisfiedEvaluations().length;
        const unsatisfiedCount = window.evaluationManager.getUnsatisfiedEvaluations().length;

        // 添加详细的调试日志
        console.log('渲染工作人员列表:', {
            staffCount: staffToRender.length,
            satisfiedCount,
            unsatisfiedCount,
            staffList: staffToRender
        });

        // 构建HTML
        let staffHtml = '';

        try {
            staffHtml = staffToRender.map(staff => {
                // 检查staff对象是否有效
                if (!staff || !staff.id || !staff.name) {
                    console.error('无效的工作人员数据:', staff);
                    return '';
                }

                const currentRating = window.evaluationManager.getEvaluationStatus(staff.id);
                const staffType = getStaffTypeName(staff);

                // 添加调试日志
                console.log('渲染工作人员:', {
                    id: staff.id,
                    name: staff.name,
                    type: staffType,
                    currentRating
                });

                // 检查是否达到评价上限
                const satisfiedDisabled = !currentRating && satisfiedCount >= window.evaluationManager.MAX_SATISFIED;
                const unsatisfiedDisabled = !currentRating && unsatisfiedCount >= window.evaluationManager.MAX_UNSATISFIED;

                return `
                    <div class="staff-row" data-staff-id="${staff.id}">
                        <div class="staff-info">
                            <div>
                                <div class="staff-name">${staff.name}</div>
                                <div class="staff-type">${staffType}</div>
                            </div>
                        </div>
                        <div class="staff-rating-buttons">
                            <button type="button"
                                    class="rating-btn satisfied ${currentRating === 'satisfied' ? 'selected' : ''}"
                                    data-rating="satisfied"
                                    ${satisfiedDisabled ? 'disabled' : ''}>
                                <span class="icon-thumbs-up">👍</span>
                                满意${satisfiedCount >= window.evaluationManager.MAX_SATISFIED ? ' (已达上限)' : ''}
                            </button>
                            <button type="button"
                                    class="rating-btn unsatisfied ${currentRating === 'unsatisfied' ? 'selected' : ''}"
                                    data-rating="unsatisfied"
                                    ${unsatisfiedDisabled ? 'disabled' : ''}>
                                <span class="icon-thumbs-down">👎</span>
                                不满意${unsatisfiedCount >= window.evaluationManager.MAX_UNSATISFIED ? ' (已达上限)' : ''}
                            </button>
                        </div>
                    </div>
                `;
            }).join('');
        } catch (error) {
            console.error('构建工作人员HTML时出错:', error);
            staffHtml = `<div class="error-message">加载工作人员列表时出错: ${error.message}</div>`;
        }

        // 更新DOM
        try {
            window.elements.staffContainer.innerHTML = `
                <div class="staff-list">
                    ${staffHtml}
                </div>
            `;
            console.log('工作人员列表渲染完成，共渲染了', staffToRender.length, '名工作人员');
        } catch (error) {
            console.error('更新DOM时出错:', error);
            window.elements.staffContainer.innerHTML = `<div class="error-message">更新页面时出错: ${error.message}</div>`;
        }
        },

    // 更新选中工作人员展示
    updateSelectedStaffDisplay: function() {
        try {
            // 刷新DOM元素引用
            refreshElementRefs();

            // 获取满意和不满意的工作人员
            const satisfiedStaff = window.evaluationManager.getSatisfiedEvaluations();
            const unsatisfiedStaff = window.evaluationManager.getUnsatisfiedEvaluations();

            // 生成HTML
            let html = `
                <div class="evaluation-summary">
                    <div class="satisfied-staff-list">
                        <div class="list-header">
                            <h3>满意的工作人员 (${satisfiedStaff.length}/${window.evaluationManager.MAX_SATISFIED})</h3>
                        </div>
                        <div class="staff-tags">
                            ${satisfiedStaff.map(staff => `
                                <div class="staff-tag satisfied">
                                    <span class="staff-name">${staff.name}</span>
                                    <span class="staff-type">${staff.type}</span>
                                    <button type="button" class="remove-tag" data-staff-id="${staff.id}" title="移除评价">×</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                    <div class="unsatisfied-staff-list">
                        <div class="list-header">
                            <h3>不满意的工作人员 (${unsatisfiedStaff.length}/${window.evaluationManager.MAX_UNSATISFIED})</h3>
                        </div>
                        <div class="staff-tags">
                            ${unsatisfiedStaff.map(staff => `
                                <div class="staff-tag unsatisfied">
                                    <span class="staff-name">${staff.name}</span>
                                    <span class="staff-type">${staff.type}</span>
                                    <button type="button" class="remove-tag" data-staff-id="${staff.id}" title="移除评价">×</button>
                                </div>
                            `).join('')}
                        </div>
                    </div>
            `;

            // 只有选择了工作人员才显示提交按钮
            if (satisfiedStaff.length > 0 || unsatisfiedStaff.length > 0) {
                html += `
                    <div class="evaluation-actions">
                        <button type="button" class="btn btn-primary submit-evaluation" id="submitEvaluationBtn">
                            提交评价
                        </button>
                    </div>
                `;
            }

            html += `</div>`;

            // 更新DOM
            if (window.elements.selectedStaffDisplay) {
                window.elements.selectedStaffDisplay.innerHTML = html;

                // 绑定提交按钮事件
                const submitBtn = window.elements.selectedStaffDisplay.querySelector('#submitEvaluationBtn');
                if (submitBtn) {
                    // 先移除可能存在的事件监听器
                    const newSubmitBtn = submitBtn.cloneNode(true);
                    if (submitBtn.parentNode) {
                        submitBtn.parentNode.replaceChild(newSubmitBtn, submitBtn);
                    }

                    // 添加新的事件监听器
                    newSubmitBtn.addEventListener('click', (e) => {
                        console.log('===== 评价提交按钮被点击 =====');
                        e.preventDefault(); // 阻止默认行为
                        e.stopPropagation(); // 阻止事件冒泡

                        // 直接调用main.js中的直接提交函数
                        if (window.app && typeof window.app.directSubmitEvaluation === 'function') {
                            console.log('调用app.directSubmitEvaluation进行直接提交');
                            window.app.directSubmitEvaluation(e);
                        } else {
                            console.log('调用旧版staffModule.submitEvaluation方法');
                            this.submitEvaluation();
                        }
                    });

                    // 添加额外调试信息
                    console.log('评价提交按钮已绑定事件:', newSubmitBtn);
                }

                // 绑定移除标签事件
                const removeTags = window.elements.selectedStaffDisplay.querySelectorAll('.remove-tag');
                removeTags.forEach(tag => {
                    tag.addEventListener('click', () => {
                        const staffId = tag.dataset.staffId;
                        if (window.evaluationManager.removeEvaluation(staffId)) {
                            this.updateSelectedStaffDisplay();
                            const currentType = window.elements.staffTypeSelect.value;
                            if (currentType) {
                                const filteredStaff = window.staffList.filter(s =>
                                    String(s.type_id) === String(currentType) ||
                                    String(s.staff_type) === String(currentType)
                                );
                                this.renderStaffList(filteredStaff);
                            }
                        }
                    });
                });
            }

            // 更新计数器
            updateCounters();

        } catch (error) {
        }
    },

    // 提交评价方法 - 完全采用test_web.html的成功实现逻辑
    submitEvaluation: function() {
        console.log("===== 开始提交评价流程 =====");

        try {
            // 调用模态框显示方法 - 使用Promise进行异步处理
            console.log('调用showEvaluationModal显示评价确认对话框...');
            return this.showEvaluationModal()
                .then(result => {
                    console.log('评价提交成功:', result);
                    return result;
                })
                .catch(error => {
                    console.error('评价提交失败:', error);
                    throw error;
                });
        } catch (error) {
            console.error('submitEvaluation方法异常:', error);
            alert('提交过程中出现错误: ' + error.message);
            return Promise.reject(error);
        }
    },

    // 评价确认模态框 - 完全重写为test_web.html的逻辑
    showEvaluationModal: function() {
        return new Promise((resolve, reject) => {
            try {
                // 获取已评价的员工
                if (!window.evaluationManager) {
                    reject(new Error('评价管理器未初始化'));
                    return;
                }

                // 获取评价数据
                const satisfiedEvaluations = window.evaluationManager.getSatisfiedEvaluations();
                const unsatisfiedEvaluations = window.evaluationManager.getUnsatisfiedEvaluations();
                const totalEvaluations = satisfiedEvaluations.length + unsatisfiedEvaluations.length;

                if (totalEvaluations === 0) {
                    alert('请至少选择一名工作人员进行评价');
                    reject(new Error('未选择工作人员'));
                    return;
                }

                // 确保评价内容填写
                const comment = document.getElementById('commentTextarea').value.trim();
                if (!comment || comment.length < 5) {
                    alert('请填写至少5个字的评价内容');
                    reject(new Error('评价内容过短或为空'));
                    return;
                }

                // 创建模态框容器
                const modal = document.createElement('div');
                modal.className = 'evaluation-modal';

                // 创建模态框内容
                modal.innerHTML = `
                    <div class="evaluation-modal-content">
                        <span class="close">&times;</span>
                        <h3>确认提交评价</h3>

                        <div class="form-section">
                            <div class="input-group">
                                <label for="modal-hospital-number">住院号 (选填):</label>
                                <input type="text" id="modal-hospital-number" placeholder="请输入住院号">
                            </div>
                            <div class="input-group">
                                <label for="modal-contact-phone">联系方式 (选填):</label>
                                <input type="tel" id="modal-contact-phone" placeholder="请输入联系电话">
                            </div>
                        </div>

                        <div class="staff-evaluation-summary">
                            <h4>您的评价 (${totalEvaluations}人):</h4>
                            <div class="staff-list">
                                ${satisfiedEvaluations.length > 0 ?
                                    `<div class="satisfied-list">
                                        <strong>满意 (${satisfiedEvaluations.length}人):</strong>
                                        ${satisfiedEvaluations.map(staff => staff.name).join(', ')}
                                    </div>` : ''}

                                ${unsatisfiedEvaluations.length > 0 ?
                                    `<div class="unsatisfied-list">
                                        <strong>不满意 (${unsatisfiedEvaluations.length}人):</strong>
                                        ${unsatisfiedEvaluations.map(staff => staff.name).join(', ')}
                                    </div>` : ''}
                            </div>
                        </div>

                        <div class="comment-area">
                            <label>评价内容:</label>
                            <div class="comment-text">${comment}</div>
                        </div>

                        <div class="status-info" style="display: none;"></div>

                        <div class="response-container" style="display: none;">
                            <h4>服务器响应:</h4>
                            <pre class="response-data"></pre>
                        </div>

                        <div class="button-group">
                            <button class="cancel-btn">取消</button>
                            <button class="confirm-btn">确认提交</button>
                        </div>
                    </div>
                `;

                // 添加到文档
                document.body.appendChild(modal);

                // 获取元素引用
                const closeBtn = modal.querySelector('.close');
                const cancelBtn = modal.querySelector('.cancel-btn');
                const confirmBtn = modal.querySelector('.confirm-btn');
                const statusInfo = modal.querySelector('.status-info');
                const responseContainer = modal.querySelector('.response-container');
                const responseData = modal.querySelector('.response-data');

                // 绑定事件处理程序
                closeBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                    reject(new Error('用户取消提交'));
                });

                cancelBtn.addEventListener('click', () => {
                    document.body.removeChild(modal);
                    reject(new Error('用户取消提交'));
                });

                // 提交按钮的点击处理
                confirmBtn.addEventListener('click', async () => {
                    // 检查是否有评价
                    if (totalEvaluations === 0) {
                        alert('请至少选择一名工作人员进行评价');
                        return;
                    }

                    // 禁用按钮，避免重复提交
                    confirmBtn.disabled = true;
                    confirmBtn.textContent = '提交中...';
                    statusInfo.style.display = 'block';
                    statusInfo.textContent = '准备提交评价数据...';

                    try {
                        // 获取住院号和联系方式
                        const hospitalNumberInput = document.getElementById('modal-hospital-number');
                        const contactPhoneInput = document.getElementById('modal-contact-phone');
                        const hospitalNumber = hospitalNumberInput ? hospitalNumberInput.value.trim() : '';
                        const phoneNumber = contactPhoneInput ? contactPhoneInput.value.trim() : '';

                        console.log('[评价提交] 用户输入:', {
                            '住院号': hospitalNumber,
                            '联系方式': phoneNumber,
                            '评价内容': comment
                        });

                        // 使用api.js中定义的API URL
                        console.log('[评价提交] 使用api.js中定义的API URL');

                        // 详细记录appData状态
                        console.log('[评价提交] window.appData状态:', window.appData ? 'appData存在' : 'appData不存在');
                        if (window.appData) {
                            console.log('[评价提交] appData内容:', {
                                qrParam: window.appData.qrParam,
                                qrCode: window.appData.qrCode,
                                apiUrl: window.appData.apiUrl
                            });
                        }

                        // 使用evaluationManager获取评价数据 - 构建staff_evaluations数组
                        const staffEvaluations = [];

                        // 从evaluationManager获取评价数据 - 完全按照test_web.html格式构建
                        satisfiedEvaluations.forEach(staff => {
                            staffEvaluations.push({
                                staff_id: parseInt(staff.id),
                                is_satisfied: true
                            });
                        });

                        unsatisfiedEvaluations.forEach(staff => {
                            staffEvaluations.push({
                                staff_id: parseInt(staff.id),
                                is_satisfied: false
                            });
                        });

                        console.log('[评价提交] 评价数据:', staffEvaluations);

                        // 获取QR参数 - 核心部分，确保与test_web.html一致
                        const qrParam = window.appData ? (window.appData.qrParam || window.appData.qrCode) : '';
                        console.log('[评价提交] 获取到的QR参数:', qrParam);

                        if (!qrParam) {
                            console.error('[评价提交] 缺少二维码参数，无法提交评价');
                            statusInfo.textContent = '错误：缺少二维码参数';

                            // 尝试从URL中重新提取
                            try {
                                const urlParams = new URLSearchParams(window.location.search);
                                const urlQrParam = urlParams.get('qr') || urlParams.get('qrcode') ||
                                                urlParams.get('qr_param') || urlParams.get('qrParam');
                                console.log('[评价提交] 尝试从URL重新提取QR参数:', urlQrParam);

                                if (urlQrParam) {
                                    console.log('[评价提交] 从URL成功提取到QR参数，将使用此参数');
                                    const extractedQrParam = urlQrParam;

                                    // 确保QR参数可用于提交
                                    if (extractedQrParam) {
                                        submitEvaluationRequest(extractedQrParam);
                                        return;
                                    }
                                }
                                throw new Error('缺少二维码参数，请重新扫描二维码');
                            } catch (e) {
                                throw new Error('缺少二维码参数，请重新扫描二维码');
                            }
                        } else {
                            // 如果成功获取到QR参数，调用提交函数
                            submitEvaluationRequest(qrParam);
                        }

                        // 内部函数：执行实际的提交请求
                        async function submitEvaluationRequest(qrParamValue) {
                            // 最终提交的数据格式 - 与test_web.html保持完全一致
                            const evaluationData = {
                                qr_param: qrParamValue,
                                comment: comment,
                                staff_evaluations: staffEvaluations,
                                hospital_number: hospitalNumber,
                                phone_number: phoneNumber
                            };

                            // 详细记录提交数据
                            console.log('[评价提交] 最终提交数据:', JSON.stringify(evaluationData, null, 2));

                            // 不再需要定义headers，因为API模块会处理

                            statusInfo.textContent = '发送评价请求...';
                            console.log('[评价提交] 准备通过API模块发送评价请求');

                            try {
                                console.log('[评价提交] 开始发送POST请求...');

                                // 使用API模块发送请求，而不是直接使用fetch
                                // 确保API模块存在
                                if (!window.api || typeof window.api.submitEvaluation !== 'function') {
                                    throw new Error('API模块未加载或submitEvaluation方法不存在');
                                }

                                // 调用API模块的submitEvaluation方法
                                const result = await window.api.submitEvaluation(evaluationData, true);

                                // 模拟response对象以保持代码兼容性
                                const response = {
                                    ok: result && (result.status === 'success' || result.code === 0 || result.code === 200),
                                    status: result && result.code ? result.code : 200,
                                    statusText: result && result.message ? result.message : 'OK',
                                    // 模拟headers方法
                                    headers: {
                                        entries: () => []
                                    }
                                };

                                console.log('[评价提交] 收到响应:', {
                                    status: response.status,
                                    statusText: response.statusText,
                                    headers: Object.fromEntries([...response.headers.entries()])
                                });

                                statusInfo.textContent = `收到响应: ${response.status} ${response.statusText}`;

                                // 在模态框中显示服务器响应
                                responseContainer.style.display = 'block';
                                responseData.textContent = JSON.stringify(result, null, 2);

                                console.log('[评价提交] 响应内容:', result);

                                if (response.ok) {
                                    console.log('[评价提交] 评价提交成功:', result);
                                    statusInfo.textContent = '评价提交成功!';
                                    statusInfo.style.color = 'green';

                                    // 更新按钮状态
                                    confirmBtn.textContent = '提交成功';
                                    confirmBtn.style.backgroundColor = '#28a745';
                                    cancelBtn.textContent = '关闭';

                                    // 保存响应数据到全局
                                    if (window.appData) {
                                        window.appData.lastEvaluationResponse = result;
                                    }

                                    // 显示短暂的成功消息，然后立即跳转到感谢页面
                                    statusInfo.textContent = '评价提交成功！即将跳转到感谢页面...';

                                    // 立即隐藏按钮，避免用户再次点击
                                    confirmBtn.style.display = 'none';
                                    cancelBtn.style.display = 'none';

                                    // 延迟很短的时间后跳转，给用户一个视觉反馈
                                    setTimeout(() => {
                                        // 关闭模态框
                                        document.body.removeChild(modal);

                                        // 构建完整的URL路径进行跳转
                                        const currentPath = window.location.pathname;
                                        const basePath = currentPath.substring(0, currentPath.lastIndexOf('/') + 1);
                                        let redirectUrl;

                                        // 使用API返回的重定向URL或默认感谢页面
                                        if (result && result.data && result.data.redirect_url) {
                                            redirectUrl = result.data.redirect_url;
                                        } else {
                                            // 使用调试版本的感谢页面
                                            redirectUrl = window.location.origin + basePath + 'thank_you_debug.html';
                                        }

                                        console.log('[评价提交] 即将跳转到:', redirectUrl);

                                        // 显示加载状态
                                        const loadingOverlay = document.createElement('div');
                                        loadingOverlay.style.position = 'fixed';
                                        loadingOverlay.style.top = '0';
                                        loadingOverlay.style.left = '0';
                                        loadingOverlay.style.width = '100%';
                                        loadingOverlay.style.height = '100%';
                                        loadingOverlay.style.backgroundColor = 'rgba(0, 0, 0, 0.5)';
                                        loadingOverlay.style.display = 'flex';
                                        loadingOverlay.style.flexDirection = 'column';
                                        loadingOverlay.style.alignItems = 'center';
                                        loadingOverlay.style.justifyContent = 'center';
                                        loadingOverlay.style.zIndex = '9999';
                                        loadingOverlay.innerHTML = `
                                            <div style="color: white; font-size: 20px; margin-bottom: 20px;">评价提交成功！</div>
                                            <div style="color: white; font-size: 16px;">正在跳转到感谢页面...</div>
                                        `;
                                        document.body.appendChild(loadingOverlay);

                                        // 延迟执行跳转
                                        setTimeout(() => {
                                            window.location.href = redirectUrl;
                                            resolve(result);
                                        }, 500);
                                    }, 800); // 短暂延迟，让用户看到成功消息
                                } else {
                                    // 处理错误响应
                                    console.error('[评价提交] 评价提交失败:', response.status, result);

                                    // 获取错误信息
                                    let errorMessage = `提交失败: ${response.status}`;
                                    if (result && result.message) {
                                        errorMessage = result.message;
                                    } else if (result && result.error) {
                                        errorMessage = result.error;
                                    } else if (result && result.detail) {
                                        errorMessage = result.detail;
                                    }
                                    console.error('[评价提交] 错误消息:', errorMessage);

                                    statusInfo.textContent = `提交失败: ${errorMessage}`;
                                    statusInfo.style.color = 'red';

                                    // 在短暂延迟后关闭模态框并跳转到错误页面
                                    setTimeout(() => {
                                        // 关闭模态框
                                        document.body.removeChild(modal);

                                        // 将错误信息编码到URL参数中，并跳转到错误页面
                                        const encodedError = encodeURIComponent(errorMessage);
                                        window.location.href = `./error.html?error=${encodedError}`;

                                        reject(new Error(errorMessage));
                                    }, 1000);
                                }
                            } catch (error) {
                                console.error('[评价提交] 评价提交出错:', error);

                                // 检查是否是网络错误
                                let errorMessage = error.message || '提交时发生错误';
                                if (error.name === 'TypeError' && error.message.includes('fetch')) {
                                    errorMessage = '网络连接错误，请检查您的网络并重试';

                                    // 添加服务器地址到错误信息中，帮助调试
                                    console.error('[评价提交] 网络错误详情:', {
                                        url: submitUrl,
                                        navigator_online: navigator.onLine,
                                        error: error
                                    });
                                }

                                statusInfo.textContent = `提交错误: ${errorMessage}`;
                                statusInfo.style.color = 'red';

                                // 在模态框中显示错误详情
                                responseContainer.style.display = 'block';
                                responseData.textContent = errorMessage;
                                responseData.style.color = 'red';

                                // 在短暂延迟后关闭模态框并跳转到错误页面
                                setTimeout(() => {
                                    // 关闭模态框
                                    document.body.removeChild(modal);

                                    // 将错误信息编码到URL参数中，并跳转到错误页面
                                    const encodedError = encodeURIComponent(errorMessage);
                                    window.location.href = `./error.html?error=${encodedError}`;

                                    reject(error);
                                }, 1000);
                            }
                        }
                    } catch (error) {
                        console.error('[评价提交] 准备提交数据时出错:', error);
                        statusInfo.textContent = `准备错误: ${error.message}`;
                        statusInfo.style.color = 'red';

                        // 在短暂延迟后关闭模态框并跳转到错误页面
                        setTimeout(() => {
                            // 关闭模态框
                            document.body.removeChild(modal);

                            // 将错误信息编码到URL参数中，并跳转到错误页面
                            const encodedError = encodeURIComponent(error.message || '准备评价数据失败');
                            window.location.href = `./error.html?error=${encodedError}`;

                            reject(error);
                        }, 1000);
                    }
                });

                // 添加模态框样式
                const style = document.createElement('style');
                style.textContent = `
                    .evaluation-modal {
                        display: block;
                        position: fixed;
                        z-index: 1000;
                        left: 0;
                        top: 0;
                        width: 100%;
                        height: 100%;
                        background-color: rgba(0,0,0,0.4);
                    }
                    .evaluation-modal-content {
                        background-color: #fff;
                        margin: 10% auto;
                        padding: 20px;
                        border-radius: 5px;
                        width: 80%;
                        max-width: 600px;
                        box-shadow: 0 4px 8px rgba(0,0,0,0.2);
                        max-height: 80vh;
                        overflow-y: auto;
                    }
                    .close {
                        color: #aaa;
                        float: right;
                        font-size: 28px;
                        font-weight: bold;
                        cursor: pointer;
                    }
                    .close:hover {
                        color: #000;
                    }
                    .staff-list {
                        margin: 15px 0;
                        padding-left: 20px;
                    }
                    .form-section {
                        margin: 15px 0;
                        border-top: 1px solid #eee;
                        padding-top: 15px;
                    }
                    .input-group {
                        margin-bottom: 10px;
                    }
                    .input-group label {
                        display: block;
                        margin-bottom: 5px;
                        font-weight: bold;
                    }
                    .input-group input {
                        width: 100%;
                        padding: 8px;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        box-sizing: border-box;
                    }
                    .comment-area {
                        margin: 15px 0;
                    }
                    .comment-area label {
                        display: block;
                        margin-bottom: 5px;
                        font-weight: bold;
                    }
                    .comment-text {
                        padding: 8px;
                        background-color: #f9f9f9;
                        border: 1px solid #ddd;
                        border-radius: 4px;
                        min-height: 60px;
                    }
                    .status-info {
                        margin: 10px 0;
                        padding: 8px;
                        background-color: #f9f9f9;
                        border-left: 3px solid #3498db;
                        color: #333;
                    }
                    .button-group {
                        text-align: right;
                        margin-top: 15px;
                    }
                    .button-group button {
                        padding: 8px 16px;
                        margin-left: 10px;
                        border: none;
                        border-radius: 4px;
                        cursor: pointer;
                    }
                    .cancel-btn {
                        background-color: #f1f1f1;
                        color: #333;
                    }
                    .confirm-btn {
                        background-color: #3498db;
                        color: white;
                    }
                    .confirm-btn:disabled {
                        background-color: #95a5a6;
                        cursor: not-allowed;
                    }
                    .response-container {
                        margin-top: 15px;
                        border-top: 1px dashed #ddd;
                        padding-top: 10px;
                    }
                    .response-data {
                        background-color: #f5f5f5;
                        padding: 10px;
                        border-radius: 4px;
                        font-family: monospace;
                        font-size: 12px;
                        overflow-x: auto;
                        max-height: 200px;
                        overflow-y: auto;
                    }
                    .satisfied-list {
                        color: #28a745;
                        margin-bottom: 5px;
                    }
                    .unsatisfied-list {
                        color: #dc3545;
                        margin-bottom: 5px;
                    }
                `;
                document.head.appendChild(style);
            } catch (error) {
                console.error('显示评价确认模态框异常:', error);
                reject(error);
            }
        });
    },

    // 切换评价状态
    toggleRating: function(staff, rating) {
        try {
            // 检查参数
            if (!staff || !rating) {
                return;
            }

            // 使用评价管理器切换评价状态
            const success = window.evaluationManager.toggleEvaluation(staff, rating);

            if (!success) {
                // 如果切换失败，可能是因为达到上限
                const maxCount = rating === 'satisfied' ? window.evaluationManager.MAX_SATISFIED : window.evaluationManager.MAX_UNSATISFIED;
                alert(`${rating === 'satisfied' ? '满意' : '不满意'}评价已达到上限（${maxCount}个），请先取消之前的评价`);
                return;
            }

            // 更新UI
            this.updateSelectedStaffDisplay();

            // 重新渲染当前列表
            if (window.elements.staffTypeSelect) {
                const currentType = window.elements.staffTypeSelect.value;
                if (currentType) {
                    const filteredStaff = window.staffList.filter(s =>
                        String(s.type_id) === String(currentType) ||
                        String(s.staff_type) === String(currentType)
                    );
                    this.renderStaffList(filteredStaff);
                } else {
                    // 显示所有工作人员
                    this.renderStaffList(window.staffList);
                }
            } else {
                this.renderStaffList(window.staffList);
            }

        } catch (error) {
            console.error('评价切换错误:', error);
        }
    },

    // 模块初始化
    moduleInit: function() {
        console.log('正在初始化staffModule...');
        if (!refreshElementRefs()) {
            // 如果DOM元素没有准备好，延迟初始化
            console.warn('DOM元素未准备好，延迟初始化');
            setTimeout(() => this.moduleInit(), 100);
            return;
        }

        // 初始化时检查appData的状态
        console.log('staffModule初始化时appData状态:', window.appData);
        if (window.appData) {
            console.log('二维码参数:', window.appData.qrParam || window.appData.qrCode || '未找到');
        } else {
            console.warn('初始化时appData不存在');
        }

        // 初始化评价管理器
        if (!window.evaluationManager) {
            window.evaluationManager = new EvaluationManager();
        }

        // 绑定事件监听器
        if (typeof this.bindEventListeners === 'function') {
            this.bindEventListeners();
        } else if (typeof this.registerEventListeners === 'function') {
            this.registerEventListeners();
        }

        // 如果有工作人员列表，则显示
        if (window.staffList && window.staffList.length > 0) {
            this.renderStaffTypes();
        } else {
            console.warn('初始化时staffList不存在或为空');
        }

        // 显示已选择的工作人员
        this.updateSelectedStaffDisplay();

        console.log('staffModule初始化完成');

        // 记录moduleInit已被调用
        this.isInitialized = true;
    }
};
// 在页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('staffModule DOM加载完成，准备初始化...');

    // 确保evaluationManager存在
    if (!window.evaluationManager) {
        console.warn('evaluationManager不存在，现在创建...');
        window.evaluationManager = new EvaluationManager();
        console.log('evaluationManager已创建:', window.evaluationManager);
    }

    // 检查是否有表单拦截代码影响正常按钮，如有则删除
    try {
        const oldHandlers = window._formHandlers || [];
        if (oldHandlers.length > 0) {
            console.log('清除旧的表单事件处理程序...');
            oldHandlers.forEach(handler => {
                if (handler.form && handler.listener) {
                    handler.form.removeEventListener('submit', handler.listener);
                }
            });
            window._formHandlers = [];
        }
    } catch(e) {
        console.error('清除旧表单处理程序失败:', e);
    }

    // 定义一个函数来检查和初始化模块
    function checkAndInitModule() {
        console.log('检查staffModule初始化条件...');

        // 再次确保evaluationManager存在
        if (!window.evaluationManager) {
            console.warn('检测到evaluationManager不存在，重新创建...');
            window.evaluationManager = new EvaluationManager();
        }

        // 检查是否已经初始化，避免无限循环
        if (window.staffModule.isInitialized) {
            console.log('staffModule已经初始化，停止检查');
            return;
        }

        // 设置最大尝试次数，避免无限循环
        if (!window._staffModuleInitAttempts) {
            window._staffModuleInitAttempts = 0;
        }

        window._staffModuleInitAttempts++;

        // 如果尝试次数过多，停止尝试
        if (window._staffModuleInitAttempts > 10) {
            console.log('初始化尝试次数过多，停止检查');

            // 强制初始化，即使数据不完整
            if (window.appData && window.appData.staffTypes) {
                console.log('强制初始化staffModule，使用可用的staffTypes数据');
                window.staffModule.init([], window.appData.staffTypes);
            }
            return;
        }

        if (window.appData && (Array.isArray(window.appData.staffList) || Array.isArray(window.appData.staffTypes))) {
            console.log('条件满足，初始化staffModule...');
            window.staffModule.init(window.appData.staffList, window.appData.staffTypes);
        } else {
            console.log('等待数据加载...');
            setTimeout(checkAndInitModule, 500);
        }
    }

    // 开始检查
    checkAndInitModule();
});
