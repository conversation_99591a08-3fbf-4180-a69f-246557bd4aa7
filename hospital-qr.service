# Systemd服务文件：/etc/systemd/system/hospital-qr.service
# 医院二维码评价系统 - Gunicorn服务配置

[Unit]
Description=Hospital QR Code Evaluation System (Gunicorn)
Documentation=https://docs.gunicorn.org/
After=network.target
Wants=network.target

[Service]
Type=notify
User=www-data
Group=www-data
RuntimeDirectory=gunicorn
WorkingDirectory=/var/www/hospital-qr/Backend
Environment="PATH=/var/www/hospital-qr/venv/bin"
Environment="DJANGO_SETTINGS_MODULE=HospitalQRCode.settings"
Environment="PYTHONPATH=/var/www/hospital-qr/Backend"
ExecStart=/var/www/hospital-qr/venv/bin/gunicorn -c gunicorn.conf.py HospitalQRCode.wsgi:application
ExecReload=/bin/kill -s HUP $MAINPID
ExecStop=/bin/kill -s TERM $MAINPID
KillMode=mixed
TimeoutStopSec=30
PrivateTmp=true
Restart=always
RestartSec=5
StartLimitInterval=60
StartLimitBurst=3

# 安全配置
NoNewPrivileges=true
ProtectSystem=strict
ProtectHome=true
ReadWritePaths=/var/www/hospital-qr/Backend
ReadWritePaths=/var/log/gunicorn
ReadWritePaths=/var/run/gunicorn
ReadWritePaths=/tmp

# 资源限制
LimitNOFILE=65536
LimitNPROC=4096

[Install]
WantedBy=multi-user.target

# 安装和使用说明：
# 1. 将此文件保存为 /etc/systemd/system/hospital-qr.service
# 2. 重载systemd配置：sudo systemctl daemon-reload
# 3. 启用服务：sudo systemctl enable hospital-qr
# 4. 启动服务：sudo systemctl start hospital-qr
# 5. 查看状态：sudo systemctl status hospital-qr
# 6. 查看日志：sudo journalctl -u hospital-qr -f
# 7. 重启服务：sudo systemctl restart hospital-qr
# 8. 停止服务：sudo systemctl stop hospital-qr
