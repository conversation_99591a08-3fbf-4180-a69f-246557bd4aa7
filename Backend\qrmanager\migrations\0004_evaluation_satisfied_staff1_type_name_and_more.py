# Generated by Django 4.2.7 on 2025-04-05 02:45

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0003_remove_title_fields'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff1_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员1 类型名称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff2_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员2 类型名称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='satisfied_staff3_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='满意工作人员3 类型名称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff1_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员1 类型名称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff2_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员2 类型名称'),
        ),
        migrations.AddField(
            model_name='evaluation',
            name='unsatisfied_staff3_type_name',
            field=models.CharField(blank=True, default='', max_length=100, verbose_name='不满意工作人员3 类型名称'),
        ),
    ]
