#!/usr/bin/env python
"""
测试最新格式专用系统 - 确保只支持56字符格式
"""
import requests
import json
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, is_encrypted_param, secure_qr_access
from qrmanager.models import QRCode

def test_latest_format_only():
    """测试只支持最新格式的系统"""
    print("=" * 80)
    print("🔒 测试最新格式专用系统")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    
    try:
        # 获取测试数据
        qrcode_obj = QRCode.objects.first()
        if not qrcode_obj:
            print("❌ 数据库中没有二维码")
            return False
        
        print(f"测试二维码: {qrcode_obj.code}")
        print()
        
        # 测试1: 最新格式加密
        print("测试1: 最新格式加密")
        try:
            encrypted = encrypt_qr_param(qrcode_obj.code)
            print(f"   加密结果: {encrypted}")
            print(f"   加密长度: {len(encrypted)} 字符")
            print(f"   加密状态: ✅ 成功")
        except Exception as e:
            print(f"   加密状态: ❌ 失败 - {e}")
            return False
        print()
        
        # 测试2: 格式检查函数
        print("测试2: 格式检查函数")
        is_valid_format = is_encrypted_param(encrypted)
        print(f"   is_encrypted_param: {'✅ 通过' if is_valid_format else '❌ 失败'}")
        
        # 测试旧格式被拒绝
        fake_old_format = "somebase64string.salt123"
        is_old_rejected = not is_encrypted_param(fake_old_format)
        print(f"   拒绝旧格式: {'✅ 正确拒绝' if is_old_rejected else '❌ 错误接受'}")
        
        # 测试错误长度被拒绝
        wrong_length = "shortstring"
        is_wrong_rejected = not is_encrypted_param(wrong_length)
        print(f"   拒绝错误长度: {'✅ 正确拒绝' if is_wrong_rejected else '❌ 错误接受'}")
        print()
        
        # 测试3: secure_qr_access函数
        print("测试3: secure_qr_access函数")
        try:
            uuid_result = secure_qr_access(encrypted)
            print(f"   解密UUID: {uuid_result}")
            print(f"   UUID匹配: {'✅ 成功' if uuid_result == str(qrcode_obj.code) else '❌ 失败'}")
            secure_success = True
        except Exception as e:
            print(f"   解密失败: ❌ {e}")
            secure_success = False
        print()
        
        # 测试4: API接口
        print("测试4: API接口验证")
        verify_url = f"{BASE_URL}/api/v1/public/qrcode/verify/"
        verify_data = {
            "qr_param": encrypted,
            "client_ip": "127.0.0.1"
        }
        
        try:
            response = requests.post(
                verify_url,
                json=verify_data,
                headers={'Content-Type': 'application/json'},
                timeout=10
            )
            
            print(f"   响应状态: {response.status_code}")
            
            if response.status_code == 200:
                response_data = response.json()
                print(f"   API状态: {response_data.get('status', 'unknown')}")
                print(f"   API验证: ✅ 成功")
                api_success = True
            else:
                print(f"   错误信息: {response.text}")
                print(f"   API验证: ❌ 失败")
                api_success = False
                
        except Exception as e:
            print(f"   API验证: ❌ 异常 - {e}")
            api_success = False
        print()
        
        # 测试5: 旧格式被API拒绝
        print("测试5: 验证API拒绝旧格式")
        fake_old_data = {
            "qr_param": "somebase64string.salt123",
            "client_ip": "127.0.0.1"
        }
        
        try:
            response = requests.post(
                verify_url,
                json=fake_old_data,
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            print(f"   旧格式响应: {response.status_code}")
            if response.status_code == 400:
                print(f"   旧格式拒绝: ✅ 正确拒绝")
                old_rejected = True
            else:
                print(f"   旧格式拒绝: ❌ 错误接受")
                old_rejected = False
                
        except Exception as e:
            print(f"   旧格式测试: ❌ 异常 - {e}")
            old_rejected = False
        print()
        
        # 总结
        print("=" * 80)
        print("📊 测试结果总结")
        print("=" * 80)
        
        all_success = is_valid_format and secure_success and api_success and old_rejected
        
        print(f"{'✅' if is_valid_format else '❌'} 格式检查: {'正常' if is_valid_format else '异常'}")
        print(f"{'✅' if secure_success else '❌'} 安全访问: {'正常' if secure_success else '异常'}")
        print(f"{'✅' if api_success else '❌'} API验证: {'正常' if api_success else '异常'}")
        print(f"{'✅' if old_rejected else '❌'} 拒绝旧格式: {'正常' if old_rejected else '异常'}")
        
        if all_success:
            print("\n🎉 最新格式专用系统测试完全成功！")
            print("✅ 只支持56字符最新格式")
            print("✅ 正确拒绝旧格式和错误格式")
            print("✅ API接口工作正常")
            print("✅ 系统已完全升级到最新标准")
        else:
            print("\n❌ 系统存在问题")
            print("❌ 需要进一步调试")
        
        return all_success
        
    except Exception as e:
        print(f"❌ 测试过程异常: {e}")
        return False

def test_batch_latest_format():
    """批量测试最新格式"""
    print("\n" + "=" * 80)
    print("🔄 批量测试最新格式")
    print("=" * 80)
    
    BASE_URL = "http://127.0.0.1:8000"
    qrcodes = QRCode.objects.filter(is_active=True)[:3]
    
    if not qrcodes:
        print("❌ 没有可用的二维码")
        return False
    
    success_count = 0
    total_count = len(qrcodes)
    
    for i, qrcode_obj in enumerate(qrcodes, 1):
        print(f"\n批量测试 {i}/{total_count}: {qrcode_obj.code}")
        
        try:
            # 加密
            encrypted = encrypt_qr_param(qrcode_obj.code)
            print(f"   加密: ✅ {len(encrypted)}字符")
            
            # 格式检查
            if not is_encrypted_param(encrypted):
                print(f"   格式检查: ❌ 失败")
                continue
            
            # API验证
            response = requests.post(
                f"{BASE_URL}/api/v1/public/qrcode/verify/",
                json={"qr_param": encrypted, "client_ip": "127.0.0.1"},
                headers={'Content-Type': 'application/json'},
                timeout=5
            )
            
            if response.status_code == 200:
                print(f"   API验证: ✅ 成功")
                success_count += 1
            else:
                print(f"   API验证: ❌ 失败 ({response.status_code})")
                
        except Exception as e:
            print(f"   测试: ❌ 异常 - {e}")
    
    success_rate = (success_count / total_count) * 100
    print(f"\n📊 批量测试结果: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")
    
    return success_rate == 100.0

if __name__ == "__main__":
    print("🚀 开始最新格式专用系统测试...")
    
    # 详细测试
    detailed_success = test_latest_format_only()
    
    # 批量测试
    batch_success = test_batch_latest_format()
    
    print("\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if detailed_success and batch_success:
        print("🎉 系统完全升级成功！")
        print("✅ 只支持最新的56字符格式")
        print("✅ 完全拒绝旧格式")
        print("✅ API接口工作完美")
        print("✅ 批量测试100%成功")
        print("\n📋 用户问题回答:")
        print("   ✅ 系统已按最新标准完全升级")
        print("   ✅ 不再支持任何旧格式")
        print("   ✅ 加密字符串完全兼容API接口")
        print("   ✅ 解密功能100%正常")
    else:
        print("❌ 系统升级存在问题")
        if not detailed_success:
            print("   - 核心功能测试失败")
        if not batch_success:
            print("   - 批量测试失败")
    
    print("=" * 80)
