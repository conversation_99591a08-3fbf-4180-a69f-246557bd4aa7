"""
自定义认证组件，用于禁用next参数
"""

from django.contrib.auth.decorators import user_passes_test
from django.contrib.auth.mixins import AccessMixin
from django.shortcuts import redirect

def login_required_no_next(function=None, login_url=None):
    """
    自定义登录装饰器，与Django的login_required类似，但不添加next参数

    用法：
    @login_required_no_next
    def my_view(request):
        ...
    """
    actual_decorator = user_passes_test(
        lambda u: u.is_authenticated,
        login_url=login_url or '/login/',  # 直接使用字符串路径，避免循环导入
        redirect_field_name=None  # 关键：设置为None，不添加next参数
    )
    if function:
        return actual_decorator(function)
    return actual_decorator

class LoginRequiredNoNextMixin(AccessMixin):
    """
    自定义LoginRequiredMixin，与Django的LoginRequiredMixin类似，但不添加next参数

    用法：
    class MyView(LoginRequiredNoNextMixin, TemplateView):
        ...
    """
    login_url = '/login/'  # 直接使用字符串路径，避免循环导入

    def dispatch(self, request, *args, **kwargs):
        if not request.user.is_authenticated:
            return self.handle_no_permission()
        return super().dispatch(request, *args, **kwargs)

    def handle_no_permission(self):
        # 直接重定向到登录页面，不添加next参数
        return redirect(self.login_url)