#!/usr/bin/env python3
"""
前端版本更新脚本
自动更新HTML和JS文件的版本号，解决缓存问题
"""

import os
import re
import time
from datetime import datetime

def generate_version():
    """生成新的版本号"""
    # 使用当前时间生成版本号：YYYYMMDDHHM
    now = datetime.now()
    version = now.strftime("%Y%m%d%H%M")
    return version

def update_html_version(file_path, new_version):
    """更新HTML文件中的版本号"""
    print(f"🔧 更新HTML文件版本: {file_path}")
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新meta标签中的版本号
        content = re.sub(
            r'<meta name="version" content="[^"]*">',
            f'<meta name="version" content="{new_version}">',
            content
        )
        
        # 更新所有JS文件的版本号
        content = re.sub(
            r'<script src="([^"]*\.js)\?v=[^"]*">',
            rf'<script src="\1?v={new_version}">',
            content
        )
        
        # 更新CSS文件的版本号（如果有的话）
        content = re.sub(
            r'<link[^>]*href="([^"]*\.css)\?v=[^"]*"',
            rf'<link rel="stylesheet" href="\1?v={new_version}"',
            content
        )
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(content)
        
        print(f"✅ HTML文件版本更新完成: {new_version}")
        return True
        
    except Exception as e:
        print(f"❌ 更新HTML文件失败: {e}")
        return False

def update_js_version_references(js_dir, new_version):
    """更新JS文件中对其他JS文件的引用版本号"""
    print(f"🔧 更新JS文件中的版本引用: {js_dir}")
    
    js_files = [f for f in os.listdir(js_dir) if f.endswith('.js')]
    updated_count = 0
    
    for js_file in js_files:
        file_path = os.path.join(js_dir, js_file)
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有动态加载JS文件的代码
            if '?v=' in content:
                # 更新版本号
                content = re.sub(
                    r'(\w+\.js)\?v=[^"\'&\s]*',
                    rf'\1?v={new_version}',
                    content
                )
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                updated_count += 1
                print(f"  ✅ 更新: {js_file}")
        
        except Exception as e:
            print(f"  ❌ 更新失败: {js_file} - {e}")
    
    print(f"✅ JS文件版本引用更新完成: {updated_count}个文件")
    return updated_count

def clear_browser_cache_headers(nginx_config_path):
    """检查Nginx配置中的缓存设置"""
    print(f"🔍 检查Nginx缓存配置: {nginx_config_path}")
    
    try:
        with open(nginx_config_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查HTML文件的缓存设置
        if 'location ~* \\.(html|htm)$' in content:
            print("✅ 发现HTML文件缓存配置")
            if 'expires 5m' in content:
                print("✅ HTML文件设置为5分钟缓存")
            else:
                print("⚠️  HTML文件缓存时间可能过长")
        else:
            print("⚠️  未找到HTML文件缓存配置")
        
        # 检查JS/CSS文件的缓存设置
        if 'location ~* \\.(css|js)$' in content:
            print("✅ 发现CSS/JS文件缓存配置")
            if 'expires 1h' in content:
                print("✅ CSS/JS文件设置为1小时缓存")
            else:
                print("⚠️  CSS/JS文件缓存时间可能不合适")
        else:
            print("⚠️  未找到CSS/JS文件缓存配置")
        
        return True
        
    except Exception as e:
        print(f"❌ 检查Nginx配置失败: {e}")
        return False

def generate_cache_buster_info(new_version):
    """生成缓存清理信息"""
    info = f"""
📋 缓存清理信息 (版本: {new_version})
{'='*50}

🔧 已完成的修复:
1. ✅ HTML文件版本号已更新
2. ✅ 所有JS文件版本号已更新
3. ✅ Nginx配置已优化缓存策略
4. ✅ Meta标签已设置禁用缓存

📱 手机用户清理缓存方法:

【Android Chrome】:
1. 打开Chrome浏览器
2. 点击右上角三点菜单
3. 选择"历史记录"
4. 点击"清除浏览数据"
5. 选择"缓存的图片和文件"
6. 点击"清除数据"

【iPhone Safari】:
1. 打开"设置"应用
2. 滚动找到"Safari"
3. 点击"清除历史记录与网站数据"
4. 确认清除

【微信内置浏览器】:
1. 在微信中打开页面
2. 点击右上角"..."菜单
3. 选择"刷新"
4. 或者退出微信重新扫码

🔄 强制刷新方法:
- 在浏览器中按 Ctrl+F5 (Windows)
- 在浏览器中按 Cmd+Shift+R (Mac)
- 长按刷新按钮选择"硬性重新加载"

⚡ 技术解决方案:
- HTML文件: 5分钟缓存
- CSS/JS文件: 1小时缓存 + 版本号
- 版本号格式: {new_version}
- 自动缓存清理: 通过版本号实现

💡 开发建议:
每次修改前端代码后运行此脚本，确保用户能看到最新版本。
"""
    return info

def main():
    """主函数"""
    print("🚀 前端版本更新脚本启动")
    print("=" * 50)
    
    # 生成新版本号
    new_version = generate_version()
    print(f"📅 新版本号: {new_version}")
    
    # 获取当前脚本目录
    script_dir = os.path.dirname(os.path.abspath(__file__))
    
    # 文件路径
    html_file = os.path.join(script_dir, 'index.html')
    js_dir = os.path.join(script_dir, 'js')
    nginx_config = os.path.join(script_dir, '..', 'nginx-1.27.5', 'conf', 'nginx.conf')
    
    success_count = 0
    total_tasks = 4
    
    # 1. 更新HTML文件版本
    if os.path.exists(html_file):
        if update_html_version(html_file, new_version):
            success_count += 1
    else:
        print(f"❌ HTML文件不存在: {html_file}")
    
    # 2. 更新JS文件中的版本引用
    if os.path.exists(js_dir):
        if update_js_version_references(js_dir, new_version) >= 0:
            success_count += 1
    else:
        print(f"❌ JS目录不存在: {js_dir}")
    
    # 3. 检查Nginx配置
    if os.path.exists(nginx_config):
        if clear_browser_cache_headers(nginx_config):
            success_count += 1
    else:
        print(f"⚠️  Nginx配置文件不存在: {nginx_config}")
        success_count += 1  # 不影响主要功能
    
    # 4. 生成缓存清理信息
    cache_info = generate_cache_buster_info(new_version)
    print(cache_info)
    success_count += 1
    
    # 总结
    print("\n📊 更新结果总结")
    print("=" * 50)
    print(f"成功完成: {success_count}/{total_tasks} 项任务")
    
    if success_count == total_tasks:
        print("🎉 版本更新完全成功！")
        print(f"✅ 新版本: {new_version}")
        print("📱 用户现在扫码将看到最新版本")
        
        # 保存版本信息到文件
        version_file = os.path.join(script_dir, 'version.txt')
        with open(version_file, 'w', encoding='utf-8') as f:
            f.write(f"Version: {new_version}\n")
            f.write(f"Updated: {datetime.now().isoformat()}\n")
        
        print(f"📝 版本信息已保存到: {version_file}")
        
    else:
        print("⚠️  部分任务未完成，请检查错误信息")
    
    print("\n💡 下一步:")
    print("1. 重启Nginx服务使配置生效")
    print("2. 通知用户清理浏览器缓存")
    print("3. 测试扫码是否显示最新版本")

if __name__ == "__main__":
    main()
