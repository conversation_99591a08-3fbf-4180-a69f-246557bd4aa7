from django.core.management.base import BaseCommand
from django.utils import timezone
from qrmanager.models import APIKey
from qrmanager.utils import LoggerHelper
import logging
import datetime

logger = logging.getLogger('cleanup')

class Command(BaseCommand):
    help = '清理过期的API令牌和登录会话'

    def add_arguments(self, parser):
        # 添加可选命令行参数
        parser.add_argument(
            '--days',
            type=int,
            default=30,
            help='未使用的令牌若超过多少天将被视为不活跃（默认30天）',
        )
        parser.add_argument(
            '--force',
            action='store_true',
            help='是否强制删除（而不仅仅是禁用）过期的令牌',
        )

    def handle(self, *args, **options):
        """执行清理过期令牌的命令"""
        days_threshold = options['days']
        force_delete = options['force']
        now = timezone.now()
        
        # 设置不活跃的时间阈值
        inactive_threshold = now - datetime.timedelta(days=days_threshold)
        
        self.stdout.write(f"开始清理过期的API令牌，日期阈值: {inactive_threshold}")
        
        # 1. 查找已过期的API密钥
        expired_keys = APIKey.objects.filter(
            expires_at__isnull=False,
            expires_at__lt=now,
            is_active=True
        )
        
        expired_count = expired_keys.count()
        self.stdout.write(f"找到 {expired_count} 个已过期的API令牌")
        
        if expired_count > 0:
            # 决定是删除还是禁用过期的密钥
            if force_delete:
                log_msg = f"已删除 {expired_count} 个过期的API令牌"
                expired_keys.delete()
                self.stdout.write(self.style.SUCCESS(log_msg))
                logger.info(log_msg)
            else:
                # 仅禁用过期的密钥
                expired_keys.update(is_active=False)
                log_msg = f"已禁用 {expired_count} 个过期的API令牌"
                self.stdout.write(self.style.SUCCESS(log_msg))
                logger.info(log_msg)
        
        # 2. 查找长时间未使用的API密钥（即使没有显式过期）
        inactive_keys = APIKey.objects.filter(
            last_used_at__isnull=False,
            last_used_at__lt=inactive_threshold,
            is_active=True
        )
        
        inactive_count = inactive_keys.count()
        self.stdout.write(f"找到 {inactive_count} 个长期未使用的API令牌（超过{days_threshold}天）")
        
        if inactive_count > 0:
            # 决定是删除还是禁用不活跃的密钥
            if force_delete:
                log_msg = f"已删除 {inactive_count} 个不活跃的API令牌"
                inactive_keys.delete()
                self.stdout.write(self.style.SUCCESS(log_msg))
                logger.info(log_msg)
            else:
                # 仅禁用不活跃的密钥
                inactive_keys.update(is_active=False)
                log_msg = f"已禁用 {inactive_count} 个不活跃的API令牌"
                self.stdout.write(self.style.SUCCESS(log_msg))
                logger.info(log_msg)
        
        # 3. 记录操作日志
        try:
            LoggerHelper.log_operation(
                user=None,  # 系统操作，没有特定用户
                action="系统维护_清理令牌",
                description=f"清理过期和不活跃的API令牌，处理了{expired_count + inactive_count}个令牌",
                status='success',
                extra_data={
                    'expired_count': expired_count,
                    'inactive_count': inactive_count,
                    'days_threshold': days_threshold,
                    'force_delete': force_delete
                }
            )
        except Exception as e:
            logger.error(f"记录清理令牌操作日志时出错: {str(e)}")
        
        self.stdout.write(self.style.SUCCESS('清理API令牌完成!')) 