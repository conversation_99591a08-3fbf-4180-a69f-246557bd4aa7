{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}管理仪表板{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <!-- 统计卡片 -->
            <div class="row g-4 mb-4">
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card bg-gradient-primary">
                        <div class="stat-card-body">
                            <div class="stat-card-icon">
                                <i class="fas fa-qrcode"></i>
                            </div>
                            <div class="stat-card-info">
                                <h3 class="stat-card-number">{{ total_qrcodes }}</h3>
                                <p class="stat-card-text">二维码总数</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card bg-gradient-success">
                        <div class="stat-card-body">
                            <div class="stat-card-icon">
                                <i class="fas fa-star"></i>
                            </div>
                            <div class="stat-card-info">
                                <h3 class="stat-card-number">{{ total_evaluations }}</h3>
                                <p class="stat-card-text">总评价数</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card bg-gradient-info">
                        <div class="stat-card-body">
                            <div class="stat-card-icon">
                                <i class="fas fa-user-md"></i>
                            </div>
                            <div class="stat-card-info">
                                <h3 class="stat-card-number">{{ total_staff }}</h3>
                                <p class="stat-card-text">工作人员</p>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6">
                    <div class="stat-card bg-gradient-warning">
                        <div class="stat-card-body">
                            <div class="stat-card-icon">
                                <i class="fas fa-hospital"></i>
                            </div>
                            <div class="stat-card-info">
                                <h3 class="stat-card-number">{{ total_departments }}</h3>
                                <p class="stat-card-text">科室总数</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主要功能区 -->
            <div class="row g-4">
                <!-- 二维码管理 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-primary-subtle">
                            <i class="fas fa-qrcode"></i>
                        </div>
                        <h3>二维码管理</h3>
                        <p>管理床位二维码，生成和打印二维码</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:qrcode_list' %}" class="btn btn-primary">
                                <i class="fas fa-arrow-right me-2"></i>进入管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 评价管理 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-success-subtle">
                            <i class="fas fa-star"></i>
                        </div>
                        <h3>评价管理</h3>
                        <p>查看和管理患者评价信息</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:evaluation_list' %}" class="btn btn-success">
                                <i class="fas fa-arrow-right me-2"></i>查看评价
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 科室管理 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-info-subtle">
                            <i class="fas fa-hospital"></i>
                        </div>
                        <h3>科室管理</h3>
                        <p>管理医院科室信息和床位</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:department_list' %}" class="btn btn-info">
                                <i class="fas fa-arrow-right me-2"></i>进入管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 工作人员管理 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-warning-subtle">
                            <i class="fas fa-user-md"></i>
                        </div>
                        <h3>工作人员</h3>
                        <p>管理医院工作人员信息</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:staff_list' %}" class="btn btn-warning">
                                <i class="fas fa-arrow-right me-2"></i>进入管理
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 打印模板 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-purple-subtle">
                            <i class="fas fa-print"></i>
                        </div>
                        <h3>打印模板</h3>
                        <p>管理二维码打印模板设置</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:print_template_list' %}" class="btn btn-purple">
                                <i class="fas fa-arrow-right me-2"></i>模板设置
                            </a>
                        </div>
                    </div>
                </div>

                <!-- 情感分析 -->
                <div class="col-lg-4">
                    <div class="feature-card">
                        <div class="feature-icon bg-danger-subtle">
                            <i class="fas fa-chart-pie"></i>
                        </div>
                        <h3>情感分析</h3>
                        <p>分析评价情感倾向和趋势</p>
                        <div class="feature-actions">
                            <a href="{% url 'qrmanager:sentiment_analysis' %}" class="btn btn-danger">
                                <i class="fas fa-arrow-right me-2"></i>查看分析
                            </a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 系统管理区域 -->
            <div class="row mt-4">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <h2 class="card-title mb-4">系统管理</h2>
                            <div class="row g-4">
                                <!-- 操作日志 -->
                                <div class="col-lg-3 col-md-6">
                                    <div class="system-card">
                                        <div class="system-icon">
                                            <i class="fas fa-history"></i>
                                        </div>
                                        <h4>操作日志</h4>
                                        <a href="{% url 'qrmanager:operation_logs' %}" class="stretched-link"></a>
                                    </div>
                                </div>

                                <!-- 字典管理 -->
                                <div class="col-lg-3 col-md-6">
                                    <div class="system-card">
                                        <div class="system-icon">
                                            <i class="fas fa-book"></i>
                                        </div>
                                        <h4>字典管理</h4>
                                        <a href="{% url 'qrmanager:dictionary_list' %}" class="stretched-link"></a>
                                    </div>
                                </div>

                                <!-- 用户权限 -->
                                <div class="col-lg-3 col-md-6">
                                    <div class="system-card">
                                        <div class="system-icon">
                                            <i class="fas fa-users-cog"></i>
                                        </div>
                                        <h4>用户权限</h4>
                                        <a href="{% url 'qrmanager:admin_permissions' %}" class="stretched-link"></a>
                                    </div>
                                </div>

                                <!-- 账号设置 -->
                                <div class="col-lg-3 col-md-6">
                                    <div class="system-card">
                                        <div class="system-icon">
                                            <i class="fas fa-user-cog"></i>
                                        </div>
                                        <h4>账号设置</h4>
                                        <a href="{% url 'qrmanager:account_settings' %}" class="stretched-link"></a>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="row mb-4">
                <div class="col-md-12">
                    <div class="card">
                        <div class="card-header">
                            <h5 class="mb-0"><i class="fas fa-key me-2"></i>API密钥管理</h5>
                        </div>
                        <div class="card-body">
                            <p>管理API密钥，控制第三方应用对API的访问权限。</p>
                            <div class="d-flex justify-content-between align-items-center">
                                <div>
                                    <a href="{% url 'qrmanager:apikey_list' %}" class="btn btn-primary">
                                        <i class="fas fa-key me-1"></i> 管理API密钥
                                    </a>
                                    <a href="{% url 'qrmanager:apilog_list' %}" class="btn btn-info ms-2">
                                        <i class="fas fa-history me-1"></i> 查看API日志
                                    </a>
                                    <a href="{% url 'qrmanager:api_docs' %}" class="btn btn-success ms-2">
                                        <i class="fas fa-book-open me-1"></i> API文档
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
/* 统计卡片样式 */
.stat-card {
    border-radius: 15px;
    overflow: hidden;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.05);
    transition: transform 0.2s;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.bg-gradient-primary {
    background: linear-gradient(135deg, #6366f1 0%, #2563eb 100%);
}

.bg-gradient-success {
    background: linear-gradient(135deg, #34d399 0%, #059669 100%);
}

.bg-gradient-info {
    background: linear-gradient(135deg, #60a5fa 0%, #2563eb 100%);
}

.bg-gradient-warning {
    background: linear-gradient(135deg, #fbbf24 0%, #d97706 100%);
}

.bg-gradient-danger {
    background: linear-gradient(135deg, #f43f5e 0%, #e11d48 100%);
}

.stat-card-body {
    padding: 1.5rem;
    display: flex;
    align-items: center;
    color: white;
}

.stat-card-icon {
    font-size: 2.5rem;
    margin-right: 1rem;
    opacity: 0.8;
}

.stat-card-info {
    flex: 1;
}

.stat-card-number {
    font-size: 2rem;
    font-weight: 700;
    margin: 0;
    line-height: 1;
}

.stat-card-text {
    margin: 0.5rem 0 0;
    opacity: 0.8;
    font-size: 0.9rem;
}

/* 功能卡片样式 */
.feature-card {
    background: white;
    border-radius: 15px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    height: 100%;
    border: 1px solid rgba(0, 0, 0, 0.05);
    position: relative;
    padding-bottom: 5rem; /* 为按钮预留空间 */
    overflow: visible; /* 确保子元素溢出时可见 */
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 1rem 2rem rgba(0, 0, 0, 0.1);
    border-color: rgba(99, 102, 241, 0.2);
}

.feature-icon {
    width: 80px;
    height: 80px;
    border-radius: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    transition: all 0.3s ease;
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-card h3 {
    margin-bottom: 1rem;
    font-size: 1.5rem;
    font-weight: 600;
}

.feature-card p {
    color: #6c757d;
    margin-bottom: 1.5rem;
}

.feature-actions {
    margin-top: auto;
    position: absolute;
    bottom: 2rem;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 10; /* 确保按钮在最上层 */
}

.feature-actions .btn {
    position: relative; /* 确保按钮可以接收悬停事件 */
    z-index: 20; /* 确保按钮在最上层 */
    transition: transform 0.2s ease, box-shadow 0.2s ease, background-color 0.2s ease;
}

.feature-actions .btn:hover {
    transform: scale(1.05); /* 鼠标悬停时按钮放大效果 */
}

/* 系统管理卡片样式 */
.system-card {
    background: #ffffff;
    border-radius: 16px;
    padding: 2rem;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    cursor: pointer;
    border: 1px solid rgba(226, 232, 240, 0.8);
    overflow: hidden;
}

.system-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(99, 102, 241, 0.03) 0%, rgba(99, 102, 241, 0.06) 100%);
    opacity: 0;
    transition: opacity 0.3s ease;
}

.system-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 24px rgba(0, 0, 0, 0.06);
    border-color: rgba(99, 102, 241, 0.2);
}

.system-card:hover::before {
    opacity: 1;
}

.system-icon {
    position: relative;
    width: 64px;
    height: 64px;
    border-radius: 16px;
    background: #f8fafc;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.25rem;
    font-size: 1.75rem;
    transition: all 0.3s ease;
}

/* 操作日志图标 */
.system-card:nth-child(1) .system-icon {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

/* 字典管理图标 */
.system-card:nth-child(2) .system-icon {
    color: #10b981;
    background: rgba(16, 185, 129, 0.1);
}

/* 用户权限图标 */
.system-card:nth-child(3) .system-icon {
    color: #8b5cf6;
    background: rgba(139, 92, 246, 0.1);
}

/* 账号设置图标 */
.system-card:nth-child(4) .system-icon {
    color: #f59e0b;
    background: rgba(245, 158, 11, 0.1);
}

.system-card:hover .system-icon {
    transform: scale(1.1) rotate(-5deg);
}

.system-card h4 {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 600;
    color: #1e293b;
    position: relative;
}

/* 系统管理标题样式 */
.card-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1e293b;
    position: relative;
    padding-bottom: 1rem;
    margin-bottom: 2rem;
}

.card-title::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    width: 40px;
    height: 3px;
    background: linear-gradient(to right, #6366f1, #8b5cf6);
    border-radius: 3px;
}

/* 系统管理区域卡片容器 */
.card {
    border: none;
    border-radius: 20px;
    background: #ffffff;
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.03);
}

.card-body {
    padding: 2rem;
}

/* 按钮基础样式 */
.btn {
    padding: 0.5rem 1.25rem;
    font-weight: 500;
    border-radius: 8px;
    transition: all 0.2s ease;
}

/* 主要按钮 - 二维码管理 */
.btn-primary {
    background-color: #4f46e5;
    border-color: #4f46e5;
    color: white;
}

.btn-primary:hover {
    background-color: #4338ca;
    border-color: #4338ca;
    box-shadow: 0 4px 12px rgba(79, 70, 229, 0.2);
}

/* 成功按钮 - 评价管理 */
.btn-success {
    background-color: #10b981;
    border-color: #10b981;
    color: white;
}

.btn-success:hover {
    background-color: #059669;
    border-color: #059669;
    box-shadow: 0 4px 12px rgba(16, 185, 129, 0.2);
}

/* 信息按钮 - 科室管理 */
.btn-info {
    background-color: #0ea5e9;
    border-color: #0ea5e9;
    color: white;
}

.btn-info:hover {
    background-color: #0284c7;
    border-color: #0284c7;
    box-shadow: 0 4px 12px rgba(14, 165, 233, 0.2);
}

/* 警告按钮 - 工作人员 */
.btn-warning {
    background-color: #f59e0b;
    border-color: #f59e0b;
    color: white;
}

.btn-warning:hover {
    background-color: #d97706;
    border-color: #d97706;
    box-shadow: 0 4px 12px rgba(245, 158, 11, 0.2);
}

/* 紫色按钮 - 打印模板 */
.btn-purple {
    background-color: #8b5cf6;
    border-color: #8b5cf6;
    color: white;
}

.btn-purple:hover {
    background-color: #7c3aed;
    border-color: #7c3aed;
    box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

/* 危险按钮 - 情感分析 */
.btn-danger {
    background-color: #f43f5e;
    border-color: #f43f5e;
    color: white;
}

.btn-danger:hover {
    background-color: #e11d48;
    border-color: #e11d48;
    box-shadow: 0 4px 12px rgba(244, 63, 94, 0.2);
}

/* 功能卡片图标背景色优化 */
.bg-primary-subtle {
    background-color: rgba(79, 70, 229, 0.1);
    color: #4f46e5;
}

.bg-success-subtle {
    background-color: rgba(16, 185, 129, 0.1);
    color: #10b981;
}

.bg-info-subtle {
    background-color: rgba(14, 165, 233, 0.1);
    color: #0ea5e9;
}

.bg-warning-subtle {
    background-color: rgba(245, 158, 11, 0.1);
    color: #f59e0b;
}

.bg-danger-subtle {
    background-color: rgba(244, 63, 94, 0.1);
    color: #f43f5e;
}

.bg-purple-subtle {
    background-color: rgba(139, 92, 246, 0.1);
    color: #8b5cf6;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .feature-card {
        padding: 1.5rem;
    }

    .feature-icon {
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
    }

    .feature-card h3 {
        font-size: 1.2rem;
    }
}
</style>
{% endblock %}