from django.core.management.base import BaseCommand
from qrmanager.models import Dictionary, DictionaryItem, StaffType
from django.db import transaction

class Command(BaseCommand):
    help = '同步字典表和StaffType模型中的数据'

    def handle(self, *args, **options):
        self.stdout.write('开始同步人员类型数据...')
        
        try:
            with transaction.atomic():
                # 确保存在staff_type字典
                dictionary, created = Dictionary.objects.get_or_create(
                    code='staff_type',
                    defaults={
                        'name': '人员类型',
                        'description': '系统中使用的人员类型',
                        'is_active': True
                    }
                )
                
                if created:
                    self.stdout.write(self.style.SUCCESS('创建了人员类型字典'))
                
                # 从StaffType模型获取数据
                staff_types = StaffType.objects.all()
                
                # 将StaffType数据同步到字典表
                for staff_type in staff_types:
                    dict_item, created = DictionaryItem.objects.get_or_create(
                        dictionary=dictionary,
                        code=staff_type.code,
                        defaults={
                            'name': staff_type.name,
                            'sort_order': staff_type.display_order,
                            'is_active': staff_type.is_active
                        }
                    )
                    
                    if not created:
                        # 更新现有项
                        if (dict_item.name != staff_type.name or 
                            dict_item.sort_order != staff_type.display_order or 
                            dict_item.is_active != staff_type.is_active):
                            dict_item.name = staff_type.name
                            dict_item.sort_order = staff_type.display_order
                            dict_item.is_active = staff_type.is_active
                            dict_item.save()
                            self.stdout.write(f'更新了字典项: {staff_type.code} - {staff_type.name}')
                    else:
                        self.stdout.write(f'创建了字典项: {staff_type.code} - {staff_type.name}')
                
                # 从字典表获取数据
                dict_items = DictionaryItem.objects.filter(dictionary=dictionary)
                
                # 将字典表数据同步到StaffType模型
                for dict_item in dict_items:
                    staff_type, created = StaffType.objects.get_or_create(
                        code=dict_item.code,
                        defaults={
                            'name': dict_item.name,
                            'display_order': dict_item.sort_order,
                            'is_active': dict_item.is_active
                        }
                    )
                    
                    if not created:
                        # 更新现有项
                        if (staff_type.name != dict_item.name or 
                            staff_type.display_order != dict_item.sort_order or 
                            staff_type.is_active != dict_item.is_active):
                            staff_type.name = dict_item.name
                            staff_type.display_order = dict_item.sort_order
                            staff_type.is_active = dict_item.is_active
                            staff_type.save()
                            self.stdout.write(f'更新了StaffType: {dict_item.code} - {dict_item.name}')
                    else:
                        self.stdout.write(f'创建了StaffType: {dict_item.code} - {dict_item.name}')
                
                self.stdout.write(self.style.SUCCESS('同步完成'))
                
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'同步失败: {str(e)}')) 