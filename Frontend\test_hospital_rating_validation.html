<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>医院整体评价验证测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-title {
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
            margin-bottom: 20px;
        }
        .test-case {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007bff;
        }
        .test-result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 5px;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .rating-input {
            margin: 10px 0;
        }
        .rating-input input {
            padding: 5px;
            border: 1px solid #ddd;
            border-radius: 3px;
            width: 100px;
        }
        .code {
            background: #f1f1f1;
            padding: 2px 5px;
            border-radius: 3px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">🔍 医院整体评价验证测试</h1>
        <p>测试医院整体评价的必填验证逻辑是否正确工作。</p>
        
        <!-- 模拟医院评价输入 -->
        <div class="rating-input">
            <label for="testHospitalRating">模拟医院整体评价输入：</label>
            <input type="number" id="testHospitalRating" min="0" max="5" value="0" placeholder="0-5">
            <small>（0表示未选择，1-5表示评分）</small>
        </div>
        
        <div class="test-case">
            <h3>测试用例 1：未选择评分（值为0）</h3>
            <p>期望结果：显示错误提示"请对医院整体服务进行评价（1-5星）"</p>
            <button class="btn" onclick="testCase1()">测试未选择评分</button>
            <div id="result1" class="test-result"></div>
        </div>
        
        <div class="test-case">
            <h3>测试用例 2：选择有效评分（1-5星）</h3>
            <p>期望结果：验证通过，不显示错误</p>
            <button class="btn" onclick="testCase2()">测试有效评分</button>
            <div id="result2" class="test-result"></div>
        </div>
        
        <div class="test-case">
            <h3>测试用例 3：无效评分（超出范围）</h3>
            <p>期望结果：显示错误提示"医院整体评价必须在1-5星之间"</p>
            <button class="btn" onclick="testCase3()">测试无效评分</button>
            <div id="result3" class="test-result"></div>
        </div>
        
        <div class="test-case">
            <h3>测试用例 4：元素不存在</h3>
            <p>期望结果：显示错误提示"请对医院整体服务进行评价（1-5星）"</p>
            <button class="btn" onclick="testCase4()">测试元素不存在</button>
            <div id="result4" class="test-result"></div>
        </div>
    </div>

    <div class="test-container">
        <h2 class="test-title">📋 验证逻辑代码</h2>
        <p>以下是当前使用的医院整体评价验证逻辑：</p>
        <pre style="background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto;"><code>// 获取医院整体评价 - 必填验证
const hospitalRating = document.getElementById('hospitalRating');
let hospitalRatingValue = null;

if (hospitalRating && hospitalRating.value) {
    hospitalRatingValue = parseInt(hospitalRating.value);
    console.log('[直接提交] 医院整体评价:', hospitalRatingValue);
} else {
    // 医院整体评价是必填项
    alert('请对医院整体服务进行评价（1-5星）');
    return;
}

// 验证评分范围
if (hospitalRatingValue < 1 || hospitalRatingValue > 5) {
    alert('医院整体评价必须在1-5星之间');
    return;
}</code></pre>
    </div>

    <script>
        // 模拟验证逻辑
        function validateHospitalRating(ratingValue, elementExists = true) {
            console.log('测试医院整体评价验证，输入值:', ratingValue, '元素存在:', elementExists);
            
            if (!elementExists) {
                return {
                    success: false,
                    message: '请对医院整体服务进行评价（1-5星）'
                };
            }
            
            let hospitalRatingValue = null;
            
            if (ratingValue && ratingValue !== '0' && ratingValue !== 0) {
                hospitalRatingValue = parseInt(ratingValue);
                console.log('解析后的评价值:', hospitalRatingValue);
            } else {
                // 医院整体评价是必填项
                return {
                    success: false,
                    message: '请对医院整体服务进行评价（1-5星）'
                };
            }
            
            // 验证评分范围
            if (hospitalRatingValue < 1 || hospitalRatingValue > 5) {
                return {
                    success: false,
                    message: '医院整体评价必须在1-5星之间'
                };
            }
            
            return {
                success: true,
                message: `验证通过，医院整体评价: ${hospitalRatingValue}星`,
                value: hospitalRatingValue
            };
        }
        
        function showResult(elementId, result) {
            const resultDiv = document.getElementById(elementId);
            resultDiv.className = `test-result ${result.success ? 'success' : 'error'}`;
            resultDiv.innerHTML = `
                <strong>${result.success ? '✅ 测试通过' : '❌ 测试失败'}</strong><br>
                ${result.message}
                ${result.value ? `<br><small>评分值: ${result.value}</small>` : ''}
            `;
        }
        
        function testCase1() {
            // 测试未选择评分（值为0）
            const testInput = document.getElementById('testHospitalRating');
            testInput.value = '0';
            
            const result = validateHospitalRating('0', true);
            showResult('result1', result);
        }
        
        function testCase2() {
            // 测试有效评分
            const testInput = document.getElementById('testHospitalRating');
            const validRatings = [1, 2, 3, 4, 5];
            const randomRating = validRatings[Math.floor(Math.random() * validRatings.length)];
            testInput.value = randomRating;
            
            const result = validateHospitalRating(randomRating, true);
            showResult('result2', result);
        }
        
        function testCase3() {
            // 测试无效评分
            const testInput = document.getElementById('testHospitalRating');
            const invalidRatings = [-1, 0, 6, 10, 'abc'];
            const randomInvalid = invalidRatings[Math.floor(Math.random() * invalidRatings.length)];
            testInput.value = randomInvalid;
            
            const result = validateHospitalRating(randomInvalid, true);
            showResult('result3', result);
        }
        
        function testCase4() {
            // 测试元素不存在
            const result = validateHospitalRating(null, false);
            showResult('result4', result);
        }
        
        // 页面加载时显示当前状态
        document.addEventListener('DOMContentLoaded', function() {
            console.log('医院整体评价验证测试页面已加载');
            
            // 显示当前测试环境信息
            const info = document.createElement('div');
            info.className = 'test-container';
            info.innerHTML = `
                <h3>🔧 测试环境信息</h3>
                <p><strong>测试时间：</strong>${new Date().toLocaleString()}</p>
                <p><strong>测试目的：</strong>验证医院整体评价必填验证逻辑</p>
                <p><strong>验证要求：</strong></p>
                <ul>
                    <li>用户必须选择1-5星评分</li>
                    <li>未选择时显示错误提示</li>
                    <li>超出范围时显示错误提示</li>
                    <li>元素不存在时显示错误提示</li>
                </ul>
            `;
            document.body.appendChild(info);
        });
    </script>
</body>
</html>
