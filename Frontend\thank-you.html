<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="default">
    <meta name="theme-color" content="#ffffff">
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <title>感谢您的评价 - 医院服务评价系统</title>
    <link rel="stylesheet" href="/styles.css">
    <link rel="stylesheet" href="/css/apple-styles.css">
    <link rel="stylesheet" href="/css/apple-svg-icons.css">
</head>
<body>
    <div class="app-container">
        <!-- 背景装饰气泡 -->
        <div class="bubbles">
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
            <div class="bubble"></div>
        </div>
        
        <!-- 医疗装饰元素 -->
        <div class="medical-decoration"></div>
        
        <!-- 页面主内容 -->
        <main class="main-content">
            <div class="success-message fade-in">
                <div class="success-icon heartbeat-icon">
                    <i class="ai-check-circle ai-2x"></i>
                </div>
                <h3>感谢您的评价</h3>
                <p>您的反馈对我们至关重要，我们将继续提升医疗服务质量。</p>
                <div class="thank-you-actions">
                    <a href="/" class="btn pulse-animation">
                        <i class="ai-hospital"></i>返回首页
                    </a>
                </div>
                
                <!-- 装饰元素 -->
                <div class="thank-you-decoration">
                    <i class="ai-heartbeat ai-3x" style="position: absolute; bottom: 20px; right: 20px; opacity: 0.1;"></i>
                    <i class="ai-medical-cross ai-2x" style="position: absolute; top: 20px; left: 20px; opacity: 0.1;"></i>
                </div>
            </div>
        </main>
        
        <!-- 页脚 -->
        <footer class="app-footer">
            <p>&copy; 2025 医院服务评价系统</p>
            <p class="mt-2 text-sm text-gray-500">
                感谢您抽出宝贵时间完成评价，祝您健康愉快！
            </p>
        </footer>
    </div>
    
    <!-- 页面动画效果 -->
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        // 添加进入动画
        const successMessage = document.querySelector('.success-message');
        if (successMessage) {
            successMessage.classList.add('fade-in');
        }
        
        // 自动在5秒后返回首页
        setTimeout(function() {
            const redirectCountdown = document.getElementById('redirectCountdown');
            if (redirectCountdown) {
                let seconds = 5;
                const countdownInterval = setInterval(function() {
                    seconds--;
                    redirectCountdown.textContent = seconds;
                    if (seconds <= 0) {
                        clearInterval(countdownInterval);
                        window.location.href = '/';
                    }
                }, 1000);
            }
        }, 1000);
    });
    </script>
</body>
</html> 