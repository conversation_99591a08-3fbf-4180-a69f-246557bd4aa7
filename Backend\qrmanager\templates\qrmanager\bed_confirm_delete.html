{% extends "qrmanager/base.html" %}

{% block title %}删除床位{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card fade-in">
                <div class="card-body">
                    <h1 class="card-title text-center">删除床位</h1>
                    <div class="text-center mb-4">
                        <p class="lead">确定要删除床位 "{{ object.number }}" 吗？</p>
                        <div class="alert alert-warning">
                            <p class="mb-1">床位信息：</p>
                            <ul class="list-unstyled">
                                <li>科室：{{ object.department.name|default:"-" }}</li>
                                <li>区域：{{ object.get_area_display }}</li>
                                <li>负责人：{{ object.staff.name|default:"-" }}</li>
                            </ul>
                        </div>
                        
                        <!-- 添加二维码删除警告 -->
                        {% if object.qrcode %}
                        <div class="alert alert-danger">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>警告：</strong> 删除此床位将同时删除关联的二维码和所有评价数据！
                        </div>
                        {% endif %}
                    </div>
                    <form method="post" class="text-center">
                        {% csrf_token %}
                        <button type="submit" class="btn btn-danger">确认删除</button>
                        {% if object.department %}
                        <a href="{% url 'qrmanager:bed_list' %}?department={{ object.department.id }}" class="btn btn-secondary">取消</a>
                        {% else %}
                        <a href="{% url 'qrmanager:bed_list' %}" class="btn btn-secondary">取消</a>
                        {% endif %}
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 