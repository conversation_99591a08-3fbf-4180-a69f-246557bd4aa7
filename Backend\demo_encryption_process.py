#!/usr/bin/env python
"""
详细演示加密解密过程 - 回答用户的问题
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid
import base64
import hashlib

def demo_encryption_process():
    """详细演示加密解密的每一步过程"""
    print("=" * 80)
    print("🔍 加密解密过程详细演示")
    print("=" * 80)
    
    # 测试多个不同的UUID
    test_uuids = [
        "12345678-1234-5678-9012-123456789012",  # 固定测试UUID
        "abcdefgh-abcd-abcd-abcd-abcdefghijkl",  # 字母UUID
        "00000000-0000-0000-0000-000000000000",  # 全零UUID
        "ffffffff-ffff-ffff-ffff-ffffffffffff",  # 全F UUID
        str(uuid.uuid4()),  # 随机UUID
    ]
    
    print("📋 问题1: 加密后的固定长度都是一样的吗？")
    print("答案: 是的！所有加密后的字符串都是固定56字符长度\n")
    
    encrypted_results = []
    
    for i, test_uuid in enumerate(test_uuids, 1):
        print(f"测试 {i}: {test_uuid}")
        
        try:
            encrypted = encrypt_qr_param(test_uuid)
            encrypted_results.append((test_uuid, encrypted))
            
            print(f"   加密结果: {encrypted}")
            print(f"   字符长度: {len(encrypted)} 字符")
            print(f"   长度固定: {'✓ 是' if len(encrypted) == 56 else '✗ 否'}")
            print()
            
        except Exception as e:
            print(f"   加密失败: {e}")
            print()
    
    print("=" * 80)
    print("📋 问题2: 怎么根据加密字符串解密的？")
    print("答案: 通过解析固定的数据结构！\n")
    
    # 详细演示解密过程
    if encrypted_results:
        demo_uuid, demo_encrypted = encrypted_results[0]
        print(f"🔍 详细解密演示 - UUID: {demo_uuid}")
        print(f"🔍 加密字符串: {demo_encrypted}")
        print()
        
        # 步骤1: Base64解码
        print("步骤1: Base64解码")
        padding_needed = len(demo_encrypted) % 4
        if padding_needed != 0:
            padded = demo_encrypted + '=' * (4 - padding_needed)
        else:
            padded = demo_encrypted
        
        decoded_data = base64.b64decode(padded).decode()
        print(f"   解码结果: {decoded_data}")
        print(f"   解码长度: {len(decoded_data)} 字符")
        print()
        
        # 步骤2: 解析固定结构
        print("步骤2: 解析固定数据结构")
        print("   数据结构: 盐值(4位) + 签名(6位) + UUID(32位) = 42字符")
        
        salt = decoded_data[:4]
        signature = decoded_data[4:10]
        uuid_clean = decoded_data[10:42]
        
        print(f"   盐值 (位置0-3):   '{salt}' (4字符)")
        print(f"   签名 (位置4-9):   '{signature}' (6字符)")
        print(f"   UUID (位置10-41): '{uuid_clean}' (32字符)")
        print()
        
        # 步骤3: 重构UUID
        print("步骤3: 重构标准UUID格式")
        reconstructed_uuid = f"{uuid_clean[:8]}-{uuid_clean[8:12]}-{uuid_clean[12:16]}-{uuid_clean[16:20]}-{uuid_clean[20:32]}"
        print(f"   重构UUID: {reconstructed_uuid}")
        print(f"   原始UUID: {demo_uuid}")
        print(f"   匹配结果: {'✓ 完全匹配' if reconstructed_uuid == demo_uuid else '✗ 不匹配'}")
        print()
        
        # 步骤4: 验证签名
        print("步骤4: 验证签名")
        from qrmanager.security import SECURITY_CONFIG
        signature_data = f"{uuid_clean}|{salt}|{SECURITY_CONFIG['encryption_key']}"
        expected_signature = hashlib.md5(signature_data.encode()).hexdigest()[:6]
        
        print(f"   签名数据: {signature_data}")
        print(f"   计算签名: {expected_signature}")
        print(f"   实际签名: {signature}")
        print(f"   验证结果: {'✓ 签名有效' if signature == expected_signature else '✗ 签名无效'}")

def demo_data_structure():
    """演示数据结构的固定性"""
    print("\n" + "=" * 80)
    print("🏗️ 数据结构固定性演示")
    print("=" * 80)
    
    print("所有UUID都遵循相同的数据结构:")
    print()
    print("┌─────────────────────────────────────────────────────────┐")
    print("│                   56字符加密字符串                        │")
    print("└─────────────────────────────────────────────────────────┘")
    print("                            │")
    print("                    Base64解码")
    print("                            ▼")
    print("┌─────────────────────────────────────────────────────────┐")
    print("│                   42字符原始数据                          │")
    print("├──────┬──────────┬─────────────────────────────────────────┤")
    print("│ 盐值 │   签名   │              UUID                      │")
    print("│ 4位  │   6位    │              32位                      │")
    print("└──────┴──────────┴─────────────────────────────────────────┘")
    print()
    
    # 实际演示
    test_cases = [
        "12345678-1234-5678-9012-123456789012",
        "abcdefgh-abcd-abcd-abcd-abcdefghijkl",
    ]
    
    for test_uuid in test_cases:
        print(f"示例: {test_uuid}")
        try:
            encrypted = encrypt_qr_param(test_uuid)
            
            # 解码
            padding_needed = len(encrypted) % 4
            if padding_needed != 0:
                padded = encrypted + '=' * (4 - padding_needed)
            else:
                padded = encrypted
            
            decoded = base64.b64decode(padded).decode()
            
            # 解析
            salt = decoded[:4]
            signature = decoded[4:10]
            uuid_part = decoded[10:42]
            
            print(f"   加密字符串: {encrypted} (长度: {len(encrypted)})")
            print(f"   解码数据:   {decoded} (长度: {len(decoded)})")
            print(f"   盐值:       {salt}")
            print(f"   签名:       {signature}")
            print(f"   UUID部分:   {uuid_part}")
            print()
            
        except Exception as e:
            print(f"   处理失败: {e}")
            print()

if __name__ == "__main__":
    demo_encryption_process()
    demo_data_structure()
    
    print("=" * 80)
    print("📝 总结:")
    print("1. ✅ 所有加密字符串都是固定56字符长度")
    print("2. ✅ 解密通过解析固定的数据结构实现")
    print("3. ✅ 数据结构: 盐值(4) + 签名(6) + UUID(32) = 42字符")
    print("4. ✅ Base64编码后变成56字符")
    print("5. ✅ 这种固定结构确保了100%的稳定性")
    print("=" * 80)
