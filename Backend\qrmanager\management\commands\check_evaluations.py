from django.core.management.base import BaseCommand
from qrmanager.models import Evaluation, StaffEvaluation
from django.db import transaction

class Command(BaseCommand):
    help = '检查评价记录中的满意和不满意工作人员'

    def handle(self, *args, **options):
        self.stdout.write('检查评价记录...')
        
        # 获取所有评价记录
        evaluations = Evaluation.objects.all().order_by('-id')[:5]
        
        for e in evaluations:
            self.stdout.write(f'评价ID: {e.id}, 评价内容: {e.comment[:30] if e.comment else "无"}...')
            
            satisfied = e.satisfied_staff()
            unsatisfied = e.unsatisfied_staff()
            
            self.stdout.write(f'  满意工作人员: {", ".join([s.staff.name for s in satisfied]) if satisfied else "无"}')
            self.stdout.write(f'  不满意工作人员: {", ".join([s.staff.name for s in unsatisfied]) if unsatisfied else "无"}')
            
            # 直接查询关联的StaffEvaluation记录
            staff_evals = StaffEvaluation.objects.filter(evaluation=e)
            self.stdout.write(f'  关联的工作人员评价数: {staff_evals.count()}')
            
            for se in staff_evals:
                self.stdout.write(f'    - {se.staff.name}: {"满意" if se.is_satisfied else "不满意"}')
        
        # 输出整体统计信息
        total_evals = Evaluation.objects.count()
        total_staff_evals = StaffEvaluation.objects.count()
        
        self.stdout.write(self.style.SUCCESS(f'\n总共有 {total_evals} 条评价记录'))
        self.stdout.write(self.style.SUCCESS(f'总共有 {total_staff_evals} 条工作人员评价关联记录')) 