"""
API安全中间件
用于保护API接口，实施访问控制、认证验证和速率限制
"""

from django.http import JsonResponse
from django.utils import timezone
from django.core.cache import cache
import time

class APISecurityMiddleware:
    """
    API安全中间件
    用于保护API接口，实施访问控制、认证验证和速率限制
    """
    
    def __init__(self, get_response):
        self.get_response = get_response
    
    def __call__(self, request):
        # 记录请求开始时间
        start_time = timezone.now()
        
        # 检查是否是API请求
        if self._is_api_request(request):
            # 根据API类型执行不同的安全检查
            response = self._check_api_security(request)
            if response:
                return response
        
        # 继续处理请求
        response = self.get_response(request)
        
        # 对API请求记录日志
        if self._is_api_request(request):
            self._log_api_request(request, response, start_time)
        
        return response
    
    def _is_api_request(self, request):
        """判断是否为API请求"""
        path = request.path
        return (
            path.startswith('/api/') or
            path.startswith('/admin/api/') or
            'api' in path
        )
    
    def _check_api_security(self, request):
        """根据API类型执行安全检查"""
        path = request.path
        
        # 1. 管理API - 需要登录验证
        if (path.startswith('/admin/api/') or
            (path.startswith('/api/') and not path.startswith('/api/v1/') and not path.startswith('/api/secure/') and not path.startswith('/api/public/'))):
            if not request.user.is_authenticated:
                return JsonResponse({
                    'status': 'error',
                    'message': '未授权访问，请先登录'
                }, status=401)
        
        # 2. RESTful API - 需要API令牌
        if path.startswith('/api/v1/') and not path.startswith('/api/v1/public/'):
            # 从请求头获取API密钥
            auth_header = request.headers.get('Authorization', '')
            if auth_header.startswith('Token '):
                token = auth_header.split(' ')[1].strip()
            else:
                token = request.GET.get('token', '')
            
            # 如果没有提供令牌
            if not token:
                return JsonResponse({
                    'status': 'error',
                    'message': '需要API令牌'
                }, status=401)
            
            # 验证API密钥
            try:
                from ..models import APIKey
                api_key = APIKey.objects.get(key=token)
                
                # 检查密钥是否有效
                if not api_key.is_active:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'API密钥已禁用'
                    }, status=403)
                
                # 检查密钥是否过期
                if api_key.expires_at and timezone.now() > api_key.expires_at:
                    return JsonResponse({
                        'status': 'error',
                        'message': 'API密钥已过期'
                    }, status=403)
                
                # 检查速率限制
                self._check_rate_limits(api_key)
                
                # 将API密钥附加到请求对象
                request.api_key = api_key
            
            except APIKey.DoesNotExist:
                return JsonResponse({
                    'status': 'error',
                    'message': '无效的API密钥'
                }, status=401)
        
        # 3. 公开API - 实施速率限制
        if path.startswith('/api/secure/') or path.startswith('/api/public/') or path.startswith('/api/v1/public/'):
            # 基于IP的速率限制
            client_ip = self._get_client_ip(request)
            if self._is_ip_rate_limited(client_ip):
                return JsonResponse({
                    'status': 'error',
                    'message': '请求过于频繁，请稍后再试'
                }, status=429)
        
        # 通过所有安全检查
        return None
    
    def _check_rate_limits(self, api_key):
        """检查API密钥的速率限制"""
        # 实现速率限制逻辑
        # 这里可以根据api_key的rate_limit_day, rate_limit_hour, rate_limit_minute属性
        # 检查API调用次数是否超过限制
        pass
    
    def _get_client_ip(self, request):
        """获取客户端IP地址"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0].strip()
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip
    
    def _is_ip_rate_limited(self, ip):
        """检查IP是否被限速"""
        # 实现基于IP的速率限制逻辑
        # 可以使用缓存或数据库记录IP的请求次数和时间
        return False
    
    def _log_api_request(self, request, response, start_time):
        """记录API请求日志"""
        try:
            # 计算请求处理时间
            process_time = (timezone.now() - start_time).total_seconds()
            
            # 获取请求信息
            method = request.method
            path = request.path
            status_code = response.status_code
            
            # 获取客户端信息
            client_ip = self._get_client_ip(request)
            user_agent = request.META.get('HTTP_USER_AGENT', '')
            
            # 获取请求数据
            request_data = None
            if method in ['POST', 'PUT', 'PATCH']:
                try:
                    if request.content_type == 'application/json':
                        import json
                        request_data = json.loads(request.body.decode('utf-8'))
                    else:
                        request_data = dict(request.POST)
                except:
                    request_data = {'error': '无法解析请求数据'}
            
            # 获取响应数据
            response_data = None
            if hasattr(response, 'content'):
                try:
                    if 'application/json' in response.get('Content-Type', ''):
                        import json
                        response_data = json.loads(response.content.decode('utf-8'))
                except:
                    response_data = {'error': '无法解析响应数据'}
            
            # 确定状态
            if 200 <= status_code < 300:
                status = 'success'
            elif 400 <= status_code < 500:
                status = 'warning'
            else:
                status = 'error'
            
            # 记录API日志
            from ..models import APILog
            APILog.objects.create(
                endpoint=path,
                method=method,
                status_code=status_code,
                status=status,
                process_time=process_time,
                client_ip=client_ip,
                user_agent=user_agent,
                request_data=request_data,
                response_data=response_data,
                user=request.user if request.user.is_authenticated else None,
                api_key=request.api_key if hasattr(request, 'api_key') else None
            )
        
        except Exception as e:
            # 记录日志失败，但不影响响应处理
            print(f"记录API日志失败: {e}")
            
        return None