from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0007_populate_work_numbers'),
    ]

    operations = [
        migrations.CreateModel(
            name='Dictionary',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, unique=True, verbose_name='字典编码')),
                ('name', models.CharField(max_length=100, verbose_name='字典名称')),
                ('description', models.TextField(blank=True, verbose_name='描述')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
            ],
            options={
                'verbose_name': '字典类型',
                'verbose_name_plural': '字典类型',
                'ordering': ['code'],
            },
        ),
        migrations.CreateModel(
            name='DictionaryItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('code', models.CharField(max_length=50, verbose_name='编码')),
                ('name', models.CharField(max_length=100, verbose_name='名称')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('dictionary', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='items', to='qrmanager.dictionary', verbose_name='所属字典')),
            ],
            options={
                'verbose_name': '字典项',
                'verbose_name_plural': '字典项',
                'ordering': ['dictionary', 'sort_order', 'code'],
                'unique_together': {('dictionary', 'code')},
            },
        ),
    ] 