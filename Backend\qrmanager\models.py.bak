from django.db import models
import uuid
import secrets
from django.core.validators import MinValueValidator, MaxValueValidator, FileExtensionValidator
from django.contrib.auth.models import User
from django.core.exceptions import ValidationError
from PIL import Image
import os
from django.utils import timezone
from django.urls import reverse
from django.conf import settings
from django.utils.translation import gettext_lazy as _
import qrcode
from .qrcode_utils import generate_qrcode  # 从专用工具文件导入二维码函数
from .security import encrypt_qr_param, SECURITY_CONFIG
import glob
from io import BytesIO
import base64
from django.utils.html import format_html
from datetime import timedelta

def generate_work_number():
    return f"WN{str(uuid.uuid4())[:8]}"

def validate_image_size(image):
    if image.size > 10 * 1024 * 1024:  # 10MB
        raise ValidationError('图片大小不能超过10MB')

def staff_photo_path(instance, filename):
    # 获取文件扩展名
    ext = filename.split('.')[-1]
    # 使用工号和姓名作为文件名
    filename = f"{instance.work_number}{instance.name}.{ext}"
    return os.path.join('staff_photos', filename)

def template_image_path(instance, filename):
    # 获取文件扩展名
    ext = filename.split('.')[-1].lower()
    
    # 确定科室编码
    department_code = instance.department.code if instance.department else 'public'
    
    # 使用固定的文件名格式，确保每个科室只有一个模板文件
    # 添加时间戳作为查询参数，避免浏览器缓存旧图片
    timestamp = timezone.now().strftime('%Y%m%d%H%M%S')
    filename = f"template_{department_code}.{ext}?v={timestamp}"
    
    return os.path.join('print_templates', filename.split('?')[0])

class Department(models.Model):
    """科室模型"""
    code = models.CharField('科室编码', max_length=50, unique=True, null=True, blank=True)
    name = models.CharField('科室名称', max_length=100)
    remarks = models.TextField('备注', blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '科室'
        verbose_name_plural = '科室'
        ordering = ['code']
    
    def __str__(self):
        return self.name

class StaffType(models.Model):
    """工作人员类型模型"""
    code = models.CharField('类型编码', max_length=50, unique=True)
    name = models.CharField('类型名称', max_length=100)
    icon = models.CharField('图标', max_length=50, blank=True, help_text='图标CSS类名，例如：fa-user-md')
    display_order = models.IntegerField('显示顺序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '工作人员类型'
        verbose_name_plural = '工作人员类型'
        ordering = ['display_order', 'code']
    
    def __str__(self):
        return self.name

class Dictionary(models.Model):
    """字典类型模型"""
    code = models.CharField('字典编码', max_length=50, unique=True)
    name = models.CharField('字典名称', max_length=100)
    description = models.TextField('描述', blank=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_active = models.BooleanField('是否启用', default=True)

    class Meta:
        verbose_name = '字典类型'
        verbose_name_plural = '字典类型'
        ordering = ['code']

    def __str__(self):
        return f"{self.code} - {self.name}"

class DictionaryItem(models.Model):
    """字典项模型"""
    dictionary = models.ForeignKey(Dictionary, on_delete=models.CASCADE, related_name='items', verbose_name='所属字典')
    code = models.CharField('编码', max_length=50)
    name = models.CharField('名称', max_length=100)
    sort_order = models.IntegerField('排序', default=0)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '字典项'
        verbose_name_plural = '字典项'
        ordering = ['dictionary', 'sort_order', 'code']
        unique_together = [['dictionary', 'code']]

    def __str__(self):
        return f"{self.dictionary.name} - {self.name}"

class Staff(models.Model):
    """工作人员模型"""
    name = models.CharField('姓名', max_length=100)
    work_number = models.CharField('工号', max_length=50, unique=True)
    title = models.CharField('职称', max_length=100, null=True, blank=True)
    department = models.ForeignKey(Department, on_delete=models.PROTECT, related_name='staffs', verbose_name='所属部门', null=True, blank=True)
    staff_type = models.ForeignKey(StaffType, on_delete=models.PROTECT, related_name='staffs', verbose_name='人员类型', null=True, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    photo = models.ImageField(
        '照片',
        upload_to=staff_photo_path,
        null=True,
        blank=True,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']),
            validate_image_size
        ],
        help_text='请上传300x400像素以内的照片,大小不超过2MB,仅支持JPG/PNG格式'
    )

    def save(self, *args, **kwargs):
        if self.photo:
            # 在保存时自动调整图片尺寸
            img = Image.open(self.photo)
            if img.height > 400 or img.width > 300:
                output_size = (300, 400)
                img.thumbnail(output_size)
                # 保存调整后的图片
                img.save(self.photo.path)
        super().save(*args, **kwargs)

    class Meta:
        verbose_name = '工作人员'
        verbose_name_plural = '工作人员'
        ordering = ['work_number']

    def __str__(self):
        return f"{self.work_number} - {self.name}"

class Bed(models.Model):
    """床位模型"""
    number = models.CharField('床号', max_length=10, default='未知床位')
    department = models.ForeignKey(
        Department, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='beds',
        verbose_name='科室'
    )
    area = models.CharField('区域', max_length=1, choices=[('A', 'A区'), ('B', 'B区')], null=True, blank=True)
    is_active = models.BooleanField('是否启用', default=True)
    staff = models.ForeignKey(Staff, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='负责人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True, null=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True, null=True)

    class Meta:
        verbose_name = '床位'
        verbose_name_plural = '床位'
        ordering = ['number']
        # 添加联合唯一约束，确保床位号在同一科室内唯一
        unique_together = [['department', 'number']]

    def __str__(self):
        dept = self.department.name if self.department else '未知科室'
        return f"{dept} - {self.number}号床"

class QRCode(models.Model):
    """二维码模型"""
    name = models.CharField('名称', max_length=100)
    description = models.TextField('描述', blank=True)
    code = models.UUIDField('唯一编码', default=uuid.uuid4, editable=False)
    bed = models.OneToOneField(Bed, on_delete=models.CASCADE, null=True, blank=True, verbose_name='关联床位')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    is_active = models.BooleanField('是否启用', default=True)

    class Meta:
        verbose_name = '二维码'
        verbose_name_plural = '二维码'
        ordering = ['-created_at']

    def __str__(self):
        return self.name

    def get_qr_image_url(self):
        """获取二维码图片的URL"""
        # 不再使用预先生成的图片，改为动态生成
        return reverse('qrmanager:generate_qrcode_image', kwargs={'qrcode_id': self.id})
        
    def get_evaluation_url(self):
        """获取评价页面的URL"""
        # 使用加密参数生成更安全的URL
        encrypted_param = encrypt_qr_param(str(self.code))
        
        # 去除前缀等号
        if encrypted_param.startswith('='):
            encrypted_param = encrypted_param[1:]
        
        # 从系统配置中获取前端URL
        from .models import SystemConfig
        frontend_url = SystemConfig.get_value('frontend_url', 
                                       getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        
        # 使用唯一的短路径格式
        return f"{frontend_url}/q/{encrypted_param}"
    
    def get_secure_evaluation_url(self):
        """获取安全的评价页面URL（使用加密参数）"""
        # 如果未启用加密，则返回普通URL
        if not SECURITY_CONFIG['enable_encryption']:
            return self.get_evaluation_url()
            
        # 加密UUID
        encrypted_param = encrypt_qr_param(str(self.code))
        
        # 移除可能的前缀等号，在URL中可能会导致问题
        if encrypted_param.startswith('='):
            encrypted_param = encrypted_param[1:]
        
        # 从系统配置中获取前端URL
        from .models import SystemConfig
        frontend_url = SystemConfig.get_value('frontend_url', 
                                       getattr(settings, 'FRONTEND_URL', 'http://hospital.local'))
        
        # 使用唯一的短路径格式
        return f"{frontend_url}/q/{encrypted_param}"
    
    def get_qr_data_object(self):
        """获取二维码数据对象，用于生成新格式的二维码"""
        data = {
            'id': str(self.code),
            'type': 'bed' if self.bed else 'general',
            'target_id': str(self.bed.id) if self.bed else None,
            'name': self.name,
            'description': self.description
        }
        
        # 添加科室信息
        if self.bed and self.bed.department:
            data['department_id'] = self.bed.department.id
            data['department_name'] = self.bed.department.name
            
        # 添加位置信息
        if self.bed:
            data['location'] = f"{self.bed.department.name if self.bed.department else '未知科室'} - {self.bed.number}号床"
            
        return data

class Evaluation(models.Model):
    """评价模型"""
    # 修改为二元评价系统
    SATISFACTION_CHOICES = [
        (True, '满意'),
        (False, '不满意'),
    ]

    SENTIMENT_CHOICES = [
        ('negative', '负面'),
        ('neutral', '中性'),
        ('positive', '正面'),
    ]
    
    PROCESS_STATUS_CHOICES = [
        ('pending', '未处理'),
        ('processing', '处理中'),
        ('processed', '已处理'),
        ('flagged', '需关注'),
    ]

    # 保持与床位和二维码的关联
    bed = models.ForeignKey(Bed, on_delete=models.SET_NULL, related_name='evaluations', verbose_name='床位', null=True)
    qr_code = models.ForeignKey(QRCode, on_delete=models.SET_NULL, related_name='evaluations', verbose_name='二维码', null=True, blank=True)
    
    # 新增住院号和联系电话字段
    hospital_number = models.CharField('住院号', max_length=30, blank=True, null=True)
    phone_number = models.CharField('联系电话', max_length=20, blank=True, null=True)
    
    # 将评分改为满意度
    is_satisfied = models.BooleanField('是否满意', choices=SATISFACTION_CHOICES)
    
    # 保留其他字段
    comment = models.TextField('评价原因', blank=True)
    photo = models.ImageField('照片', upload_to='evaluation_photos/', blank=True)
    sentiment = models.CharField('情感倾向', max_length=10, choices=SENTIMENT_CHOICES, default='neutral')
    created_at = models.DateTimeField('评价时间', auto_now_add=True)
    
    # 处理状态相关字段
    process_status = models.CharField('处理状态', max_length=15, choices=PROCESS_STATUS_CHOICES, default='pending')
    processed_by = models.ForeignKey(
        settings.AUTH_USER_MODEL, 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True, 
        related_name='processed_evaluations', 
        verbose_name='处理人'
    )
    processed_at = models.DateTimeField('处理时间', null=True, blank=True)
    process_notes = models.TextField('处理备注', blank=True)
    
    # 添加工作人员评价信息 - 直接存储在评价表中
    # 满意工作人员 - 最多3个
    satisfied_staff1_id = models.IntegerField('满意工作人员1 ID', null=True, blank=True)
    satisfied_staff1_name = models.CharField('满意工作人员1 姓名', max_length=100, blank=True, default='')
    satisfied_staff1_title = models.CharField('满意工作人员1 职称', max_length=100, blank=True, default='')
    
    satisfied_staff2_id = models.IntegerField('满意工作人员2 ID', null=True, blank=True)
    satisfied_staff2_name = models.CharField('满意工作人员2 姓名', max_length=100, blank=True, default='')
    satisfied_staff2_title = models.CharField('满意工作人员2 职称', max_length=100, blank=True, default='')
    
    satisfied_staff3_id = models.IntegerField('满意工作人员3 ID', null=True, blank=True)
    satisfied_staff3_name = models.CharField('满意工作人员3 姓名', max_length=100, blank=True, default='')
    satisfied_staff3_title = models.CharField('满意工作人员3 职称', max_length=100, blank=True, default='')
    
    # 不满意工作人员 - 最多3个
    unsatisfied_staff1_id = models.IntegerField('不满意工作人员1 ID', null=True, blank=True)
    unsatisfied_staff1_name = models.CharField('不满意工作人员1 姓名', max_length=100, blank=True, default='')
    unsatisfied_staff1_title = models.CharField('不满意工作人员1 职称', max_length=100, blank=True, default='')
    
    unsatisfied_staff2_id = models.IntegerField('不满意工作人员2 ID', null=True, blank=True)
    unsatisfied_staff2_name = models.CharField('不满意工作人员2 姓名', max_length=100, blank=True, default='')
    unsatisfied_staff2_title = models.CharField('不满意工作人员2 职称', max_length=100, blank=True, default='')
    
    unsatisfied_staff3_id = models.IntegerField('不满意工作人员3 ID', null=True, blank=True)
    unsatisfied_staff3_name = models.CharField('不满意工作人员3 姓名', max_length=100, blank=True, default='')
    unsatisfied_staff3_title = models.CharField('不满意工作人员3 职称', max_length=100, blank=True, default='')

    class Meta:
        verbose_name = '评价'
        verbose_name_plural = '评价'
        ordering = ['-created_at']

    def __str__(self):
        staff_name = "无工作人员"
        # 显示第一个满意或不满意工作人员的名字
        if self.satisfied_staff1_name:
            staff_name = self.satisfied_staff1_name
            status = '满意'
        elif self.unsatisfied_staff1_name:
            staff_name = self.unsatisfied_staff1_name
            status = '不满意'
        else:
            status = '未知'
        
        return f"{staff_name} - {status}"
    
    @property
    def satisfied_staff(self):
        """获取满意的工作人员列表"""
        staff_list = []
        
        for i in range(1, 4):
            staff_id = getattr(self, f'satisfied_staff{i}_id')
            if staff_id:
                staff_name = getattr(self, f'satisfied_staff{i}_name', '')
                staff_title = getattr(self, f'satisfied_staff{i}_title', '')
                # 创建一个类似StaffEvaluation的对象，保持API兼容性
                staff_obj = type('StaffEvalObj', (), {
                    'staff': type('StaffObj', (), {
                        'id': staff_id,
                        'name': staff_name,
                        'title': staff_title
                    })
                })
                staff_list.append(staff_obj)
        
        return staff_list
    
    @property
    def unsatisfied_staff(self):
        """获取不满意的工作人员列表"""
        staff_list = []
        
        for i in range(1, 4):
            staff_id = getattr(self, f'unsatisfied_staff{i}_id')
            if staff_id:
                staff_name = getattr(self, f'unsatisfied_staff{i}_name', '')
                staff_title = getattr(self, f'unsatisfied_staff{i}_title', '')
                # 创建一个类似StaffEvaluation的对象，保持API兼容性
                staff_obj = type('StaffEvalObj', (), {
                    'staff': type('StaffObj', (), {
                        'id': staff_id,
                        'name': staff_name,
                        'title': staff_title
                    })
                })
                staff_list.append(staff_obj)
        
        return staff_list
        
    def save(self, *args, **kwargs):
        # 自动分析情感
        if not self.sentiment:
            if not self.is_satisfied:
                self.sentiment = 'negative'
            else:
                self.sentiment = 'positive'
        super().save(*args, **kwargs)
        
    def process(self, user, status, notes=None):
        """更新评价处理状态"""
        self.process_status = status
        self.processed_by = user
        self.processed_at = timezone.now()
        
        if notes:
            self.process_notes = notes
            
        self.save()
        return self
    
    def add_staff_evaluation(self, staff_id, staff_name, staff_title='', is_satisfied=True):
        """添加工作人员评价"""
        # 确定要添加到哪个位置
        if is_satisfied:
            prefix = 'satisfied_staff'
            max_count = 3
        else:
            prefix = 'unsatisfied_staff'
            max_count = 3
            
        # 查找第一个空位
        for i in range(1, max_count + 1):
            field_name = f'{prefix}{i}_id'
            if getattr(self, field_name) is None:
                # 找到空位，添加数据
                setattr(self, field_name, staff_id)
                setattr(self, f'{prefix}{i}_name', staff_name)
                setattr(self, f'{prefix}{i}_title', staff_title)
                self.save()
                return True
        
        # 没有空位，返回失败
        return False

class DeviceFingerprint(models.Model):
    """
    设备指纹模型（可选）：
    可单独记录所有详情信息，用于安全审核和防刷。
    """
    evaluation = models.OneToOneField(Evaluation, on_delete=models.CASCADE, related_name="fingerprint_info", verbose_name="评价")
    fingerprint = models.CharField(max_length=200, verbose_name="指纹信息")

    def __str__(self) -> str:
        return f"设备指纹记录：{self.fingerprint}"

# 新增操作日志模型，用于记录用户操作
class OperationLog(models.Model):
    """操作日志模型，用于记录用户操作"""
    STATUS_CHOICES = [
        ('success', '成功'),
        ('error', '失败'),
        ('warning', '警告'),
    ]

    user = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name="操作用户")
    action = models.CharField("操作", max_length=100)
    description = models.TextField("操作描述", blank=True)
    created_at = models.DateTimeField("操作时间", auto_now_add=True)
    ip_address = models.GenericIPAddressField("IP地址", null=True, blank=True)
    status = models.CharField("状态", max_length=10, choices=STATUS_CHOICES, default='success')
    browser = models.CharField("浏览器", max_length=200, null=True, blank=True)
    os = models.CharField("操作系统", max_length=200, null=True, blank=True)
    extra_data = models.JSONField("额外数据", null=True, blank=True)

    class Meta:
        verbose_name = "操作日志"
        verbose_name_plural = "操作日志"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.created_at} - {self.user}: {self.action}"

    @property
    def get_status_display(self):
        return dict(self.STATUS_CHOICES).get(self.status, self.status)

# 新增日志配置模型
class LoggingConfiguration(models.Model):
    """日志配置模型，用于控制哪些操作类型需要记录日志"""
    
    # 常见操作类型选项
    ACTION_CHOICES = [
        ('create', '创建操作'),
        ('update', '更新操作'),
        ('delete', '删除操作'),
        ('login', '登录操作'),
        ('logout', '退出操作'),
        ('view', '查看操作'),
        ('export', '导出操作'),
        ('import', '导入操作'),
        ('print', '打印操作'),
        ('generate', '生成操作'),
        ('permission_change', '权限变更'),
        ('system_config', '系统配置'),
        ('api_request', 'API请求'),
        ('file_upload', '文件上传'),
        ('file_download', '文件下载'),
        ('bulk_operation', '批量操作'),
        ('error', '错误操作'),
    ]
    
    action_type = models.CharField("操作类型", max_length=50, choices=ACTION_CHOICES, unique=True)
    is_enabled = models.BooleanField("是否记录", default=True)
    description = models.TextField("说明", blank=True)
    created_at = models.DateTimeField("创建时间", auto_now_add=True)
    updated_at = models.DateTimeField("更新时间", auto_now=True)
    
    class Meta:
        verbose_name = "日志配置"
        verbose_name_plural = "日志配置"
        ordering = ["action_type"]
    
    def __str__(self):
        status = "启用" if self.is_enabled else "禁用"
        return f"{self.get_action_type_display()} ({status})"

class PrintTemplate(models.Model):
    """打印模板模型"""
    department = models.OneToOneField(Department, on_delete=models.CASCADE, related_name='print_template', verbose_name='科室', null=True, blank=True)
    name = models.CharField('模板名称', max_length=100)
    background_image = models.ImageField(
        '背景图片',
        upload_to=template_image_path,
        validators=[
            FileExtensionValidator(allowed_extensions=['jpg', 'jpeg', 'png']),
            validate_image_size
        ],
        help_text='支持JPG/PNG格式，大小不超过10MB'
    )
    print_width = models.DecimalField('打印宽度', max_digits=5, decimal_places=1, default=210.0, help_text='单位：毫米')
    print_height = models.DecimalField('打印高度', max_digits=5, decimal_places=1, default=297.0, help_text='单位：毫米')
    qr_position_x = models.IntegerField('二维码X坐标', default=105, help_text='单位：毫米，从左边开始计算')
    qr_position_y = models.IntegerField('二维码Y坐标', default=148, help_text='单位：毫米，从上边开始计算')
    qr_size = models.IntegerField('二维码尺寸', default=20, help_text='单位：毫米，建议20-30毫米，过大会遮挡背景，过小可能难以扫描')
    is_active = models.BooleanField('是否启用', default=True)
    is_public = models.BooleanField('是否为公共模板', default=False, help_text='设置为公共模板后，所有未设置自定义模板的科室将使用此模板')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)

    class Meta:
        verbose_name = '打印模板'
        verbose_name_plural = '打印模板'
        ordering = ['department__name']

    def __str__(self):
        if self.is_public:
            return f"公共模板：{self.name}"
        return f"{self.department.name}的打印模板" if self.department else self.name

    def save(self, *args, **kwargs):
        # 如果设置为公共模板，则清除科室关联
        if self.is_public:
            self.department = None
            
        # 检查是否是更新操作（已有ID）
        if self.pk:
            try:
                # 获取数据库中的旧记录
                old_instance = PrintTemplate.objects.get(pk=self.pk)
                
                # 如果上传了新的背景图片，且旧记录有背景图片
                if self.background_image and old_instance.background_image and self.background_image != old_instance.background_image:
                    # 删除旧的背景图片文件
                    if os.path.exists(old_instance.background_image.path):
                        os.remove(old_instance.background_image.path)
            except (PrintTemplate.DoesNotExist, Exception) as e:
                # 记录错误但不中断保存过程
                print(f"清理旧模板文件时出错: {str(e)}")
        else:
            # 新建模板时，检查是否已存在同科室的模板文件
            try:
                # 确定科室编码
                department_code = self.department.code if self.department else 'public'
                
                # 构建可能存在的旧文件路径模式
                import glob
                
                template_dir = os.path.join(settings.MEDIA_ROOT, 'print_templates')
                pattern = os.path.join(template_dir, f"template_{department_code}.*")
                
                # 查找匹配的文件并删除
                for old_file in glob.glob(pattern):
                    if os.path.exists(old_file) and os.path.basename(old_file) != os.path.basename(self.background_image.name):
                        os.remove(old_file)
                        print(f"删除旧模板文件: {old_file}")
            except Exception as e:
                print(f"清理旧模板文件时出错: {str(e)}")
                
        super().save(*args, **kwargs)

    @classmethod
    def get_default_template(cls):
        """获取默认的公共模板"""
        return cls.objects.filter(is_public=True, is_active=True).first()

class APIKey(models.Model):
    """API密钥模型，用于管理API访问权限"""
    name = models.CharField(max_length=100, verbose_name="名称", help_text='API密钥用途描述')
    key = models.CharField(max_length=64, unique=True, db_index=True, verbose_name="密钥")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    expires_at = models.DateTimeField(null=True, blank=True, verbose_name="过期时间", help_text='留空表示永不过期')
    is_active = models.BooleanField(default=True, verbose_name="是否启用")
    rate_limit_day = models.IntegerField(default=1000, verbose_name="每日请求限制", help_text='0表示无限制')
    rate_limit_hour = models.IntegerField(default=100, verbose_name="每小时请求限制", help_text='0表示无限制')
    rate_limit_minute = models.IntegerField(default=10, verbose_name="每分钟请求限制", help_text='0表示无限制')
    last_used_at = models.DateTimeField(null=True, blank=True, verbose_name="最后使用时间")
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, related_name='created_api_keys', verbose_name="创建人")
    allowed_ips = models.TextField(blank=True, null=True, verbose_name="允许的IP地址", help_text="多个IP地址用逗号分隔")
    
    # 权限控制
    can_read = models.BooleanField(default=True, verbose_name="读取权限")
    can_write = models.BooleanField(default=False, verbose_name="写入权限")
    can_delete = models.BooleanField(default=False, verbose_name="删除权限")
    
    # 资源访问控制
    can_access_departments = models.BooleanField(default=False, verbose_name="访问科室")
    can_access_beds = models.BooleanField(default=False, verbose_name="访问床位")
    can_access_staff = models.BooleanField(default=False, verbose_name="访问员工")
    can_access_qrcodes = models.BooleanField(default=False, verbose_name="访问二维码")
    can_access_evaluations = models.BooleanField(default=False, verbose_name="访问评价")
    
    class Meta:
        verbose_name = "API密钥"
        verbose_name_plural = "API密钥"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.name} ({self.key[:8]}...)"
    
    def save(self, *args, **kwargs):
        if not self.key:
            self.key = secrets.token_hex(32)  # 生成64字符的随机密钥
        super().save(*args, **kwargs)
    
    def is_expired(self):
        if not self.expires_at:
            return False
        return timezone.now() > self.expires_at
    
    def is_valid(self, ip_address=None):
        """检查API密钥是否有效"""
        if not self.is_active:
            return False
        
        if self.is_expired():
            return False
            
        if ip_address and self.allowed_ips:
            allowed_ips_list = [ip.strip() for ip in self.allowed_ips.split(',')]
            if ip_address not in allowed_ips_list:
                return False
                
        return True


class APILog(models.Model):
    """API访问日志模型"""
    api_key = models.ForeignKey(APIKey, on_delete=models.SET_NULL, null=True, blank=True, related_name='logs', verbose_name="API密钥")
    endpoint = models.CharField(max_length=200, verbose_name="接口")
    method = models.CharField(max_length=10, verbose_name="请求方法")
    status_code = models.IntegerField(verbose_name="状态码")
    status = models.CharField(max_length=10, choices=[('success', '成功'), ('error', '错误')], default='success', verbose_name="状态")
    response_time = models.FloatField(default=0, verbose_name="响应时间(ms)")
    remote_addr = models.GenericIPAddressField(null=True, blank=True, verbose_name="IP地址")
    user_agent = models.TextField(blank=True, verbose_name="用户代理")
    request_data = models.JSONField(null=True, blank=True, verbose_name="请求数据")
    error_message = models.TextField(blank=True, verbose_name="错误信息")
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="调用时间")
    
    class Meta:
        verbose_name = "API调用日志"
        verbose_name_plural = "API调用日志"
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.endpoint} - {self.status_code} - {self.created_at}"

class QRCodeHistory(models.Model):
    """二维码历史记录模型，记录二维码的变更历史"""
    bed = models.ForeignKey(Bed, on_delete=models.CASCADE, related_name='qrcode_history', verbose_name='床位')
    old_code = models.UUIDField('旧二维码编码')
    new_code = models.UUIDField('新二维码编码')
    reason = models.CharField('更换原因', max_length=200, blank=True)
    created_by = models.ForeignKey(User, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='操作人')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    is_security_issue = models.BooleanField('是否安全问题', default=False, help_text='标记为安全问题的二维码将被禁用')

    class Meta:
        verbose_name = '二维码历史'
        verbose_name_plural = '二维码历史'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.bed} - {self.created_at.strftime('%Y-%m-%d %H:%M')}"

class SystemConfig(models.Model):
    """系统配置模型，用于存储全局配置项"""
    key = models.CharField('配置键', max_length=50, unique=True)
    value = models.TextField('配置值')
    description = models.TextField('描述', blank=True)
    is_public = models.BooleanField('是否公开', default=False, help_text='公开配置可在前端显示')
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    updated_at = models.DateTimeField('更新时间', auto_now=True)
    
    class Meta:
        verbose_name = '系统配置'
        verbose_name_plural = '系统配置'
        ordering = ['key']
    
    def __str__(self):
        return f"{self.key}: {self.value}"
    
    @classmethod
    def get_value(cls, key, default=None):
        """获取配置值，如果不存在则返回默认值"""
        try:
            config = cls.objects.get(key=key)
            return config.value
        except cls.DoesNotExist:
            return default
    
    @classmethod
    def set_value(cls, key, value, description=None, is_public=False):
        """设置配置值，如果不存在则创建"""
        config, created = cls.objects.get_or_create(
            key=key,
            defaults={
                'value': value,
                'description': description or '',
                'is_public': is_public
            }
        )
        if not created:
            config.value = value
            if description:
                config.description = description
            config.is_public = is_public
            config.save()
        return config 

class TempToken(models.Model):
    """临时令牌模型，用于二维码验证后提交评价"""
    token = models.CharField('令牌', max_length=64, unique=True)
    qr_code = models.ForeignKey(QRCode, on_delete=models.CASCADE, verbose_name='关联二维码')
    qr_param = models.CharField('加密参数', max_length=512)  
    created_at = models.DateTimeField('创建时间', auto_now_add=True)
    expires_at = models.DateTimeField('过期时间')
    is_used = models.BooleanField('是否已使用', default=False)
    used_at = models.DateTimeField('使用时间', null=True, blank=True)
    ip_address = models.GenericIPAddressField('IP地址', null=True, blank=True)
    
    class Meta:
        verbose_name = '临时令牌'
        verbose_name_plural = verbose_name
        ordering = ['-created_at']
    
    def __str__(self):
        return f"{self.token[:8]}... ({self.qr_code})"
    
    def is_valid(self):
        """检查令牌是否有效（未过期且未使用）"""
        now = timezone.now()
        return not self.is_used and now <= self.expires_at
    
    def mark_as_used(self):
        """标记令牌为已使用"""
        self.is_used = True
        self.used_at = timezone.now()
        self.save(update_fields=['is_used', 'used_at'])
    
    @classmethod
    def generate_token(cls, qr_param, qr_code, ip_address=None):
        """
        生成或获取临时令牌
        如果存在同一加密参数的有效令牌，则直接返回
        否则创建新令牌
        
        参数:
            qr_param: 原始加密参数
            qr_code: 关联的二维码对象
            ip_address: 客户端IP地址
            
        返回:
            TempToken: 临时令牌对象
        """
        # 先检查是否存在有效的令牌
        now = timezone.now()
        existing_token = cls.objects.filter(
            qr_param=qr_param,
            qr_code=qr_code,
            is_used=False,
            expires_at__gt=now
        ).order_by('-created_at').first()
        
        if existing_token:
            return existing_token
        
        # 如果没有有效的令牌，创建新令牌
        token = uuid.uuid4().hex
        
        # 设置过期时间
        expires_at = now + timedelta(minutes=settings.TEMP_TOKEN_EXPIRY_MINUTES)
        
        # 创建新令牌
        temp_token = cls.objects.create(
            token=token,
            qr_code=qr_code,
            qr_param=qr_param,
            expires_at=expires_at,
            ip_address=ip_address
        )
        
        return temp_token 

# 临时添加的StaffEvaluation类，作为过渡解决方案
# 该类不应被用于实际存储数据，仅用于兼容旧版代码
class StaffEvaluation(models.Model):
    """
    工作人员评价关联表（已弃用）
    
    注意：该模型已被弃用，仅作为过渡兼容保留
    现在所有工作人员评价信息已直接存储在Evaluation模型中
    """
    evaluation = models.ForeignKey(Evaluation, on_delete=models.CASCADE, verbose_name="评价")
    staff = models.ForeignKey(Staff, on_delete=models.CASCADE, verbose_name="工作人员")
    is_satisfied = models.BooleanField("是否满意", default=True)
    created_at = models.DateTimeField("创建时间", default=timezone.now)
    
    class Meta:
        verbose_name = "工作人员评价关联(已弃用)"
        verbose_name_plural = "工作人员评价关联(已弃用)"
        # 添加唯一约束，确保一个评价只评价一个工作人员一次
        unique_together = ('evaluation', 'staff')
        
    def __str__(self):
        status = "满意" if self.is_satisfied else "不满意"
        return f"{self.staff.name} - {status}" 