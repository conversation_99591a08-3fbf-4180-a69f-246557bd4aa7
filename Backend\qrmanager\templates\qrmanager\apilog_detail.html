{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{{ title }}{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4">{{ title }}</h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">首页</a></li>
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:apilog_list' %}">API访问日志</a></li>
        <li class="breadcrumb-item active">日志详情</li>
    </ol>
    
    <div class="card mb-4">
        <div class="card-header">
            <i class="fas fa-file-alt me-1"></i>
            API日志详情
            <a href="{% url 'qrmanager:apilog_list' %}" class="btn btn-secondary btn-sm float-end">
                <i class="fas fa-arrow-left"></i> 返回列表
            </a>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-primary text-white">
                            <i class="fas fa-info-circle me-1"></i> 基本信息
                        </div>
                        <div class="card-body">
                            <table class="table table-striped">
                                <tbody>
                                    <tr>
                                        <th style="width: 30%">日志ID</th>
                                        <td>{{ apilog.id }}</td>
                                    </tr>
                                    <tr>
                                        <th>时间</th>
                                        <td>{{ apilog.timestamp|date:"Y-m-d H:i:s" }}</td>
                                    </tr>
                                    <tr>
                                        <th>API密钥</th>
                                        <td>
                                            {% if apilog.api_key %}
                                                <a href="{% url 'qrmanager:apikey_update' apilog.api_key.id %}">{{ apilog.api_key.name }}</a>
                                            {% else %}
                                                <span class="text-muted">未使用密钥</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>访问端点</th>
                                        <td>{{ apilog.endpoint }}</td>
                                    </tr>
                                    <tr>
                                        <th>请求方法</th>
                                        <td>
                                            {% if apilog.method == 'GET' %}
                                                <span class="badge bg-success">GET</span>
                                            {% elif apilog.method == 'POST' %}
                                                <span class="badge bg-primary">POST</span>
                                            {% elif apilog.method == 'PUT' %}
                                                <span class="badge bg-warning">PUT</span>
                                            {% elif apilog.method == 'DELETE' %}
                                                <span class="badge bg-danger">DELETE</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ apilog.method }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>状态码</th>
                                        <td>
                                            {% if apilog.response_status >= 200 and apilog.response_status < 300 %}
                                                <span class="badge bg-success">{{ apilog.response_status }}</span>
                                            {% elif apilog.response_status >= 400 and apilog.response_status < 500 %}
                                                <span class="badge bg-warning">{{ apilog.response_status }}</span>
                                            {% elif apilog.response_status >= 500 %}
                                                <span class="badge bg-danger">{{ apilog.response_status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ apilog.response_status }}</span>
                                            {% endif %}
                                        </td>
                                    </tr>
                                    <tr>
                                        <th>响应时间</th>
                                        <td>{{ apilog.response_time|floatformat:3 }} 秒</td>
                                    </tr>
                                    <tr>
                                        <th>IP地址</th>
                                        <td>{{ apilog.ip_address }}</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                
                <div class="col-md-6">
                    <div class="card mb-3">
                        <div class="card-header bg-info text-white">
                            <i class="fas fa-user-agent me-1"></i> 用户代理
                        </div>
                        <div class="card-body">
                            <div class="border p-2 bg-light">
                                <pre class="mb-0" style="white-space: pre-wrap;">{{ apilog.user_agent|default:"未记录用户代理信息" }}</pre>
                            </div>
                        </div>
                    </div>
                    
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <i class="fas fa-exchange-alt me-1"></i> 请求数据
                        </div>
                        <div class="card-body">
                            <div class="border p-2 bg-light">
                                <pre class="mb-0" style="white-space: pre-wrap;">{{ apilog.request_data|default:"未记录请求数据" }}</pre>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 