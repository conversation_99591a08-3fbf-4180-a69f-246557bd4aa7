#!/usr/bin/env python
"""
测试加密解密的稳定性 - 确保同一个UUID总是生成相同的加密字符串
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid

def test_encryption_stability():
    """测试加密的稳定性 - 同一个UUID必须总是生成相同的加密字符串"""
    print("=" * 60)
    print("🔒 加密稳定性测试 - 关键测试！")
    print("=" * 60)
    
    # 测试多个UUID
    test_uuids = [
        str(uuid.uuid4()),
        str(uuid.uuid4()),
        str(uuid.uuid4()),
        "12345678-1234-1234-1234-123456789012",  # 标准格式
        "abcdefgh-abcd-abcd-abcd-abcdefghijkl",  # 字母格式
    ]
    
    all_stable = True
    
    for i, test_uuid in enumerate(test_uuids, 1):
        print(f"\n测试 {i}: {test_uuid}")
        
        # 对同一个UUID进行多次加密，检查结果是否一致
        encrypted_results = []
        for attempt in range(10):  # 进行10次加密
            encrypted = encrypt_qr_param(test_uuid)
            encrypted_results.append(encrypted)
        
        # 检查所有结果是否相同
        first_result = encrypted_results[0]
        is_stable = all(result == first_result for result in encrypted_results)
        
        print(f"   加密结果: {first_result}")
        print(f"   稳定性: {'✓ 稳定' if is_stable else '✗ 不稳定'}")
        
        if not is_stable:
            print(f"   ⚠️  发现不稳定！不同的加密结果:")
            for j, result in enumerate(encrypted_results):
                if result != first_result:
                    print(f"      尝试 {j+1}: {result}")
            all_stable = False
        
        # 测试解密是否也稳定
        try:
            decrypted = decrypt_qr_param(first_result)
            decrypt_success = decrypted['uuid'] == test_uuid
            print(f"   解密验证: {'✓ 成功' if decrypt_success else '✗ 失败'}")
            
            if not decrypt_success:
                print(f"      期望: {test_uuid}")
                print(f"      实际: {decrypted['uuid']}")
                all_stable = False
        except Exception as e:
            print(f"   解密验证: ✗ 异常 - {e}")
            all_stable = False
    
    print(f"\n" + "=" * 60)
    if all_stable:
        print("🎉 所有测试通过！加密解密完全稳定！")
    else:
        print("🚨 发现稳定性问题！需要立即修复！")
    print("=" * 60)
    
    return all_stable

def test_edge_cases():
    """测试边缘情况"""
    print("\n" + "=" * 60)
    print("🔍 边缘情况测试")
    print("=" * 60)
    
    edge_cases = [
        "00000000-0000-0000-0000-000000000000",  # 全零UUID
        "ffffffff-ffff-ffff-ffff-ffffffffffff",  # 全F UUID
        "12345678-90ab-cdef-1234-567890abcdef",  # 混合字符
    ]
    
    for test_uuid in edge_cases:
        print(f"\n边缘测试: {test_uuid}")
        
        try:
            # 多次加密检查稳定性
            results = [encrypt_qr_param(test_uuid) for _ in range(5)]
            is_stable = all(r == results[0] for r in results)
            
            print(f"   加密结果: {results[0]}")
            print(f"   稳定性: {'✓ 稳定' if is_stable else '✗ 不稳定'}")
            
            # 解密验证
            decrypted = decrypt_qr_param(results[0])
            decrypt_ok = decrypted['uuid'] == test_uuid
            print(f"   解密验证: {'✓ 成功' if decrypt_ok else '✗ 失败'}")
            
        except Exception as e:
            print(f"   错误: {e}")

if __name__ == "__main__":
    stability_ok = test_encryption_stability()
    test_edge_cases()
    
    if not stability_ok:
        print("\n🚨 警告：发现稳定性问题，需要立即修复！")
        print("   建议：检查盐值生成逻辑，确保确定性")
    else:
        print("\n✅ 系统稳定性验证通过，可以安全打印二维码！")
