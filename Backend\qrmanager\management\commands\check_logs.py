from django.core.management.base import BaseCommand
from qrmanager.models import APILog, OperationLog
from django.utils import timezone
import datetime

class Command(BaseCommand):
    help = '查看最新的系统日志'

    def handle(self, *args, **options):
        self.stdout.write('查看最新的系统日志...')
        
        # 获取最近5分钟的日志
        time_threshold = timezone.now() - datetime.timedelta(minutes=5)
        
        # 检查是否有APILog模型
        try:
            self.stdout.write('尝试检查APILog日志...')
            log_entries = APILog.objects.filter(created_at__gte=time_threshold).order_by('-created_at')
            
            if log_entries.exists():
                self.stdout.write(f'找到{log_entries.count()}条APILog日志记录')
                for log in log_entries[:10]:
                    self.stdout.write(f'时间: {log.created_at}, 请求路径: {log.endpoint}, 状态码: {log.status_code}')
                    if hasattr(log, 'request_data') and log.request_data:
                        self.stdout.write(f'  请求数据: {log.request_data[:100]}...')
                    if hasattr(log, 'status') and log.status:
                        self.stdout.write(f'  状态: {log.status}')
                    if hasattr(log, 'error_message') and log.error_message:
                        self.stdout.write(f'  错误信息: {log.error_message[:100]}...')
            else:
                self.stdout.write('未找到最近5分钟的APILog日志记录')
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'检查APILog日志出错: {str(e)}'))
        
        # 检查是否有OperationLog模型
        try:
            self.stdout.write('\n尝试检查OperationLog日志...')
            op_logs = OperationLog.objects.filter(created_at__gte=time_threshold).order_by('-created_at')
            
            if op_logs.exists():
                self.stdout.write(f'找到{op_logs.count()}条OperationLog日志记录')
                for log in op_logs[:10]:
                    self.stdout.write(f'时间: {log.created_at}, 用户: {log.user.username if log.user else "匿名"}, 动作: {log.action}, 状态: {log.status}')
                    if hasattr(log, 'description') and log.description:
                        self.stdout.write(f'  描述: {log.description[:100]}...')
                    if hasattr(log, 'extra_data') and log.extra_data:
                        self.stdout.write(f'  附加数据: {log.extra_data[:100]}...')
            else:
                self.stdout.write('未找到最近5分钟的OperationLog日志记录')
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'检查OperationLog日志出错: {str(e)}'))
            
        # 设置更宽的时间范围，查看最近30分钟的操作日志
        wide_time_threshold = timezone.now() - datetime.timedelta(minutes=30)
        try:
            self.stdout.write('\n查看最近30分钟的操作日志...')
            wider_op_logs = OperationLog.objects.filter(created_at__gte=wide_time_threshold).order_by('-created_at')
            
            if wider_op_logs.exists():
                self.stdout.write(f'找到{wider_op_logs.count()}条OperationLog日志记录')
                for log in wider_op_logs[:20]:
                    self.stdout.write(f'时间: {log.created_at}, 动作: {log.action}, 状态: {log.status}')
                    if 'API' in log.action or '提交评价' in log.action:
                        self.stdout.write(self.style.SUCCESS(f'  发现API相关操作: {log.description}'))
                        if hasattr(log, 'extra_data') and log.extra_data:
                            self.stdout.write(f'  附加数据: {log.extra_data}')
            else:
                self.stdout.write('未找到最近30分钟的OperationLog日志记录')
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'检查更宽时间范围的操作日志出错: {str(e)}'))
            
        # 查看是否有API日志文件
        self.stdout.write('\n尝试检查API日志文件...')
        try:
            from django.conf import settings
            import os
            
            log_dir = getattr(settings, 'LOG_DIR', os.path.join(settings.BASE_DIR, 'logs'))
            api_log_file = os.path.join(log_dir, 'api.log')
            
            if os.path.exists(api_log_file):
                self.stdout.write(f'找到API日志文件: {api_log_file}')
                
                # 读取最后50行
                with open(api_log_file, 'r', encoding='utf-8', errors='ignore') as f:
                    lines = f.readlines()
                    last_lines = lines[-50:] if len(lines) > 50 else lines
                    
                    self.stdout.write('最近的API日志内容:')
                    for line in last_lines:
                        self.stdout.write(f'  {line.strip()}')
            else:
                self.stdout.write('未找到API日志文件')
        
        except Exception as e:
            self.stdout.write(self.style.ERROR(f'检查API日志文件出错: {str(e)}')) 