#!/usr/bin/env python
"""
测试安全监控页面功能
"""
import os
import sys
import django
import requests
from datetime import datetime

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.contrib.auth.models import User
from django.test import Client
from qrmanager.models import QRCode
from qrmanager.security import encrypt_qr_param

def test_security_monitoring_page():
    """测试安全监控页面"""
    print("🔍 测试安全监控页面功能")
    print("=" * 50)
    
    # 创建测试客户端
    client = Client()
    
    # 获取或创建管理员用户
    try:
        admin_user = User.objects.get(username='admin')
    except User.DoesNotExist:
        admin_user = User.objects.create_superuser('admin', '<EMAIL>', 'admin123')
        print("✅ 创建了测试管理员用户")
    
    # 登录
    login_success = client.login(username='admin', password='admin123')
    if login_success:
        print("✅ 管理员登录成功")
    else:
        print("❌ 管理员登录失败")
        return False
    
    # 生成一些安全活动数据
    print("\n📊 生成测试安全数据...")
    
    # 获取测试二维码
    qrcode_obj = QRCode.objects.first()
    if qrcode_obj:
        encrypted_param = encrypt_qr_param(qrcode_obj.code)
        
        # 发送一些API请求来生成安全记录
        base_url = "http://127.0.0.1:8000"
        verify_url = f"{base_url}/api/v1/public/qrcode/verify/"
        
        for i in range(5):
            try:
                response = requests.post(
                    verify_url,
                    json={"qr_param": encrypted_param, "client_ip": "127.0.0.1"},
                    headers={'Content-Type': 'application/json'},
                    timeout=3
                )
                print(f"   请求 {i+1}: {response.status_code}")
            except Exception as e:
                print(f"   请求 {i+1}: 异常 - {e}")
    
    # 测试安全监控页面访问
    print("\n🔒 测试安全监控页面访问...")
    
    try:
        response = client.get('/security/monitoring/')
        
        if response.status_code == 200:
            print("✅ 安全监控页面访问成功")
            
            # 检查页面内容
            content = response.content.decode('utf-8')
            
            checks = [
                ('安全监控中心', '页面标题'),
                ('活跃二维码限制', '安全概览'),
                ('安全事件数量', '事件统计'),
                ('最近安全事件', '事件列表'),
                ('访问最多的二维码', '访问统计'),
                ('IP访问统计', 'IP统计'),
                ('速率限制统计', '限制统计'),
            ]
            
            for check_text, description in checks:
                if check_text in content:
                    print(f"   ✅ {description}: 包含 '{check_text}'")
                else:
                    print(f"   ❌ {description}: 缺少 '{check_text}'")
            
            return True
            
        else:
            print(f"❌ 安全监控页面访问失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 安全监控页面测试异常: {e}")
        return False

def test_security_api_endpoints():
    """测试安全API端点"""
    print("\n🔧 测试安全API端点...")
    
    client = Client()
    
    # 登录
    client.login(username='admin', password='admin123')
    
    # 测试二维码安全详情API
    qrcode_obj = QRCode.objects.first()
    if qrcode_obj:
        try:
            response = client.get(f'/security/qrcodes/{qrcode_obj.code}/')
            if response.status_code == 200:
                print("   ✅ 二维码安全详情API正常")
                data = response.json()
                if data.get('success'):
                    print(f"      UUID: {data['qrcode_info']['uuid'][:8]}...")
                    print(f"      床位: {data['qrcode_info']['bed_info']}")
            else:
                print(f"   ❌ 二维码安全详情API异常: {response.status_code}")
        except Exception as e:
            print(f"   ❌ 二维码安全详情API异常: {e}")
    
    # 测试IP安全详情API
    try:
        response = client.get('/security/ips/127.0.0.1/')
        if response.status_code == 200:
            print("   ✅ IP安全详情API正常")
            data = response.json()
            if data.get('success'):
                profile = data['ip_profile']
                print(f"      IP: {profile['ip']}")
                print(f"      风险评分: {profile['risk_score']}")
        else:
            print(f"   ❌ IP安全详情API异常: {response.status_code}")
    except Exception as e:
        print(f"   ❌ IP安全详情API异常: {e}")

def main():
    """主函数"""
    print("🚀 开始安全监控页面测试")
    print("=" * 60)
    
    # 测试页面功能
    page_success = test_security_monitoring_page()
    
    # 测试API端点
    test_security_api_endpoints()
    
    print("\n" + "=" * 60)
    print("📊 测试结果总结")
    print("=" * 60)
    
    if page_success:
        print("🎉 安全监控页面测试通过！")
        print("✅ 页面可以正常访问")
        print("✅ 安全数据正常显示")
        print("✅ API端点正常工作")
        print("\n🔗 访问地址: http://127.0.0.1:8000/security/monitoring/")
        print("📋 导航路径: 登录后台 → 系统管理 → 安全监控")
    else:
        print("❌ 安全监控页面测试失败")
        print("需要检查页面配置和权限设置")

if __name__ == "__main__":
    main()
