<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>缓存机制测试页面</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .test-result {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .success {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background-color: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        #console-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <h1>🔧 缓存破坏机制测试</h1>
    
    <div class="test-container">
        <h2>📋 测试项目</h2>
        <div id="test-results"></div>
        
        <h3>🎮 手动测试</h3>
        <button onclick="testCacheBuster()">测试缓存破坏器</button>
        <button onclick="testVersionCheck()">检查版本</button>
        <button onclick="testMobileDetection()">测试移动端检测</button>
        <button onclick="clearTestCache()">清除缓存</button>
        <button onclick="forceRefresh()">强制刷新</button>
        
        <h3>📊 控制台输出</h3>
        <div id="console-output"></div>
    </div>

    <!-- 引入缓存破坏器 -->
    <script src="/js/cache_buster.js"></script>
    
    <script>
        // 捕获控制台输出
        const originalLog = console.log;
        const consoleOutput = document.getElementById('console-output');
        
        console.log = function(...args) {
            originalLog.apply(console, args);
            const message = args.map(arg => 
                typeof arg === 'object' ? JSON.stringify(arg, null, 2) : String(arg)
            ).join(' ');
            consoleOutput.textContent += new Date().toLocaleTimeString() + ': ' + message + '\n';
            consoleOutput.scrollTop = consoleOutput.scrollHeight;
        };

        function addTestResult(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result ${type}`;
            resultDiv.textContent = message;
            resultsDiv.appendChild(resultDiv);
        }

        // 自动测试
        function runAutoTests() {
            console.log('🚀 开始自动测试缓存破坏机制');
            
            // 测试1: 检查缓存破坏器是否加载
            if (typeof window.cacheBuster !== 'undefined') {
                addTestResult('✅ 缓存破坏器已成功加载', 'success');
                console.log('✅ 缓存破坏器对象:', window.cacheBuster);
            } else {
                addTestResult('❌ 缓存破坏器未加载', 'error');
                console.log('❌ 缓存破坏器未找到');
            }
            
            // 测试2: 检查版本信息
            if (window.cacheBuster && window.cacheBuster.version) {
                addTestResult(`✅ 版本信息: ${window.cacheBuster.version}`, 'success');
                console.log('✅ 当前版本:', window.cacheBuster.version);
            } else {
                addTestResult('❌ 版本信息不可用', 'error');
            }
            
            // 测试3: 检查localStorage中的版本
            const storedVersion = localStorage.getItem('app_version');
            if (storedVersion) {
                addTestResult(`✅ 本地存储版本: ${storedVersion}`, 'success');
                console.log('✅ 本地存储版本:', storedVersion);
            } else {
                addTestResult('ℹ️ 本地存储中无版本信息（首次访问）', 'info');
            }
            
            // 测试4: 检查移动端检测
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
            addTestResult(`📱 移动端检测: ${isMobile ? '是' : '否'}`, 'info');
            console.log('📱 移动端检测结果:', isMobile);
            
            // 测试5: 检查version.json可访问性
            testVersionJsonAccess();
        }

        async function testVersionJsonAccess() {
            try {
                const response = await fetch(`/version.json?t=${Date.now()}`, {
                    cache: 'no-cache',
                    headers: {
                        'Cache-Control': 'no-cache',
                        'Pragma': 'no-cache'
                    }
                });
                
                if (response.ok) {
                    const versionData = await response.json();
                    addTestResult(`✅ version.json可访问: ${versionData.version}`, 'success');
                    console.log('✅ 服务器版本数据:', versionData);
                } else {
                    addTestResult(`❌ version.json访问失败: ${response.status}`, 'error');
                    console.log('❌ version.json响应状态:', response.status);
                }
            } catch (error) {
                addTestResult(`❌ version.json访问错误: ${error.message}`, 'error');
                console.log('❌ version.json访问错误:', error);
            }
        }

        // 手动测试函数
        function testCacheBuster() {
            if (window.cacheBuster) {
                console.log('🔧 缓存破坏器功能测试:');
                console.log('- 版本:', window.cacheBuster.version);
                console.log('- 清除缓存功能:', typeof window.cacheBuster.clearCache);
                console.log('- 强制刷新功能:', typeof window.cacheBuster.forceRefresh);
                console.log('- 版本检查功能:', typeof window.cacheBuster.checkVersion);
                addTestResult('✅ 缓存破坏器功能测试完成', 'success');
            } else {
                addTestResult('❌ 缓存破坏器不可用', 'error');
            }
        }

        function testVersionCheck() {
            if (window.cacheBuster && window.cacheBuster.checkVersion) {
                console.log('🔍 执行版本检查...');
                window.cacheBuster.checkVersion();
                addTestResult('✅ 版本检查已执行', 'success');
            } else {
                addTestResult('❌ 版本检查功能不可用', 'error');
            }
        }

        function testMobileDetection() {
            const userAgent = navigator.userAgent;
            const isMobile = /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent);
            console.log('📱 用户代理:', userAgent);
            console.log('📱 移动端检测结果:', isMobile);
            addTestResult(`📱 当前设备: ${isMobile ? '移动设备' : '桌面设备'}`, 'info');
        }

        function clearTestCache() {
            if (window.cacheBuster && window.cacheBuster.clearCache) {
                console.log('🧹 清除缓存...');
                window.cacheBuster.clearCache();
                addTestResult('✅ 缓存已清除', 'success');
            } else {
                addTestResult('❌ 缓存清除功能不可用', 'error');
            }
        }

        function forceRefresh() {
            if (window.cacheBuster && window.cacheBuster.forceRefresh) {
                console.log('🔄 执行强制刷新...');
                addTestResult('🔄 即将强制刷新页面...', 'info');
                setTimeout(() => {
                    window.cacheBuster.forceRefresh();
                }, 1000);
            } else {
                addTestResult('❌ 强制刷新功能不可用', 'error');
            }
        }

        // 页面加载完成后运行自动测试
        window.addEventListener('load', () => {
            setTimeout(runAutoTests, 1000); // 等待缓存破坏器初始化
        });

        console.log('🔧 缓存机制测试页面已加载');
    </script>
</body>
</html>