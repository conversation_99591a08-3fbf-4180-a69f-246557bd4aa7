{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}管理用户权限{% endblock %}

{% block content %}
<div class="container">
    <div class="row justify-content-center">
        <div class="col-md-10">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h2 class="card-title">
                            <i class="fas fa-users-cog me-2"></i>管理用户权限
                        </h2>
                        <a href="{% url 'qrmanager:admin_account_create' %}" class="btn btn-primary">
                            <i class="fas fa-user-plus me-2"></i>创建新管理账户
                        </a>
                    </div>

                    {% if messages %}
                    <div class="messages mb-4">
                        {% for message in messages %}
                        <div class="alert alert-{{ message.tags }} alert-dismissible fade show">
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                        {% endfor %}
                    </div>
                    {% endif %}

                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th>用户名</th>
                                    <th>状态</th>
                                    <th>员工权限</th>
                                    <th>超级管理员</th>
                                    <th>最后登录</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for user in admin_users %}
                                <tr>
                                    <td>
                                        <i class="fas fa-user me-2"></i>{{ user.username }}
                                        {% if user.is_superuser %}
                                        <span class="badge bg-warning">超级管理员</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_active %}
                                            <span class="badge bg-success">已激活</span>
                                        {% else %}
                                            <span class="badge bg-danger">已禁用</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_staff %}
                                            <span class="badge bg-primary">是</span>
                                        {% else %}
                                            <span class="badge bg-secondary">否</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        {% if user.is_superuser %}
                                            <span class="badge bg-warning">是</span>
                                        {% else %}
                                            <span class="badge bg-secondary">否</span>
                                        {% endif %}
                                    </td>
                                    <td>{{ user.last_login|date:"Y-m-d H:i"|default:"-" }}</td>
                                    <td>
                                        <div class="btn-group">
                                            <a href="{% url 'qrmanager:admin_account_update' user.pk %}"
                                               class="btn btn-sm btn-outline-primary" title="编辑权限">
                                                <i class="fas fa-edit"></i>
                                            </a>
                                            {% if request.user.is_superuser and not user.is_superuser %}
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-warning"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#resetPasswordModal"
                                                    data-user-id="{{ user.pk }}"
                                                    data-username="{{ user.username }}"
                                                    title="重置密码">
                                                <i class="fas fa-key"></i>
                                            </button>
                                            <button type="button"
                                                    class="btn btn-sm btn-outline-danger"
                                                    data-bs-toggle="modal"
                                                    data-bs-target="#deleteUserModal"
                                                    data-user-id="{{ user.pk }}"
                                                    data-username="{{ user.username }}"
                                                    title="删除用户">
                                                <i class="fas fa-trash"></i>
                                            </button>
                                            {% endif %}
                                        </div>
                                    </td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="6" class="text-center py-4">暂无管理用户数据</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 重置密码模态框 -->
<div class="modal fade" id="resetPasswordModal" tabindex="-1" aria-labelledby="resetPasswordModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="resetPasswordModalLabel">重置用户密码</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="resetPasswordForm" method="post">
                    {% csrf_token %}
                    <input type="hidden" id="targetUserId" name="user_id">
                    <div class="alert alert-warning">
                        您正在重置用户 <strong id="targetUsername"></strong> 的密码，请谨慎操作！
                    </div>
                    <div class="mb-3">
                        <label for="newPassword" class="form-label">新密码</label>
                        <input type="password" class="form-control" id="newPassword" name="new_password"
                               required minlength="8">
                        <div class="form-text">密码长度至少8位</div>
                    </div>
                    <div class="mb-3">
                        <label for="confirmPassword" class="form-label">确认新密码</label>
                        <input type="password" class="form-control" id="confirmPassword" name="confirm_password"
                               required minlength="8">
                    </div>
                    <div class="mb-3">
                        <label for="adminPassword" class="form-label">您的管理员密码</label>
                        <input type="password" class="form-control" id="adminPassword" name="admin_password"
                               required>
                        <div class="form-text text-danger">请输入您的密码以确认此操作</div>
                    </div>
                    <div id="resetPasswordError" class="alert alert-danger d-none"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-warning" onclick="submitResetPassword()">
                    确认重置
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除用户确认模态框 -->
<div class="modal fade" id="deleteUserModal" tabindex="-1" aria-labelledby="deleteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteUserModalLabel">删除用户</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="deleteUserForm" method="post">
                    {% csrf_token %}
                    <input type="hidden" id="deleteUserId" name="user_id">
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        您确定要删除用户 <strong id="deleteUsername"></strong> 吗？此操作不可恢复！
                    </div>
                    <div class="mb-3">
                        <label for="deleteAdminPassword" class="form-label">请输入您的管理员密码确认</label>
                        <input type="password" class="form-control" id="deleteAdminPassword" name="admin_password" required>
                        <div class="form-text text-danger">请输入您的密码以确认此操作</div>
                    </div>
                    <div id="deleteUserError" class="alert alert-danger d-none"></div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-danger" onclick="submitDeleteUser()">
                    <i class="fas fa-trash me-2"></i>确认删除
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Reset Password Modal Script -->
<script>
// 为模态框绑定显示和隐藏事件
document.addEventListener('DOMContentLoaded', function() {
    const resetPasswordModal = document.getElementById('resetPasswordModal');
    if (resetPasswordModal) {
        resetPasswordModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');
            document.getElementById('targetUserId').value = userId;
            document.getElementById('targetUsername').textContent = username;
        });
        resetPasswordModal.addEventListener('hidden.bs.modal', function () {
            document.getElementById('resetPasswordForm').reset();
            document.getElementById('resetPasswordError').classList.add('d-none');
        });
    }

    const deleteUserModal = document.getElementById('deleteUserModal');
    if (deleteUserModal) {
        deleteUserModal.addEventListener('show.bs.modal', function (event) {
            const button = event.relatedTarget;
            const userId = button.getAttribute('data-user-id');
            const username = button.getAttribute('data-username');
            document.getElementById('deleteUserId').value = userId;
            document.getElementById('deleteUsername').textContent = username;
        });
        deleteUserModal.addEventListener('hidden.bs.modal', function () {
            document.getElementById('deleteUserForm').reset();
            document.getElementById('deleteUserError').classList.add('d-none');
        });
    }
});

// 提交重置密码请求的函数，用于在点击确认重置按钮时调用
function submitResetPassword() {
    const userId = document.getElementById('targetUserId').value;
    const formData = new FormData();
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const adminPassword = document.getElementById('adminPassword').value;
    const errorDiv = document.getElementById('resetPasswordError');
    const modal = document.getElementById('resetPasswordModal');

    // 清除之前的错误信息
    errorDiv.textContent = '';
    errorDiv.classList.add('d-none');

    // 获取CSRF Token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    formData.append('new_password', newPassword);
    formData.append('confirm_password', confirmPassword);
    formData.append('admin_password', adminPassword);

    const url = `{% url 'qrmanager:reset_user_password' 0 %}`.replace('0', userId);

    fetch(url, {
        method: 'POST',
        credentials: 'same-origin',  // 确保发送 cookies
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json().then(data => {
                if (!response.ok) {
                    throw new Error(data.message || '请求失败');
                }
                return data;
            });
        } else {
            // 如果响应不是 JSON，可能是 HTML（如登录页面）
            return response.text().then(text => {
                // 检查是否是登录页面或其他错误页面
                if (text.includes('<!DOCTYPE html>')) {
                    if (text.includes('login') || text.includes('登录')) {
                        throw new Error('会话已过期，请重新登录');
                    } else {
                        throw new Error('服务器返回了非预期的响应');
                    }
                }
                throw new Error('请求失败');
            });
        }
    })
    .then(data => {
        if (data.status === 'success') {
            // 重置成功
            errorDiv.classList.add('d-none');
            // 显示成功消息
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success';
            successDiv.textContent = data.message;
            errorDiv.parentNode.insertBefore(successDiv, errorDiv);

            // 关闭模态框
            const modalInstance = bootstrap.Modal.getInstance(modal);
            modalInstance.hide();

            // 可选：刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            // 显示错误信息
            errorDiv.textContent = data.message;
            errorDiv.classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorDiv.textContent = error.message || '发生错误，请稍后重试';
        errorDiv.classList.remove('d-none');

        // 如果是会话过期，重定向到登录页面
        if (error.message === '会话已过期，请重新登录') {
            setTimeout(() => {
                window.location.href = '/login/';
            }, 2000);
        }
    });
}

// 提交删除用户请求的函数
function submitDeleteUser() {
    const userId = document.getElementById('deleteUserId').value;
    const adminPassword = document.getElementById('deleteAdminPassword').value;
    const errorDiv = document.getElementById('deleteUserError');
    const modal = document.getElementById('deleteUserModal');

    // 清除之前的错误信息
    errorDiv.textContent = '';
    errorDiv.classList.add('d-none');

    // 获取CSRF Token
    const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

    const formData = new FormData();
    formData.append('admin_password', adminPassword);

    fetch(`{% url 'qrmanager:delete_user' 0 %}`.replace('0', userId), {
        method: 'POST',
        credentials: 'same-origin',
        headers: {
            'X-Requested-With': 'XMLHttpRequest',
            'X-CSRFToken': csrfToken
        },
        body: formData
    })
    .then(response => {
        const contentType = response.headers.get('content-type');
        if (contentType && contentType.includes('application/json')) {
            return response.json().then(data => {
                if (!response.ok) {
                    throw new Error(data.message || '请求失败');
                }
                return data;
            });
        } else {
            return response.text().then(text => {
                if (text.includes('<!DOCTYPE html>')) {
                    if (text.includes('login') || text.includes('登录')) {
                        throw new Error('会话已过期，请重新登录');
                    } else {
                        throw new Error('服务器返回了非预期的响应');
                    }
                }
                throw new Error('请求失败');
            });
        }
    })
    .then(data => {
        if (data.status === 'success') {
            // 删除成功
            const successDiv = document.createElement('div');
            successDiv.className = 'alert alert-success';
            successDiv.textContent = data.message || '用户已成功删除';
            errorDiv.parentNode.insertBefore(successDiv, errorDiv);

            // 关闭模态框
            const modalInstance = bootstrap.Modal.getInstance(modal);
            modalInstance.hide();

            // 刷新页面
            setTimeout(() => {
                window.location.reload();
            }, 1500);
        } else {
            errorDiv.textContent = data.message;
            errorDiv.classList.remove('d-none');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        errorDiv.textContent = error.message || '发生错误，请稍后重试';
        errorDiv.classList.remove('d-none');

        if (error.message === '会话已过期，请重新登录') {
            setTimeout(() => {
                window.location.href = '/login/';
            }, 2000);
        }
    });
}
</script>
{% endblock %}