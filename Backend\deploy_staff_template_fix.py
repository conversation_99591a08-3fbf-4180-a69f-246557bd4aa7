#!/usr/bin/env python
"""
部署工作人员模板下载修复
"""

import os
import shutil
import subprocess
from datetime import datetime

def deploy_fix():
    """部署修复"""
    print("🚀 开始部署工作人员模板下载修复...")
    
    # 1. 备份原文件
    template_file = "qrmanager/templates/qrmanager/staff_list.html"
    views_file = "qrmanager/views.py"
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    
    if os.path.exists(template_file):
        backup_template = f"{template_file}.backup_{timestamp}"
        shutil.copy2(template_file, backup_template)
        print(f"✅ 模板文件已备份: {backup_template}")
    
    if os.path.exists(views_file):
        backup_views = f"{views_file}.backup_{timestamp}"
        shutil.copy2(views_file, backup_views)
        print(f"✅ 视图文件已备份: {backup_views}")
    
    # 2. 验证修改内容
    print("\n🔍 验证修改内容...")
    
    with open(template_file, 'r', encoding='utf-8') as f:
        template_content = f.read()
    
    with open(views_file, 'r', encoding='utf-8') as f:
        views_content = f.read()
    
    # 检查关键修改
    template_checks = [
        'id="downloadStaffTemplateBtn"',
        'downloadStaffTemplateBtn.addEventListener',
        'method: \'HEAD\'',
        'response.blob()',
        'window.URL.createObjectURL',
    ]
    
    views_checks = [
        'Content-Transfer-Encoding',
        'X-Content-Type-Options',
        'no-cache, no-store, must-revalidate',
    ]
    
    print("📋 模板文件检查:")
    for check in template_checks:
        if check in template_content:
            print(f"   ✅ {check}")
        else:
            print(f"   ❌ {check}")
    
    print("\n📋 视图文件检查:")
    for check in views_checks:
        if check in views_content:
            print(f"   ✅ {check}")
        else:
            print(f"   ❌ {check}")
    
    # 3. 收集静态文件（如果需要）
    print("\n📦 收集静态文件...")
    try:
        result = subprocess.run(['python', 'manage.py', 'collectstatic', '--noinput'], 
                              capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ 静态文件收集成功")
        else:
            print(f"⚠️ 静态文件收集警告: {result.stderr}")
    except Exception as e:
        print(f"⚠️ 静态文件收集跳过: {e}")
    
    # 4. 生成部署说明
    deploy_notes = f"""
# 工作人员模板下载修复部署说明

## 修复时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 修复内容

### 前端修改 (staff_list.html)
1. 将直接链接改为按钮 + JavaScript 处理
2. 添加会话状态检查（HEAD 请求）
3. 使用 Blob 下载方式强制下载
4. 添加加载状态和错误处理
5. 会话过期时友好提示并重定向

### 后端修改 (views.py)
1. 添加强制下载响应头
2. 防止浏览器在线预览
3. 添加缓存控制头

## 解决的问题
- 防止跳转到 Office Online 预览
- 强制文件直接下载到本地
- 改善用户体验和错误处理

## 测试方法
1. 清除浏览器缓存
2. 登录系统访问工作人员管理页面
3. 点击"下载导入模板"按钮
4. 验证文件直接下载而不是跳转预览

## 回滚方法
如果出现问题，可以恢复备份文件：
- cp {backup_template} {template_file}
- cp {backup_views} {views_file}
"""
    
    with open(f"deploy_notes_{timestamp}.md", 'w', encoding='utf-8') as f:
        f.write(deploy_notes)
    
    print(f"\n📝 部署说明已生成: deploy_notes_{timestamp}.md")
    
    print("\n🎉 部署完成！")
    print("\n⚠️ 重要提醒:")
    print("1. 请重启 Web 服务器以确保修改生效")
    print("2. 清除浏览器缓存后测试")
    print("3. 如果使用了反向代理，可能需要重启代理服务")
    
    return True

if __name__ == '__main__':
    deploy_fix()
