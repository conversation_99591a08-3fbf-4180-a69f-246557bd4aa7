#!/usr/bin/env python
"""
测试API接口兼容性 - 验证加密算法改变后API是否正常工作
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param, secure_qr_access, is_encrypted_param
from qrmanager.models import QRCode, Bed, Department
import uuid

def test_api_compatibility():
    """测试API接口兼容性"""
    print("=" * 80)
    print("🔗 API接口兼容性测试")
    print("=" * 80)
    
    # 生成测试UUID
    test_uuid = str(uuid.uuid4())
    print(f"测试UUID: {test_uuid}")
    
    try:
        # 步骤1: 测试加密
        print("\n1. 测试加密功能:")
        encrypted = encrypt_qr_param(test_uuid)
        print(f"   加密结果: {encrypted}")
        print(f"   长度: {len(encrypted)} 字符")
        print(f"   加密成功: ✅")
        
        # 步骤2: 测试格式检查
        print("\n2. 测试格式检查:")
        is_encrypted = is_encrypted_param(encrypted)
        print(f"   格式检查结果: {is_encrypted}")
        print(f"   格式检查: {'✅ 通过' if is_encrypted else '❌ 失败'}")
        
        # 步骤3: 测试解密
        print("\n3. 测试解密功能:")
        decrypted_data = decrypt_qr_param(encrypted)
        print(f"   解密数据: {decrypted_data}")
        decrypted_uuid = decrypted_data['uuid']
        print(f"   解密UUID: {decrypted_uuid}")
        print(f"   UUID匹配: {'✅ 成功' if decrypted_uuid == test_uuid else '❌ 失败'}")
        
        # 步骤4: 测试secure_qr_access函数（API核心函数）
        print("\n4. 测试API核心函数 secure_qr_access:")
        try:
            api_uuid = secure_qr_access(encrypted)
            print(f"   API返回UUID: {api_uuid}")
            print(f"   API函数: {'✅ 成功' if api_uuid == test_uuid else '❌ 失败'}")
            api_success = True
        except Exception as e:
            print(f"   API函数: ❌ 失败 - {e}")
            api_success = False
        
        # 步骤5: 测试带等号前缀的情况（URL参数常见情况）
        print("\n5. 测试URL参数格式（带等号前缀）:")
        url_param = "=" + encrypted
        try:
            url_uuid = secure_qr_access(url_param)
            print(f"   URL参数: {url_param[:30]}...")
            print(f"   解析UUID: {url_uuid}")
            print(f"   URL处理: {'✅ 成功' if url_uuid == test_uuid else '❌ 失败'}")
            url_success = True
        except Exception as e:
            print(f"   URL处理: ❌ 失败 - {e}")
            url_success = False
        
        # 总结
        print("\n" + "=" * 80)
        print("📊 兼容性测试结果:")
        print("=" * 80)
        
        all_success = api_success and url_success
        
        print(f"✅ 加密功能: 正常")
        print(f"✅ 解密功能: 正常")
        print(f"{'✅' if api_success else '❌'} API核心函数: {'正常' if api_success else '异常'}")
        print(f"{'✅' if url_success else '❌'} URL参数处理: {'正常' if url_success else '异常'}")
        
        if all_success:
            print("\n🎉 所有API接口兼容性测试通过！")
            print("✅ 前后端接口无需调整")
        else:
            print("\n🚨 发现兼容性问题！")
            print("❌ 需要调整API接口")
        
        return all_success
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        return False

def test_frontend_api_simulation():
    """模拟前端API调用"""
    print("\n" + "=" * 80)
    print("🌐 模拟前端API调用测试")
    print("=" * 80)
    
    # 模拟前端发送的数据格式
    test_uuid = str(uuid.uuid4())
    encrypted = encrypt_qr_param(test_uuid)
    
    # 模拟前端验证二维码的API调用
    print("1. 模拟前端验证二维码API调用:")
    frontend_verify_data = {
        "qr_param": encrypted,
        "client_ip": "127.0.0.1"
    }
    
    print(f"   前端发送数据: {frontend_verify_data}")
    
    # 模拟后端处理
    try:
        qr_param = frontend_verify_data["qr_param"]
        uuid_result = secure_qr_access(qr_param)
        print(f"   后端解析UUID: {uuid_result}")
        print(f"   验证API: {'✅ 成功' if uuid_result == test_uuid else '❌ 失败'}")
        verify_success = True
    except Exception as e:
        print(f"   验证API: ❌ 失败 - {e}")
        verify_success = False
    
    # 模拟前端提交评价的API调用
    print("\n2. 模拟前端提交评价API调用:")
    frontend_submit_data = {
        "qr_param": encrypted,
        "comment": "服务很好",
        "staff_evaluations": [
            {"staff_id": 1, "is_satisfied": True}
        ],
        "hospital_number": "12345",
        "phone_number": "13800138000"
    }
    
    print(f"   前端发送数据: qr_param={encrypted[:20]}..., comment='{frontend_submit_data['comment']}'")
    
    # 模拟后端处理
    try:
        qr_param = frontend_submit_data["qr_param"]
        uuid_result = secure_qr_access(qr_param)
        print(f"   后端解析UUID: {uuid_result}")
        print(f"   提交API: {'✅ 成功' if uuid_result == test_uuid else '❌ 失败'}")
        submit_success = True
    except Exception as e:
        print(f"   提交API: ❌ 失败 - {e}")
        submit_success = False
    
    # 总结
    print("\n📊 前端API模拟测试结果:")
    all_success = verify_success and submit_success
    print(f"{'✅' if verify_success else '❌'} 验证二维码API: {'正常' if verify_success else '异常'}")
    print(f"{'✅' if submit_success else '❌'} 提交评价API: {'正常' if submit_success else '异常'}")
    
    if all_success:
        print("\n🎉 前端API调用完全兼容！")
        print("✅ 前端代码无需修改")
    else:
        print("\n🚨 前端API调用存在问题！")
        print("❌ 需要修改前端或后端代码")
    
    return all_success

if __name__ == "__main__":
    print("🔧 开始API接口兼容性测试...")
    
    # 基础兼容性测试
    basic_success = test_api_compatibility()
    
    # 前端API模拟测试
    frontend_success = test_frontend_api_simulation()
    
    # 最终结论
    print("\n" + "=" * 80)
    print("🏁 最终测试结论")
    print("=" * 80)
    
    if basic_success and frontend_success:
        print("🎉 所有测试通过！")
        print("✅ API接口完全兼容")
        print("✅ 前端填写页面到后端API接口无需调整")
        print("✅ 可以直接使用新的加密算法")
    else:
        print("🚨 发现兼容性问题！")
        print("❌ 需要调整API接口或前端代码")
        if not basic_success:
            print("   - 基础API功能存在问题")
        if not frontend_success:
            print("   - 前端API调用存在问题")
    
    print("=" * 80)
