# Generated by Django 4.2.7 on 2025-02-07 01:59

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0004_operationlog'),
    ]

    operations = [
        migrations.AddField(
            model_name='operationlog',
            name='browser',
            field=models.CharField(blank=True, max_length=200, verbose_name='浏览器'),
        ),
        migrations.AddField(
            model_name='operationlog',
            name='extra_data',
            field=models.JSONField(blank=True, null=True, verbose_name='额外数据'),
        ),
        migrations.AddField(
            model_name='operationlog',
            name='ip_address',
            field=models.GenericIPAddressField(blank=True, null=True, verbose_name='IP地址'),
        ),
        migrations.AddField(
            model_name='operationlog',
            name='os',
            field=models.CharField(blank=True, max_length=200, verbose_name='操作系统'),
        ),
        migrations.AddField(
            model_name='operationlog',
            name='status',
            field=models.Char<PERSON>ield(choices=[('success', '成功'), ('error', '失败'), ('warning', '警告')], default='success', max_length=10, verbose_name='状态'),
        ),
    ]
