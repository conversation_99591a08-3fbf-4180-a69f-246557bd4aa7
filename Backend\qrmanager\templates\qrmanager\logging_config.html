{% extends 'qrmanager/base.html' %}
{% load static %}

{% block title %}日志配置管理{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <h1 class="mt-4"><i class="fas fa-sliders-h me-2 text-primary"></i><span class="text-primary">日志配置管理</span></h1>
    <ol class="breadcrumb mb-4">
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">仪表板</a></li>
        <li class="breadcrumb-item"><a href="{% url 'qrmanager:operation_logs' %}">操作日志</a></li>
        <li class="breadcrumb-item active">日志配置</li>
    </ol>
    
    <div class="row">
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-chart-pie me-1 text-success"></i>
                    日志统计信息
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card bg-primary text-white mb-4">
                                <div class="card-body">
                                    <h2>{{ total_logs }}</h2>
                                    <p>总日志数量</p>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card bg-success text-white mb-4">
                                <div class="card-body">
                                    <h2>{{ recent_logs }}</h2>
                                    <p>最近7天日志数量</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <h5 class="mt-4">日志类型分布</h5>
                    <div class="table-responsive">
                        <table class="table table-sm">
                            <thead>
                                <tr>
                                    <th>操作类型</th>
                                    <th>数量</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in log_counts|slice:":10" %}
                                <tr>
                                    <td>{{ log.action }}</td>
                                    <td>{{ log.count }}</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-trash-alt me-1"></i>
                    清理旧日志
                </div>
                <div class="card-body">
                    <form method="post" action="{% url 'qrmanager:clear_old_logs' %}" onsubmit="return confirm('确定要删除旧日志吗？此操作不可恢复！');">
                        {% csrf_token %}
                        <div class="row g-3 align-items-center">
                            <div class="col-auto">
                                <label for="days" class="col-form-label">删除多少天前的日志：</label>
                            </div>
                            <div class="col-auto">
                                <select name="days" id="days" class="form-select">
                                    <option value="0">0天（全部日志）</option>
                                    <option value="7">7天</option>
                                    <option value="14">14天</option>
                                    <option value="30">30天</option>
                                    <option value="60">60天</option>
                                    <option value="90">90天</option>
                                    <option value="180">180天</option>
                                    <option value="365">365天</option>
                                </select>
                            </div>
                            <div class="col-auto">
                                <button type="submit" class="btn btn-danger">清理日志</button>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
        
        <div class="col-xl-6">
            <div class="card mb-4">
                <div class="card-header">
                    <i class="fas fa-cogs me-1"></i>
                    日志记录配置
                </div>
                <div class="card-body">
                    <p class="text-muted">选择需要记录的操作类型，未选中的操作类型将不会被记录到数据库中。</p>
                    
                    <form method="post" action="{% url 'qrmanager:logging_config' %}">
                        {% csrf_token %}
                        
                        {% for category, configs in grouped_configs.items %}
                        <h5 class="mt-4">{{ category }}</h5>
                        <div class="table-responsive">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th style="width: 50px;">启用</th>
                                        <th>操作类型</th>
                                        <th>说明</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for config in configs %}
                                    <tr>
                                        <td>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="config_{{ config.id }}" name="config_{{ config.id }}" {% if config.is_enabled %}checked{% endif %}>
                                            </div>
                                        </td>
                                        <td>{{ config.get_action_type_display }}</td>
                                        <td>{{ config.description }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% endfor %}
                        
                        <div class="mt-4">
                            <button type="submit" class="btn btn-primary">保存配置</button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 全选/取消全选功能
    function toggleCategory(categoryId) {
        const checkboxes = document.querySelectorAll(`#${categoryId} input[type="checkbox"]`);
        const toggleBtn = document.getElementById(`toggle_${categoryId}`);
        const isChecked = toggleBtn.getAttribute('data-checked') === 'true';
        
        checkboxes.forEach(checkbox => {
            checkbox.checked = !isChecked;
        });
        
        toggleBtn.setAttribute('data-checked', isChecked ? 'false' : 'true');
        toggleBtn.textContent = isChecked ? '全选' : '取消全选';
    }
</script>
{% endblock %} 