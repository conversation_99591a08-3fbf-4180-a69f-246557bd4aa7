#!/usr/bin/env python3
"""
测试前端API提交医院评分功能
"""

import os
import sys
import django
import json

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.test import Client
from django.urls import reverse
from qrmanager.models import Evaluation, QRCode
from qrmanager.security import encrypt_qr_param

def test_api_submission():
    """测试API提交功能"""
    print("🔍 测试API提交医院评分")
    print("=" * 50)
    
    # 创建Django测试客户端
    client = Client()
    
    try:
        # 获取一个测试二维码
        qr_code = QRCode.objects.first()
        if not qr_code:
            print("❌ 没有找到测试二维码")
            return False
        
        # 生成加密参数
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        print(f"测试数据:")
        print(f"  二维码: {qr_code.name}")
        print(f"  床位: {qr_code.bed.number}")
        print(f"  科室: {qr_code.bed.department.name}")
        print(f"  加密参数: {encrypted_param}")
        
        # 构建测试数据
        test_data = {
            "qr_param": encrypted_param,
            "comment": "前端API测试 - 医院评分功能",
            "hospital_rating": 3,  # 3星评分
            "hospital_number": "API_TEST001",
            "phone_number": "13700137000",
            "staff_evaluations": []
        }
        
        print(f"\n提交数据:")
        for key, value in test_data.items():
            print(f"  {key}: {value}")
        
        # 发送POST请求
        response = client.post(
            '/api/evaluation/submit/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        print(f"\nAPI响应:")
        print(f"  状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"  状态: {result.get('status')}")
            print(f"  消息: {result.get('message')}")
            
            data = result.get('data', {})
            evaluation_id = data.get('evaluation_id')
            hospital_rating = data.get('hospital_rating')
            
            print(f"  评价ID: {evaluation_id}")
            print(f"  医院评分: {hospital_rating}")
            
            if evaluation_id:
                # 验证数据库中的记录
                try:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    print(f"\n数据库验证:")
                    print(f"  评价ID: {evaluation.id}")
                    print(f"  医院评分: {evaluation.hospital_rating}")
                    print(f"  评价内容: {evaluation.comment}")
                    print(f"  住院号: {evaluation.hospital_number}")
                    print(f"  联系电话: {evaluation.phone_number}")
                    
                    if evaluation.hospital_rating == 3:
                        print(f"✅ 医院评分正确保存到数据库")
                        return True
                    else:
                        print(f"❌ 医院评分保存错误: 期望3，实际{evaluation.hospital_rating}")
                        return False
                        
                except Evaluation.DoesNotExist:
                    print(f"❌ 数据库中未找到评价记录")
                    return False
            else:
                print(f"❌ API响应中没有evaluation_id")
                return False
        else:
            print(f"❌ API请求失败")
            print(f"  响应内容: {response.content.decode()}")
            return False
            
    except Exception as e:
        print(f"❌ API测试失败: {e}")
        return False

def test_rating_validation_api():
    """测试API评分验证"""
    print(f"\n🔍 测试API评分验证")
    print("=" * 50)
    
    client = Client()
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试有效评分
        valid_ratings = [1, 2, 3, 4, 5]
        for rating in valid_ratings:
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"测试{rating}星评分",
                "hospital_rating": rating,
                "staff_evaluations": []
            }
            
            response = client.post(
                '/api/evaluation/submit/',
                data=json.dumps(test_data),
                content_type='application/json'
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('status') == 'success':
                    print(f"✅ {rating}星评分API验证通过")
                else:
                    print(f"❌ {rating}星评分API验证失败: {result.get('message')}")
            else:
                print(f"❌ {rating}星评分API请求失败: {response.status_code}")
        
        # 测试无效评分
        invalid_ratings = [0, 6, -1, 10]
        for rating in invalid_ratings:
            test_data = {
                "qr_param": encrypted_param,
                "comment": f"测试无效{rating}星评分",
                "hospital_rating": rating,
                "staff_evaluations": []
            }
            
            response = client.post(
                '/api/evaluation/submit/',
                data=json.dumps(test_data),
                content_type='application/json'
            )
            
            if response.status_code == 400:
                result = response.json()
                print(f"✅ {rating}星评分正确被拒绝: {result.get('message')}")
            elif response.status_code == 200:
                result = response.json()
                if result.get('status') == 'error':
                    print(f"✅ {rating}星评分正确被拒绝: {result.get('message')}")
                else:
                    print(f"⚠️  {rating}星评分意外通过验证")
            else:
                print(f"⚠️  {rating}星评分验证结果未知: {response.status_code}")
        
        return True
        
    except Exception as e:
        print(f"❌ 评分验证测试失败: {e}")
        return False

def test_optional_rating():
    """测试可选评分"""
    print(f"\n🔍 测试可选评分")
    print("=" * 50)
    
    client = Client()
    
    try:
        qr_code = QRCode.objects.first()
        encrypted_param = encrypt_qr_param(qr_code.code)
        
        # 测试不提供评分
        test_data = {
            "qr_param": encrypted_param,
            "comment": "测试不提供医院评分",
            "staff_evaluations": []
        }
        
        response = client.post(
            '/api/evaluation/submit/',
            data=json.dumps(test_data),
            content_type='application/json'
        )
        
        if response.status_code == 200:
            result = response.json()
            if result.get('status') == 'success':
                evaluation_id = result.get('data', {}).get('evaluation_id')
                if evaluation_id:
                    evaluation = Evaluation.objects.get(id=evaluation_id)
                    if evaluation.hospital_rating is None:
                        print(f"✅ 可选评分测试通过：允许不提供评分")
                        return True
                    else:
                        print(f"❌ 可选评分测试失败：评分应为空，实际为{evaluation.hospital_rating}")
                        return False
                else:
                    print(f"❌ 可选评分测试失败：没有返回evaluation_id")
                    return False
            else:
                print(f"❌ 可选评分测试失败：{result.get('message')}")
                return False
        else:
            print(f"❌ 可选评分测试失败：状态码{response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 可选评分测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 前端API医院评分功能测试")
    print("=" * 60)
    
    tests = [
        ("API提交测试", test_api_submission),
        ("评分验证测试", test_rating_validation_api),
        ("可选评分测试", test_optional_rating),
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"❌ {name}执行异常: {e}")
            results.append((name, False))
    
    # 总结
    print(f"\n📋 测试结果总结")
    print("=" * 60)
    
    passed = 0
    for name, result in results:
        if result:
            print(f"✅ {name}: 通过")
            passed += 1
        else:
            print(f"❌ {name}: 失败")
    
    print(f"\n总结: {passed}/{len(results)} 测试通过")
    
    if passed == len(results):
        print("🎉 前端API医院评分功能完全可用！")
        print("\n✅ 功能确认:")
        print("  - 前端可以提交1-5星医院评分")
        print("  - 后端正确处理和验证评分数据")
        print("  - 数据库正确保存评分信息")
        print("  - 评分是可选的，允许不评分")
        print("  - 无效评分会被正确拒绝")
    else:
        print("⚠️  部分功能存在问题，需要修复")

if __name__ == "__main__":
    main()
