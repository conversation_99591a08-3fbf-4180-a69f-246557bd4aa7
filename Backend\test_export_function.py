#!/usr/bin/env python3
"""
测试导出功能
"""

import os
import sys
import django

# 设置Django环境
backend_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(backend_dir)
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.test import RequestFactory
from django.contrib.auth.models import User
from qrmanager.views import export_sentiment_report

def test_export_function():
    """测试导出功能"""
    
    # 创建请求工厂
    factory = RequestFactory()
    
    # 创建测试用户
    try:
        user = User.objects.get(username='123')
    except User.DoesNotExist:
        user = User.objects.create_user(username='123', password='Qaz!@#123')
    
    # 创建GET请求
    request = factory.get('/sentiment/export/?format=csv&time_range=7')
    request.user = user
    
    try:
        print("测试CSV导出...")
        response = export_sentiment_report(request)
        print(f"响应状态码: {response.status_code}")
        print(f"响应类型: {type(response)}")
        print(f"Content-Type: {response.get('Content-Type', 'N/A')}")
        
        if hasattr(response, 'content'):
            print(f"响应内容长度: {len(response.content)} bytes")
            if response.get('Content-Type', '').startswith('text/csv'):
                print("CSV内容预览:")
                print(response.content.decode('utf-8')[:500])
        
        print("CSV导出测试成功!")
        
    except Exception as e:
        print(f"CSV导出测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试Excel导出
    try:
        print("\n测试Excel导出...")
        request = factory.get('/sentiment/export/?format=excel&time_range=7')
        request.user = user
        
        response = export_sentiment_report(request)
        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.get('Content-Type', 'N/A')}")
        
        if hasattr(response, 'content'):
            print(f"响应内容长度: {len(response.content)} bytes")
        
        print("Excel导出测试成功!")
        
    except Exception as e:
        print(f"Excel导出测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    # 测试PDF导出
    try:
        print("\n测试PDF导出...")
        request = factory.get('/sentiment/export/?format=pdf&time_range=7')
        request.user = user
        
        response = export_sentiment_report(request)
        print(f"响应状态码: {response.status_code}")
        print(f"Content-Type: {response.get('Content-Type', 'N/A')}")
        
        if hasattr(response, 'content'):
            print(f"响应内容长度: {len(response.content)} bytes")
        
        print("PDF导出测试成功!")
        
    except Exception as e:
        print(f"PDF导出测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_export_function()
