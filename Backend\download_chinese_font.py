#!/usr/bin/env python3
"""
下载中文字体文件用于PDF报告生成
"""

import os
import requests
from pathlib import Path

def download_chinese_font():
    """下载中文字体文件"""
    
    # 字体文件URL（使用Google Fonts的Noto Sans SC）
    font_url = "https://fonts.gstatic.com/s/notosanssc/v36/k3kCo84MPvpLmixcA63oeAL7Iqp5JJXA2g.woff2"
    
    # 本地字体文件路径
    font_dir = Path(__file__).parent / "static" / "fonts"
    font_path = font_dir / "NotoSansSC-Regular.woff2"
    
    # 确保目录存在
    font_dir.mkdir(parents=True, exist_ok=True)
    
    # 如果字体文件已存在，跳过下载
    if font_path.exists():
        print(f"字体文件已存在: {font_path}")
        return str(font_path)
    
    try:
        print(f"正在下载中文字体文件...")
        print(f"URL: {font_url}")
        
        # 下载字体文件
        response = requests.get(font_url, timeout=30)
        response.raise_for_status()
        
        # 保存字体文件
        with open(font_path, 'wb') as f:
            f.write(response.content)
        
        print(f"字体文件下载成功: {font_path}")
        print(f"文件大小: {len(response.content)} bytes")
        
        return str(font_path)
        
    except Exception as e:
        print(f"下载字体文件失败: {e}")
        
        # 尝试使用系统字体
        system_fonts = [
            "C:/Windows/Fonts/msyh.ttc",  # 微软雅黑
            "C:/Windows/Fonts/simsun.ttc",  # 宋体
            "C:/Windows/Fonts/simhei.ttf",  # 黑体
        ]
        
        for font in system_fonts:
            if os.path.exists(font):
                print(f"使用系统字体: {font}")
                return font
        
        print("未找到可用的中文字体")
        return None

if __name__ == "__main__":
    download_chinese_font()
