"""
安全头部中间件
用于添加安全相关的HTTP头部
"""

from django.utils.deprecation import MiddlewareMixin

class SecurityHeadersMiddleware(MiddlewareMixin):
    """
    安全头部中间件
    添加安全相关的HTTP头部
    """

    def process_response(self, request, response):
        """
        处理响应，添加安全头部

        参数:
            request: HTTP请求对象
            response: HTTP响应对象

        返回:
            HTTP响应对象
        """
        # 内容安全策略（CSP）由django-csp中间件处理

        # X-Content-Type-Options: 防止MIME类型嗅探
        response['X-Content-Type-Options'] = 'nosniff'

        # X-Frame-Options: 防止点击劫持
        response['X-Frame-Options'] = 'DENY'

        # X-XSS-Protection: 启用XSS过滤
        response['X-XSS-Protection'] = '1; mode=block'

        # Referrer-Policy: 控制Referer头的发送
        response['Referrer-Policy'] = 'strict-origin-when-cross-origin'

        # Permissions-Policy: 控制浏览器功能
        response['Permissions-Policy'] = 'camera=(), microphone=(), geolocation=(), interest-cohort=()'

        # Cache-Control: 控制缓存
        if not response.has_header('Cache-Control'):
            response['Cache-Control'] = 'no-store, no-cache, must-revalidate, max-age=0'

        # Pragma: 兼容HTTP/1.0
        if not response.has_header('Pragma'):
            response['Pragma'] = 'no-cache'

        # Strict-Transport-Security: 强制HTTPS - 临时注释掉以排查问题
        # response['Strict-Transport-Security'] = 'max-age=31536000; includeSubDomains; preload'

        # Feature-Policy: 控制浏览器功能（已弃用，使用Permissions-Policy代替）
        # response['Feature-Policy'] = 'camera none; microphone none; geolocation none'

        # Clear-Site-Data: 清除站点数据（仅在注销时使用）
        # if request.path == '/logout/':
        #     response['Clear-Site-Data'] = '"cache", "cookies", "storage"'

        return response