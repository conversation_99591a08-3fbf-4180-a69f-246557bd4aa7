# 科室排名和工作人员总体统计功能说明

## 🎯 新增功能概述

在情感分析页面中新增了两个重要功能：
1. **科室排名TOP5**：基于科室内工作人员的表扬批评情况进行排名
2. **工作人员总体统计**：显示全院工作人员评价的整体数据

## 📊 功能详细设计

### 1. 科室排名功能

#### 数据统计逻辑
```python
# 按科室汇总工作人员评价数据
for staff_stat in staff_stats:
    dept_name = staff_stat['department']
    department_staff_stats[dept_name]['praised_count'] += staff_stat['praised_count']
    department_staff_stats[dept_name]['criticized_count'] += staff_stat['criticized_count']
    department_staff_stats[dept_name]['total_mentions'] += staff_stat['total_mentions']
    department_staff_stats[dept_name]['staff_count'] += 1
```

#### 科室统计指标
- **表扬总数**：科室内所有工作人员被表扬的总次数
- **批评总数**：科室内所有工作人员被批评的总次数
- **表扬率**：表扬总数 / 评价总数 × 100%
- **批评率**：批评总数 / 评价总数 × 100%
- **工作人员数**：参与评价的工作人员数量
- **评价总数**：科室内工作人员被提及的总次数

#### 排名算法

**最佳科室TOP5**：
1. **主要指标**：表扬总数（绝对数量优先）
2. **次要指标**：表扬率（相同表扬数时的参考）
3. **辅助指标**：工作人员数（团队规模参考）

```python
'top_departments': sorted([d for d in department_rankings if d['praised_count'] >= 1],
                         key=lambda x: (-x['praised_count'], -x['praise_rate'], -x['staff_count']))[:5]
```

**需要改进科室TOP5**：
1. **主要指标**：批评总数（绝对数量优先）
2. **次要指标**：批评率（相同批评数时的参考）
3. **辅助指标**：工作人员数（团队规模参考）

```python
'bottom_departments': sorted([d for d in department_rankings if d['criticized_count'] >= 1],
                            key=lambda x: (-x['criticized_count'], -x['criticism_rate'], -x['staff_count']))[:5]
```

### 2. 工作人员总体统计功能

#### 统计指标
```python
staff_overall_stats = {
    'total_praised': total_staff_praised,           # 全院表扬总数
    'total_criticized': total_staff_criticized,     # 全院批评总数
    'total_mentions': total_staff_mentions,         # 全院评价总数
    'active_staff_count': active_staff_count,       # 参与评价工作人员数
    'avg_praise_rate': avg_praise_rate,             # 平均表扬率
    'avg_criticism_rate': avg_criticism_rate        # 平均批评率
}
```

#### 计算逻辑
- **全院表扬总数**：所有工作人员被表扬次数的总和
- **全院批评总数**：所有工作人员被批评次数的总和
- **参与评价工作人员数**：有评价记录的工作人员总数
- **平均表扬率**：全院表扬总数 / 全院评价总数 × 100%
- **平均批评率**：全院批评总数 / 全院评价总数 × 100%

## 🎨 前端显示设计

### 1. 工作人员总体统计卡片
```html
<!-- 4个统计卡片横向排列 -->
<div class="row mb-4">
    <div class="col-md-3">活跃工作人员数</div>
    <div class="col-md-3">表扬总数 + 平均表扬率</div>
    <div class="col-md-3">批评总数 + 平均批评率</div>
    <div class="col-md-3">评价总数</div>
</div>
```

### 2. 科室排名表格
```html
<!-- 两个表格并排显示 -->
<div class="row mb-4">
    <div class="col-md-6">最佳科室TOP5</div>
    <div class="col-md-6">需要改进科室TOP5</div>
</div>
```

#### 表格字段设计

**最佳科室TOP5表格**：
- 科室名称
- 表扬总数（绿色高亮）
- 表扬率（绿色显示）
- 工作人员数（蓝色徽章）
- 评价总数（主色徽章）

**需要改进科室TOP5表格**：
- 科室名称
- 批评总数（红色高亮）
- 批评率（橙色显示）
- 工作人员数（蓝色徽章）
- 评价总数（灰色徽章）

## 📈 业务价值

### 1. 科室管理价值
- **科室对比**：直观比较各科室的服务质量表现
- **激励机制**：为科室建立公平的评价和激励体系
- **资源配置**：为人力资源配置和培训计划提供数据支持
- **质量改进**：识别表现优秀和需要改进的科室

### 2. 整体管理价值
- **全局视角**：提供医院工作人员服务质量的整体概况
- **趋势监控**：通过总体数据监控服务质量变化趋势
- **基准设定**：为各科室和工作人员设定合理的表现基准
- **决策支持**：为管理层决策提供量化的数据支持

## 🔧 技术实现要点

### 1. 数据聚合
- 基于现有的工作人员评价数据进行二次聚合
- 确保数据一致性和准确性
- 优化查询性能，避免重复计算

### 2. 排名算法
- 优先考虑绝对数量，避免样本量偏差
- 多级排序确保排名的公平性和合理性
- 设置最小阈值避免无意义的排名

### 3. 前端展示
- 响应式设计适配不同屏幕尺寸
- 颜色编码提高数据可读性
- 空数据处理提升用户体验

## 📁 修改的文件

### 后端文件
- `Backend/qrmanager/views.py`
  - 新增科室统计聚合逻辑
  - 新增工作人员总体统计计算
  - 新增科室排名算法
  - 更新context数据传递

### 前端文件
- `Backend/qrmanager/templates/qrmanager/sentiment_analysis.html`
  - 新增工作人员总体统计卡片显示
  - 新增科室排名表格显示
  - 优化页面布局和样式

## 🧪 测试建议

### 1. 数据验证
- 验证科室统计数据的准确性
- 检查排名算法的合理性
- 确认总体统计数据的正确性

### 2. 边界情况测试
- 测试没有评价数据的科室
- 测试只有单个工作人员的科室
- 测试数据为空的情况

### 3. 用户体验测试
- 验证页面布局的合理性
- 检查数据显示的清晰度
- 确认响应式设计的效果

## 🔮 后续优化建议

### 1. 功能扩展
- 添加科室排名的历史趋势图
- 支持按时间范围筛选科室排名
- 增加科室详细分析页面

### 2. 数据优化
- 添加科室排名变化提醒
- 支持科室排名数据导出
- 增加科室对比分析功能

### 3. 可视化增强
- 添加科室排名的图表展示
- 增加工作人员分布的可视化
- 提供交互式的数据探索功能
