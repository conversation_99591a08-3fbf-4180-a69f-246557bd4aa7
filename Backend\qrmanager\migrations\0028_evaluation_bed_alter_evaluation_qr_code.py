# Generated by Django 4.2.7 on 2025-03-10 00:38

from django.db import migrations, models
import django.db.models.deletion


def migrate_evaluations_data(apps, schema_editor):
    """将评价从关联二维码改为关联床位"""
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    
    # 遍历所有评价
    for evaluation in Evaluation.objects.all():
        # 如果评价关联了二维码，且二维码关联了床位
        if evaluation.qr_code and evaluation.qr_code.bed:
            # 将评价关联到床位
            evaluation.bed = evaluation.qr_code.bed
            evaluation.save()


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0027_migrate_evaluations_to_bed'),
    ]

    operations = [
        migrations.AddField(
            model_name='evaluation',
            name='bed',
            field=models.ForeignKey(null=True, on_delete=django.db.models.deletion.CASCADE, related_name='evaluations', to='qrmanager.bed', verbose_name='床位'),
        ),
        migrations.AlterField(
            model_name='evaluation',
            name='qr_code',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='evaluations', to='qrmanager.qrcode', verbose_name='二维码'),
        ),
        # 添加数据迁移操作
        migrations.RunPython(migrate_evaluations_data),
    ]
