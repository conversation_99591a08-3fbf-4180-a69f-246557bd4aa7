#!/usr/bin/env python
# 修复views.py中的缩进错误

with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
    lines = f.readlines()

# 查找错误的行号
error_line = 1813
error_found = False

# 检查前后几行的上下文
start_line = max(0, error_line - 10)
end_line = min(len(lines), error_line + 10)

# 打印错误附近的代码，帮助调试
print(f"错误附近的代码（行 {start_line} 到 {end_line}）：")
for i in range(start_line, end_line):
    print(f"{i+1}: {lines[i].rstrip()}")

# 尝试修复缩进错误
fixed_lines = lines.copy()
# 检查是否是多余的右括号
if fixed_lines[error_line-1].strip() == ')':
    # 删除这一行
    fixed_lines.pop(error_line-1)
    error_found = True

# 如果找到并修复了错误，写回文件
if error_found:
    with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
        f.writelines(fixed_lines)
    print("\n已修复缩进错误！")
else:
    print("\n未能自动修复错误，请手动检查文件。")
    
    # 尝试更通用的修复方法
    print("\n尝试更通用的修复方法...")
    
    # 读取整个文件内容
    with open('qrmanager/views.py', 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 尝试修复常见的缩进问题
    # 1. 删除多余的右括号
    fixed_content = content.replace('\n)', '')
    
    # 2. 修复可能的缩进不一致
    lines = fixed_content.split('\n')
    for i in range(len(lines)-1, 0, -1):
        if lines[i].strip() == ')' and i > 0:
            # 检查上一行是否以逗号结尾
            if lines[i-1].strip().endswith(','):
                # 删除单独一行的右括号
                lines.pop(i)
    
    fixed_content = '\n'.join(lines)
    
    # 写回修复后的内容
    with open('qrmanager/views.py', 'w', encoding='utf-8') as f:
        f.write(fixed_content)
    
    print("已尝试修复文件，请重新启动服务器检查是否解决问题。") 