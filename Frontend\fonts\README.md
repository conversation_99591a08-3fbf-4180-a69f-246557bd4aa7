# 医院评价系统本地化字体文件

此目录包含系统所需的所有本地化字体文件，确保系统不依赖任何外部CDN或网络资源。

## 字体文件列表

1. `apple-icons.woff2` - 苹果风格的图标字体，完全本地化实现
2. `fa-solid-900.woff2` - Font Awesome固体图标样式 (本地化版本)
3. `fa-regular-400.woff2` - Font Awesome常规图标样式 (本地化版本)

## 重要提示

- 以上字体文件必须实际存在并包含真实的字体数据，不能只是占位符
- 当前的占位符文件需要被真实的字体文件替换
- 可以使用字体子集化工具（如FontForge, pyftsubset等）减小字体文件的大小，只包含实际使用的图标

## 方法一：使用字体转换工具

1. 下载官方字体文件到本地
2. 使用FontForge或其他工具进行子集化，只保留使用到的图标
3. 导出为WOFF2格式，替换当前占位符

## 方法二：使用自定义图标字体

1. 使用IcoMoon或Fontello等工具创建自定义图标字体
2. 只选择项目中需要的图标
3. 导出为WOFF2格式，替换当前占位符

## 注意事项

确保所有使用的图标字体都有合法的许可证。如使用开源字体，请确保遵循其许可要求。 