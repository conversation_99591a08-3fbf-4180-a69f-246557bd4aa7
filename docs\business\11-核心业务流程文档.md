# 医院服务评价系统 - 核心业务流程文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-27
- **文档类型**: 业务流程设计
- **目标读者**: 业务分析师、产品经理、开发工程师

## 1. 业务流程概述

### 1.1 系统业务目标
- **主要目标**: 收集患者对医疗服务的真实反馈
- **次要目标**: 提升医疗服务质量，优化患者体验
- **业务价值**: 数据驱动的服务改进，提高患者满意度

### 1.2 核心业务流程
```
医院服务评价系统核心业务流程
├── 1. 系统初始化流程
│   ├── 科室数据配置
│   ├── 工作人员信息录入
│   ├── 床位信息管理
│   └── 二维码生成与打印
├── 2. 患者评价流程
│   ├── 二维码扫描
│   ├── 身份验证
│   ├── 工作人员选择
│   ├── 评价内容填写
│   └── 评价提交确认
├── 3. 数据处理流程
│   ├── 评价数据验证
│   ├── 情感分析处理
│   ├── 数据存储归档
│   └── 统计分析生成
└── 4. 管理监控流程
    ├── 评价数据查看
    ├── 统计报表生成
    ├── 问题处理跟踪
    └── 系统维护管理
```

### 1.3 业务角色定义
| 角色 | 职责 | 权限 |
|------|------|------|
| 患者/家属 | 提交服务评价 | 扫描二维码、填写评价 |
| 科室管理员 | 管理科室数据 | 查看本科室评价、管理工作人员 |
| 系统管理员 | 系统维护管理 | 全系统数据管理、配置修改 |
| 医院领导 | 决策支持 | 查看统计报表、分析数据 |

## 2. 详细业务流程

### 2.1 系统初始化流程

#### 2.1.1 流程图
```mermaid
graph TD
    A[系统部署] --> B[基础数据配置]
    B --> C[科室信息录入]
    C --> D[工作人员类型配置]
    D --> E[工作人员信息录入]
    E --> F[床位信息配置]
    F --> G[二维码批量生成]
    G --> H[二维码打印分发]
    H --> I[系统上线运行]
```

#### 2.1.2 详细步骤

**步骤1: 科室信息录入**
```
输入: 科室基础信息
处理: 
  1. 验证科室编码唯一性
  2. 检查科室名称规范性
  3. 保存科室信息到数据库
输出: 科室数据记录
业务规则:
  - 科室编码必须唯一
  - 科室名称不能为空
  - 支持科室信息修改
```

**步骤2: 工作人员信息录入**
```
输入: 工作人员详细信息
处理:
  1. 验证工号唯一性
  2. 关联科室和人员类型
  3. 上传工作人员照片
  4. 保存人员信息
输出: 工作人员数据记录
业务规则:
  - 工号全局唯一
  - 必须关联有效科室
  - 照片格式限制: JPG/PNG, 最大2MB
  - 支持批量导入Excel
```

**步骤3: 床位信息配置**
```
输入: 床位基础信息
处理:
  1. 验证床位号在科室内唯一性
  2. 关联负责工作人员
  3. 设置床位区域信息
  4. 保存床位数据
输出: 床位数据记录
业务规则:
  - 床位号在科室内唯一
  - 可指定负责人(可选)
  - 支持区域划分管理
```

**步骤4: 二维码生成**
```
输入: 床位信息
处理:
  1. 为每个床位生成唯一UUID
  2. 创建加密参数
  3. 生成二维码图片
  4. 关联床位和二维码
输出: 二维码数据和图片
业务规则:
  - 一个床位对应一个二维码
  - UUID全局唯一
  - 支持二维码重新生成
  - 支持批量打印
```

### 2.2 患者评价流程

#### 2.2.1 流程图
```mermaid
graph TD
    A[患者扫描二维码] --> B[获取加密参数]
    B --> C[前端参数验证]
    C --> D{参数格式正确?}
    D -->|否| E[显示错误信息]
    D -->|是| F[调用验证API]
    F --> G{二维码有效?}
    G -->|否| H[显示无效提示]
    G -->|是| I[获取床位科室信息]
    I --> J[加载工作人员列表]
    J --> K[显示评价表单]
    K --> L[患者选择工作人员]
    L --> M[填写评价内容]
    M --> N[提交评价数据]
    N --> O{数据验证通过?}
    O -->|否| P[显示验证错误]
    O -->|是| Q[保存评价数据]
    Q --> R[显示提交成功]
    R --> S[记录设备指纹]
```

#### 2.2.2 详细步骤

**步骤1: 二维码扫描验证**
```
输入: 加密的二维码参数
处理:
  1. 前端接收URL参数
  2. 验证参数格式
  3. 调用后端验证API
  4. 解密获取UUID
  5. 查询二维码信息
输出: 床位、科室、工作人员信息
业务规则:
  - 参数必须是有效的加密格式
  - 二维码必须处于激活状态
  - 参数有24小时有效期
异常处理:
  - 参数格式错误: 显示"二维码格式无效"
  - 二维码不存在: 显示"二维码无效或已过期"
  - 网络错误: 显示"网络连接失败，请重试"
```

**步骤2: 工作人员选择**
```
输入: 科室工作人员列表
处理:
  1. 按人员类型分组显示
  2. 显示工作人员照片和信息
  3. 支持满意/不满意选择
  4. 实时验证选择数量
输出: 工作人员评价列表
业务规则:
  - 最多选择3个满意的工作人员
  - 最多选择3个不满意的工作人员
  - 同一工作人员不能同时满意和不满意
  - 至少选择1个工作人员
界面交互:
  - 点击头像切换满意/不满意/未选择
  - 超出数量限制时显示提示
  - 实时显示已选择数量
```

**步骤3: 评价内容填写**
```
输入: 用户输入的评价信息
处理:
  1. 验证评价内容长度
  2. 验证联系信息格式
  3. 实时字数统计
  4. 敏感词过滤
输出: 验证后的评价数据
业务规则:
  - 评价内容: 10-1000字
  - 住院号: 可选，6-20位
  - 联系电话: 可选，11位手机号
  - 禁止输入恶意代码
数据验证:
  - 前端实时验证
  - 后端二次验证
  - XSS攻击防护
```

**步骤4: 评价提交处理**
```
输入: 完整的评价数据
处理:
  1. 最终数据验证
  2. 生成设备指纹
  3. 检查重复提交
  4. 保存评价记录
  5. 更新统计数据
输出: 提交结果确认
业务规则:
  - 同一设备1小时内只能评价一次
  - 评价数据完整性检查
  - 自动进行情感分析
防重复机制:
  - 设备指纹识别
  - IP地址记录
  - 时间窗口限制
```

### 2.3 数据处理流程

#### 2.3.1 评价数据处理
```mermaid
graph TD
    A[接收评价数据] --> B[数据格式验证]
    B --> C[业务规则检查]
    C --> D[设备指纹验证]
    D --> E{是否重复提交?}
    E -->|是| F[拒绝提交]
    E -->|否| G[保存评价数据]
    G --> H[情感分析处理]
    H --> I[更新统计缓存]
    I --> J[记录操作日志]
    J --> K[发送处理结果]
```

**数据验证规则**:
```
1. 格式验证:
   - JSON格式正确性
   - 必填字段完整性
   - 数据类型正确性

2. 业务规则验证:
   - 工作人员ID有效性
   - 评价数量限制检查
   - 内容长度限制检查

3. 安全验证:
   - XSS攻击检测
   - SQL注入防护
   - 恶意内容过滤
```

**情感分析处理**:
```
输入: 评价文本内容
处理:
  1. 文本预处理(去除标点、分词)
  2. 关键词提取
  3. 情感倾向分析
  4. 情感强度评分
输出: 情感分析结果
算法:
  - 基于词典的情感分析
  - 正面/负面/中性分类
  - 情感强度0-1评分
```

### 2.4 管理监控流程

#### 2.4.1 数据查看流程
```mermaid
graph TD
    A[管理员登录] --> B[权限验证]
    B --> C{权限类型?}
    C -->|科室管理员| D[查看本科室数据]
    C -->|系统管理员| E[查看全系统数据]
    D --> F[科室评价列表]
    E --> G[全院评价列表]
    F --> H[筛选和搜索]
    G --> H
    H --> I[详细评价查看]
    I --> J[评价处理标记]
    J --> K[生成处理报告]
```

**数据权限控制**:
```
科室管理员权限:
  - 查看本科室评价数据
  - 查看本科室工作人员评价
  - 导出本科室报表
  - 管理本科室工作人员信息

系统管理员权限:
  - 查看全院评价数据
  - 管理所有科室信息
  - 系统配置管理
  - 用户权限管理
  - 数据备份恢复
```

#### 2.4.2 统计分析流程
```
数据统计维度:
1. 时间维度: 日/周/月/季/年
2. 科室维度: 按科室分组统计
3. 人员维度: 按工作人员统计
4. 类型维度: 按人员类型统计

统计指标:
1. 满意度指标:
   - 整体满意率
   - 科室满意率
   - 个人满意率
   - 满意度趋势

2. 评价量指标:
   - 评价总数
   - 日均评价量
   - 科室评价分布
   - 时段评价分布

3. 质量指标:
   - 问题反馈率
   - 处理及时率
   - 改进效果评估
```

## 3. 异常处理流程

### 3.1 系统异常处理

#### 3.1.1 网络异常处理
```
异常类型: 网络连接失败
处理流程:
  1. 检测网络状态
  2. 自动重试机制(最多3次)
  3. 显示友好错误提示
  4. 提供手动重试选项
  5. 记录异常日志

用户体验:
  - 显示网络状态指示器
  - 提供离线模式提示
  - 保存用户输入数据
  - 网络恢复后自动重试
```

#### 3.1.2 数据异常处理
```
异常类型: 数据验证失败
处理流程:
  1. 前端实时验证提示
  2. 后端二次验证确认
  3. 详细错误信息返回
  4. 用户友好的错误提示
  5. 引导用户正确操作

错误类型:
  - 必填字段缺失
  - 数据格式错误
  - 业务规则违反
  - 数据长度超限
```

### 3.2 业务异常处理

#### 3.2.1 重复提交处理
```
检测机制:
  1. 设备指纹识别
  2. IP地址记录
  3. 时间窗口检查
  4. 二维码使用记录

处理策略:
  1. 友好提示用户
  2. 显示上次提交时间
  3. 提供申诉渠道
  4. 记录异常行为

用户提示:
  "您已经对此床位进行过评价，感谢您的参与！
   如有疑问，请联系护士站。"
```

#### 3.2.2 数据不一致处理
```
检测场景:
  1. 工作人员信息变更
  2. 科室调整
  3. 床位变更
  4. 二维码失效

处理机制:
  1. 数据一致性检查
  2. 自动数据修复
  3. 人工审核确认
  4. 历史数据保护

修复策略:
  - 保持历史评价数据完整性
  - 新评价使用最新数据
  - 提供数据迁移工具
  - 记录所有变更操作
```

## 4. 业务规则总结

### 4.1 核心业务规则
```
1. 评价数量限制:
   - 每次最多选择3个满意的工作人员
   - 每次最多选择3个不满意的工作人员
   - 至少选择1个工作人员进行评价

2. 重复提交限制:
   - 同一设备1小时内只能对同一床位评价一次
   - 基于设备指纹和IP地址识别

3. 数据有效性:
   - 二维码参数24小时有效期
   - 评价内容10-1000字限制
   - 联系信息格式验证

4. 权限控制:
   - 科室管理员只能查看本科室数据
   - 系统管理员可以查看全部数据
   - 患者只能提交评价，不能查看他人评价
```

### 4.2 数据完整性规则
```
1. 关联关系:
   - 床位必须关联科室
   - 二维码必须关联床位
   - 工作人员必须关联科室和类型
   - 评价必须关联二维码和床位

2. 唯一性约束:
   - 科室编码全局唯一
   - 工作人员工号全局唯一
   - 床位号在科室内唯一
   - 二维码UUID全局唯一

3. 数据保护:
   - 历史评价数据不可修改
   - 删除操作需要确认
   - 重要操作记录日志
   - 定期数据备份
```

---

**文档维护**: 业务分析组  
**审核状态**: 已审核  
**更新频率**: 业务流程变更时同步更新