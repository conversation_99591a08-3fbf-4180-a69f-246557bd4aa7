# Generated by Django 4.2.7 on 2025-02-22 16:58

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0020_printtemplate_print_height_printtemplate_print_width'),
    ]

    operations = [
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_position_x',
            field=models.IntegerField(default=105, verbose_name='二维码X坐标'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_position_y',
            field=models.IntegerField(default=148, verbose_name='二维码Y坐标'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='qr_size',
            field=models.IntegerField(default=40, help_text='建议30-50像素，过大会遮挡背景，过小可能难以扫描', verbose_name='二维码尺寸'),
        ),
    ]
