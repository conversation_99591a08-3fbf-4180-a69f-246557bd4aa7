<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>感谢您的评价</title>
    <link rel="stylesheet" href="css/bootstrap.min.css">
    <link rel="stylesheet" href="css/style.css">
    <style>
        .thank-you-container {
            text-align: center;
            padding: 50px 20px;
            max-width: 600px;
            margin: 0 auto;
        }
        .thank-you-icon {
            font-size: 80px;
            color: #28a745;
            margin-bottom: 30px;
        }
        .thank-you-title {
            font-size: 28px;
            margin-bottom: 20px;
            color: #333;
        }
        .thank-you-message {
            font-size: 18px;
            margin-bottom: 30px;
            color: #666;
            line-height: 1.6;
        }
        .home-button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 25px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
            display: inline-block;
            margin-top: 10px;
        }
        .home-button:hover {
            background-color: #0069d9;
            color: white;
            text-decoration: none;
        }

        .redirect-countdown {
            max-width: 300px;
            margin: 0 auto 20px;
        }

        .redirect-countdown p {
            text-align: center;
            margin-bottom: 10px;
            font-size: 16px;
            color: #666;
        }

        .redirect-countdown .progress {
            height: 10px;
            border-radius: 5px;
            background-color: #f0f0f0;
        }

        .redirect-countdown .progress-bar {
            background-color: #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="thank-you-container">
            <div class="thank-you-icon">✓</div>
            <h1 class="thank-you-title">感谢您的评价!</h1>
            <p class="thank-you-message">
                您的反馈对我们非常重要，我们将认真考虑您的意见和建议，
                不断改进我们的服务质量，为您提供更好的体验。
            </p>
            <div class="redirect-countdown mt-4">
                <p>页面将在 <span id="countdown">5</span> 秒后自动返回首页</p>
                <div class="progress">
                    <div id="countdown-progress" class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                </div>
            </div>

        </div>
    </div>

    <script src="js/jquery-3.5.1.min.js"></script>
    <script src="js/bootstrap.min.js"></script>
    <script>
        // 5秒后自动返回首页，并显示倒计时
        const totalSeconds = 5;
        let secondsLeft = totalSeconds;
        const countdownElement = document.getElementById('countdown');
        const progressBar = document.getElementById('countdown-progress');

        // 初始化倒计时显示
        countdownElement.textContent = secondsLeft;

        // 更新倒计时和进度条
        function updateCountdown() {
            secondsLeft--;
            countdownElement.textContent = secondsLeft;

            // 更新进度条
            const progressPercentage = ((totalSeconds - secondsLeft) / totalSeconds) * 100;
            progressBar.style.width = progressPercentage + '%';
            progressBar.setAttribute('aria-valuenow', progressPercentage);

            if (secondsLeft <= 0) {
                window.location.href = 'index.html';
            } else {
                setTimeout(updateCountdown, 1000);
            }
        }

        // 开始倒计时
        setTimeout(updateCountdown, 1000);
    </script>
</body>
</html>