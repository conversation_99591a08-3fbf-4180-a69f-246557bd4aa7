#!/usr/bin/env python
# 修复OperationLogListView类中的get_queryset方法

import re

# 读取views.py文件
views_file = 'qrmanager/views.py'
with open(views_file, 'r', encoding='utf-8') as f:
    content = f.read()

# 查找OperationLogListView类的位置
class_pattern = r'class\s+OperationLogListView\s*\(\s*LoginRequiredMixin\s*,\s*ListView\s*\)\s*:'
match = re.search(class_pattern, content)

if match:
    class_start = match.start()
    print(f"找到OperationLogListView类，开始位置：{class_start}")
    
    # 查找类中的get_queryset方法
    method_pattern = r'def\s+get_queryset\s*\(\s*self\s*\)\s*:'
    class_content = content[class_start:]
    method_match = re.search(method_pattern, class_content)
    
    if method_match:
        method_start = class_start + method_match.start()
        print(f"找到get_queryset方法，开始位置：{method_start}")
        
        # 查找下一个方法（或类）的开始位置，作为get_queryset方法的结束位置
        next_def = re.search(r'\n\s*def\s+', class_content[method_match.end():])
        next_class = re.search(r'\n\s*class\s+', class_content[method_match.end():])
        
        method_end = None
        if next_def:
            method_end = class_start + method_match.end() + next_def.start()
        elif next_class:
            method_end = class_start + method_match.end() + next_class.start()
        else:
            # 如果找不到下一个方法或类，就使用文件结尾
            method_end = len(content)
        
        if method_end:
            print(f"确定get_queryset方法结束位置：{method_end}")
            
            # 创建正确的get_queryset方法
            correct_method = """    def get_queryset(self):
        queryset = OperationLog.objects.select_related('user')

        # 过滤条件
        user_id = self.request.GET.get('user')
        action = self.request.GET.get('action')
        status = self.request.GET.get('status')
        date_from = self.request.GET.get('date_from')
        date_to = self.request.GET.get('date_to')
        time_range = self.request.GET.get('time_range')

        # 应用过滤器
        if user_id and user_id.isdigit():
            queryset = queryset.filter(user_id=user_id)
        if action:
            queryset = queryset.filter(action__icontains=action)
        if status:
            queryset = queryset.filter(status=status)

        # 日期范围过滤
        if date_from:
            try:
                date_from = timezone.datetime.strptime(date_from, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__gte=date_from)
            except ValueError:
                pass
        if date_to:
            try:
                date_to = timezone.datetime.strptime(date_to, '%Y-%m-%d').date()
                queryset = queryset.filter(created_at__date__lte=date_to)
            except ValueError:
                pass

        # 时间范围快捷筛选
        if time_range:
            now = timezone.now()
            if time_range == 'today':
                queryset = queryset.filter(created_at__date=now.date())
            elif time_range == 'yesterday':
                queryset = queryset.filter(created_at__date=now.date() - timezone.timedelta(days=1))
            elif time_range == 'this_week':
                # 本周的开始（星期一）
                week_start = now.date() - timezone.timedelta(days=now.weekday())
                queryset = queryset.filter(created_at__date__gte=week_start)
            elif time_range == 'this_month':
                # 本月的开始（1号）
                month_start = now.date().replace(day=1)
                queryset = queryset.filter(created_at__date__gte=month_start)
            elif time_range == 'last_30_days':
                # 过去30天
                days_30 = now.date() - timezone.timedelta(days=30)
                queryset = queryset.filter(created_at__date__gte=days_30)

        return queryset.order_by('-created_at')
"""
            
            # 替换方法
            new_content = content[:method_start] + correct_method + content[method_end:]
            
            # 写回文件
            with open(views_file, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            print("\n✅ 已成功修复get_queryset方法！")
            print("✅ 移除了错误的prefetch_related('evaluation_set')调用")
            print("✅ 使用了正确的select_related('user')方法")
            print("✅ 修复了所有缩进问题")
            print("\n请重新启动后端服务。")
        else:
            print("\n无法确定get_queryset方法的结束位置，请手动修复。")
    else:
        print("\n在OperationLogListView类中找不到get_queryset方法，请手动修复。")
else:
    print("\n找不到OperationLogListView类，请检查views.py文件。") 