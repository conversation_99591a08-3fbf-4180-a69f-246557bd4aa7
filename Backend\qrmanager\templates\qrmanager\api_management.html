{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}API管理{% endblock %}

{% block extra_css %}
<style>
    /* 统计卡片样式 */
    .stat-card {
        border-radius: 12px;
        overflow: hidden;
        transition: all 0.3s ease;
        height: 100%;
    }
    
    .stat-card:hover {
        transform: translateY(-5px);
        box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }
    
    .stat-card-body {
        padding: 1.5rem;
        display: flex;
        align-items: center;
    }
    
    .stat-card-icon {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 3rem;
        height: 3rem;
        border-radius: 50%;
        background-color: rgba(255, 255, 255, 0.2);
        margin-right: 1rem;
        font-size: 1.5rem;
    }
    
    .stat-card-number {
        font-size: 1.8rem;
        font-weight: 600;
        margin-bottom: 0.2rem;
        line-height: 1;
    }
    
    .stat-card-text {
        font-size: 0.875rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
    }
    
    .stat-trend {
        font-size: 0.75rem;
        display: flex;
        align-items: center;
        padding: 0.2rem 0.5rem;
        border-radius: 20px;
        width: fit-content;
    }
    
    .stat-trend.up {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    .stat-trend.down {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    .stat-trend.stable {
        background-color: rgba(255, 255, 255, 0.2);
        color: white;
    }
    
    .stat-trend i {
        margin-right: 0.25rem;
    }
    
    /* 表格样式增强 */
    .table {
        border-collapse: separate;
        border-spacing: 0;
    }
    
    .table th {
        font-weight: 600;
        border-bottom-width: 2px;
    }
    
    .table td {
        vertical-align: middle;
    }
    
    .table-hover tbody tr:hover {
        background-color: rgba(13, 110, 253, 0.05);
    }
    
    /* 卡片样式增强 */
    .card {
        border: none;
        box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
        border-radius: 0.5rem;
        transition: all 0.2s ease;
    }
    
    .card:hover {
        box-shadow: 0 0.5rem 1rem rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        background-color: transparent;
        border-bottom: 1px solid rgba(0, 0, 0, 0.05);
        padding: 1rem 1.25rem;
    }
    
    /* 按钮样式增强 */
    .btn {
        border-radius: 0.375rem;
        padding: 0.375rem 0.75rem;
        font-weight: 500;
        transition: all 0.2s ease;
    }
    
    .btn-sm {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
    
    .btn:hover {
        transform: translateY(-1px);
    }
    
    /* 标签页导航增强 */
    .nav-tabs {
        border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    }
    
    .nav-tabs .nav-link {
        border: none;
        padding: 0.75rem 1rem;
        font-weight: 500;
        color: #666;
        position: relative;
        transition: all 0.2s ease;
    }
    
    .nav-tabs .nav-link:hover {
        color: #333;
    }
    
    .nav-tabs .nav-link.active {
        color: #0d6efd;
        font-weight: 600;
        background-color: transparent;
        border: none;
    }
    
    .nav-tabs .nav-link.active::after {
        content: '';
        position: absolute;
        bottom: 0;
        left: 0;
        width: 100%;
        height: 2px;
        background-color: #0d6efd;
    }
    
    /* 动画效果 */
    .fade-in {
        animation: fadeIn 0.5s ease-in-out;
    }
    
    @keyframes fadeIn {
        from { opacity: 0; transform: translateY(10px); }
        to { opacity: 1; transform: translateY(0); }
    }
    
    /* 响应式调整 */
    @media (max-width: 768px) {
        .card-body {
            padding: 1rem;
        }
        
        .stat-card-number {
            font-size: 1.4rem;
        }
        
        .stat-card-icon {
            width: 2.5rem;
            height: 2.5rem;
            font-size: 1.2rem;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <div class="card fade-in shadow-sm">
                <div class="card-body">
                    <h2 class="card-title mb-4">
                        <i class="fas fa-laptop-code me-2" style="color: #00acc1;"></i>API管理中心
                    </h2>
                    <p class="text-muted">管理和监控系统API接口、授权以及访问统计</p>
                    
                    <!-- 概览统计 -->
                    <div class="row g-4 mb-4">
                        <div class="col-xl-3 col-md-6">
                            <div class="stat-card bg-primary bg-gradient shadow-sm">
                                <div class="stat-card-body">
                                    <div class="stat-card-icon">
                                        <i class="fas fa-bolt"></i>
                                    </div>
                                    <div class="stat-card-info">
                                        <h3 class="stat-card-number">{{ api_count|default:"24" }}</h3>
                                        <p class="stat-card-text">活跃API</p>
                                        <div class="stat-trend up">
                                            <i class="fas fa-arrow-up"></i> <span>5%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="stat-card bg-success bg-gradient shadow-sm">
                                <div class="stat-card-body">
                                    <div class="stat-card-icon">
                                        <i class="fas fa-key"></i>
                                    </div>
                                    <div class="stat-card-info">
                                        <h3 class="stat-card-number">{{ api_keys|default:"8" }}</h3>
                                        <p class="stat-card-text">API密钥</p>
                                        <div class="stat-trend stable">
                                            <i class="fas fa-equals"></i> <span>稳定</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="stat-card bg-info bg-gradient shadow-sm">
                                <div class="stat-card-body">
                                    <div class="stat-card-icon">
                                        <i class="fas fa-chart-line"></i>
                                    </div>
                                    <div class="stat-card-info">
                                        <h3 class="stat-card-number">{{ api_requests|default:"2.4K" }}</h3>
                                        <p class="stat-card-text">今日请求</p>
                                        <div class="stat-trend up">
                                            <i class="fas fa-arrow-up"></i> <span>12%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-3 col-md-6">
                            <div class="stat-card bg-warning bg-gradient shadow-sm">
                                <div class="stat-card-body">
                                    <div class="stat-card-icon">
                                        <i class="fas fa-exclamation-triangle"></i>
                                    </div>
                                    <div class="stat-card-info">
                                        <h3 class="stat-card-number">{{ api_errors|default:"3" }}%</h3>
                                        <p class="stat-card-text">错误率</p>
                                        <div class="stat-trend down">
                                            <i class="fas fa-arrow-down"></i> <span>2%</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- 功能导航按钮 -->
                    <div class="d-flex flex-wrap gap-2 mb-4">
                        <button class="btn btn-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#newApiKeyModal">
                            <i class="fas fa-plus-circle me-2"></i>新建API密钥
                        </button>
                        <button class="btn btn-outline-primary shadow-sm" data-bs-toggle="modal" data-bs-target="#apiDocsModal">
                            <i class="fas fa-book me-2"></i>API文档简介
                        </button>
                        <a href="{% url 'qrmanager:api_docs' %}" class="btn btn-outline-primary shadow-sm">
                            <i class="fas fa-book-open me-2"></i>完整API文档
                        </a>
                        <button class="btn btn-outline-secondary shadow-sm" id="exportDataBtn">
                            <i class="fas fa-download me-2"></i>导出数据
                        </button>
                        <button class="btn btn-outline-info shadow-sm" id="showAnalyticsBtn">
                            <i class="fas fa-chart-pie me-2"></i>详细分析
                        </button>
                    </div>
                    
                    <!-- 状态提示栏 -->
                    <div class="alert alert-info d-flex align-items-center mb-4" role="alert">
                        <i class="fas fa-info-circle me-2"></i>
                        <div>
                            系统监测到 <strong>/api/departments/25/</strong> 和 <strong>/api/beds/template/</strong> 接口近期访问量激增，可能需要优化性能。
                        </div>
                    </div>
                    
                    <!-- 标签页导航 -->
                    <ul class="nav nav-tabs mb-4" id="apiTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active" id="endpoints-tab" data-bs-toggle="tab" data-bs-target="#endpoints" type="button" role="tab">
                                <i class="fas fa-link me-2"></i>接口端点
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="keys-tab" data-bs-toggle="tab" data-bs-target="#keys" type="button" role="tab">
                                <i class="fas fa-key me-2"></i>API密钥
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs" type="button" role="tab">
                                <i class="fas fa-list-alt me-2"></i>访问日志
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="settings-tab" data-bs-toggle="tab" data-bs-target="#settings" type="button" role="tab">
                                <i class="fas fa-cog me-2"></i>设置
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link" id="monitor-tab" data-bs-toggle="tab" data-bs-target="#monitor" type="button" role="tab">
                                <i class="fas fa-heartbeat me-2"></i>实时监控
                            </button>
                        </li>
                    </ul>
                    
                    <!-- 标签页内容 -->
                    <div class="tab-content" id="apiTabsContent">
                        <!-- 接口端点标签页 -->
                        <div class="tab-pane fade show active" id="endpoints" role="tabpanel">
                            <!-- 搜索和筛选工具栏 -->
                            <div class="d-flex justify-content-between align-items-center flex-wrap mb-3">
                                <div class="d-flex align-items-center mb-2 mb-md-0">
                                    <div class="input-group me-2" style="width: 280px;">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" placeholder="搜索接口..." id="endpointSearch">
                                    </div>
                                    <select class="form-select me-2" style="width: 120px;" id="methodFilter">
                                        <option value="">所有方法</option>
                                        <option value="GET">GET</option>
                                        <option value="POST">POST</option>
                                        <option value="PUT">PUT</option>
                                        <option value="DELETE">DELETE</option>
                                    </select>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary me-2">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-plus me-1"></i>添加
                                    </button>
                                </div>
                            </div>
                            
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>端点名称</th>
                                            <th>URL路径</th>
                                            <th>请求方法</th>
                                            <th>状态</th>
                                            <th>请求数</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">GET</span>
                                                    科室详情
                                                </div>
                                            </td>
                                            <td><code>/api/departments/25/</code></td>
                                            <td>GET</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    928
                                                    <span class="ms-2 text-success"><i class="fas fa-arrow-up"></i></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="查看详情"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-outline-success" data-bs-toggle="tooltip" title="编辑"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger" data-bs-toggle="tooltip" title="禁用"><i class="fas fa-ban"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">GET</span>
                                                    床位模板
                                                </div>
                                            </td>
                                            <td><code>/api/beds/template/</code></td>
                                            <td>GET</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    754
                                                    <span class="ms-2 text-success"><i class="fas fa-arrow-up"></i></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="查看详情"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-outline-success" data-bs-toggle="tooltip" title="编辑"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger" data-bs-toggle="tooltip" title="禁用"><i class="fas fa-ban"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-success me-2">POST</span>
                                                    提交评价
                                                </div>
                                            </td>
                                            <td><code>/api/evaluations/</code></td>
                                            <td>POST</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    586
                                                    <span class="ms-2 text-muted"><i class="fas fa-equals"></i></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="查看详情"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-outline-success" data-bs-toggle="tooltip" title="编辑"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger" data-bs-toggle="tooltip" title="禁用"><i class="fas fa-ban"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-primary me-2">GET</span>
                                                    查询二维码
                                                </div>
                                            </td>
                                            <td><code>/api/qrcodes/</code></td>
                                            <td>GET</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    743
                                                    <span class="ms-2 text-success"><i class="fas fa-arrow-up"></i></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="查看详情"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-outline-success" data-bs-toggle="tooltip" title="编辑"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger" data-bs-toggle="tooltip" title="禁用"><i class="fas fa-ban"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    <span class="badge bg-danger me-2">DELETE</span>
                                                    删除评价
                                                </div>
                                            </td>
                                            <td><code>/api/evaluations/34/</code></td>
                                            <td>DELETE</td>
                                            <td><span class="badge bg-warning">低流量</span></td>
                                            <td>
                                                <div class="d-flex align-items-center">
                                                    12
                                                    <span class="ms-2 text-danger"><i class="fas fa-arrow-down"></i></span>
                                                </div>
                                            </td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary" data-bs-toggle="tooltip" title="查看详情"><i class="fas fa-eye"></i></button>
                                                    <button class="btn btn-outline-success" data-bs-toggle="tooltip" title="编辑"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger" data-bs-toggle="tooltip" title="禁用"><i class="fas fa-ban"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <nav aria-label="接口分页" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true"><i class="fas fa-chevron-left"></i></a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        
                        <!-- API密钥标签页 -->
                        <div class="tab-pane fade" id="keys" role="tabpanel">
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead>
                                        <tr>
                                            <th>名称</th>
                                            <th>密钥ID</th>
                                            <th>创建日期</th>
                                            <th>权限</th>
                                            <th>状态</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>移动端应用</td>
                                            <td><code>apk_*****b6c98</code></td>
                                            <td>2023-12-01</td>
                                            <td>只读</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary"><i class="fas fa-key"></i></button>
                                                    <button class="btn btn-outline-success"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                        <tr>
                                            <td>Web前端</td>
                                            <td><code>web_*****f3e21</code></td>
                                            <td>2023-11-15</td>
                                            <td>读写</td>
                                            <td><span class="badge bg-success">活跃</span></td>
                                            <td>
                                                <div class="btn-group btn-group-sm">
                                                    <button class="btn btn-outline-primary"><i class="fas fa-key"></i></button>
                                                    <button class="btn btn-outline-success"><i class="fas fa-edit"></i></button>
                                                    <button class="btn btn-outline-danger"><i class="fas fa-trash"></i></button>
                                                </div>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- 访问日志标签页 -->
                        <div class="tab-pane fade" id="logs" role="tabpanel">
                            <!-- 日志筛选工具栏 -->
                            <div class="d-flex justify-content-between align-items-center flex-wrap mb-3">
                                <div class="d-flex align-items-center mb-2 mb-md-0">
                                    <div class="input-group me-2" style="width: 200px;">
                                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                                        <input type="text" class="form-control" placeholder="搜索日志..." id="logSearch">
                                    </div>
                                    <select class="form-select me-2" style="width: 110px;" id="statusFilter">
                                        <option value="">所有状态</option>
                                        <option value="2xx">成功(2xx)</option>
                                        <option value="4xx">客户端错误(4xx)</option>
                                        <option value="5xx">服务器错误(5xx)</option>
                                    </select>
                                    <select class="form-select" style="width: 160px;" id="timeFilter">
                                        <option value="today">今天</option>
                                        <option value="yesterday">昨天</option>
                                        <option value="week">最近7天</option>
                                        <option value="month">最近30天</option>
                                    </select>
                                </div>
                                <div>
                                    <button class="btn btn-sm btn-outline-secondary me-2">
                                        <i class="fas fa-sync-alt me-1"></i>刷新
                                    </button>
                                    <button class="btn btn-sm btn-outline-primary">
                                        <i class="fas fa-download me-1"></i>导出
                                    </button>
                                </div>
                            </div>
                            
                            <!-- 日志警告框 -->
                            {% if recent_errors %}
                            <div class="alert alert-warning d-flex align-items-center mb-3" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <div>
                                    检测到 <strong>{{ recent_errors|length }}</strong> 个API日志错误。<a href="#" class="alert-link">查看详情</a>
                                </div>
                            </div>
                            {% else %}
                            <div class="alert alert-success d-flex align-items-center mb-3" role="alert">
                                <i class="fas fa-check-circle me-2"></i>
                                <div>
                                    API日志系统运行正常，未检测到错误。
                                </div>
                            </div>
                            {% endif %}
                            
                            <div class="table-responsive">
                                <table class="table table-hover align-middle">
                                    <thead class="table-light">
                                        <tr>
                                            <th>时间</th>
                                            <th>用户</th>
                                            <th>操作</th>
                                            <th>描述</th>
                                            <th>详情</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        {% for log in access_logs %}
                                        <tr>
                                            <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                            <td>{{ log.user.username|default:"未登录用户" }}</td>
                                            <td>{{ log.action }}</td>
                                            <td>{{ log.description|truncatechars:60 }}</td>
                                            <td>
                                                <button class="btn btn-sm btn-outline-secondary" title="查看详情">
                                                    <i class="fas fa-info-circle"></i>
                                                </button>
                                            </td>
                                        </tr>
                                        {% empty %}
                                        <tr>
                                            <td colspan="5" class="text-center py-3">
                                                <i class="fas fa-info-circle me-1 text-info"></i> 暂无访问日志记录
                                            </td>
                                        </tr>
                                        {% endfor %}
                                    </tbody>
                                </table>
                            </div>
                            
                            <!-- 分页 -->
                            <nav aria-label="日志分页" class="mt-3">
                                <ul class="pagination justify-content-center">
                                    <li class="page-item disabled">
                                        <a class="page-link" href="#" tabindex="-1" aria-disabled="true"><i class="fas fa-chevron-left"></i></a>
                                    </li>
                                    <li class="page-item active"><a class="page-link" href="#">1</a></li>
                                    <li class="page-item"><a class="page-link" href="#">2</a></li>
                                    <li class="page-item"><a class="page-link" href="#">3</a></li>
                                    <li class="page-item">
                                        <a class="page-link" href="#"><i class="fas fa-chevron-right"></i></a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                        
                        <!-- 实时监控标签页 -->
                        <div class="tab-pane fade" id="monitor" role="tabpanel">
                            <div class="row g-4">
                                <!-- 实时性能图表 -->
                                <div class="col-xl-8">
                                    <div class="card shadow-sm mb-4">
                                        <div class="card-header d-flex justify-content-between align-items-center bg-transparent">
                                            <h5 class="card-title mb-0">API性能监控</h5>
                                            <div class="btn-group btn-group-sm">
                                                <button class="btn btn-outline-secondary active">1小时</button>
                                                <button class="btn btn-outline-secondary">6小时</button>
                                                <button class="btn btn-outline-secondary">24小时</button>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div style="height: 300px; position: relative;">
                                                <canvas id="performanceChart"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <div class="card shadow-sm">
                                        <div class="card-header d-flex justify-content-between align-items-center bg-transparent">
                                            <h5 class="card-title mb-0">请求分布</h5>
                                            <div class="dropdown">
                                                <button class="btn btn-sm btn-outline-secondary dropdown-toggle" type="button" data-bs-toggle="dropdown">
                                                    今日
                                                </button>
                                                <ul class="dropdown-menu">
                                                    <li><a class="dropdown-item" href="#">今日</a></li>
                                                    <li><a class="dropdown-item" href="#">本周</a></li>
                                                    <li><a class="dropdown-item" href="#">本月</a></li>
                                                </ul>
                                            </div>
                                        </div>
                                        <div class="card-body">
                                            <div style="height: 250px; position: relative;">
                                                <canvas id="requestDistribution"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                
                                <!-- 监控侧边栏 -->
                                <div class="col-xl-4">
                                    <!-- 系统状态 -->
                                    <div class="card shadow-sm mb-4">
                                        <div class="card-header bg-transparent">
                                            <h5 class="card-title mb-0">系统状态</h5>
                                        </div>
                                        <div class="card-body">
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>API服务器</span>
                                                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>正常</span>
                                                </div>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 87%"></div>
                                                </div>
                                                <small class="text-muted">负载: 87%</small>
                                            </div>
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>数据库</span>
                                                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>正常</span>
                                                </div>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 65%"></div>
                                                </div>
                                                <small class="text-muted">负载: 65%</small>
                                            </div>
                                            <div class="mb-3">
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>缓存服务</span>
                                                    <span class="text-warning"><i class="fas fa-exclamation-circle me-1"></i>注意</span>
                                                </div>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-warning" style="width: 92%"></div>
                                                </div>
                                                <small class="text-muted">负载: 92%</small>
                                            </div>
                                            <div>
                                                <div class="d-flex justify-content-between mb-1">
                                                    <span>文件存储</span>
                                                    <span class="text-success"><i class="fas fa-check-circle me-1"></i>正常</span>
                                                </div>
                                                <div class="progress" style="height: 6px;">
                                                    <div class="progress-bar bg-success" style="width: 41%"></div>
                                                </div>
                                                <small class="text-muted">负载: 41%</small>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 热门接口 -->
                                    <div class="card shadow-sm mb-4">
                                        <div class="card-header bg-transparent">
                                            <h5 class="card-title mb-0">热门接口</h5>
                                        </div>
                                        <div class="card-body p-0">
                                            <div class="list-group list-group-flush">
                                                <a href="#" class="list-group-item list-group-item-action">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-primary me-2">GET</span>
                                                            <code>/api/departments/25/</code>
                                                        </div>
                                                        <span class="badge bg-light text-dark">928次</span>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-primary me-2">GET</span>
                                                            <code>/api/beds/template/</code>
                                                        </div>
                                                        <span class="badge bg-light text-dark">754次</span>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-primary me-2">GET</span>
                                                            <code>/api/qrcodes/</code>
                                                        </div>
                                                        <span class="badge bg-light text-dark">743次</span>
                                                    </div>
                                                </a>
                                                <a href="#" class="list-group-item list-group-item-action">
                                                    <div class="d-flex justify-content-between align-items-center">
                                                        <div>
                                                            <span class="badge bg-success me-2">POST</span>
                                                            <code>/api/evaluations/</code>
                                                        </div>
                                                        <span class="badge bg-light text-dark">586次</span>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                    
                                    <!-- 异常警告 -->
                                    <div class="card bg-danger bg-opacity-10 border-danger mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title"><i class="fas fa-exclamation-triangle me-2"></i>需要注意</h5>
                                            <p class="card-text">检测到缓存服务负载过高，有可能影响API性能。</p>
                                            <button class="btn btn-sm btn-outline-danger">查看详情</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- 设置标签页 -->
                        <div class="tab-pane fade" id="settings" role="tabpanel">
                            <div class="row">
                                <div class="col-md-6">
                                    <div class="card mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title mb-3">API限流设置</h5>
                                            <div class="mb-3">
                                                <label class="form-label">每分钟请求限制</label>
                                                <input type="number" class="form-control" value="60">
                                                <small class="text-muted">每个IP地址每分钟最大请求次数</small>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">API密钥请求限制</label>
                                                <input type="number" class="form-control" value="1000">
                                                <small class="text-muted">每个API密钥每天最大请求次数</small>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" checked id="enableRateLimit">
                                                <label class="form-check-label" for="enableRateLimit">启用限流保护</label>
                                            </div>
                                            <button class="btn btn-primary">保存设置</button>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-6">
                                    <div class="card mb-4">
                                        <div class="card-body">
                                            <h5 class="card-title mb-3">安全设置</h5>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" checked id="enableCORS">
                                                <label class="form-check-label" for="enableCORS">启用CORS保护</label>
                                            </div>
                                            <div class="mb-3">
                                                <label class="form-label">允许的域名</label>
                                                <textarea class="form-control" rows="3">localhost, 127.0.0.1, *.zg120.cn</textarea>
                                                <small class="text-muted">每行一个域名，支持通配符</small>
                                            </div>
                                            <div class="form-check form-switch mb-3">
                                                <input class="form-check-input" type="checkbox" checked id="enableIPFilter">
                                                <label class="form-check-label" for="enableIPFilter">启用IP过滤</label>
                                            </div>
                                            <button class="btn btn-primary">保存设置</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row mb-4">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0"><i class="fas fa-key me-2"></i>API密钥管理</h5>
                </div>
                <div class="card-body">
                    <p>管理API密钥，控制第三方应用对API的访问权限。</p>
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <a href="{% url 'qrmanager:apikey_list' %}" class="btn btn-primary">
                                <i class="fas fa-key me-1"></i> 管理API密钥
                            </a>
                            <a href="{% url 'qrmanager:apilog_list' %}" class="btn btn-info ms-2">
                                <i class="fas fa-history me-1"></i> 查看API日志
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 新建API密钥模态框 -->
<div class="modal fade" id="newApiKeyModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">创建新API密钥</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form>
                    <div class="mb-3">
                        <label class="form-label">密钥名称</label>
                        <input type="text" class="form-control" placeholder="例如：移动应用API密钥">
                        <small class="text-muted">为您的API密钥指定一个易于识别的名称</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">权限级别</label>
                        <select class="form-select">
                            <option value="readonly">只读</option>
                            <option value="readwrite">读写</option>
                            <option value="admin">管理员</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">有效期</label>
                        <select class="form-select">
                            <option value="30">30天</option>
                            <option value="90">90天</option>
                            <option value="180">180天</option>
                            <option value="365">365天</option>
                            <option value="0" selected>永不过期</option>
                        </select>
                    </div>
                    <div class="form-check form-switch mb-3">
                        <input class="form-check-input" type="checkbox" id="enableIpRestriction">
                        <label class="form-check-label" for="enableIpRestriction">限制IP地址</label>
                    </div>
                    <div class="mb-3" id="ipRestrictionContainer" style="display: none;">
                        <label class="form-label">允许的IP地址</label>
                        <textarea class="form-control" rows="3" placeholder="每行一个IP地址或IP范围"></textarea>
                        <small class="text-muted">留空表示不限制IP地址</small>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary">创建密钥</button>
            </div>
        </div>
    </div>
</div>

<!-- API文档模态框 -->
<div class="modal fade" id="apiDocsModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">API文档</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="mb-4">
                    <h5>接口概述</h5>
                    <p>医院服务评价系统API提供了对科室、人员、评价等核心功能的编程访问能力。所有请求必须包含有效的API密钥。</p>
                </div>
                <div class="mb-4">
                    <h5>认证方式</h5>
                    <p>所有API请求都需要在HTTP头部包含以下认证信息:</p>
                    <pre><code>Authorization: Bearer YOUR_API_KEY</code></pre>
                </div>
                <div class="mb-4">
                    <h5>常用接口</h5>
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">获取科室列表</h6>
                            <p><code>GET /api/v1/departments/</code></p>
                            <p>参数:</p>
                            <ul>
                                <li><code>staff_type</code> (可选): 按工作人员类型筛选</li>
                                <li><code>search</code> (可选): 按名称模糊查询</li>
                            </ul>
                        </div>
                    </div>
                    <div class="card mb-3">
                        <div class="card-body">
                            <h6 class="card-title">提交评价</h6>
                            <p><code>POST /api/v1/evaluations/</code></p>
                            <p>必填字段:</p>
                            <ul>
                                <li><code>staff_id</code>: 工作人员ID</li>
                                <li><code>rating</code>: 评分(1-5)</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-primary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- API测试表单：提交评价 -->
<div class="card mt-4">
    <div class="card-header">
        <h5 class="mb-0">测试提交评价API</h5>
    </div>
    <div class="card-body">
        <form id="testSubmitEvaluationForm">
            <div class="mb-3">
                <label for="qrParam" class="form-label">二维码加密参数</label>
                <input type="text" class="form-control" id="qrParam" name="qrParam" required>
            </div>
            
            <div class="mb-3">
                <label for="hospitalNumber" class="form-label">住院号（选填）</label>
                <input type="text" class="form-control" id="hospitalNumber" name="hospitalNumber">
            </div>
            
            <div class="mb-3">
                <label for="phoneNumber" class="form-label">联系电话（选填）</label>
                <input type="text" class="form-control" id="phoneNumber" name="phoneNumber">
            </div>
            
            <div class="mb-3">
                <label for="comment" class="form-label">评价内容</label>
                <textarea class="form-control" id="comment" name="comment" rows="3" required></textarea>
            </div>
            
            <div id="staffEvaluationsContainer">
                <label class="form-label">工作人员评价</label>
                <div class="staff-evaluation mb-3 p-3 border rounded">
                    <div class="row">
                        <div class="col-md-6">
                            <label for="staffId1" class="form-label">工作人员ID</label>
                            <input type="number" class="form-control staffId" id="staffId1" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">满意度</label>
                            <div class="d-flex">
                                <div class="form-check me-3">
                                    <input class="form-check-input isSatisfied" type="radio" name="isSatisfied1" id="satisfied1" value="true" checked>
                                    <label class="form-check-label" for="satisfied1">满意</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input isSatisfied" type="radio" name="isSatisfied1" id="unsatisfied1" value="false">
                                    <label class="form-check-label" for="unsatisfied1">不满意</label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mb-3">
                <button type="button" id="addStaffEvalBtn" class="btn btn-outline-primary btn-sm">
                    <i class="fas fa-plus"></i> 添加更多工作人员
                </button>
            </div>
            
            <button type="submit" class="btn btn-primary">提交测试</button>
        </form>
        
        <div class="mt-3">
            <h6>API响应结果：</h6>
            <pre id="submitEvaluationResponse" class="border p-3 bg-light">尚未执行测试</pre>
        </div>
    </div>
</div>

{% endblock %}

{% block extra_js %}
<script src="https://cdn.jsdelivr.net/npm/chart.js@3.7.1/dist/chart.min.js"></script>
<script>
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl);
    });
    
    // 切换IP限制输入框
    document.getElementById('enableIpRestriction')?.addEventListener('change', function() {
        const container = document.getElementById('ipRestrictionContainer');
        if (container) {
            container.style.display = this.checked ? 'block' : 'none';
        }
    });
    
    // 搜索和筛选功能
    document.getElementById('endpointSearch')?.addEventListener('input', function() {
        filterEndpoints();
    });
    
    document.getElementById('methodFilter')?.addEventListener('change', function() {
        filterEndpoints();
    });
    
    document.getElementById('logSearch')?.addEventListener('input', function() {
        filterLogs();
    });
    
    document.getElementById('statusFilter')?.addEventListener('change', function() {
        filterLogs();
    });
    
    document.getElementById('timeFilter')?.addEventListener('change', function() {
        filterLogs();
    });
    
    function filterEndpoints() {
        const searchTerm = document.getElementById('endpointSearch').value.toLowerCase();
        const methodFilter = document.getElementById('methodFilter').value;
        const table = document.querySelector('#endpoints table');
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const endpoint = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const method = row.querySelector('td:nth-child(3)').textContent;
            
            const matchesSearch = endpoint.includes(searchTerm);
            const matchesMethod = methodFilter === '' || method === methodFilter;
            
            row.style.display = (matchesSearch && matchesMethod) ? '' : 'none';
        });
    }
    
    function filterLogs() {
        const searchTerm = document.getElementById('logSearch').value.toLowerCase();
        const statusFilter = document.getElementById('statusFilter').value;
        const timeFilter = document.getElementById('timeFilter').value;
        const table = document.querySelector('#logs table');
        const rows = table.querySelectorAll('tbody tr');
        
        rows.forEach(row => {
            const endpoint = row.querySelector('td:nth-child(2)').textContent.toLowerCase();
            const statusText = row.querySelector('td:nth-child(6) span').textContent;
            const statusCode = parseInt(statusText);
            const time = row.querySelector('td:nth-child(1)').textContent;
            
            const matchesSearch = endpoint.includes(searchTerm);
            const matchesStatus = statusFilter === '' || 
                                 (statusFilter === '2xx' && statusCode >= 200 && statusCode < 300) ||
                                 (statusFilter === '4xx' && statusCode >= 400 && statusCode < 500) ||
                                 (statusFilter === '5xx' && statusCode >= 500 && statusCode < 600);
            const matchesTime = timeFilter === 'today' || timeFilter === ''; // 简化版，实际需要日期比较
            
            row.style.display = (matchesSearch && matchesStatus && matchesTime) ? '' : 'none';
        });
    }
    
    // 初始化图表
    document.addEventListener('DOMContentLoaded', function() {
        // API性能监控图表
        const performanceCtx = document.getElementById('performanceChart');
        if (performanceCtx) {
            const performanceChart = new Chart(performanceCtx, {
                type: 'line',
                data: {
                    labels: generateTimeLabels(24),
                    datasets: [
                        {
                            label: '响应时间 (ms)',
                            data: generateRandomData(24, 30, 150),
                            borderColor: '#0d6efd',
                            backgroundColor: 'rgba(13, 110, 253, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true
                        },
                        {
                            label: '请求/分钟',
                            data: generateRandomData(24, 10, 100),
                            borderColor: '#20c997',
                            backgroundColor: 'rgba(32, 201, 151, 0.1)',
                            borderWidth: 2,
                            tension: 0.4,
                            fill: true,
                            yAxisID: 'y1'
                        }
                    ]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        x: {
                            grid: {
                                display: false
                            }
                        },
                        y: {
                            beginAtZero: true,
                            title: {
                                display: true,
                                text: '响应时间 (ms)'
                            }
                        },
                        y1: {
                            beginAtZero: true,
                            position: 'right',
                            grid: {
                                drawOnChartArea: false
                            },
                            title: {
                                display: true,
                                text: '请求/分钟'
                            }
                        }
                    },
                    plugins: {
                        tooltip: {
                            mode: 'index',
                            intersect: false
                        },
                        legend: {
                            position: 'top'
                        }
                    }
                }
            });
        }
        
        // 请求分布图表
        const distributionCtx = document.getElementById('requestDistribution');
        if (distributionCtx) {
            const distributionChart = new Chart(distributionCtx, {
                type: 'bar',
                data: {
                    labels: ['/api/departments/25/', '/api/beds/template/', '/api/evaluations/', '/api/qrcodes/', '其他'],
                    datasets: [{
                        label: '请求次数',
                        data: [928, 754, 586, 743, 321],
                        backgroundColor: [
                            'rgba(13, 110, 253, 0.7)',
                            'rgba(32, 201, 151, 0.7)',
                            'rgba(255, 193, 7, 0.7)',
                            'rgba(220, 53, 69, 0.7)',
                            'rgba(108, 117, 125, 0.7)'
                        ],
                        borderWidth: 0
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    scales: {
                        y: {
                            beginAtZero: true
                        }
                    },
                    plugins: {
                        legend: {
                            display: false
                        }
                    }
                }
            });
        }
    });
    
    // 自动刷新功能
    setInterval(function() {
        const now = new Date();
        document.querySelectorAll('.auto-refresh-time').forEach(el => {
            el.textContent = `${now.getHours()}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
        });
    }, 1000);
    
    // 生成随机数据
    function generateRandomData(count, min, max) {
        return Array.from({length: count}, () => Math.floor(Math.random() * (max - min + 1)) + min);
    }
    
    // 生成时间标签
    function generateTimeLabels(count) {
        const now = new Date();
        const labels = [];
        for (let i = count - 1; i >= 0; i--) {
            const time = new Date(now.getTime() - i * 60 * 60 * 1000);
            labels.push(`${time.getHours()}:00`);
        }
        return labels;
    }
    
    // 导出功能
    document.getElementById('exportDataBtn')?.addEventListener('click', function() {
        alert('数据导出功能将在下一版本实现');
    });
    
    // 详细分析
    document.getElementById('showAnalyticsBtn')?.addEventListener('click', function() {
        document.getElementById('monitor-tab').click();
    });

    // 评价提交API测试
    document.addEventListener('DOMContentLoaded', function() {
        const submitEvaluationForm = document.getElementById('testSubmitEvaluationForm');
        const addStaffEvalBtn = document.getElementById('addStaffEvalBtn');
        let staffCounter = 1;
        
        // 添加更多工作人员评价
        if (addStaffEvalBtn) {
            addStaffEvalBtn.addEventListener('click', function() {
                staffCounter++;
                const container = document.getElementById('staffEvaluationsContainer');
                const newEvalDiv = document.createElement('div');
                newEvalDiv.className = 'staff-evaluation mb-3 p-3 border rounded';
                newEvalDiv.innerHTML = `
                    <div class="row">
                        <div class="col-md-6">
                            <label for="staffId${staffCounter}" class="form-label">工作人员ID</label>
                            <input type="number" class="form-control staffId" id="staffId${staffCounter}" required>
                        </div>
                        <div class="col-md-6">
                            <label class="form-label">满意度</label>
                            <div class="d-flex">
                                <div class="form-check me-3">
                                    <input class="form-check-input isSatisfied" type="radio" name="isSatisfied${staffCounter}" id="satisfied${staffCounter}" value="true" checked>
                                    <label class="form-check-label" for="satisfied${staffCounter}">满意</label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input isSatisfied" type="radio" name="isSatisfied${staffCounter}" id="unsatisfied${staffCounter}" value="false">
                                    <label class="form-check-label" for="unsatisfied${staffCounter}">不满意</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <button type="button" class="btn btn-sm btn-outline-danger mt-2 remove-staff-btn">
                        <i class="fas fa-trash"></i> 移除
                    </button>
                `;
                container.appendChild(newEvalDiv);
                
                // 为新添加的移除按钮添加事件监听器
                newEvalDiv.querySelector('.remove-staff-btn').addEventListener('click', function() {
                    container.removeChild(newEvalDiv);
                });
            });
        }
        
        // 处理提交测试
        if (submitEvaluationForm) {
            submitEvaluationForm.addEventListener('submit', async function(e) {
                e.preventDefault();
                
                const responseDisplay = document.getElementById('submitEvaluationResponse');
                responseDisplay.textContent = '正在提交请求...';
                
                try {
                    // 收集所有工作人员评价
                    const staffEvaluations = [];
                    const staffDivs = document.querySelectorAll('.staff-evaluation');
                    
                    staffDivs.forEach((div, index) => {
                        const staffId = div.querySelector('.staffId').value;
                        const isSatisfiedValue = document.querySelector(`input[name="isSatisfied${index+1}"]:checked`).value;
                        const isSatisfied = isSatisfiedValue === 'true';
                        
                        if (staffId) {
                            staffEvaluations.push({
                                staff_id: parseInt(staffId),
                                is_satisfied: isSatisfied
                            });
                        }
                    });
                    
                    const requestData = {
                        qr_param: document.getElementById('qrParam').value,
                        comment: document.getElementById('comment').value,
                        hospital_number: document.getElementById('hospitalNumber').value,
                        phone_number: document.getElementById('phoneNumber').value,
                        staff_evaluations: staffEvaluations
                    };
                    
                    // 发送请求
                    const response = await fetch('/api/v1/public/submit-evaluation/', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify(requestData)
                    });
                    
                    const result = await response.json();
                    
                    // 显示格式化的JSON响应
                    responseDisplay.textContent = JSON.stringify(result, null, 2);
                    responseDisplay.className = 'border p-3 bg-light ' + 
                        (response.ok ? 'text-success' : 'text-danger');
                    
                } catch (error) {
                    responseDisplay.textContent = '请求失败: ' + error.message;
                    responseDisplay.className = 'border p-3 bg-light text-danger';
                }
            });
        }
    });
</script>
{% endblock %} 