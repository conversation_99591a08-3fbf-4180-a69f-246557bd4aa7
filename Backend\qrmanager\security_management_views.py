"""
安全管理操作视图 - 提供安全管理操作功能
"""
import json
from datetime import datetime, timedelta
from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib.auth.mixins import LoginRequiredMixin
from django.views.generic import TemplateView
from django.http import JsonResponse
from django.core.cache import cache
from django.contrib import messages
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt

from .models import QRCode, SecurityEvent
from qrcode_based_security import get_qrcode_security_stats, get_ip_security_profile

class SecurityManagementView(LoginRequiredMixin, TemplateView):
    """安全管理主页面"""
    template_name = 'qrmanager/security_management.html'
    
    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        
        # 获取当前安全状态
        context.update({
            'blacklisted_ips': self.get_blacklisted_ips(),
            'active_limits': self.get_active_limits(),
            'security_config': self.get_security_config(),
            'recent_operations': self.get_recent_operations(),
        })
        
        return context
    
    def get_blacklisted_ips(self):
        """获取黑名单IP列表"""
        blacklist = cache.get('security:ip_blacklist', set())
        result = []
        
        for ip in blacklist:
            # 获取封禁详情
            ban_info = cache.get(f'security:ip_ban:{ip}', {})
            result.append({
                'ip': ip,
                'banned_at': ban_info.get('banned_at', '未知'),
                'reason': ban_info.get('reason', '未知'),
                'expires_at': ban_info.get('expires_at', '永久'),
                'banned_by': ban_info.get('banned_by', '系统')
            })
        
        return result
    
    def get_active_limits(self):
        """获取活跃的速率限制"""
        limits = []
        current_time = int(datetime.now().timestamp())
        
        # 检查二维码限制
        qrcodes = QRCode.objects.all()[:20]
        for qrcode in qrcodes:
            uuid = str(qrcode.code)
            
            # 检查评价限制
            eval_key = f"qrcode_limit:{uuid}:evaluation"
            eval_data = cache.get(eval_key)
            if eval_data:
                limits.append({
                    'type': '二维码评价限制',
                    'target': f"{qrcode.bed.department.name} - {qrcode.bed.number}" if qrcode.bed else uuid[:8],
                    'uuid': uuid,
                    'operation': 'evaluation',
                    'current_count': len(eval_data),
                    'limit': 2,
                    'cache_key': eval_key
                })
            
            # 检查验证限制
            verify_key = f"qrcode_limit:{uuid}:verification"
            verify_data = cache.get(verify_key)
            if verify_data:
                limits.append({
                    'type': '二维码验证限制',
                    'target': f"{qrcode.bed.department.name} - {qrcode.bed.number}" if qrcode.bed else uuid[:8],
                    'uuid': uuid,
                    'operation': 'verification',
                    'current_count': len(verify_data),
                    'limit': 10,
                    'cache_key': verify_key
                })
        
        return limits
    
    def get_security_config(self):
        """获取当前安全配置"""
        return {
            'qrcode_evaluation_limit': 2,
            'qrcode_verification_limit': 10,
            'ip_requests_per_minute': 30,
            'anomaly_detection_enabled': True,
            'auto_ban_enabled': False,
            'ban_duration': 3600,  # 1小时
        }
    
    def get_recent_operations(self):
        """获取最近的管理操作"""
        operations = []
        
        # 从缓存中获取最近的操作记录
        for i in range(10):
            op_key = f"security:operation:{datetime.now().timestamp() - i * 60}"
            operation = cache.get(op_key)
            if operation:
                operations.append(operation)
        
        return operations

@login_required
@require_POST
def ban_ip_action(request):
    """封禁IP操作"""
    try:
        data = json.loads(request.body)
        ip = data.get('ip')
        duration = int(data.get('duration', 3600))  # 默认1小时
        reason = data.get('reason', '手动封禁')
        
        if not ip:
            return JsonResponse({'success': False, 'error': '缺少IP地址'})
        
        # 添加到黑名单
        blacklist = cache.get('security:ip_blacklist', set())
        blacklist.add(ip)
        cache.set('security:ip_blacklist', blacklist, None)  # 永久存储
        
        # 记录封禁详情
        ban_info = {
            'ip': ip,
            'banned_at': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'banned_by': request.user.username,
            'reason': reason,
            'duration': duration,
            'expires_at': (datetime.now() + timedelta(seconds=duration)).strftime('%Y-%m-%d %H:%M:%S') if duration > 0 else '永久'
        }
        cache.set(f'security:ip_ban:{ip}', ban_info, duration if duration > 0 else None)
        
        # 记录操作日志
        operation = {
            'type': 'ban_ip',
            'operator': request.user.username,
            'target': ip,
            'reason': reason,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        cache.set(f"security:operation:{datetime.now().timestamp()}", operation, 86400)
        
        return JsonResponse({
            'success': True,
            'message': f'IP {ip} 已被封禁 {duration}秒'
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def unban_ip_action(request):
    """解封IP操作"""
    try:
        data = json.loads(request.body)
        ip = data.get('ip')
        
        if not ip:
            return JsonResponse({'success': False, 'error': '缺少IP地址'})
        
        # 从黑名单移除
        blacklist = cache.get('security:ip_blacklist', set())
        if ip in blacklist:
            blacklist.remove(ip)
            cache.set('security:ip_blacklist', blacklist, None)
            
            # 删除封禁详情
            cache.delete(f'security:ip_ban:{ip}')
            
            # 记录操作日志
            operation = {
                'type': 'unban_ip',
                'operator': request.user.username,
                'target': ip,
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            }
            cache.set(f"security:operation:{datetime.now().timestamp()}", operation, 86400)
            
            return JsonResponse({
                'success': True,
                'message': f'IP {ip} 已解封'
            })
        else:
            return JsonResponse({'success': False, 'error': 'IP不在黑名单中'})
            
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def clear_qrcode_limits_action(request):
    """清除二维码速率限制"""
    try:
        data = json.loads(request.body)
        uuid = data.get('uuid')
        operation = data.get('operation', 'all')  # all, evaluation, verification
        
        if not uuid:
            return JsonResponse({'success': False, 'error': '缺少二维码UUID'})
        
        cleared_count = 0
        
        if operation in ['all', 'evaluation']:
            eval_key = f"qrcode_limit:{uuid}:evaluation"
            if cache.get(eval_key):
                cache.delete(eval_key)
                cleared_count += 1
        
        if operation in ['all', 'verification']:
            verify_key = f"qrcode_limit:{uuid}:verification"
            if cache.get(verify_key):
                cache.delete(verify_key)
                cleared_count += 1
        
        # 记录操作日志
        operation_log = {
            'type': 'clear_limits',
            'operator': request.user.username,
            'target': f'{uuid[:8]}...({operation})',
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        cache.set(f"security:operation:{datetime.now().timestamp()}", operation_log, 86400)
        
        return JsonResponse({
            'success': True,
            'message': f'已清除 {cleared_count} 个限制'
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
@require_POST
def update_security_config_action(request):
    """更新安全配置"""
    try:
        data = json.loads(request.body)
        
        # 验证配置参数
        config = {
            'qrcode_evaluation_limit': int(data.get('qrcode_evaluation_limit', 2)),
            'qrcode_verification_limit': int(data.get('qrcode_verification_limit', 10)),
            'ip_requests_per_minute': int(data.get('ip_requests_per_minute', 30)),
            'anomaly_detection_enabled': bool(data.get('anomaly_detection_enabled', True)),
            'auto_ban_enabled': bool(data.get('auto_ban_enabled', False)),
            'ban_duration': int(data.get('ban_duration', 3600)),
        }
        
        # 保存配置到缓存
        cache.set('security:config', config, None)  # 永久存储
        
        # 记录操作日志
        operation = {
            'type': 'update_config',
            'operator': request.user.username,
            'config': config,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        cache.set(f"security:operation:{datetime.now().timestamp()}", operation, 86400)
        
        return JsonResponse({
            'success': True,
            'message': '安全配置已更新'
        })
        
    except Exception as e:
        return JsonResponse({'success': False, 'error': str(e)})

@login_required
def clear_all_limits_action(request):
    """清除所有速率限制"""
    try:
        cleared_count = 0
        
        # 清除二维码限制
        qrcodes = QRCode.objects.all()
        for qrcode in qrcodes:
            uuid = str(qrcode.code)
            
            eval_key = f"qrcode_limit:{uuid}:evaluation"
            verify_key = f"qrcode_limit:{uuid}:verification"
            
            if cache.get(eval_key):
                cache.delete(eval_key)
                cleared_count += 1
            
            if cache.get(verify_key):
                cache.delete(verify_key)
                cleared_count += 1
        
        # 清除IP限制（这里可以扩展）
        
        # 记录操作日志
        operation = {
            'type': 'clear_all_limits',
            'operator': request.user.username,
            'cleared_count': cleared_count,
            'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        cache.set(f"security:operation:{datetime.now().timestamp()}", operation, 86400)
        
        messages.success(request, f'已清除 {cleared_count} 个速率限制')
        return redirect('qrmanager:security_management')
        
    except Exception as e:
        messages.error(request, f'清除失败: {str(e)}')
        return redirect('qrmanager:security_management')
