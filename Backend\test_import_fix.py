# -*- coding: utf-8 -*-
"""
简单测试脚本：验证工作人员导入修复
"""

import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.models import Department, StaffType, DictionaryItem, Dictionary

def test_data_integrity():
    """测试数据完整性"""
    print("=== 数据完整性测试 ===")
    
    # 检查科室
    dept_count = Department.objects.count()
    print(f"科室数量: {dept_count}")
    
    # 检查人员类型字典
    try:
        staff_type_dict = Dictionary.objects.get(code='staff_type')
        dict_items = DictionaryItem.objects.filter(
            dictionary=staff_type_dict, 
            is_active=True
        ).count()
        print(f"人员类型字典项: {dict_items}")
    except Dictionary.DoesNotExist:
        print("人员类型字典不存在")
    
    # 检查StaffType
    staff_type_count = StaffType.objects.count()
    print(f"StaffType记录: {staff_type_count}")
    
    # 检查职称字典
    try:
        title_dict = Dictionary.objects.get(code='staff_title')
        title_items = DictionaryItem.objects.filter(
            dictionary=title_dict, 
            is_active=True
        ).count()
        print(f"职称字典项: {title_items}")
    except Dictionary.DoesNotExist:
        print("职称字典不存在")

if __name__ == "__main__":
    test_data_integrity()
    print("测试完成")
