# 业务逻辑处理模块，用于封装评价相关的业务处理
from django.contrib import messages
from .models import QRCode


def process_evaluation(form, qr_code, request):
    """处理评价逻辑：根据传入的二维码编码找到对应的床位，并设置评价表单的bed属性。
    如果二维码存在且关联了床位，设置后返回True，否则返回False，并添加错误消息。"""
    try:
        # 使用安全访问函数处理参数
        from .security import secure_qr_access, is_encrypted_param
        
        # 检查是否为加密参数
        if is_encrypted_param(qr_code):
            try:
                # 尝试解密参数
                uuid = secure_qr_access(qr_code)
                # 更新qr_code为解密后的UUID
                qr_code = uuid
            except ValueError as e:
                messages.error(request, f"二维码验证失败: {str(e)}")
                return False
        
        qrcode_obj = QRCode.objects.get(code=qr_code)
        
        # 检查该二维码是否被标记为安全问题
        from .models import QRCodeHistory
        security_issue = QRCodeHistory.objects.filter(
            old_code=qr_code,
            is_security_issue=True
        ).exists()
        
        if security_issue:
            messages.error(request, '该二维码已被禁用，请使用新的二维码！')
            return False
            
        if qrcode_obj.bed:
            form.instance.bed = qrcode_obj.bed
            form.instance.qr_code = qrcode_obj  # 保留二维码关联，以便向后兼容
            
            # 记录评价创建操作
            from .utils import LoggerHelper
            LoggerHelper.log_model_operation(
                user=None,  # 匿名用户
                instance=form.instance,
                operation_type="create",
                description=f"创建评价: {'满意' if form.instance.is_satisfied else '不满意'}",
                extra_data={
                    'qr_code': str(qr_code),
                    'bed': form.instance.bed.number if form.instance.bed else None,
                    'department': form.instance.bed.department.name if form.instance.bed and form.instance.bed.department else None,
                    'is_satisfied': form.instance.is_satisfied,
                    'sentiment': form.instance.sentiment,
                    'has_comment': bool(form.instance.comment),
                    'has_photo': bool(form.instance.photo)
                }
            )
            
            messages.success(request, '评价提交成功！')
            return True
        else:
            messages.error(request, '该二维码未关联床位，无法提交评价！')
            return False
    except QRCode.DoesNotExist:
        messages.error(request, '无效的二维码！')
        return False
    except Exception as e:
        # 记录评价创建失败
        from .utils import LoggerHelper
        LoggerHelper.log_operation(
            user=None,
            action="create_evaluation_failed",
            description=f"创建评价失败: {str(e)}",
            status='error',
            extra_data={
                'qr_code': str(qr_code),
                'error': str(e)
            }
        )
        messages.error(request, f'提交评价时发生错误: {str(e)}')
        return False 