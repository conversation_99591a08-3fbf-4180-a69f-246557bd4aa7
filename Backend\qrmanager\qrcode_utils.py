import qrcode
import io
import base64
import logging
from PIL import Image
from django.utils import timezone

logger = logging.getLogger(__name__)

def generate_qrcode(data, box_size=40, border=0):
    """
    统一的二维码生成函数，确保所有地方生成的二维码参数一致

    参数:
        data (str): 二维码中包含的数据
        box_size (int): 二维码中每个小方块的像素大小，默认40以确保高质量
        border (int): 二维码边框宽度，默认0（无边框）

    返回:
        tuple: (PIL图像对象, base64编码的图像字符串)
    """
    try:
        logger.info(f"开始生成二维码，数据长度: {len(data) if data else 0}")

        # 确保参数有效
        if not data:
            logger.error("二维码数据为空")
            return None, None

        # 使用固定参数，确保所有地方生成的二维码完全一致
        box_size = max(1, int(box_size))  # 确保box_size至少为1
        border = max(0, int(border))      # 确保border至少为0

        logger.info(f"二维码参数: box_size={box_size}, border={border}")

        # 使用高纠错级别，提高扫描成功率
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_H,  # 使用高纠错级别
            box_size=box_size,
            border=border
        )
        qr.add_data(data)
        qr.make(fit=True)
        img = qr.make_image(fill='black', back_color='white')

        # 转换为base64字符串，使用最高质量
        buffered = io.BytesIO()
        img.save(buffered, format="PNG", quality=100, dpi=(300, 300))  # 设置最高质量和高DPI
        img_base64 = base64.b64encode(buffered.getvalue()).decode('utf-8')

        logger.info(f"二维码生成成功，base64数据长度: {len(img_base64)}")
        return img, img_base64

    except Exception as e:
        logger.exception(f"生成二维码时出错: {str(e)}")
        return None, None

def generate_template_preview(template, qr_data=None):
    """
    统一的模板预览生成函数，用于打印模板预览和二维码打印预览

    参数:
        template: 打印模板对象
        qr_data: 二维码数据，如果为None则使用默认示例数据（医院网站URL）

    返回:
        dict: 包含模板数据和二维码base64图像的字典
    """
    try:
        # 如果没有提供二维码数据，使用默认示例数据
        if qr_data is None:
            qr_data = 'https://www.zg120.cn/'

        # 生成高质量二维码
        _, img_base64 = generate_qrcode(qr_data, box_size=40, border=0)

        # 记录模板图片信息
        if template.background_image:
            try:
                img = Image.open(template.background_image.path)
                logger.info(f"模板背景图片: {template.background_image.path}, 尺寸: {img.size}, 格式: {img.format}")
            except Exception as e:
                logger.warning(f"无法获取模板背景图片信息: {str(e)}")

        # 确保背景图片URL包含时间戳查询参数，防止浏览器缓存
        background_image_url = template.background_image.url if template.background_image else None
        if background_image_url:
            # 添加时间戳参数，确保每次都获取最新的图片
            timestamp = int(timezone.now().timestamp())
            if '?' in background_image_url:
                background_image_url = f"{background_image_url}&t={timestamp}"
            else:
                background_image_url = f"{background_image_url}?t={timestamp}"

        # 处理模板数据
        template_data = {
            'id': template.id,
            'name': template.name,
            'template_name': template.name,
            'template_id': template.id,
            'is_public': getattr(template, 'is_public', False),
            'print_width': template.print_width,
            'print_height': template.print_height,
            'qr_size': template.qr_size,
            'qr_position_x': template.qr_position_x,
            'qr_position_y': template.qr_position_y,
            'background_image': background_image_url,
            'original_qr_size': template.qr_size,
            'original_qr_position_x': template.qr_position_x,
            'original_qr_position_y': template.qr_position_y,
        }

        return {
            'template_data': template_data,
            'qr_image_base64': img_base64
        }

    except Exception as e:
        logger.exception(f"生成模板预览时出错: {str(e)}")
        return {
            'template_data': None,
            'qr_image_base64': None
        }

def generate_template_image(template, qr_data, output_format='png'):
    """
    生成带有模板背景和二维码的完整图像

    参数:
        template: 打印模板对象
        qr_data: 二维码数据
        output_format: 输出格式，'png'或'pdf'

    返回:
        tuple: (PIL图像对象或BytesIO对象, 文件格式)
    """
    try:
        logger.info(f"开始生成模板图像，格式: {output_format}")

        # 生成二维码图像
        qr_img, _ = generate_qrcode(qr_data, box_size=40, border=0)

        # 检查模板背景图片
        if not template or not template.background_image or not template.background_image.path:
            logger.warning("模板背景图片不存在，只返回二维码图像")
            return qr_img, output_format

        # 打开背景图片
        try:
            background_img = Image.open(template.background_image.path)
            logger.info(f"背景图片已加载，尺寸: {background_img.size}, 格式: {background_img.format}")
        except Exception as e:
            logger.error(f"无法打开背景图片: {str(e)}")
            return qr_img, output_format

        # 获取模板参数
        qr_size = float(template.qr_size)
        qr_position_x = float(template.qr_position_x)
        qr_position_y = float(template.qr_position_y)

        logger.info(f"模板参数: 二维码尺寸={qr_size}mm, X={qr_position_x}mm, Y={qr_position_y}mm")

        if output_format == 'pdf':
            # PDF格式处理
            from reportlab.lib.pagesizes import A4
            from reportlab.pdfgen import canvas
            import io

            # 创建PDF
            buffer = io.BytesIO()
            c = canvas.Canvas(buffer, pagesize=A4)

            # 设置PDF元数据
            c.setTitle(f"打印模板: {template.name}")
            c.setAuthor("医院服务评价系统")
            c.setSubject("二维码打印")

            # 计算毫米到点的转换
            mm_to_pt = 72 / 25.4  # 1毫米 = 72/25.4点
            width, height = A4

            # 保存背景图片为临时BytesIO
            bg_buffer = io.BytesIO()
            background_img.save(bg_buffer, format=background_img.format)
            bg_buffer.seek(0)

            # 在PDF中绘制背景图片
            c.drawImage(bg_buffer, 0, 0, width, height, preserveAspectRatio=True)

            # 保存二维码图像为临时BytesIO
            qr_buffer = io.BytesIO()
            qr_img.save(qr_buffer, format="PNG")
            qr_buffer.seek(0)

            # 计算二维码位置（从中心点计算左上角）
            qr_x = qr_position_x * mm_to_pt
            qr_y = height - (qr_position_y * mm_to_pt)
            qr_size_pt = qr_size * mm_to_pt

            # 在PDF中绘制二维码
            c.drawImage(qr_buffer, qr_x - qr_size_pt/2, qr_y - qr_size_pt/2, qr_size_pt, qr_size_pt)

            # 保存PDF
            c.save()

            # 返回PDF数据
            buffer.seek(0)
            return buffer, 'pdf'

        else:
            # PNG格式处理
            # 计算毫米到像素的转换（假设300 DPI）
            dpi = 300
            mm_to_px = dpi / 25.4  # 1毫米 = dpi/25.4像素

            # 调整二维码大小
            qr_size_px = int(qr_size * mm_to_px)
            qr_img = qr_img.resize((qr_size_px, qr_size_px), Image.LANCZOS)

            # 计算粘贴位置（从中心点计算左上角）
            paste_x = int(qr_position_x * mm_to_px) - qr_size_px // 2
            paste_y = int(qr_position_y * mm_to_px) - qr_size_px // 2

            # 确保坐标不为负
            paste_x = max(0, paste_x)
            paste_y = max(0, paste_y)

            logger.info(f"二维码粘贴位置: x={paste_x}px, y={paste_y}px, size={qr_size_px}px")

            # 创建背景图片的副本
            result_img = background_img.copy()

            # 将二维码粘贴到背景上
            result_img.paste(qr_img, (paste_x, paste_y), qr_img if qr_img.mode == 'RGBA' else None)

            return result_img, 'png'

    except Exception as e:
        logger.exception(f"生成模板图像时出错: {str(e)}")
        # 生成简单的二维码作为回退方案
        qr_img, _ = generate_qrcode(qr_data, box_size=40, border=0)
        return qr_img, output_format