{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}二维码预览{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <div class="d-flex justify-content-between align-items-center">
                        <h1 class="h3 mb-0">二维码预览</h1>
                        <div>
                            <a href="{% url 'qrmanager:bed_list' %}{% if bed.department %}?department={{ bed.department.id }}{% endif %}" 
                               class="btn btn-secondary me-2">
                                <i class="fas fa-arrow-left me-2"></i>返回床位列表
                            </a>
                            <button type="button" class="btn btn-primary" onclick="printQRCode()">
                                <i class="fas fa-print me-2"></i>打印二维码
                            </button>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h5 class="mb-3">床位信息</h5>
                            <table class="table">
                                <tr>
                                    <th style="width: 120px;">床位号：</th>
                                    <td>{{ bed.number }}</td>
                                </tr>
                                <tr>
                                    <th>所属科室：</th>
                                    <td>{{ bed.department.name }}</td>
                                </tr>
                                <tr>
                                    <th>区域：</th>
                                    <td>{{ bed.get_area_display }}</td>
                                </tr>
                                <tr>
                                    <th>负责人：</th>
                                    <td>{{ bed.staff.name|default:"-" }}</td>
                                </tr>
                            </table>

                            <h5 class="mb-3 mt-4">二维码信息</h5>
                            <table class="table">
                                <tr>
                                    <th style="width: 120px;">创建时间：</th>
                                    <td>{{ qrcode_obj.created_at|date:"Y-m-d H:i" }}</td>
                                </tr>
                                <tr>
                                    <th>评价次数：</th>
                                    <td>{{ qrcode_obj.evaluations.count }}</td>
                                </tr>
                                <tr>
                                    <th>二维码编号：</th>
                                    <td><code>{{ qrcode_obj.code }}</code></td>
                                </tr>
                            </table>
                        </div>

                        <div class="col-md-6">
                            <div class="preview-container">
                                {% if bed.department.print_template %}
                                    <div class="template-preview" style="position: relative; width: 100%; max-width: 500px; margin: 0 auto; border: 1px solid #ddd; overflow: hidden; background-color: white;" id="template-preview-container">
                                        {% if bed.department.print_template.background_image %}
                                        <img src="{{ bed.department.print_template.background_image.url }}" 
                                             style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; object-fit: contain; z-index: 1;">
                                        {% else %}
                                        <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; background-color: #f8f9fa;">
                                            <span>无背景图片</span>
                                        </div>
                                        {% endif %}
                                        
                                        <div id="qrcode-overlay" style="position: absolute; z-index: 2; display: flex; align-items: center; justify-content: center;">
                                            <img src="data:image/png;base64,{{ qr_image }}" 
                                                 style="width: 100%; height: 100%; border: none; box-shadow: none; background: transparent;">
                                        </div>
                                    </div>
                                    <div style="text-align: center; margin-top: 10px; font-size: 12px; color: #666;">
                                        模板尺寸: {{ bed.department.print_template.print_width|default:'210' }} × {{ bed.department.print_template.print_height|default:'297' }}mm | 
                                        二维码尺寸: {{ bed.department.print_template.qr_size }}mm |
                                        位置: X={{ bed.department.print_template.qr_position_x }}mm, Y={{ bed.department.print_template.qr_position_y }}mm
                                    </div>
                                {% else %}
                                    <div class="text-center p-4">
                                        <img src="data:image/png;base64,{{ qr_image }}" 
                                             style="max-width: 300px; height: auto; border: none; box-shadow: none;">
                                        <div class="mt-3">
                                            <a href="{% url 'qrmanager:print_template_create' bed.department.id %}" 
                                               class="btn btn-outline-primary">
                                                <i class="fas fa-plus me-2"></i>设置打印模板
                                            </a>
                                        </div>
                                    </div>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-container {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 20px;
    text-align: center;
}

.template-preview {
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    background-color: white;
}

.table th {
    background-color: #f8f9fa;
}
</style>

<script>
function printQRCode() {
    // 打印二维码逻辑
    window.print();
}

// 设置模板容器的宽高比和二维码位置
document.addEventListener('DOMContentLoaded', function() {
    {% if bed.department.print_template %}
    const container = document.getElementById('template-preview-container');
    const qrOverlay = document.getElementById('qrcode-overlay');
    
    // 设置容器的宽高比
    const printWidth = {{ bed.department.print_template.print_width|default:'210' }};
    const printHeight = {{ bed.department.print_template.print_height|default:'297' }};
    container.style.aspectRatio = printWidth + "/" + printHeight;
    
    // 设置二维码位置和大小
    const qrSize = {{ bed.department.print_template.qr_size }};
    const qrX = {{ bed.department.print_template.qr_position_x }};
    const qrY = {{ bed.department.print_template.qr_position_y }};
    
    qrOverlay.style.width = (qrSize / printWidth * 100) + "%";
    qrOverlay.style.height = (qrSize / printWidth * 100) + "%";
    qrOverlay.style.left = (qrX / printWidth * 100) + "%";
    qrOverlay.style.top = (qrY / printHeight * 100) + "%";
    {% endif %}
});
</script>
{% endblock %} 