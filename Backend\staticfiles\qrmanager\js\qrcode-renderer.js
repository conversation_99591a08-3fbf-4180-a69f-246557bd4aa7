/**
 * 二维码渲染器
 * 用于在管理界面预览和渲染二维码
 */
document.addEventListener('DOMContentLoaded', function() {
    // 查找页面中所有需要渲染二维码的元素
    const qrContainers = document.querySelectorAll('.qr-preview-container');
    
    qrContainers.forEach(container => {
        const qrData = container.getAttribute('data-qr-content');
        const size = container.getAttribute('data-size') || 200;
        
        if (qrData) {
            // 使用已有的QRCode库渲染二维码
            if (typeof QRCode !== 'undefined') {
                new QRCode(container, {
                    text: qrData,
                    width: parseInt(size),
                    height: parseInt(size),
                    colorDark: "#000000",
                    colorLight: "#ffffff",
                    correctLevel: QRCode.CorrectLevel.H
                });
            } else {
                console.error('QRCode库未加载，无法渲染二维码');
                container.innerHTML = '<div class="error-message">二维码渲染失败</div>';
            }
        }
    });
    
    // 处理二维码预览按钮点击事件
    const previewButtons = document.querySelectorAll('.qrcode-preview-btn');
    previewButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            e.preventDefault();
            const bedId = this.getAttribute('data-bed-id');
            const bedNumber = this.getAttribute('data-bed-number');
            const departmentName = this.getAttribute('data-department-name');
            
            if (bedId) {
                showBedQRCodePreview(bedId, departmentName, bedNumber);
            } else {
                console.error('缺少床位ID，无法预览二维码');
            }
        });
    });
});

/**
 * 显示床位二维码预览
 * @param {string} bedId 床位ID
 * @param {string} departmentName 科室名称
 * @param {string} bedNumber 床位号
 */
function showBedQRCodePreview(bedId, departmentName, bedNumber) {
    console.log('预览床位二维码:', bedId, departmentName, bedNumber);
    
    // 获取CSRF令牌
    const csrfToken = document.querySelector('input[name="csrfmiddlewaretoken"]').value;
    
    // 设置模态框标题
    const modalTitle = document.getElementById('qrcodeModalLabel');
    if (modalTitle) {
        modalTitle.textContent = `${departmentName} - ${bedNumber}号床位二维码`;
    }
    
    // 获取模态框内容区域
    const modalBody = document.querySelector('#qrcodeModal .modal-body');
    if (!modalBody) {
        console.error('找不到模态框内容区域');
        return;
    }
    
    // 清空模态框内容并显示加载提示
    modalBody.innerHTML = `
        <div class="d-flex justify-content-center p-5">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">加载中...</span>
            </div>
        </div>
    `;
    
    // 显示模态框
    const qrcodeModal = new bootstrap.Modal(document.getElementById('qrcodeModal'));
    qrcodeModal.show();
    
    // 发送AJAX请求获取二维码数据
    fetch('/beds/qr_preview/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'X-CSRFToken': csrfToken
        },
        body: `bed_id=${bedId}`
    })
    .then(response => {
        if (!response.ok) {
            // 添加更详细的错误处理
            console.error('HTTP错误状态:', response.status, response.statusText);
            return response.text().then(text => {
                try {
                    // 尝试解析为JSON
                    const errorData = JSON.parse(text);
                    throw new Error(errorData.error || `HTTP错误 ${response.status}`);
                } catch (e) {
                    // 如果不是JSON，则显示原始文本
                    console.error('响应内容:', text);
                    throw new Error(`HTTP错误 ${response.status}: ${text.substring(0, 100)}`);
                }
            });
        }
        return response.json();
    })
    .then(data => {
        console.log('获取到二维码预览数据:', data);
        
        // 检查二维码图片是否存在
        if (!data.qr_image_base64) {
            throw new Error('服务器未返回二维码图片数据');
        }
        
        // 创建预览内容
        let content = `
            <div class="qrcode-preview-container">
                <div class="preview-content">
                    <!-- 左侧信息区 -->
                    <div class="preview-info">
                        <div class="info-section">
                            <h6><i class="fas fa-info-circle me-2"></i>二维码信息</h6>
                            <div class="info-item"><strong>科室:</strong> ${departmentName}</div>
                            <div class="info-item"><strong>床位号:</strong> ${bedNumber}</div>
                            <div class="info-item"><strong>床位ID:</strong> ${bedId}</div>
                            ${data.qrcode ? `<div class="info-item"><strong>二维码ID:</strong> ${data.qrcode.id}</div>` : ''}
                            ${data.qrcode ? `<div class="info-item"><strong>二维码编码:</strong> ${data.qrcode.code}</div>` : ''}
                        </div>
                    </div>
                    
                    <!-- 右侧预览区 -->
                    <div class="preview-display">
                        ${data.template_data ? `
                        <div class="template-preview-container">
                            <div class="template-preview">
                                ${data.template_data.background_image ? 
                                    `<img src="${data.template_data.background_image}" class="template-background" alt="模板背景">` : 
                                    `<div class="template-no-background">无背景图片</div>`
                                }
                                <div class="qrcode-overlay" style="
                                    position: absolute;
                                    left: calc(${data.template_data.original_qr_position_x} / ${data.template_data.print_width} * 100%);
                                    top: calc(${data.template_data.original_qr_position_y} / ${data.template_data.print_height} * 100%);
                                    width: calc(${data.template_data.original_qr_size} / ${data.template_data.print_width} * 100%); 
                                    height: calc(${data.template_data.original_qr_size} / ${data.template_data.print_height} * 100%);
                                    z-index: 2;
                                    transform: translate(-50%, -50%);
                                    display: flex;
                                    align-items: center;
                                    justify-content: center;
                                ">
                                    <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-image" style="width: 100%; height: 100%; border: none; box-shadow: none; background: transparent;">
                                </div>
                            </div>
                        </div>
                        ` : `
                        <div class="qrcode-only-preview">
                            <div class="qrcode-container">
                                <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-large">
                            </div>
                            <div class="qrcode-notice">
                                <i class="fas fa-info-circle me-2"></i>
                                <span>该科室尚未设置打印模板</span>
                            </div>
                        </div>
                        `}
                    </div>
                </div>
            </div>
        `;
        
        // 更新模态框内容
        modalBody.innerHTML = content;
    })
    .catch(error => {
        console.error('获取二维码预览数据失败:', error);
        
        // 显示错误信息
        modalBody.innerHTML = `
            <div class="alert alert-danger m-3">
                <h5 class="alert-heading"><i class="fas fa-exclamation-triangle me-2"></i>获取二维码数据失败</h5>
                <hr>
                <p>${error.message || '未知错误'}</p>
            </div>
        `;
    });
}

/**
 * 打印二维码
 * @param {Object} data 二维码数据
 */
function printQRCode(data) {
    if (!data || !data.qr_image_base64) {
        console.error('缺少二维码数据，无法打印');
        return;
    }
    
    // 创建打印内容
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
        <html>
        <head>
            <title>打印二维码</title>
            <style>
                body {
                    margin: 0;
                    padding: 0;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    height: 100vh;
                }
                .print-container {
                    position: relative;
                    width: ${data.template_data ? data.template_data.print_width : 210}mm;
                    height: ${data.template_data ? data.template_data.print_height : 297}mm;
                }
                .background-image {
                    width: 100%;
                    height: 100%;
                    position: absolute;
                    top: 0;
                    left: 0;
                }
                .qrcode-image {
                    position: absolute;
                    left: ${data.template_data ? data.template_data.qr_position_x : 105}mm;
                    top: ${data.template_data ? data.template_data.qr_position_y : 148}mm;
                    width: ${data.template_data ? data.template_data.qr_size : 50}mm;
                    height: ${data.template_data ? data.template_data.qr_size : 50}mm;
                    transform: translate(-50%, -50%);
                }
                @media print {
                    @page {
                        size: ${data.template_data ? data.template_data.print_width : 210}mm ${data.template_data ? data.template_data.print_height : 297}mm;
                        margin: 0;
                    }
                }
            </style>
        </head>
        <body>
            <div class="print-container">
                ${data.template_data && data.template_data.background_image ? 
                    `<img src="${data.template_data.background_image}" class="background-image">` : ''
                }
                <img src="data:image/png;base64,${data.qr_image_base64}" class="qrcode-image">
            </div>
            <script>
                window.onload = function() {
                    setTimeout(function() {
                        window.print();
                        setTimeout(function() {
                            window.close();
                        }, 500);
                    }, 500);
                };
            </script>
        </body>
        </html>
    `);
    printWindow.document.close();
} 