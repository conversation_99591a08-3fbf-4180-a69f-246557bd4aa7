#!/usr/bin/env python
"""
简化的模板导入测试
"""

def test_header_detection_logic():
    """测试表头检测逻辑"""
    print("🧪 测试表头检测逻辑...")
    
    # 模拟Excel数据结构
    expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
    
    # 测试场景1：第1行是表头
    print("\n📋 场景1: 第1行是表头")
    scenario1_row1 = ['工号', '姓名', '人员类型', '职称', '科室']
    scenario1_row2 = ['001', '张三', '医生', '主任医师', '内科']
    
    # 检测逻辑
    headers_found = None
    header_row = 1
    
    if all(header in scenario1_row1 for header in expected_headers):
        headers_found = scenario1_row1
        header_row = 1
        print(f"   ✅ 第1行识别为表头: {headers_found}")
    else:
        print(f"   ❌ 第1行不是表头: {scenario1_row1}")
    
    # 测试场景2：第2行是表头（第1行是提示）
    print("\n📋 场景2: 第2行是表头（第1行是提示）")
    scenario2_row1 = ['重要提示：请勿删除或修改示例行，只需在下方添加您的数据。空行将被自动忽略。', None, None, None, None]
    scenario2_row2 = ['工号', '姓名', '人员类型', '职称', '科室']
    scenario2_row3 = ['001', '张三', '医生', '主任医师', '内科']
    
    # 检测逻辑
    headers_found = None
    header_row = 1
    
    # 先检查第1行
    if all(header in scenario2_row1 for header in expected_headers):
        headers_found = scenario2_row1
        header_row = 1
        print(f"   ✅ 第1行识别为表头: {headers_found}")
    else:
        print(f"   ❌ 第1行不是表头: {scenario2_row1}")
        # 检查第2行
        if all(header in scenario2_row2 for header in expected_headers):
            headers_found = scenario2_row2
            header_row = 2
            print(f"   ✅ 第2行识别为表头: {headers_found}")
        else:
            print(f"   ❌ 第2行也不是表头: {scenario2_row2}")
    
    if headers_found:
        data_start_row = header_row + 1
        print(f"   📍 表头位置: 第{header_row}行")
        print(f"   📊 数据开始行: 第{data_start_row}行")
        
        # 获取字段索引
        work_number_idx = headers_found.index('工号')
        name_idx = headers_found.index('姓名')
        staff_type_idx = headers_found.index('人员类型')
        title_idx = headers_found.index('职称')
        department_idx = headers_found.index('科室')
        
        print(f"   📍 字段索引: 工号={work_number_idx}, 姓名={name_idx}, 人员类型={staff_type_idx}, 职称={title_idx}, 科室={department_idx}")
        
        return True
    else:
        print("   ❌ 未找到有效表头")
        return False

def test_data_reading():
    """测试数据读取逻辑"""
    print("\n🧪 测试数据读取逻辑...")
    
    # 模拟修复后的模板结构
    template_data = [
        ['重要提示：请勿删除或修改示例行，只需在下方添加您的数据。空行将被自动忽略。', None, None, None, None],  # 第1行：提示
        ['工号', '姓名', '人员类型', '职称', '科室'],  # 第2行：表头
        ['001', '张三', '医生', '主任医师', '内科'],  # 第3行：示例数据
        ['002', '李四', '护士', '护师', '外科'],      # 第4行：用户数据
        [None, None, None, None, None],              # 第5行：空行
        ['003', '王五', '技师', '主管技师', '检验科'], # 第6行：用户数据
    ]
    
    expected_headers = ['工号', '姓名', '人员类型', '职称', '科室']
    
    # 查找表头
    headers_found = None
    header_row = 1
    
    for row_idx, row in enumerate(template_data, 1):
        if all(header in row for header in expected_headers):
            headers_found = row
            header_row = row_idx
            break
    
    if not headers_found:
        print("   ❌ 未找到表头")
        return False
    
    print(f"   ✅ 表头位置: 第{header_row}行")
    print(f"   📋 表头内容: {headers_found}")
    
    # 获取字段索引
    work_number_idx = headers_found.index('工号')
    name_idx = headers_found.index('姓名')
    staff_type_idx = headers_found.index('人员类型')
    title_idx = headers_found.index('职称')
    department_idx = headers_found.index('科室')
    
    # 读取数据行
    data_start_row = header_row + 1
    valid_data_rows = 0
    empty_rows = 0
    
    print(f"\n   📊 从第{data_start_row}行开始读取数据:")
    
    for row_idx in range(data_start_row - 1, len(template_data)):
        row = template_data[row_idx]
        actual_row_num = row_idx + 1
        
        # 获取各字段值
        work_number = row[work_number_idx] if work_number_idx < len(row) else None
        name = row[name_idx] if name_idx < len(row) else None
        staff_type_name = row[staff_type_idx] if staff_type_idx < len(row) else None
        title_name = row[title_idx] if title_idx < len(row) else None
        department_name = row[department_idx] if department_idx < len(row) else None
        
        # 检查是否为空行
        if not any([work_number, name, staff_type_name, title_name, department_name]):
            empty_rows += 1
            print(f"   ⚪ 第{actual_row_num}行: 空行，跳过")
            continue
        
        valid_data_rows += 1
        print(f"   ✅ 第{actual_row_num}行: 工号={work_number}, 姓名={name}, 人员类型={staff_type_name}, 职称={title_name}, 科室={department_name}")
    
    print(f"\n   📊 统计结果:")
    print(f"      有效数据行: {valid_data_rows}")
    print(f"      空行: {empty_rows}")
    print(f"      总处理行: {len(template_data) - header_row}")
    
    return valid_data_rows > 0

def main():
    """主测试函数"""
    print("🚀 开始工作人员模板导入修复测试...")
    print("=" * 60)
    
    # 测试1：表头检测逻辑
    test1_result = test_header_detection_logic()
    
    # 测试2：数据读取逻辑
    test2_result = test_data_reading()
    
    print("\n" + "=" * 60)
    print("📊 测试总结:")
    
    if test1_result and test2_result:
        print("🎉 所有测试通过！")
        print("\n✅ 修复效果:")
        print("   • 智能表头检测：自动识别第1行或第2行的表头")
        print("   • 动态数据起始：根据表头位置确定数据开始行")
        print("   • 空行处理：正确跳过空行")
        print("   • 字段索引：准确获取各字段的列位置")
        
        print("\n🔧 解决的问题:")
        print("   • 模板第1行有提示文字时导入失败")
        print("   • 表头和数据行号不匹配")
        print("   • 验证逻辑读取错误的行作为表头")
        
        print("\n📋 现在支持的模板格式:")
        print("   格式1: 第1行直接是表头")
        print("   格式2: 第1行是提示，第2行是表头")
        
        return True
    else:
        print("❌ 部分测试失败")
        if not test1_result:
            print("   • 表头检测逻辑测试失败")
        if not test2_result:
            print("   • 数据读取逻辑测试失败")
        return False

if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
