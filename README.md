# 医院服务评价系统

## 项目概述

医院服务评价系统是一个基于Django的前后端分离Web应用，用于收集和管理医院服务的评价信息。系统通过二维码扫描方式收集患者对医疗服务的评价，并提供数据分析和管理功能。

### 主要功能

- **科室管理**：添加、编辑、删除科室信息
- **工作人员管理**：管理医院工作人员信息，支持批量导入
- **床位管理**：按科室管理床位，添加、编辑、删除床位
- **二维码管理**：自动生成二维码，按科室批量打印
- **评价收集**：通过扫描二维码收集患者评价
- **数据分析**：评价统计、情感分析、趋势分析
- **数据导出**：导出各类数据报表
- **安全防护**：设备指纹防刷机制、多层安全防护

### 技术栈

- **后端**：Python 3.8+, Django 4.2.7
- **数据库**：SQLite3
- **前端**：HTML5, CSS3, JavaScript (模块化架构)
- **部署**：Nginx + Gunicorn
- **工具库**：qrcode, Pillow, coverage, django-bootstrap5

## 快速开始

### 环境要求

- Python 3.8+
- SQLite3
- 2GB+ RAM
- 10GB+ 磁盘空间

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [项目地址]
   cd 前后端分离
   ```

2. **创建虚拟环境**
   ```bash
   # Windows
   python -m venv venv
   venv\Scripts\activate
   
   # Linux/Mac
   python3 -m venv venv
   source venv/bin/activate
   ```

3. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

4. **配置环境变量**
   ```bash
   # 复制环境变量模板（如果存在）
   cp .env.example .env
   # 编辑 .env 文件配置必要参数
   ```

5. **数据库迁移**
   ```bash
   cd Backend
   python manage.py migrate
   ```

6. **创建超级用户**
   ```bash
   python manage.py createsuperuser
   ```

7. **收集静态文件**
   ```bash
   python manage.py collectstatic
   ```

8. **启动开发服务器**
   ```bash
   python manage.py runserver
   ```

9. **启动前端服务器**（可选）
   ```bash
   cd ../Frontend
   python frontend_server.py
   ```

### 访问系统

- 后端管理界面：http://localhost:8000/admin/
- 前端评价界面：http://localhost:8000/
- API文档：http://localhost:8000/api/

## 项目结构

```
前后端分离/
├── Backend/                 # Django后端项目
│   ├── HospitalQRCode/     # 项目配置
│   ├── qrmanager/          # 主应用模块
│   ├── docs/               # 后端文档
│   ├── static/             # 静态文件
│   ├── media/              # 上传文件
│   ├── logs/               # 日志文件
│   └── manage.py           # Django管理脚本
├── Frontend/               # 前端静态文件
│   ├── js/                 # JavaScript模块
│   ├── css/                # 样式文件
│   ├── assets/             # 资源文件
│   └── index.html          # 主页面
├── docs/                   # 项目文档
├── nginx-1.27.5/          # Nginx配置
├── requirements.txt        # Python依赖
└── README.md              # 项目说明
```

## 文档

- [系统架构](docs/system_architecture.md) - 整体架构设计
- [前端架构](docs/frontend_architecture.md) - 前端模块结构
- [API文档](Backend/docs/api_documentation.md) - API接口说明
- [数据库文档](Backend/docs/database_structure.md) - 数据库结构
- [用户手册](Backend/docs/user_manual.md) - 用户操作指南
- [开发规范](Backend/docs/development_guidelines.md) - 开发规范
- [部署指南](Backend/docs/deployment.md) - 部署说明
- [测试文档](Backend/docs/testing.md) - 测试指南
- [安全配置](docs/security_configuration.md) - 安全配置说明
- [快速开始](docs/quick_start.md) - 快速开始指南
- [代码结构](docs/code_structure.md) - 代码结构说明

## 开发

### 开发环境设置

1. 按照快速开始步骤设置基础环境
2. 安装开发依赖：`pip install -r requirements-dev.txt`（如果存在）
3. 配置IDE和代码格式化工具

### 运行测试

```bash
cd Backend
python manage.py test
```

### 代码规范

- 遵循PEP 8 Python编码规范
- 使用Django最佳实践
- 提交前运行测试和代码检查
- 详见[开发规范](Backend/docs/development_guidelines.md)

## 部署

详细部署说明请参考[部署指南](Backend/docs/deployment.md)。

### 生产环境部署要点

1. 使用Nginx + Gunicorn部署
2. 配置SSL证书
3. 设置环境变量
4. 配置日志轮转
5. 设置监控和备份

## 安全特性

- **设备指纹防刷**：防止恶意刷评价
- **CSRF保护**：防止跨站请求伪造
- **XSS防护**：防止跨站脚本攻击
- **SQL注入防护**：使用Django ORM防护
- **访问控制**：基于角色的权限控制
- **数据加密**：敏感数据加密存储

## 贡献

1. Fork项目
2. 创建功能分支：`git checkout -b feature/new-feature`
3. 提交更改：`git commit -am 'Add new feature'`
4. 推送分支：`git push origin feature/new-feature`
5. 创建Pull Request

## 许可证

[许可证信息]

## 联系方式

- 项目维护者：[维护者信息]
- 技术支持：[支持联系方式]
- 问题反馈：[反馈渠道]

## 更新日志

- **v1.3.0** (2025-03-15) - 二维码管理页面优化
- **v1.2.0** (2025-03-07) - API管理界面优化
- **v1.0.0** (2024-02-20) - 初始版本发布
