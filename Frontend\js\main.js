/**
 * 主应用模块 - 处理页面初始化和用户交互
 * 2025-03-24 重构版
 */

(function() {
    // 创建全局初始化命名空间（如果不存在）
    window._appInitFlags = window._appInitFlags || {};

    // 全局应用数据
    window.appData = window.appData || {};

    // 应用初始化标志变量
    let isInitializing = false;
    let domContentLoaded = false;

    // 监听DOM内容加载完成事件
    document.addEventListener('DOMContentLoaded', function() {
        console.log('DOM内容已加载完成');
        domContentLoaded = true;

        // 重新获取DOM元素引用
        refreshElementRefs();

        // 尝试初始化应用，如果还没有初始化的话
        if (!window._appInitFlags.mainInitialized && !isInitializing) {
            setTimeout(function() {
                app.init();
            }, 100);
        }

        // 如果staffModule已存在但还未初始化，尝试初始化
        if (window.staffModule && typeof window.staffModule.isDomReady === 'function') {
            if (window.staffModule.isDomReady() && !window.staffModule.isInitialized) {
                // 如果有数据则使用已有数据初始化
                if (window.appData.staffList && window.appData.staffTypes) {
                    window.staffModule.init(window.appData.staffList, window.appData.staffTypes);
                }
            }
        }
    });

    // 全局加载状态管理
    const loadingManager = {
        // 加载请求计数器
        loadingCount: 0,

        // 显示加载指示器
        show: function() {
            this.loadingCount++;
            console.log(`显示加载指示器 (计数: ${this.loadingCount})`);

            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.classList.remove('hidden');
            }
        },

        // 隐藏加载指示器
        hide: function() {
            this.loadingCount = Math.max(0, this.loadingCount - 1);
            console.log(`尝试隐藏加载指示器 (计数: ${this.loadingCount})`);

            // 只有所有加载请求都完成才隐藏
            if (this.loadingCount === 0) {
                const loadingIndicator = document.getElementById('loadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.classList.add('hidden');
                }
            }
        },

        // 强制重置加载状态
        reset: function() {
            this.loadingCount = 0;
            console.log('重置加载状态计数器');

            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                loadingIndicator.classList.add('hidden');
            }
        }
    };

    // 页面元素引用
    const elements = {
        appContainer: document.getElementById('app'),
        loadingIndicator: document.getElementById('loadingIndicator'),
        errorMessage: document.getElementById('errorMessage'),
        qrCodeRequiredSection: document.getElementById('qrCodeRequiredSection'),
        verificationInfo: document.getElementById('verificationInfo'),
        evaluationForm: document.getElementById('evaluationForm'),
        evaluationSubmitBtn: document.getElementById('evaluationSubmitBtn'),
        networkStatus: document.getElementById('networkStatus'),
        successMessage: document.getElementById('successMessage'),
        staffListContainer: document.getElementById('staffListContainer'),
        departmentInfo: document.getElementById('departmentInfo'),
        bedInfo: document.getElementById('bedInfo'),
        errorContainer: document.getElementById('errorContainer'),
        hospitalNumberInput: document.getElementById('hospitalNumberInput'),
        contactPhoneInput: document.getElementById('contactPhoneInput'),
        commentTextarea: document.getElementById('commentTextarea'),
        staffTypeSelect: document.getElementById('staffTypeSelect')
    };

    // 重新获取DOM元素引用
    function refreshElementRefs() {
        // 使用日志模块记录信息（如果已加载）
        if (window.logger && typeof window.logger.debug === 'function') {
            window.logger.debug('重新获取DOM元素引用...');
        } else {
            console.log('重新获取DOM元素引用...');
        }

        // 更新现有元素引用
        for (const key in elements) {
            if (!elements[key] && key !== 'app') {
                elements[key] = document.getElementById(key);
                if (elements[key] && window.logger && typeof window.logger.trace === 'function') {
                    window.logger.trace(`成功找到元素: ${key}`);
                } else if (elements[key]) {
                    // 生产环境不输出这些详细日志
                    if (window.location.search.includes('debug=true') || localStorage.getItem('debugMode') === 'true') {
                        console.log(`成功找到元素: ${key}`);
                    }
                }
            }
        }

        // 确保特定的重要元素引用正确
        elements.evaluationForm = document.getElementById('evaluationForm');
        elements.staffListContainer = document.getElementById('staffListContainer');
        elements.staffTypeSelect = document.getElementById('staffTypeSelect');

        // 使用日志模块记录信息（如果已加载）
        if (window.logger && typeof window.logger.debug === 'function') {
            window.logger.debug('DOM元素引用更新完成');
        } else {
            console.log('DOM元素引用更新完成');
        }
    }

    // 主要应用逻辑
    const app = {
        /**
         * 初始化应用
         */
        init: function() {
            // 防止重复初始化 - 使用全局初始化标志和局部变量双重检查
            if (window._appInitFlags && window._appInitFlags.mainInitialized || isInitializing) {
                console.log('应用已经初始化，跳过重复初始化');
                return;
            }

            // 设置初始化中标志
            isInitializing = true;

            // 检查是否已经提交过评价，如果是，不显示加载指示器
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，不显示加载指示器');

                // 强制隐藏加载指示器
                const loadingIndicator = document.getElementById('loadingIndicator');
                if (loadingIndicator) {
                    loadingIndicator.classList.add('hidden');
                }

                // 重置加载状态计数器
                loadingManager.loadingCount = 0;
            } else {
                // 重置加载状态，确保初始化时加载指示器正确
                if (typeof this.resetLoading === 'function') {
                    this.resetLoading();
                }
            }

            console.log('初始化应用程序...');

            // 确保window.appData存在
            window.appData = window.appData || {};

            // 不再直接设置API URL，统一使用api.js中的定义
            console.log('使用api.js中定义的API URL');

            // 检查评价状态的代码已移至processUrlParameters函数中

            // 如果有提取QR参数的函数，立即调用
            if (typeof window.extractAndSaveQRParam === 'function') {
                console.log('调用extractAndSaveQRParam函数获取URL参数...');
                window.extractAndSaveQRParam();
            }

            // 获取元素引用
            refreshElementRefs();

            // 注册事件处理程序
            if (typeof this.registerEventHandlers === 'function') {
                this.registerEventHandlers();
            } else if (typeof this.bindEventListeners === 'function') {
                this.bindEventListeners();
            }

            // 检查网络状态
            if (typeof this.updateNetworkStatus === 'function') {
                this.updateNetworkStatus();
            }

            // 处理URL参数
            this.processUrlParameters();

            // 初始化staffModule (如果已加载)
            if (window.staffModule) {
                if (typeof window.staffModule.moduleInit === 'function') {
                    window.staffModule.moduleInit();
                }
            }

            // 确保评价表单提交事件能正确处理
            const evaluationForm = document.getElementById('evaluationForm');
            if (evaluationForm) {
                console.log('评价表单已找到，由staffModule负责处理提交事件');
                // 不再在这里绑定事件，由staffModule完全处理
            }

            // 设置全局初始化标志
            if (window._appInitFlags) {
                window._appInitFlags.mainInitialized = true;
            }
            isInitializing = false;

            // 添加初始化完成标志
            this.isInitialized = true;

            console.log('应用程序初始化完成');
        },

        /**
         * 注册事件处理程序
         */
        registerEventHandlers: function() {
            console.log('注册事件处理程序...');

            // 网络状态变更监听
            window.addEventListener('online', this.updateNetworkStatus.bind(this));
            window.addEventListener('offline', this.updateNetworkStatus.bind(this));

            // 错误消息关闭按钮
            const closeErrorBtn = document.querySelector('.error-container .close-btn');
            if (closeErrorBtn) {
                closeErrorBtn.addEventListener('click', this.hideError.bind(this));
            }

            // 直接提交按钮绑定事件
            const directSubmitBtn = document.getElementById('directSubmitBtn');
            if (directSubmitBtn) {
                console.log('找到直接提交按钮，绑定点击事件');

                // 移除旧的事件处理程序
                const newBtn = directSubmitBtn.cloneNode(true);
                directSubmitBtn.parentNode.replaceChild(newBtn, directSubmitBtn);

                // 绑定新的事件处理程序
                newBtn.addEventListener('click', this.directSubmitEvaluation.bind(this));

                // 添加点击特效
                newBtn.addEventListener('click', function(e) {
                    // 创建一个水波纹元素
                    const ripple = document.createElement('span');
                    ripple.classList.add('ripple');
                    this.appendChild(ripple);

                    // 设置水波纹位置
                    const x = e.clientX - e.target.getBoundingClientRect().left;
                    const y = e.clientY - e.target.getBoundingClientRect().top;
                    ripple.style.left = `${x}px`;
                    ripple.style.top = `${y}px`;

                    // 清理水波纹元素
                    setTimeout(() => {
                        ripple.remove();
                    }, 600);
                });
            } else {
                console.warn('直接提交按钮未找到，无法绑定事件');
            }

            // 评价表单提交事件仍然保留，但不再针对submitBtn
            if (elements.evaluationForm) {
                console.log('绑定评价表单提交事件到directSubmitEvaluation函数');

                // 移除所有现有的提交事件监听器
                const oldForm = elements.evaluationForm;
                const newForm = oldForm.cloneNode(true);
                oldForm.parentNode.replaceChild(newForm, oldForm);
                elements.evaluationForm = newForm;

                // 绑定新的事件处理程序
                elements.evaluationForm.addEventListener('submit', this.directSubmitEvaluation.bind(this));
            }

            console.log('事件处理程序注册完成');
        },

        /**
         * 验证二维码参数
         * @param {string} qrParam - 二维码参数
         */
        verifyQRCode: async function(qrParam) {
            console.log(`验证二维码参数: ${qrParam}`);

            // 检查是否已经提交过评价，如果是，立即返回，不执行后续的验证流程
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，跳过验证流程');
                return null;
            }

            try {
                // 保存二维码参数到appData，确保其他地方可以使用
                window.appData = window.appData || {};
                window.appData.qrParam = qrParam;
                window.appData.qrCode = qrParam;

                // 显示加载指示器
                this.showLoading();

                // 确认参数有效
                if (!qrParam || qrParam.length < 5) {
                    this.showError('无效的二维码参数');
                    this.hideLoading();

                    // 确保设置失败状态
                    window.appData.verificationSuccess = false;

                    throw new Error('无效的二维码参数');
                }

                // 确保存在API模块
                if (!window.api || typeof window.api.verifyQRCode !== 'function') {
                    this.hideLoading();

                    // 确保设置失败状态
                    window.appData.verificationSuccess = false;

                    throw new Error('API模块未加载');
                }

                // 验证二维码参数
                const data = await window.api.verifyQRCode(qrParam);

                // 再次确认验证状态，防止不一致
                if (!window.appData.verificationSuccess) {
                    console.warn('API返回成功但验证状态未设置为成功，可能存在不一致');
                    window.appData.verificationSuccess = true;
                }

                // 验证成功后更新UI
                console.log('二维码验证成功，调用UI更新...');

                // 确保刷新DOM元素引用
                refreshElementRefs();

                // 显示验证成功信息
                this.showVerificationInfo();

                // 更新UI显示
                this.updateUI(data);

                // 显示评价表单
                this.showEvaluationForm();

                console.log('二维码验证成功');

                // 验证到此结束
                console.log('验证流程结束，隐藏加载指示器');
                this.hideLoading();

                return data;
            } catch (error) {
                // 记录详细错误信息
                console.error('验证二维码时出错:', error);

                // 确保设置失败状态
                window.appData.verificationSuccess = false;

                // 隐藏加载指示器
                this.hideLoading();

                // 显示错误消息
                this.showError('验证失败: ' + error.message);

                // 重新抛出错误以便上层处理
                throw error;
            }
        },

        /**
         * 更新UI显示
         * @param {object} data - 响应数据
         */
        updateUI: function(data) {
            console.log('更新UI...', data);

            // 首先确保强制隐藏加载指示器
            this.resetLoading();

            // 重新获取DOM元素引用，确保所有元素都正确引用
            refreshElementRefs();

            // 确保隐藏二维码表单区域
            if (elements.qrCodeRequiredSection) {
                elements.qrCodeRequiredSection.classList.add('hidden');
            }

            // 检查验证状态
            if (!window.appData.verificationSuccess) {
                console.warn('验证未成功，不显示评价表单');
                if (elements.evaluationForm) {
                    elements.evaluationForm.classList.add('hidden');
                }
                return;
            }

            // 记录API返回的数据
            console.log('API返回的数据:', {
                staff_types: data && data.staff_types ? data.staff_types.length + '项' : '无',
                staff: data && data.staff ? data.staff.length + '项' : '无',
                department: data && data.department ? data.department : '无',
                bed: data && data.bed ? data.bed : '无'
            });

            // 保存API返回的数据到全局变量
            if (data) {
                if (data.staff_types) {
                    console.log('保存工作人员类型数据:', data.staff_types);
                    window.appData.staffTypes = data.staff_types;

                    // 打印每种类型的详细信息
                    data.staff_types.forEach(type => {
                        console.log(`工作人员类型: ID=${type.id}, 名称=${type.name}, 代码=${type.code}`);
                    });
                }

                if (data.staff) {
                    console.log('保存工作人员列表数据:', data.staff.length + '名工作人员');
                    window.appData.staffList = data.staff;

                    // 打印每个工作人员的类型ID
                    data.staff.forEach(staff => {
                        console.log(`工作人员: ID=${staff.id}, 名称=${staff.name}, 类型ID=${staff.type_id || staff.staff_type}`);
                    });
                }
            }

            // 通过staffModule更新员工列表 - 增加DOM就绪检查和初始化逻辑
            if (window.staffModule) {
                console.log('找到staffModule，准备初始化...');

                // 强制重新初始化staffModule
                if (window.staffModule.isInitialized) {
                    console.log('staffModule已初始化，重置状态后重新初始化');
                    window.staffModule.isInitialized = false;
                    window.staffModule.isInitializing = false;
                }

                // 确保从API响应中获取数据
                if (data && data.staff_types) {
                    window.appData.staffTypes = data.staff_types;
                }
                if (data && data.staff) {
                    window.appData.staffList = data.staff;
                }

                // 使用可用数据初始化
                console.log('调用staffModule.init初始化模块');
                window.staffModule.init(window.appData.staffList || [], window.appData.staffTypes || []);
            } else {
                console.error('staffModule未找到，无法初始化工作人员列表');

                // 尝试加载staffModule脚本
                const staffModuleScript = document.querySelector('script[src*="staffModule.js"]');
                if (!staffModuleScript) {
                    console.error('staffModule脚本未找到，尝试动态加载');

                    // 创建并添加脚本元素
                    const script = document.createElement('script');
                    script.src = 'js/staffModule.js';
                    script.onload = () => {
                        console.log('staffModule脚本加载成功，尝试初始化');
                        if (window.staffModule && typeof window.staffModule.init === 'function') {
                            window.staffModule.init(window.appData.staffList || [], window.appData.staffTypes || []);
                        }
                    };
                    document.head.appendChild(script);
                }
            }

            // 显示评价表单 - 强制移除hidden类
            if (elements.evaluationForm) {
                console.log('显示评价表单');
                elements.evaluationForm.classList.remove('hidden');
            } else {
                console.error('评价表单元素未找到');
                // 尝试重新获取元素
                elements.evaluationForm = document.getElementById('evaluationForm');
                if (elements.evaluationForm) {
                    console.log('重新获取到评价表单元素，现在显示');
                    elements.evaluationForm.classList.remove('hidden');
                } else {
                    console.error('评价表单元素仍然未找到，无法显示');
                }
            }

            console.log('UI更新完成');
        },

        /**
         * 处理URL参数
         */
        processUrlParameters: function() {
            try {
                console.log('处理URL参数...');

                // 解析URL参数
                const urlParams = new URLSearchParams(window.location.search);

                // 查找二维码参数 - 支持多种可能的参数名
                let qrParam = urlParams.get('qr') || urlParams.get('qrcode') || urlParams.get('code') ||
                             urlParams.get('qr_param') || urlParams.get('qrParam');

                // 如果URL中没有参数，尝试从路径中提取
                if (!qrParam) {
                    const pathMatch = window.location.pathname.match(/\/q\/([^\/]+)/i);
                    if (pathMatch && pathMatch[1]) {
                        qrParam = pathMatch[1];
                        console.log('从URL路径中提取到二维码参数:', qrParam);
                    }
                }

                // 记录提取的二维码参数
                console.log('获取的二维码参数:', qrParam);

                // 确保appData对象存在
                window.appData = window.appData || {};

                // 立即保存二维码参数到appData，无论是否验证
                if (qrParam) {
                    window.appData.qrParam = qrParam;
                    window.appData.qrCode = qrParam; // 同时保存两个名称以确保兼容性
                    console.log('已将二维码参数保存到appData中:', qrParam);

                    // 不再直接设置API URL，统一使用api.js中的定义
                    console.log('使用api.js中定义的API URL');

                    // 检查会话存储中是否有评价状态，如果有，说明用户已经提交过评价
                    if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                        console.log('用户通过返回按钮回到评价页面，显示提示信息');

                        // 隐藏评价表单
                        const evaluationForm = document.getElementById('evaluationForm');
                        if (evaluationForm) {
                            evaluationForm.classList.add('hidden');
                        }

                        // 隐藏验证信息
                        const verificationInfo = document.getElementById('verificationInfo');
                        if (verificationInfo) {
                            verificationInfo.classList.add('hidden');
                        }

                        // 更新加载指示器文本并隐藏
                        if (elements.loadingIndicator) {
                            const loadingText = elements.loadingIndicator.querySelector('.loading-text');
                            if (loadingText) {
                                loadingText.textContent = '您已经提交过评价，请重新扫描二维码';
                            }
                            elements.loadingIndicator.classList.add('hidden');
                        }

                        // 创建提示信息
                        const submittedMessage = document.createElement('div');
                        submittedMessage.className = 'success-message';
                        submittedMessage.style.textAlign = 'center';
                        submittedMessage.style.padding = '30px';
                        submittedMessage.style.margin = '30px auto';
                        submittedMessage.style.maxWidth = '600px';
                        submittedMessage.style.backgroundColor = '#f8f9fa';
                        submittedMessage.style.borderRadius = '8px';
                        submittedMessage.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
                        submittedMessage.style.animation = 'fadeIn 0.5s';
                        submittedMessage.innerHTML = `
                            <style>
                                @keyframes fadeIn {
                                    from { opacity: 0; transform: translateY(-20px); }
                                    to { opacity: 1; transform: translateY(0); }
                                }
                            </style>
                            <div style="font-size: 70px; color: #28a745; margin-bottom: 20px;">✓</div>
                            <h2 style="color: #333; margin-bottom: 20px; font-size: 24px;">您已经提交过评价</h2>
                            <p style="color: #666; margin-bottom: 20px; font-size: 16px;">感谢您的反馈，您的评价已经成功提交。</p>
                            <p style="color: #666; margin-bottom: 20px; font-size: 16px;">如需再次评价，请重新扫描二维码。</p>
                        `;

                        // 添加到页面
                        const mainContent = document.querySelector('.main-content');
                        if (mainContent) {
                            mainContent.appendChild(submittedMessage);
                        } else {
                            document.body.appendChild(submittedMessage);
                        }

                        // 滚动到提示信息
                        submittedMessage.scrollIntoView({ behavior: 'smooth' });

                        // 阻止后续的验证流程
                        return;
                    }
                }

                // 如果找到二维码参数则开始验证
                if (qrParam) {
                    // 检查是否已经提交过评价，如果是，不执行验证流程
                    if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                        console.log('用户已经提交过评价，不执行验证流程');
                        // 更新加载指示器文本并隐藏
                        if (elements.loadingIndicator) {
                            const loadingText = elements.loadingIndicator.querySelector('.loading-text');
                            if (loadingText) {
                                loadingText.textContent = '您已经提交过评价，请重新扫描二维码';
                            }
                            elements.loadingIndicator.classList.add('hidden');
                        }
                        return;
                    }

                    // 确保参数健康 - 只保留合法字符
                    qrParam = qrParam.trim().replace(/[^a-zA-Z0-9_\-\.]/g, '');

                    // 如果参数有效则验证
                    if (qrParam.length >= 5) {
                        console.log('参数有效，开始验证流程');

                        // 开始验证流程
                        this.verifyQRCode(qrParam).catch(error => {
                            console.error('二维码验证过程出错:', error);
                            this.showError('验证失败: ' + error.message);
                        });
                    } else {
                        console.error('二维码参数太短，不验证:', qrParam);
                        this.showError('无效的二维码参数');
                    }
                } else {
                    // 没有找到二维码参数
                    console.log('URL中未找到二维码参数');
                    this.showQrCodeFormIfNeeded();
                }
            } catch (error) {
                console.error('处理URL参数出错:', error);
                this.showError('参数处理失败');
            }
        },

        /**
         * 更新网络状态显示
         */
        updateNetworkStatus: function() {
            const isOnline = navigator.onLine;
            console.log(`当前网络状态: ${isOnline ? '在线' : '离线'}`);

            if (elements.networkStatus) {
                // 如果离线，显示离线状态
                if (!isOnline) {
                    elements.networkStatus.className = 'status-offline';
                    elements.networkStatus.textContent = '网络离线';
                    this.showError('您当前处于离线状态，部分功能可能无法使用');
                    return;
                }

                // 在线状态下，总是显示系统已连接，不进行API测试
                elements.networkStatus.textContent = '系统已连接 ✓';
                elements.networkStatus.className = 'network-status status-online';
                console.log('网络在线，设置系统已连接状态');
                return;
            }

            // 以下代码保留但不会执行，因为上面已经return了
            // 检查是否已经提交过评价，如果是，不测试API连接
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，不测试API连接');

                // 直接设置网络状态为已连接，避免不必要的API请求
                if (elements.networkStatus) {
                    elements.networkStatus.textContent = '系统已连接 ✓';
                    elements.networkStatus.className = 'network-status status-online';
                }
                return;
            }

            // 测试API连接 - 避免同时多次测试
            this.testApiConnection();
        },

        /**
         * 测试API连接 - 单独方法，避免重复逻辑
         */
        testApiConnection: function() {
            // 检查API模块是否加载
            if (!window.api || typeof window.api.testConnection !== 'function') {
                console.log('API模块未加载，无法测试连接');
                return;
            }

            // 如果二维码验证已成功，不显示连接失败状态
            if (window.appData && window.appData.verificationSuccess) {
                console.log('二维码验证已成功，跳过连接状态显示');
                if (elements.networkStatus) {
                    elements.networkStatus.textContent = '系统已连接 ✓';
                    elements.networkStatus.className = 'network-status status-online';
                }
                return;
            }

            console.log('开始测试API连接...');

            // 直接调用API连接测试
            window.api.testConnection()
                .then(isConnected => {
                    console.log('API服务器连接测试:', isConnected ? '成功' : '失败');

                    if (elements.networkStatus) {
                        if (isConnected) {
                            elements.networkStatus.textContent = '系统已连接 ✓';
                            elements.networkStatus.className = 'network-status status-online';
                            // 强制更新页面标题
                            document.title = "自贡市第四人民医院服务评价系统";
                        } else {
                            // 如果验证成功但连接测试失败，仍显示已连接状态
                            if (window.appData && window.appData.verificationSuccess) {
                                elements.networkStatus.textContent = '系统已连接 ✓';
                                elements.networkStatus.className = 'network-status status-online';
                            } else {
                                elements.networkStatus.textContent = '系统连接失败 ⚠';
                                elements.networkStatus.className = 'network-status status-warning';
                            }
                        }
                    }
                })
                .catch(error => {
                    console.error('测试API连接时出错:', error);
                    if (elements.networkStatus) {
                        // 如果验证成功但连接测试出错，仍显示已连接状态
                        if (window.appData && window.appData.verificationSuccess) {
                            elements.networkStatus.textContent = '系统已连接 ✓';
                            elements.networkStatus.className = 'network-status status-online';
                        } else {
                            elements.networkStatus.textContent = '系统连接错误 ⚠';
                            elements.networkStatus.className = 'network-status status-warning';
                        }
                    }
                });
        },

        /**
         * 显示加载指示器
         */
        showLoading: function() {
            loadingManager.show();
        },

        /**
         * 隐藏加载指示器
         */
        hideLoading: function() {
            loadingManager.hide();
        },

        /**
         * 重置加载状态
         */
        resetLoading: function() {
            loadingManager.reset();
        },

        /**
         * 显示错误消息
         * @param {string} message - 错误消息
         * @param {Error} [error] - 错误对象（可选）
         */
        showError: function(message, error) {
            // 使用日志模块记录错误（如果已加载）
            if (window.logger && typeof window.logger.error === 'function') {
                if (error) {
                    window.logger.error(`错误: ${message}`, error);
                } else {
                    window.logger.error(`错误: ${message}`);
                }
            } else {
                // 日志模块未加载，使用console.error
                console.error(`错误: ${message}`);
                if (error && error.stack) {
                    console.error(error.stack);
                }
            }

            // 保存错误信息到全局变量，用于调试
            window.appData = window.appData || {};
            window.appData.lastUIError = {
                message: message,
                error: error,
                timestamp: new Date().toISOString()
            };

            // 显示用户友好的错误消息
            if (elements.errorContainer && elements.errorMessage) {
                // 在生产环境中，显示用户友好的错误消息，而不是技术错误信息
                const isProduction = !window.location.search.includes('debug=true') &&
                                    localStorage.getItem('debugMode') !== 'true';

                // 如果是生产环境，且错误消息包含技术细节，则显示通用错误消息
                if (isProduction && (message.includes('API') || message.includes('模块') ||
                    message.includes('服务器') || message.includes('请求') ||
                    message.includes('响应') || message.includes('错误'))) {
                    elements.errorMessage.textContent = '操作失败，请稍后重试';
                } else {
                    elements.errorMessage.textContent = message;
                }

                elements.errorContainer.classList.remove('hidden');
            }
        },

        /**
         * 隐藏错误消息
         */
        hideError: function() {
            if (elements.errorContainer) {
                elements.errorContainer.classList.add('hidden');
            }
        },

        /**
         * 提交评价
         */
        submitEvaluation: async function() {
            console.log('main.js: 提交评价函数被调用');

            try {
                // 确保DOM元素引用正确
                refreshElementRefs();

                // 确保staffModule已初始化
                if (!window.staffModule) {
                    this.showError('评价模块未初始化');
                    return;
                }

                // 确保evaluationManager存在
                if (!window.evaluationManager) {
                    console.warn('evaluationManager不存在，尝试创建');
                    if (typeof EvaluationManager === 'function') {
                        window.evaluationManager = new EvaluationManager();
                    } else {
                        this.showError('评价管理器未加载');
                        return;
                    }
                }

                // 使用staffModule显示评价对话框而不是直接提交
                if (typeof window.staffModule.showEvaluationModal === 'function') {
                    window.staffModule.showEvaluationModal();
                    return;
                }

                // 以下是兼容旧代码的逻辑，新版本不应执行到这里

                // 表单验证 - 工作人员选择为可选项
                const staffEvaluations = window.staffModule ? window.staffModule.getEvaluations() : [];
                // 🔧 修复：工作人员选择改为可选，不再强制要求
                if (staffEvaluations.length === 0) {
                    console.log('[旧版提交] 用户未选择工作人员，但允许继续提交');
                    // 不再阻止提交，允许只提交反馈内容
                }

                const comment = elements.commentTextarea ? elements.commentTextarea.value.trim() : '';
                if (comment.length < 5) {
                    this.showError('评价内容至少需要5个字');
                    return;
                }

                // 收集评价数据
                const evaluationData = {
                    qr_param: window.appData.qrCode,
                    temp_token: window.appData.tempToken,
                    hospital_number: elements.hospitalNumberInput ? elements.hospitalNumberInput.value.trim() : '',
                    contact_phone: elements.contactPhoneInput ? elements.contactPhoneInput.value.trim() : '',
                    comment: comment,
                    staff_evaluations: staffEvaluations
                };

                console.log('评价数据:', evaluationData);

                // 显示加载指示器
                this.showLoading();

                // 尝试使用staffModule提交
                if (typeof window.staffModule.submitEvaluation === 'function') {
                    const result = await window.staffModule.submitEvaluation(comment);

                    // 处理提交结果
                    if (result && result.success) {
                        console.log('评价提交成功');
                    } else {
                        this.showError(result.message || '提交失败');
                    }

                    this.hideLoading();
                    return;
                }

                // 确保API模块存在
                if (!window.api || typeof window.api.submitEvaluation !== 'function') {
                    throw new Error('API模块未加载');
                }

                // 调用API提交评价
                const response = await window.api.submitEvaluation(evaluationData);

                // 隐藏加载指示器
                this.hideLoading();

                // 更新UI
                this.showSuccessMessage('评价提交成功！');

                return response;
            } catch (error) {
                // 使用日志模块记录错误
                if (window.logger && typeof window.logger.error === 'function') {
                    window.logger.error('提交评价失败:', error);
                } else {
                    console.error('提交评价失败:', error);
                }

                // 隐藏加载指示器
                this.hideLoading();

                // 显示错误信息
                this.showError(error.message || '提交失败，请稍后重试', error);

                throw error;
            }
        },

        /**
         * 显示验证信息
         */
        showVerificationInfo: function() {
            // 只有在验证真正成功时才显示验证成功信息
            if (!window.appData || window.appData.verificationSuccess !== true) {
                console.warn('验证未成功，不显示验证成功信息');
                if (elements.verificationInfo) {
                    elements.verificationInfo.classList.add('hidden');
                }
                return;
            }

            if (elements.verificationInfo) {
                elements.verificationInfo.classList.remove('hidden');
            }
        },

        /**
         * 显示成功消息
         */
        showSuccessMessage: function(message) {
            // 隐藏评价表单
            if (elements.evaluationForm) {
                elements.evaluationForm.classList.add('hidden');
            }

            // 隐藏验证信息
            if (elements.verificationInfo) {
                elements.verificationInfo.classList.add('hidden');
            }

            // 显示成功消息
            if (elements.successMessage) {
                elements.successMessage.textContent = message;
                elements.successMessage.classList.remove('hidden');
            }
        },

        /**
         * 更新选中员工显示
         */
        updateSelectedStaffDisplay: function() {
            if (!elements.selectedStaffDisplay) return;

            try {
                const selectedCount = window.appData.selectedStaff.length;

                if (selectedCount > 0) {
                    // 显示选中员工
                    const selectedNames = window.appData.selectedStaff.map(staff => staff.name).join(', ');
                    const spanElement = elements.selectedStaffDisplay.querySelector('span');
                    if (spanElement) {
                        spanElement.textContent = selectedNames;
                    }
                    elements.selectedStaffDisplay.classList.remove('hidden');
                } else {
                    // 隐藏选中显示
                    elements.selectedStaffDisplay.classList.add('hidden');
                }
            } catch (error) {
                console.error('更新选中员工显示时出错:', error);
            }
        },

        /**
         * 显示评价表单
         */
        showEvaluationForm: function() {
            // 检查验证状态
            if (!window.appData || window.appData.verificationSuccess !== true) {
                console.warn('验证未成功，不显示评价表单');
                return;
            }

            // 确保评价表单存在
            if (!elements.evaluationForm) {
                console.error('评价表单元素未找到');
                elements.evaluationForm = document.getElementById('evaluationForm');
                if (!elements.evaluationForm) {
                    console.error('无法获取评价表单元素');
                    return;
                }
            }

            console.log('显示评价表单');
            elements.evaluationForm.classList.remove('hidden');

            // 隐藏无效参数提示
            const invalidParamMessage = document.getElementById('invalidParamMessage');
            if (invalidParamMessage) {
                invalidParamMessage.classList.add('hidden');
            }

            // 不再在这里绑定提交按钮事件，统一由staffModule处理
        },

        /**
         * 显示二维码表单
         */
        showQrCodeFormIfNeeded: function() {
            // 检查二维码表单是否需要显示
            if (elements.qrCodeRequiredSection) {
                elements.qrCodeRequiredSection.classList.remove('hidden');
            }
        },

        /**
         * 直接提交评价 - 完全复制测试网页的提交逻辑
         * 绕过所有复杂的处理逻辑，直接使用简单的fetch请求
         */
        directSubmitEvaluation: async function(event) {
            // 如果是表单提交事件，阻止默认行为
            if (event && event.preventDefault) {
                event.preventDefault();
            }

            console.log('[直接提交] 开始处理评价提交...');

            // 检查是否已经提交过评价，如果是，不执行提交操作
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('[直接提交] 用户已经提交过评价，不执行提交操作');
                return;
            }

            try {
                // 显示加载状态
                if (elements.loadingIndicator) {
                    elements.loadingIndicator.classList.remove('hidden');
                }

                // 1. 收集表单数据
                const commentTextarea = document.getElementById('commentTextarea');
                const hospitalNumberInput = document.getElementById('hospitalNumberInput');
                const contactPhoneInput = document.getElementById('contactPhoneInput');

                if (!commentTextarea) {
                    throw new Error('评价内容输入框未找到');
                }

                const comment = commentTextarea.value.trim();
                const hospitalNumber = hospitalNumberInput ? hospitalNumberInput.value.trim() : '';
                const phoneNumber = contactPhoneInput ? contactPhoneInput.value.trim() : '';

                // 验证评价内容
                if (comment.length < 5) {
                    alert('评价内容至少需要5个字');
                    if (elements.loadingIndicator) {
                        elements.loadingIndicator.classList.add('hidden');
                    }
                    return;
                }

                // 2. 获取评价数据
                let staffEvaluations = [];

                if (window.evaluationManager) {
                    // 获取满意评价
                    window.evaluationManager.getSatisfiedEvaluations().forEach(staff => {
                        staffEvaluations.push({
                            staff_id: parseInt(staff.id),
                            is_satisfied: true
                        });
                    });

                    // 获取不满意评价
                    window.evaluationManager.getUnsatisfiedEvaluations().forEach(staff => {
                        staffEvaluations.push({
                            staff_id: parseInt(staff.id),
                            is_satisfied: false
                        });
                    });

                    console.log('[直接提交] 从evaluationManager获取评价数据:', staffEvaluations);
                } else {
                    console.error('[直接提交] evaluationManager不存在');

                    // 使用默认测试数据
                    staffEvaluations = [
                        { staff_id: 36, is_satisfied: true },
                        { staff_id: 57, is_satisfied: false }
                    ];
                    console.log('[直接提交] 使用默认测试数据:', staffEvaluations);
                }

                // 🔧 修复：工作人员选择改为可选项
                // 验证是否选择了工作人员 - 现在允许不选择工作人员
                if (staffEvaluations.length === 0) {
                    console.log('[直接提交] 用户未选择工作人员，但允许继续提交');
                    // 不再阻止提交，允许只提交反馈内容
                }

                // 3. 获取QR参数
                const qrParam = window.appData ? (window.appData.qrParam || window.appData.qrCode) : null;

                if (!qrParam) {
                    // 尝试从URL中获取
                    const urlParams = new URLSearchParams(window.location.search);
                    const urlQrParam = urlParams.get('qr') || urlParams.get('qrcode') ||
                                    urlParams.get('qr_param') || urlParams.get('qrParam');

                    if (!urlQrParam) {
                        alert('缺少二维码参数，请重新扫描二维码');
                        if (elements.loadingIndicator) {
                            elements.loadingIndicator.classList.add('hidden');
                        }
                        return;
                    }

                    // 保存获取到的参数
                    window.appData = window.appData || {};
                    window.appData.qrParam = urlQrParam;
                    window.appData.qrCode = urlQrParam;
                    console.log('[直接提交] 从URL获取QR参数:', urlQrParam);
                }

                // 获取医院整体评价 - 必填验证
                const hospitalRating = document.getElementById('hospitalRating');
                let hospitalRatingValue = null;

                if (hospitalRating && hospitalRating.value !== null && hospitalRating.value !== undefined) {
                    hospitalRatingValue = parseInt(hospitalRating.value);
                    console.log('[直接提交] 医院整体评价原始值:', hospitalRating.value, '解析后:', hospitalRatingValue);

                    // 检查解析后的值是否有效（不是0且不是NaN）
                    if (isNaN(hospitalRatingValue) || hospitalRatingValue === 0) {
                        alert('请对医院整体服务进行评价（1-5星）');
                        if (elements.loadingIndicator) {
                            elements.loadingIndicator.classList.add('hidden');
                        }
                        return;
                    }
                } else {
                    // 医院整体评价是必填项
                    alert('请对医院整体服务进行评价（1-5星）');
                    if (elements.loadingIndicator) {
                        elements.loadingIndicator.classList.add('hidden');
                    }
                    return;
                }

                // 验证评分范围
                if (hospitalRatingValue < 1 || hospitalRatingValue > 5) {
                    alert('医院整体评价必须在1-5星之间');
                    if (elements.loadingIndicator) {
                        elements.loadingIndicator.classList.add('hidden');
                    }
                    return;
                }

                // 4. 构建提交数据 - 与测试网页完全相同的格式
                const evaluationData = {
                    qr_param: window.appData.qrParam,
                    comment: comment,
                    staff_evaluations: staffEvaluations,
                    hospital_number: hospitalNumber,
                    phone_number: phoneNumber,
                    hospital_rating: hospitalRatingValue // 添加医院整体评价
                };

                // 5. 使用API模块提交评价
                console.log('[直接提交] 提交数据:', JSON.stringify(evaluationData, null, 2));

                // 6. 使用API模块发送请求，而不是直接使用fetch
                // 确保API模块存在
                if (!window.api || typeof window.api.submitEvaluation !== 'function') {
                    throw new Error('API模块未加载或submitEvaluation方法不存在');
                }

                // 调用API模块的submitEvaluation方法
                const result = await window.api.submitEvaluation(evaluationData, true);

                // 模拟response对象以保持代码兼容性
                const response = {
                    ok: result && (result.status === 'success' || result.code === 0 || result.code === 200),
                    status: result && result.code ? result.code : 200,
                    statusText: result && result.message ? result.message : 'OK'
                };

                console.log('[直接提交] 收到响应:', {
                    status: response.status,
                    statusText: response.statusText
                });

                // 7. 记录响应结果
                console.log('[直接提交] 响应结果:', result);

                // 不需要再解析JSON，因为API模块已经处理了这部分

                // 9. 处理成功和失败情况
                if (response.ok) {
                    console.log('[直接提交] 评价提交成功');

                    // 保存响应数据到全局
                    window.appData.lastEvaluationResponse = result;

                    // 记录评价状态到会话存储
                    sessionStorage.setItem('evaluationSubmitted', 'true');
                    sessionStorage.setItem('evaluationSubmittedTime', new Date().toISOString());
                    sessionStorage.setItem('qrParam', window.appData.qrParam || '');
                    console.log('[直接提交] 已记录评价状态到会话存储');

                    // 防止用户通过返回按钮回到评价页面
                    // 使用history.pushState修改历史记录
                    const randomState = Math.random().toString(36).substring(2, 15);
                    history.pushState({ state: randomState }, document.title, window.location.href);

                    // 监听popstate事件，当用户点击返回按钮时触发
                    window.addEventListener('popstate', function(event) {
                        // 再次修改历史记录，防止用户返回
                        history.pushState({ state: randomState }, document.title, window.location.href);

                        // 显示提示信息
                        alert('您已经提交过评价，如需再次评价，请重新扫描二维码。');
                    });

                    // 隐藏评价表单
                    const evaluationForm = document.getElementById('evaluationForm');
                    if (evaluationForm) {
                        evaluationForm.classList.add('hidden');
                    }

                    // 隐藏验证信息
                    const verificationInfo = document.getElementById('verificationInfo');
                    if (verificationInfo) {
                        verificationInfo.classList.add('hidden');
                    }

                    // 隐藏加载指示器
                    if (elements.loadingIndicator) {
                        elements.loadingIndicator.classList.add('hidden');
                    }

                    // 显示成功信息（直接在当前页面上）
                    const successMessage = document.createElement('div');
                    successMessage.className = 'success-message';
                    successMessage.style.textAlign = 'center';
                    successMessage.style.padding = '30px';
                    successMessage.style.margin = '30px auto';
                    successMessage.style.maxWidth = '600px';
                    successMessage.style.backgroundColor = '#f8f9fa';
                    successMessage.style.borderRadius = '8px';
                    successMessage.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
                    successMessage.style.animation = 'fadeIn 0.5s';
                    successMessage.innerHTML = `
                        <style>
                            @keyframes fadeIn {
                                from { opacity: 0; transform: translateY(-20px); }
                                to { opacity: 1; transform: translateY(0); }
                            }
                        </style>
                        <div style="font-size: 70px; color: #28a745; margin-bottom: 20px;">✓</div>
                        <h2 style="color: #333; margin-bottom: 20px; font-size: 24px;">评价提交成功！</h2>
                        <p style="color: #666; margin-bottom: 20px; font-size: 16px;">感谢您的反馈，我们将认真考虑您的意见和建议，不断改进我们的服务质量，为您提供更好的体验。</p>
                    `;

                    // 添加到页面
                    const mainContent = document.querySelector('.main-content');
                    if (mainContent) {
                        mainContent.appendChild(successMessage);
                    } else {
                        document.body.appendChild(successMessage);
                    }

                    // 滚动到成功信息
                    successMessage.scrollIntoView({ behavior: 'smooth' });
                } else {
                    // 处理错误响应
                    console.error('[直接提交] 评价提交失败:', responseText);

                    // 尝试解析错误信息
                    let errorMessage = `提交失败: ${response.status}`;
                    try {
                        const errorJson = JSON.parse(responseText);
                        if (errorJson.message) {
                            errorMessage = errorJson.message;
                        } else if (errorJson.error) {
                            errorMessage = errorJson.error;
                        } else if (errorJson.detail) {
                            errorMessage = errorJson.detail;
                        }
                    } catch (e) {
                        if (responseText && responseText.length < 100) {
                            errorMessage = responseText;
                        }
                    }

                    // 隐藏评价表单
                    const evaluationForm = document.getElementById('evaluationForm');
                    if (evaluationForm) {
                        evaluationForm.classList.add('hidden');
                    }

                    // 隐藏验证信息
                    const verificationInfo = document.getElementById('verificationInfo');
                    if (verificationInfo) {
                        verificationInfo.classList.add('hidden');
                    }

                    // 显示失败信息（直接在当前页面上）
                    const failureMessage = document.createElement('div');
                    failureMessage.className = 'failure-message';
                    failureMessage.style.textAlign = 'center';
                    failureMessage.style.padding = '30px';
                    failureMessage.style.margin = '30px auto';
                    failureMessage.style.maxWidth = '600px';
                    failureMessage.style.backgroundColor = '#f8f9fa';
                    failureMessage.style.borderRadius = '8px';
                    failureMessage.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
                    failureMessage.style.animation = 'fadeIn 0.5s';
                    failureMessage.innerHTML = `
                        <style>
                            @keyframes fadeIn {
                                from { opacity: 0; transform: translateY(-20px); }
                                to { opacity: 1; transform: translateY(0); }
                            }
                        </style>
                        <div style="font-size: 70px; color: #dc3545; margin-bottom: 20px;">✗</div>
                        <h2 style="color: #333; margin-bottom: 20px; font-size: 24px;">评价提交失败</h2>
                        <p style="color: #666; margin-bottom: 20px; font-size: 16px;">抱歉，您的评价提交失败。请稍后再试。</p>
                        <p style="color: #666; margin-bottom: 20px; font-size: 16px;">如需再次评价，请重新扫描二维码。</p>
                    `;

                    // 添加到页面
                    const mainContent = document.querySelector('.main-content');
                    if (mainContent) {
                        mainContent.appendChild(failureMessage);
                    } else {
                        document.body.appendChild(failureMessage);
                    }

                    // 滚动到失败信息
                    failureMessage.scrollIntoView({ behavior: 'smooth' });

                    // 防止用户通过返回按钮回到评价页面
                    // 使用history.pushState修改历史记录
                    const randomState = Math.random().toString(36).substring(2, 15);
                    history.pushState({ state: randomState }, document.title, window.location.href);

                    // 监听popstate事件，当用户点击返回按钮时触发
                    window.addEventListener('popstate', function(event) {
                        // 再次修改历史记录，防止用户返回
                        history.pushState({ state: randomState }, document.title, window.location.href);

                        // 显示提示信息
                        alert('评价提交失败，如需再次评价，请重新扫描二维码。');
                    });
                }
            } catch (error) {
                console.error('[直接提交] 处理评价提交时出错:', error);

                // 隐藏评价表单
                const evaluationForm = document.getElementById('evaluationForm');
                if (evaluationForm) {
                    evaluationForm.classList.add('hidden');
                }

                // 隐藏验证信息
                const verificationInfo = document.getElementById('verificationInfo');
                if (verificationInfo) {
                    verificationInfo.classList.add('hidden');
                }

                // 显示失败信息（直接在当前页面上）
                const failureMessage = document.createElement('div');
                failureMessage.className = 'failure-message';
                failureMessage.style.textAlign = 'center';
                failureMessage.style.padding = '30px';
                failureMessage.style.margin = '30px auto';
                failureMessage.style.maxWidth = '600px';
                failureMessage.style.backgroundColor = '#f8f9fa';
                failureMessage.style.borderRadius = '8px';
                failureMessage.style.boxShadow = '0 0 10px rgba(0,0,0,0.1)';
                failureMessage.style.animation = 'fadeIn 0.5s';
                failureMessage.innerHTML = `
                    <style>
                        @keyframes fadeIn {
                            from { opacity: 0; transform: translateY(-20px); }
                            to { opacity: 1; transform: translateY(0); }
                        }
                    </style>
                    <div style="font-size: 70px; color: #dc3545; margin-bottom: 20px;">✗</div>
                    <h2 style="color: #333; margin-bottom: 20px; font-size: 24px;">评价提交失败</h2>
                    <p style="color: #666; margin-bottom: 20px; font-size: 16px;">抱歉，您的评价提交失败。请稍后再试。</p>
                    <p style="color: #666; margin-bottom: 20px; font-size: 16px;">如需再次评价，请重新扫描二维码。</p>
                `;

                // 添加到页面
                const mainContent = document.querySelector('.main-content');
                if (mainContent) {
                    mainContent.appendChild(failureMessage);
                } else {
                    document.body.appendChild(failureMessage);
                }

                // 滚动到失败信息
                failureMessage.scrollIntoView({ behavior: 'smooth' });

                // 防止用户通过返回按钮回到评价页面
                // 使用history.pushState修改历史记录
                const randomState = Math.random().toString(36).substring(2, 15);
                history.pushState({ state: randomState }, document.title, window.location.href);

                // 监听popstate事件，当用户点击返回按钮时触发
                window.addEventListener('popstate', function(event) {
                    // 再次修改历史记录，防止用户返回
                    history.pushState({ state: randomState }, document.title, window.location.href);

                    // 显示提示信息
                    alert('评价提交失败，如需再次评价，请重新扫描二维码。');
                });
            } finally {
                // 隐藏加载状态
                if (elements.loadingIndicator) {
                    elements.loadingIndicator.classList.add('hidden');
                }
            }
        },

        // 添加向后兼容的initPage函数
        initPage: function() {
            console.log('调用了兼容函数 window.initPage，将执行 app.init()');
            app.init();
        }
    };

    // 导出为全局对象
    window.app = app;

    // 为其他模块提供加载/错误处理函数
    window.showLoading = app.showLoading.bind(app);
    window.hideLoading = app.hideLoading.bind(app);
    window.showError = app.showError.bind(app);
    window.resetLoading = app.resetLoading.bind(app);

    // 添加向后兼容的initPage函数
    window.initPage = function() {
        console.log('调用了兼容函数 window.initPage，将执行 app.init()');
        app.init();
    };
})();