#!/usr/bin/env python
"""
演示固定长度特性
"""
import os
import sys
import django

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from qrmanager.security import encrypt_qr_param, decrypt_qr_param
import uuid

def demo_fixed_length():
    """演示所有UUID加密后都是固定长度"""
    print("=" * 100)
    print("🔒 固定长度加密演示 - 证明所有UUID加密后都是56字符")
    print("=" * 100)
    
    # 各种不同类型的UUID
    test_cases = [
        ("全零UUID", "00000000-0000-0000-0000-000000000000"),
        ("全F UUID", "ffffffff-ffff-ffff-ffff-ffffffffffff"),
        ("数字UUID", "12345678-1234-5678-9012-123456789012"),
        ("混合UUID", "a1b2c3d4-e5f6-7890-abcd-ef1234567890"),
        ("随机UUID1", str(uuid.uuid4())),
        ("随机UUID2", str(uuid.uuid4())),
        ("随机UUID3", str(uuid.uuid4())),
    ]
    
    print(f"{'序号':<4} {'类型':<10} {'原始UUID':<38} {'加密长度':<8} {'加密结果前20字符':<22}")
    print("-" * 100)
    
    all_lengths = []
    
    for i, (desc, test_uuid) in enumerate(test_cases, 1):
        try:
            encrypted = encrypt_qr_param(test_uuid)
            all_lengths.append(len(encrypted))
            
            print(f"{i:<4} {desc:<10} {test_uuid:<38} {len(encrypted):<8} {encrypted[:20]:<22}")
            
        except Exception as e:
            print(f"{i:<4} {desc:<10} {test_uuid:<38} {'失败':<8} {str(e)[:20]:<22}")
    
    print("-" * 100)
    print(f"统计结果:")
    print(f"  测试数量: {len(all_lengths)}")
    print(f"  最小长度: {min(all_lengths) if all_lengths else 0}")
    print(f"  最大长度: {max(all_lengths) if all_lengths else 0}")
    print(f"  长度一致: {'✅ 是' if len(set(all_lengths)) == 1 else '❌ 否'}")
    print(f"  固定长度: {all_lengths[0] if all_lengths else 0} 字符")

def demo_data_structure_parsing():
    """演示数据结构解析过程"""
    print("\n" + "=" * 100)
    print("🔍 数据结构解析演示 - 解释如何从加密字符串解密")
    print("=" * 100)
    
    test_uuid = "12345678-1234-5678-9012-123456789012"
    print(f"演示UUID: {test_uuid}")
    print()
    
    try:
        # 加密
        encrypted = encrypt_qr_param(test_uuid)
        print(f"1. 加密结果: {encrypted}")
        print(f"   长度: {len(encrypted)} 字符")
        print()
        
        # 手动解析过程
        import base64
        
        # Base64解码
        padding_needed = len(encrypted) % 4
        if padding_needed != 0:
            padded = encrypted + '=' * (4 - padding_needed)
        else:
            padded = encrypted
        
        decoded = base64.b64decode(padded).decode()
        print(f"2. Base64解码: {decoded}")
        print(f"   长度: {len(decoded)} 字符")
        print()
        
        # 解析结构
        print("3. 解析固定结构:")
        print("   ┌────────────────────────────────────────────────┐")
        print("   │  位置   │  长度  │  内容  │      值            │")
        print("   ├────────────────────────────────────────────────┤")
        
        salt = decoded[:4]
        signature = decoded[4:10]
        uuid_part = decoded[10:42]
        
        print(f"   │  0-3    │   4    │  盐值  │  {salt:<16}  │")
        print(f"   │  4-9    │   6    │  签名  │  {signature:<16}  │")
        print(f"   │ 10-41   │  32    │ UUID   │  {uuid_part[:16]:<16}  │")
        print(f"   │         │        │        │  {uuid_part[16:]:<16}  │")
        print("   └────────────────────────────────────────────────┘")
        print()
        
        # 重构UUID
        reconstructed = f"{uuid_part[:8]}-{uuid_part[8:12]}-{uuid_part[12:16]}-{uuid_part[16:20]}-{uuid_part[20:32]}"
        print(f"4. 重构UUID: {reconstructed}")
        print(f"   原始UUID: {test_uuid}")
        print(f"   匹配结果: {'✅ 完全匹配' if reconstructed == test_uuid else '❌ 不匹配'}")
        print()
        
        # 验证解密函数
        result = decrypt_qr_param(encrypted)
        print(f"5. 官方解密验证:")
        print(f"   解密UUID: {result['uuid']}")
        print(f"   验证结果: {'✅ 成功' if result['uuid'] == test_uuid else '❌ 失败'}")
        
    except Exception as e:
        print(f"演示失败: {e}")

def demo_why_fixed_length():
    """解释为什么是固定长度"""
    print("\n" + "=" * 100)
    print("💡 为什么加密后是固定长度？")
    print("=" * 100)
    
    print("原因分析:")
    print()
    print("1. 📏 输入标准化:")
    print("   - 只接受标准UUID格式 (36字符)")
    print("   - 去除连字符后变成32字符")
    print("   - 所有UUID都是32个十六进制字符")
    print()
    
    print("2. 🔧 固定组件:")
    print("   - 盐值: 固定4字符 (UUID前4位)")
    print("   - 签名: 固定6字符 (MD5前6位)")
    print("   - UUID: 固定32字符 (标准化后)")
    print("   - 总计: 4 + 6 + 32 = 42字符")
    print()
    
    print("3. 📦 编码固定:")
    print("   - Base64编码42字符数据")
    print("   - 42字符 → 56字符 (固定比例)")
    print("   - 去除填充字符保持长度一致")
    print()
    
    print("4. ✅ 结果保证:")
    print("   - 任何标准UUID → 固定56字符加密字符串")
    print("   - 100%可预测的长度")
    print("   - 医院级稳定性保证")

if __name__ == "__main__":
    demo_fixed_length()
    demo_data_structure_parsing()
    demo_why_fixed_length()
    
    print("\n" + "=" * 100)
    print("📋 回答您的问题:")
    print("1. ✅ 加密后的固定长度都是一样的 - 都是56字符")
    print("2. ✅ 通过解析固定的数据结构来解密:")
    print("   - Base64解码得到42字符数据")
    print("   - 按固定位置解析: 盐值(4) + 签名(6) + UUID(32)")
    print("   - 重构UUID格式并验证签名")
    print("3. ✅ 这种设计确保了绝对的稳定性和可靠性")
    print("=" * 100)
