# 医院服务评价系统 - API接口规范文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-27
- **文档类型**: API接口规范
- **目标读者**: 前端开发工程师、第三方集成开发者

## 1. API概述

### 1.1 基础信息
- **Base URL**: `https://your-domain.com`
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8
- **时间格式**: ISO 8601 (YYYY-MM-DDTHH:mm:ss.sssZ)

### 1.2 认证机制
系统提供三层API认证机制：

| API类型 | 认证方式 | 使用场景 |
|---------|----------|----------|
| 公开API | 无需认证 | 患者评价提交 |
| RESTful API | API密钥 | 第三方系统集成 |
| 管理API | Session认证 | 管理后台 |

### 1.3 响应格式
所有API响应均采用统一格式：

```json
{
    "status": "success|error",
    "message": "响应消息",
    "data": {},
    "errors": {}
}
```

### 1.4 HTTP状态码
| 状态码 | 说明 |
|--------|------|
| 200 | 请求成功 |
| 400 | 请求参数错误 |
| 401 | 未授权 |
| 403 | 权限不足 |
| 404 | 资源不存在 |
| 429 | 请求过于频繁 |
| 500 | 服务器内部错误 |

## 2. 公开API (无需认证)

### 2.1 二维码验证

#### 接口信息
- **URL**: `/api/v1/public/qrcode/verify/`
- **方法**: POST
- **认证**: 无需认证
- **限速**: 100次/分钟/IP

#### 请求参数
```json
{
    "qr_param": "加密的二维码参数"
}
```

#### 请求示例
```bash
curl -X POST https://your-domain.com/api/v1/public/qrcode/verify/ \
  -H "Content-Type: application/json" \
  -d '{
    "qr_param": "gAAAAABhkJ8x..."
  }'
```

#### 响应示例
```json
{
    "status": "success",
    "message": "验证成功",
    "data": {
        "temp_token": "abc123...",
        "expires_at": "2025-01-27T15:30:00.000Z",
        "qrcode": {},
        "bed": {
            "id": 1,
            "number": "101",
            "area": "A区"
        },
        "department": {
            "id": 1,
            "name": "内科",
            "code": "NK"
        },
        "staff_types": [
            {
                "id": 1,
                "name": "医生",
                "code": "doctor"
            },
            {
                "id": 2,
                "name": "护士",
                "code": "nurse"
            }
        ],
        "staff": [
            {
                "id": 1,
                "name": "张医生",
                "title": "主治医师",
                "staff_type": 1
            },
            {
                "id": 2,
                "name": "李护士",
                "title": "护师",
                "staff_type": 2
            }
        ]
    }
}
```

#### 错误响应
```json
{
    "status": "error",
    "message": "二维码验证失败",
    "errors": {
        "qr_param": "参数格式无效"
    }
}
```

### 2.2 评价提交

#### 接口信息
- **URL**: `/api/v1/public/submit-evaluation/`
- **方法**: POST
- **认证**: 无需认证
- **限速**: 10次/小时/设备指纹

#### 请求参数
```json
{
    "qr_param": "加密的二维码参数",
    "comment": "评价内容",
    "hospital_number": "住院号(可选)",
    "phone_number": "联系电话(可选)",
    "staff_evaluations": [
        {
            "staff_id": 1,
            "is_satisfied": true
        },
        {
            "staff_id": 2,
            "is_satisfied": false
        }
    ]
}
```

#### 业务规则
- 最多选择3个满意的工作人员
- 最多选择3个不满意的工作人员
- 评价内容不能为空
- 同一设备1小时内只能评价一次

#### 请求示例
```bash
curl -X POST https://your-domain.com/api/v1/public/submit-evaluation/ \
  -H "Content-Type: application/json" \
  -d '{
    "qr_param": "gAAAAABhkJ8x...",
    "comment": "服务很好，医生很专业",
    "hospital_number": "2025010001",
    "phone_number": "13800138000",
    "staff_evaluations": [
        {
            "staff_id": 1,
            "is_satisfied": true
        },
        {
            "staff_id": 2,
            "is_satisfied": true
        }
    ]
  }'
```

#### 响应示例
```json
{
    "status": "success",
    "message": "评价提交成功",
    "data": {
        "evaluation_id": 123,
        "successful_count": 2,
        "failed_count": 0,
        "failed_staff_ids": [],
        "staff_evaluations": [
            {
                "id": 1,
                "name": "张医生",
                "is_satisfied": true,
                "type": "医生"
            },
            {
                "id": 2,
                "name": "李护士",
                "is_satisfied": true,
                "type": "护士"
            }
        ]
    }
}
```

#### 错误响应
```json
{
    "status": "error",
    "message": "满意评价超出限制，最多选择3个",
    "errors": {
        "staff_evaluations": "评价数量超出限制"
    }
}
```

## 3. RESTful API (需要API密钥)

### 3.1 认证方式
在请求头中添加API密钥：
```
Authorization: Token your-api-key-here
```

或在URL参数中添加：
```
?token=your-api-key-here
```

### 3.2 科室管理

#### 3.2.1 获取科室列表
- **URL**: `/api/v1/departments/`
- **方法**: GET

**查询参数**:
- `page`: 页码 (默认: 1)
- `page_size`: 每页数量 (默认: 20)
- `search`: 搜索关键词

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "results": [
            {
                "id": 1,
                "code": "NK",
                "name": "内科",
                "remarks": "内科科室",
                "created_at": "2025-01-01T00:00:00.000Z",
                "updated_at": "2025-01-01T00:00:00.000Z"
            }
        ],
        "count": 10,
        "next": "?page=2",
        "previous": null
    }
}
```

#### 3.2.2 获取科室详情
- **URL**: `/api/v1/departments/{id}/`
- **方法**: GET

#### 3.2.3 创建科室
- **URL**: `/api/v1/departments/`
- **方法**: POST

**请求参数**:
```json
{
    "code": "WK",
    "name": "外科",
    "remarks": "外科科室"
}
```

#### 3.2.4 更新科室
- **URL**: `/api/v1/departments/{id}/`
- **方法**: PUT

#### 3.2.5 删除科室
- **URL**: `/api/v1/departments/{id}/`
- **方法**: DELETE

### 3.3 工作人员管理

#### 3.3.1 获取工作人员列表
- **URL**: `/api/v1/staff/`
- **方法**: GET

**查询参数**:
- `department`: 科室ID筛选
- `staff_type`: 人员类型筛选
- `search`: 搜索工号或姓名

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "results": [
            {
                "id": 1,
                "work_number": "D001",
                "name": "张医生",
                "staff_type_id": 1,
                "staff_type": {
                    "id": 1,
                    "name": "医生"
                },
                "title": "主治医师",
                "department_id": 1,
                "department": {
                    "id": 1,
                    "name": "内科"
                },
                "photo_url": "/media/staff_photos/zhang.jpg"
            }
        ],
        "count": 50,
        "next": "?page=2",
        "previous": null
    }
}
```

### 3.4 床位管理

#### 3.4.1 获取床位列表
- **URL**: `/api/v1/beds/`
- **方法**: GET

**查询参数**:
- `department`: 科室ID筛选
- `is_active`: 状态筛选 (true/false)

### 3.5 二维码管理

#### 3.5.1 获取二维码列表
- **URL**: `/api/v1/qrcodes/`
- **方法**: GET

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "results": [
            {
                "id": 1,
                "name": "101床二维码",
                "description": "内科101床位二维码",
                "code": "550e8400-e29b-41d4-a716-446655440000",
                "bed_id": 1,
                "bed": {
                    "id": 1,
                    "number": "101"
                },
                "qr_image_url": "/qr/image/550e8400-e29b-41d4-a716-446655440000/",
                "evaluation_url": "/q/gAAAAABhkJ8x...",
                "is_active": true,
                "created_at": "2025-01-01T00:00:00.000Z",
                "updated_at": "2025-01-01T00:00:00.000Z"
            }
        ]
    }
}
```

### 3.6 评价管理

#### 3.6.1 获取评价列表
- **URL**: `/api/v1/evaluations/`
- **方法**: GET

**查询参数**:
- `department`: 科室ID筛选
- `sentiment`: 情感倾向筛选 (positive/negative/neutral)
- `start_date`: 开始日期 (YYYY-MM-DD)
- `end_date`: 结束日期 (YYYY-MM-DD)

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "results": [
            {
                "id": 1,
                "is_satisfied": true,
                "comment": "服务很好",
                "hospital_number": "2025010001",
                "phone_number": "138****8000",
                "sentiment": "positive",
                "bed": {
                    "id": 1,
                    "number": "101",
                    "department": {
                        "id": 1,
                        "name": "内科"
                    }
                },
                "satisfied_staff": [
                    {
                        "id": 1,
                        "name": "张医生",
                        "type_name": "医生"
                    }
                ],
                "unsatisfied_staff": [],
                "created_at": "2025-01-27T10:00:00.000Z"
            }
        ],
        "count": 100,
        "next": "?page=2",
        "previous": null
    }
}
```

## 4. 管理API (需要Session认证)

### 4.1 认证方式
需要先登录管理后台获取Session，然后在请求中携带Session Cookie。

### 4.2 统计数据

#### 4.2.1 获取仪表板数据
- **URL**: `/admin/api/dashboard/`
- **方法**: GET

**响应示例**:
```json
{
    "status": "success",
    "data": {
        "total_evaluations": 1000,
        "total_qrcodes": 50,
        "total_staff": 100,
        "satisfaction_rate": 85.5,
        "recent_evaluations": [...],
        "department_stats": [
            {
                "department_name": "内科",
                "evaluation_count": 300,
                "satisfaction_rate": 90.0
            }
        ]
    }
}
```

#### 4.2.2 获取科室统计
- **URL**: `/admin/api/departments/{id}/stats/`
- **方法**: GET

### 4.3 批量操作

#### 4.3.1 批量导出评价
- **URL**: `/admin/api/evaluations/export/`
- **方法**: POST

**请求参数**:
```json
{
    "format": "excel",
    "filters": {
        "department_id": 1,
        "start_date": "2025-01-01",
        "end_date": "2025-01-31"
    }
}
```

#### 4.3.2 批量打印二维码
- **URL**: `/admin/api/qrcodes/batch-print/`
- **方法**: POST

**请求参数**:
```json
{
    "qrcode_ids": [1, 2, 3, 4, 5],
    "template_id": 1
}
```

## 5. 错误处理

### 5.1 错误码定义
| 错误码 | 说明 |
|--------|------|
| 1001 | 参数验证失败 |
| 1002 | 二维码无效 |
| 1003 | 评价数量超限 |
| 1004 | 设备指纹限制 |
| 2001 | API密钥无效 |
| 2002 | 权限不足 |
| 2003 | 速率限制 |
| 5001 | 服务器内部错误 |

### 5.2 错误响应格式
```json
{
    "status": "error",
    "message": "错误描述",
    "error_code": 1001,
    "errors": {
        "field_name": ["具体错误信息"]
    },
    "timestamp": "2025-01-27T10:00:00.000Z"
}
```

## 6. 速率限制

### 6.1 限制规则
| API类型 | 限制规则 |
|---------|----------|
| 公开API | 100次/分钟/IP |
| 评价提交 | 1次/小时/设备指纹 |
| RESTful API | 1000次/小时/API密钥 |
| 管理API | 无限制 |

### 6.2 限制响应头
```
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 99
X-RateLimit-Reset: 1643270400
```

## 7. 数据模型

### 7.1 科室 (Department)
```json
{
    "id": 1,
    "code": "NK",
    "name": "内科",
    "remarks": "内科科室",
    "created_at": "2025-01-01T00:00:00.000Z",
    "updated_at": "2025-01-01T00:00:00.000Z"
}
```

### 7.2 工作人员 (Staff)
```json
{
    "id": 1,
    "work_number": "D001",
    "name": "张医生",
    "staff_type_id": 1,
    "title": "主治医师",
    "department_id": 1,
    "photo_url": "/media/staff_photos/zhang.jpg",
    "is_active": true
}
```

### 7.3 床位 (Bed)
```json
{
    "id": 1,
    "number": "101",
    "department_id": 1,
    "area": "A区",
    "staff_id": 1,
    "is_active": true
}
```

### 7.4 二维码 (QRCode)
```json
{
    "id": 1,
    "code": "550e8400-e29b-41d4-a716-446655440000",
    "name": "101床二维码",
    "description": "内科101床位二维码",
    "bed_id": 1,
    "qr_image_url": "/qr/image/550e8400-e29b-41d4-a716-446655440000/",
    "evaluation_url": "/q/gAAAAABhkJ8x...",
    "is_active": true,
    "created_at": "2025-01-01T00:00:00.000Z"
}
```

### 7.5 评价 (Evaluation)
```json
{
    "id": 1,
    "qr_code_id": 1,
    "bed_id": 1,
    "is_satisfied": true,
    "comment": "服务很好",
    "hospital_number": "2025010001",
    "phone_number": "138****8000",
    "sentiment": "positive",
    "satisfied_staff": [
        {
            "id": 1,
            "name": "张医生",
            "type_name": "医生"
        }
    ],
    "unsatisfied_staff": [],
    "created_at": "2025-01-27T10:00:00.000Z"
}
```

## 8. SDK和示例

### 8.1 JavaScript SDK示例
```javascript
class HospitalAPI {
    constructor(baseURL, apiKey = null) {
        this.baseURL = baseURL;
        this.apiKey = apiKey;
    }
    
    async verifyQRCode(qrParam) {
        const response = await fetch(`${this.baseURL}/api/v1/public/qrcode/verify/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ qr_param: qrParam })
        });
        return response.json();
    }
    
    async submitEvaluation(data) {
        const response = await fetch(`${this.baseURL}/api/v1/public/submit-evaluation/`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        return response.json();
    }
    
    async getDepartments() {
        const response = await fetch(`${this.baseURL}/api/v1/departments/`, {
            headers: {
                'Authorization': `Token ${this.apiKey}`
            }
        });
        return response.json();
    }
}

// 使用示例
const api = new HospitalAPI('https://your-domain.com', 'your-api-key');

// 验证二维码
const result = await api.verifyQRCode('gAAAAABhkJ8x...');

// 提交评价
const evaluation = await api.submitEvaluation({
    qr_param: 'gAAAAABhkJ8x...',
    comment: '服务很好',
    staff_evaluations: [
        { staff_id: 1, is_satisfied: true }
    ]
});
```

### 8.2 Python SDK示例
```python
import requests

class HospitalAPI:
    def __init__(self, base_url, api_key=None):
        self.base_url = base_url
        self.api_key = api_key
        self.session = requests.Session()
        if api_key:
            self.session.headers.update({'Authorization': f'Token {api_key}'})
    
    def verify_qrcode(self, qr_param):
        url = f"{self.base_url}/api/v1/public/qrcode/verify/"
        data = {"qr_param": qr_param}
        response = self.session.post(url, json=data)
        return response.json()
    
    def submit_evaluation(self, data):
        url = f"{self.base_url}/api/v1/public/submit-evaluation/"
        response = self.session.post(url, json=data)
        return response.json()
    
    def get_departments(self):
        url = f"{self.base_url}/api/v1/departments/"
        response = self.session.get(url)
        return response.json()

# 使用示例
api = HospitalAPI('https://your-domain.com', 'your-api-key')

# 获取科室列表
departments = api.get_departments()
print(departments)
```

---

**文档维护**: API开发组  
**审核状态**: 已审核  
**更新频率**: API变更时同步更新