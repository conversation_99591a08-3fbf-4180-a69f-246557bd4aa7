# 统一二维码处理解决方案

## 背景与问题

在我们的QR码管理系统中，存在多个功能需要处理二维码的定位与渲染：

1. 模板预览页面 - 使用CSS位置和transform进行偏移
2. 打印功能 - 需要精确的二维码位置
3. 图片生成功能 - 使用Canvas绘制二维码

这些不同实现导致了二维码在不同场景下的位置不一致，特别是当我们使用中心点定位时，预览、打印和图片生成的结果可能不一致。

## 统一解决方案

我们创建了统一的二维码处理模块，确保所有场景下的二维码位置一致。

### 核心原则

1. **中心点定位** - 所有坐标均指定二维码的中心点位置
2. **统一转换** - 当需要左上角坐标时（如Canvas绘制），使用一致的转换方法
3. **模块化设计** - 所有二维码处理逻辑统一管理

### 文件结构

- `static/qrmanager/js/qrcode-unified.js` - 核心处理模块
- 各模板文件中使用统一的引用和调用方式

## 使用方法

### 1. 引入模块

在需要处理二维码的模板中添加：

```html
<script src="{% static 'qrmanager/js/qrcode-unified.js' %}"></script>
```

### 2. 预览元素定位

使用`QRCodeUnified.updatePreview`方法更新预览容器中的二维码位置：

```js
QRCodeUnified.updatePreview({
    container: container,    // 容器元素
    qrElement: qrPreview,    // 二维码元素
    printWidth: width,       // 打印宽度(mm)
    printHeight: height,     // 打印高度(mm)
    qrX: centerX,            // 二维码X中心位置(mm)
    qrY: centerY,            // 二维码Y中心位置(mm)
    qrSize: size,            // 二维码尺寸(mm)
    containerMaxWidth: 500,  // 容器最大宽度(px)
    containerMaxHeight: 707  // 容器最大高度(px)
});
```

### 3. Canvas绘制

使用`QRCodeUnified.drawQRCodeOnCanvas`方法在Canvas上绘制二维码：

```js
QRCodeUnified.drawQRCodeOnCanvas(
    ctx,              // Canvas上下文
    qrImg,            // 二维码图像
    centerX,          // 二维码X中心位置
    centerY,          // 二维码Y中心位置
    size              // 二维码尺寸
);
```

### 4. 打印功能

使用`QRCodeUnified.print`方法进行打印：

```js
QRCodeUnified.print({
    width: 100,                 // 打印宽度(mm)
    height: 100,                // 打印高度(mm)
    qrX: 50,                    // 二维码X中心位置(mm)
    qrY: 50,                    // 二维码Y中心位置(mm)
    qrSize: 30,                 // 二维码尺寸(mm)
    backgroundImage: 'url',     // 背景图片URL
    qrImage: 'data:image/...'   // 二维码图片数据
}, callback);                   // 打印完成回调
```

### 5. 图像生成

使用`QRCodeUnified.generateImage`方法生成带二维码的图像：

```js
QRCodeUnified.generateImage({
    width: 800,                 // 图像宽度(px)
    height: 600,                // 图像高度(px)
    qrX: 400,                   // 二维码X中心位置(px)
    qrY: 300,                   // 二维码Y中心位置(px)
    qrSize: 150,                // 二维码尺寸(px)
    backgroundColor: '#fff',    // 背景颜色
    backgroundImage: 'url',     // 背景图片URL
    qrImage: 'data:image/...'   // 二维码图片数据
}).then(imageData => {
    // 使用生成的图像数据
});
```

## 核心实现

### 1. 中心点到左上角的转换

```js
function centerToTopLeft(centerX, centerY, size) {
    return {
        left: centerX - (size / 2),
        top: centerY - (size / 2)
    };
}
```

### 2. 设置HTML元素的二维码位置

```js
function setElementPosition(element, x, y, size) {
    element.style.width = `${size}px`;
    element.style.height = `${size}px`;
    element.style.left = `${x}px`;
    element.style.top = `${y}px`;
    element.style.transform = 'translate(-50%, -50%)';
    element.style.position = 'absolute';
}
```

### 3. Canvas绘制二维码

```js
function drawQRCodeOnCanvas(ctx, qrImage, x, y, size) {
    const position = centerToTopLeft(x, y, size);
    ctx.drawImage(qrImage, position.left, position.top, size, size);
}
```

## 修改历史

1. 创建统一模块 `qrcode-unified.js`
2. 更新打印模板预览页面
3. 更新打印处理逻辑
4. 更新二维码列表页面Canvas绘制逻辑

## 注意事项

1. 所有坐标均为中心点坐标
2. 在Canvas绘制时需使用转换后的左上角坐标
3. 打印和预览使用CSS transform确保一致性
4. 添加新功能时，应遵循统一模块的使用方式 