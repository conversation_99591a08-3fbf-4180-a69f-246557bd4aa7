# 医院服务评价系统

## 安装

1. 克隆仓库
2. 安装依赖
   ```
   pip install -r requirements.txt
   ```
3. 创建.env文件（参考.env.example）
4. 运行迁移
   ```
   python manage.py migrate
   ```
5. 收集静态文件
   ```
   python manage.py collectstatic
   ```
6. 运行服务器
   ```
   python manage.py runserver
   ```

## 安全配置

系统已实施多层次的安全防护措施，包括：

1. 敏感信息保护
   - 使用环境变量存储敏感信息
   - 加密二维码参数
   - 遮蔽日志中的敏感数据

2. HTTPS配置
   - 启用HSTS
   - 设置安全Cookie
   - 强制HTTPS连接

3. 会话安全
   - 会话过期时间设置为8小时
   - 浏览器关闭时会话过期
   - 会话Cookie设置为HttpOnly和Secure

4. 内容安全策略
   - 限制资源加载来源
   - 防止XSS攻击
   - 防止点击劫持

5. 速率限制
   - 全局请求速率限制
   - 基于IP的速率限制
   - 基于二维码参数的速率限制

6. 日志记录
   - 记录所有重要操作
   - 记录API请求和响应
   - 记录错误和异常

## 环境变量

系统使用环境变量来配置敏感信息和运行环境。请创建.env文件，包含以下变量：

```
# Django安全设置
DJANGO_SECRET_KEY=your_secret_key
ENCRYPTION_KEY=your_encryption_key

# 是否启用调试模式
DEBUG=False

# 允许的主机
ALLOWED_HOSTS=127.0.0.1,localhost

# 站点域名和前端URL
SITE_DOMAIN=http://localhost
FRONTEND_URL=http://localhost

# 数据库配置
DATABASE_URL=sqlite:///db.sqlite3

# 会话设置
SESSION_COOKIE_AGE=28800
SESSION_EXPIRE_AT_BROWSER_CLOSE=True
SESSION_COOKIE_SECURE=True
CSRF_COOKIE_SECURE=True

# CORS设置
CORS_ALLOWED_ORIGINS=http://localhost,http://127.0.0.1
CORS_ALLOW_CREDENTIALS=True
CORS_ALLOW_ALL_ORIGINS=False

# 安全设置
SECURE_SSL_REDIRECT=False
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True
SECURE_CONTENT_TYPE_NOSNIFF=True
SECURE_BROWSER_XSS_FILTER=True
X_FRAME_OPTIONS=DENY
```

注意：在生产环境中，请将`SECURE_SSL_REDIRECT`设置为`True`，并确保使用HTTPS。