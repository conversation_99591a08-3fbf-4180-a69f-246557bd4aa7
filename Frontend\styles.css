/*
 * 主样式文件 - 医院服务评价系统
 * 完全本地化，无外部依赖
 * 针对移动端优化 - 医疗主题增强版
 */

/* 基础重置样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent; /* 移除移动端点击高亮 */
}

:root {
    /* 医疗主题色彩方案 */
    --primary-blue: #0070e4; /* 医疗蓝 - 主色调 */
    --primary-light: #e1f5fe; /* 浅蓝 - 背景色 */
    --primary-dark: #01579b; /* 深蓝 - 重点强调 */
    --accent-red: #e53935; /* 医疗红 - 强调色 */
    --accent-green: #66bb6a; /* 医疗绿 - 成功状态 */
    --accent-yellow: #ffca28; /* 医疗黄 - 警告状态 */
    --neutral-light: #f5f9ff; /* 浅色背景 */
    --neutral-medium: #e0e6f0; /* 中性色 - 边框等 */
    --neutral-dark: #37474f; /* 深色文本 */
    --white: #ffffff;
    --shadow-light: 0 2px 10px rgba(0, 112, 228, 0.1);
    --shadow-medium: 0 4px 20px rgba(0, 112, 228, 0.15);
}

body {
    font-family: " ", "Hiagino  GB", "  Hei", "Segoe UI", Arial, -;
    line-height: 1.6;
    color: var(--neutral-dark);
    background-color: var(--neutral-light);
    font-size: 16px;
    -webkit-text-size-adjust: 100%; /* 防止iOS中改变字体大小 */
    touch-action: manipulation; /* 提高移动端触摸响应速度 */
}

/* 容器样式 */
.app-container {
    max-width: 600px;
    margin: 0 auto;
    padding: 20px 15px;
    background-color: var(--white);
    min-height: 100vh;
    position: relative;
    box-shadow: var(--shadow-medium);
    overflow: hidden;
}

/* 医疗装饰元素 */
.bubbles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.bubble {
    position: absolute;
    border-radius: 50%;
    background: rgba(0, 112, 228, 0.05);
    animation: float 20s infinite ease-in-out;
}

.bubble:nth-child(1) {
    width: 80px;
    height: 80px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.bubble:nth-child(2) {
    width: 40px;
    height: 40px;
    top: 30%;
    right: 15%;
    animation-delay: 1s;
}

.bubble:nth-child(3) {
    width: 60px;
    height: 60px;
    bottom: 20%;
    right: 10%;
    animation-delay: 2s;
}

.bubble:nth-child(4) {
    width: 50px;
    height: 50px;
    bottom: 40%;
    left: 15%;
    animation-delay: 3s;
}

.bubble:nth-child(5) {
    width: 70px;
    height: 70px;
    top: 60%;
    left: 5%;
    animation-delay: 4s;
}

.bubble:nth-child(6) {
    width: 45px;
    height: 45px;
    top: 50%;
    right: 5%;
    animation-delay: 5s;
}

/* 气泡漂浮动画 */
@keyframes float {
    0% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.5;
    }
    50% {
        transform: translateY(-20px) rotate(5deg);
        opacity: 0.8;
    }
    100% {
        transform: translateY(0) rotate(0deg);
        opacity: 0.5;
    }
}

/* 医疗装饰图形 */
.medical-decoration {
    position: absolute;
    bottom: -60px;
    right: -60px;
    width: 240px;
    height: 240px;
    border-radius: 50%;
    background: linear-gradient(135deg, rgba(0, 112, 228, 0.05) 0%, rgba(0, 112, 228, 0.01) 100%);
    z-index: -1;
}

.medical-decoration::before {
    content: '';
    position: absolute;
    top: 60px;
    left: 60px;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    border: 2px solid rgba(0, 112, 228, 0.1);
}

.medical-decoration::after {
    content: '';
    position: absolute;
    top: 80px;
    left: 80px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(0, 112, 228, 0.05);
}

/* 心跳动画 */
.heartbeat-icon {
    animation: heartbeat 1.5s ease infinite;
}

@keyframes heartbeat {
    0% { transform: scale(1); }
    14% { transform: scale(1.15); }
    28% { transform: scale(1); }
    42% { transform: scale(1.15); }
    70% { transform: scale(1); }
}

/* 脉搏动画 */
.pulse-animation {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(0, 112, 228, 0.3); }
    70% { transform: scale(1); box-shadow: 0 0 0 10px rgba(0, 112, 228, 0); }
    100% { transform: scale(0.95); box-shadow: 0 0 0 0 rgba(0, 112, 228, 0); }
}

/* 页头样式 */
.header {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    margin-bottom: 25px;
    padding-bottom: 15px;
    position: relative;
    border-bottom: 1px solid var(--neutral-medium);
}

.header h1 {
    font-size: 24px;
    margin-bottom: 8px;
    color: var(--primary-dark);
    font-weight: 600;
}

.header p {
    font-size: 16px;
    color: rgba(55, 71, 79, 0.8);
    margin-bottom: 5px;
    max-width: 90%;
}

.hospital-logo {
    margin-bottom: 15px;
    height: 60px;
    width: auto;
}

/* 主内容区样式 */
.main-content {
    position: relative;
    z-index: 1;
}

.section {
    background-color: var(--white);
    border-radius: 12px;
    margin-bottom: 20px;
    padding: 20px;
    box-shadow: var(--shadow-light);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
    position: relative;
    overflow: hidden;
}

.section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--primary-blue);
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
}

.section h2 {
    font-size: 20px;
    margin-bottom: 15px;
    color: var(--primary-dark);
    font-weight: 600;
    display: flex;
    align-items: center;
}

.section h2 [class^="ai-"] {
    margin-right: 10px;
    color: var(--primary-blue);
}

.section p {
    margin-bottom: 15px;
    color: rgba(55, 71, 79, 0.9);
}

/* 表单样式 */
.evaluation-form {
    display: flex;
    flex-direction: column;
    gap: 22px;
}

.form-group {
    margin-bottom: 18px;
    position: relative;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: var(--neutral-dark);
    font-size: 16px;
}

.form-group input,
.form-group textarea,
.form-group select {
    width: 100%;
    padding: 14px 16px;
    border: 1px solid var(--neutral-medium);
    border-radius: 10px;
    background-color: var(--white);
    font-size: 16px;
    color: var(--neutral-dark);
    transition: all 0.3s ease;
    box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
}

.form-group input:focus,
.form-group textarea:focus,
.form-group select:focus {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(0, 112, 228, 0.15);
    outline: none;
}

.form-group select {
    -webkit-appearance: none;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="%230070e4" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="6 9 12 15 18 9"></polyline></svg>');
    background-repeat: no-repeat;
    background-position: right 16px center;
    background-size: 16px;
    padding-right: 40px;
}

.form-group textarea {
    min-height: 120px;
    resize: vertical;
}

/* 错误提示 */
.error-message {
    color: var(--accent-red);
    font-size: 14px;
    margin-top: 5px;
    display: none;
    border-radius: 6px;
    transition: all 0.3s ease;
    position: relative;
}

.form-group.has-error input,
.form-group.has-error textarea,
.form-group.has-error select {
    border-color: var(--accent-red);
    background-color: rgba(229, 57, 53, 0.03);
    animation: error-pulse 1s ease;
}

@keyframes error-pulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(229, 57, 53, 0);
    }
    50% {
        box-shadow: 0 0 0 5px rgba(229, 57, 53, 0.1);
    }
}

.form-group.has-error .error-message {
    display: block;
    padding-left: 20px;
    animation: slide-in 0.3s ease forwards;
}

.form-group.has-error .error-message::before {
    content: '';
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 14px;
    height: 14px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%23e53935" stroke="%23e53935" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"/><line x1="12" y1="8" x2="12" y2="12"/><line x1="12" y1="16" x2="12.01" y2="16"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
}

@keyframes slide-in {
    0% {
        opacity: 0;
        transform: translateY(-10px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表单验证成功状态 */
.form-group.is-valid input,
.form-group.is-valid textarea,
.form-group.is-valid select {
    border-color: var(--accent-green);
    background-color: rgba(102, 187, 106, 0.03);
}

.form-group.is-valid::after {
    content: '';
    position: absolute;
    right: 12px;
    top: calc(50% + 10px);
    width: 18px;
    height: 18px;
    background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2366bb6a" stroke="%2366bb6a" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"/><polyline points="22 4 12 14.01 9 11.01"/></svg>');
    background-size: contain;
    background-repeat: no-repeat;
    opacity: 0;
    transform: scale(0);
    animation: pop-in 0.3s ease forwards;
}

@keyframes pop-in {
    0% {
        opacity: 0;
        transform: scale(0);
    }
    70% {
        transform: scale(1.2);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

/* 评分模块样式 */
.rating-section {
    background-color: var(--white);
    padding: 20px;
    border-radius: 12px;
    box-shadow: var(--shadow-light);
    margin-bottom: 25px;
    position: relative;
    overflow: hidden;
}

.rating-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background-color: var(--primary-blue);
    border-top-left-radius: 12px;
    border-bottom-left-radius: 12px;
}

/* 星级评分样式增强 - 现代设计 */
.rating-container {
    margin-top: 15px;
    position: relative;
    padding: 10px;
    background: linear-gradient(to right, rgba(255, 255, 255, 0.5), rgba(240, 249, 255, 0.8));
    border-radius: 15px;
    box-shadow: 0 3px 15px rgba(0, 112, 228, 0.05);
}

.rating-value {
    text-align: center;
    font-size: 28px;
    font-weight: 600;
    margin: 15px 0;
    color: var(--primary-blue);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
    background: linear-gradient(135deg, var(--primary-blue), #4FC3F7);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    animation: pulse-rating 2s infinite ease-in-out;
}

@keyframes pulse-rating {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

.rating-max {
    font-size: 16px;
    color: #999;
    font-weight: normal;
    opacity: 0.8;
}

.rating-labels {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
    font-size: 14px;
    color: #666;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 10px;
    padding: 5px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.rating-label {
    flex: 1;
    text-align: center;
    padding: 8px 5px;
    border-radius: 8px;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    cursor: pointer;
    position: relative;
    overflow: hidden;
}

.rating-label:hover {
    background-color: rgba(0, 112, 228, 0.1);
    color: var(--primary-blue);
    transform: translateY(-2px);
}

.rating-label.active {
    background-color: rgba(0, 112, 228, 0.15);
    color: var(--primary-blue);
    font-weight: 500;
    box-shadow: 0 2px 8px rgba(0, 112, 228, 0.2);
    transform: translateY(-3px) scale(1.05);
}

.rating-label.active::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 0;
    border-left: 5px solid transparent;
    border-right: 5px solid transparent;
    border-bottom: 5px solid var(--primary-blue);
    transform: translateX(-50%);
}
.star-rating {
    position: relative;
    margin: 20px 0;
}

.star-rating-display {
    display: flex;
    justify-content: center;
    margin-bottom: 15px;
    perspective: 100px;
}

.star {
    font-size: 0; /* 隐藏文本内容，使用伪元素创建星星 */
    cursor: pointer;
    margin: 0 8px;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    position: relative;
    display: inline-block;
    width: 36px;
    height: 36px;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1));
}

.star::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e0e0e0' stroke='%23bdbdbd' stroke-width='1'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
    background-size: contain;
    background-repeat: no-repeat;
    transition: all 0.3s ease;
    transform-origin: center;
}

.star:hover {
    transform: translateY(-4px) scale(1.15);
}

.star:hover::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFEB3B' stroke='%23FF9800' stroke-width='1'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
    filter: drop-shadow(0 0 8px rgba(255, 235, 59, 0.6));
}

.star.active::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FFD700' stroke='%23FF8F00' stroke-width='1.5'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E");
    filter: drop-shadow(0 0 12px rgba(255, 215, 0, 0.8)) drop-shadow(0 0 4px rgba(255, 143, 0, 0.6));
    animation: star-activate 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    transform: scale(1.1);
}

/* 立即取消激活状态 - 禁用所有动画和过渡，提高优先级 */
.star.instant-deactivate.instant-deactivate {
    transition: none !important;
    animation: none !important;
    transform: none !important;
}

.star.instant-deactivate.instant-deactivate::before {
    transition: none !important;
    animation: none !important;
    transform: none !important;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e0e0e0' stroke='%23bdbdbd' stroke-width='1'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E") !important;
    filter: none !important;
}

.star.instant-deactivate.instant-deactivate::after {
    display: none !important;
}

/* 确保移除active类时立即生效 */
.star:not(.active)::before {
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23e0e0e0' stroke='%23bdbdbd' stroke-width='1'%3E%3Cpath d='M12 17.27L18.18 21l-1.64-7.03L22 9.24l-7.19-.61L12 2 9.19 8.63 2 9.24l5.46 4.73L5.82 21z'/%3E%3C/svg%3E") !important;
    filter: drop-shadow(0 2px 3px rgba(0, 0, 0, 0.1)) !important;
    animation: none !important;
    transform: none !important;
}

.star:not(.active)::after {
    display: none !important;
}

@keyframes star-activate {
    0% { transform: scale(1) rotate(0deg); }
    25% { transform: scale(1.3) rotate(-5deg); }
    50% { transform: scale(1.2) rotate(5deg); }
    75% { transform: scale(1.3) rotate(-3deg); }
    100% { transform: scale(1) rotate(0deg); }
}

/* 星星选中时的波纹效果 */
.star.active::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 5px;
    height: 5px;
    background: rgba(255, 193, 7, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    animation: star-ripple 1s cubic-bezier(0, 0.55, 0.45, 1) forwards;
    z-index: -1;
    opacity: 0;
}

@keyframes star-ripple {
    0% {
        width: 5px;
        height: 5px;
        opacity: 1;
    }
    100% {
        width: 50px;
        height: 50px;
        opacity: 0;
    }
}

.rating-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    margin-top: 15px;
}

.rating-option {
    flex: 1 0 calc(33.333% - 10px);
    min-width: 100px;
    height: 110px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    background-color: var(--white);
    border-radius: 12px;
    padding: 15px 10px;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 2px solid var(--neutral-medium);
    position: relative;
    overflow: hidden;
}

.rating-option:hover {
    border-color: rgba(0, 112, 228, 0.5);
    transform: translateY(-2px);
    box-shadow: var(--shadow-light);
}

.rating-option.active {
    border-color: var(--primary-blue);
    background-color: rgba(0, 112, 228, 0.05);
    transform: scale(1.02);
    box-shadow: 0 4px 12px rgba(0, 112, 228, 0.15);
}

.rating-option .rating-icon {
    font-size: 32px;
    margin-bottom: 10px;
    color: var(--neutral-dark);
    transition: color 0.2s ease;
}

.rating-option .rating-text {
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    transition: color 0.2s ease;
}

.rating-option.active .rating-icon {
    color: var(--primary-blue);
}

.rating-option.active .rating-text {
    color: var(--primary-blue);
}

/* 工作人员列表样式 */
.staff-list-container {
    margin-top: 15px;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
}

/* 工作人员行样式 */
.staff-row {
    display: flex;
    align-items: center;
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    background-color: #fff;
}

.staff-row:last-child {
    border-bottom: none;
}

/* 工作人员信息 */
.staff-info {
    flex: 1;
}

.staff-name {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.staff-title {
    font-size: 13px;
    color: #888;
    margin-top: 2px;
}

/* 评价按钮组 */
.staff-rating-buttons {
    display: flex;
    gap: 8px;
}

/* 评价按钮样式 */
.rating-btn {
    padding: 6px 12px;
    border-radius: 4px;
    border: 1px solid #ddd;
    background-color: #f9f9f9;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.rating-btn.satisfied {
    color: #2E7D32;
}

.rating-btn.unsatisfied {
    color: #C62828;
}

/* 选中状态 */
.rating-btn.selected {
    color: #fff;
    border: none;
}

.rating-btn.satisfied.selected {
    background-color: #2E7D32;
}

.rating-btn.unsatisfied.selected {
    background-color: #C62828;
}

/* 禁用状态 */
.rating-btn.disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

/* 评价计数器 */
.evaluation-counters {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
    padding: 10px 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
}

.counter {
    display: flex;
    flex-direction: column;
    align-items: center;
}

.counter-label {
    font-size: 14px;
    color: #666;
}

.counter-value {
    font-size: 16px;
    font-weight: 600;
}

.satisfaction .counter-value {
    color: #2E7D32;
}

.dissatisfaction .counter-value {
    color: #C62828;
}

/* 限制提示消息 */
.limit-message {
    padding: 8px 12px;
    background-color: #FFF3E0;
    border-left: 3px solid #FF9800;
    font-size: 14px;
    color: #E65100;
    margin-bottom: 15px;
    border-radius: 4px;
}

/* 无工作人员消息 */
.no-staff-message {
    padding: 20px;
    text-align: center;
    background-color: #f9f9f9;
    border-radius: 8px;
    color: #888;
    font-size: 15px;
}

/* 按钮样式 */
.btn,
.submit-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 14px 24px;
    background-color: var(--primary-blue);
    color: var(--white);
    border-radius: 12px;
    font-size: 16px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 10px rgba(0, 112, 228, 0.2);
    width: 100%;
    position: relative;
    overflow: hidden;
}

.btn:hover,
.submit-btn:hover {
    background-color: var(--primary-dark);
    transform: translateY(-2px);
    box-shadow: 0 6px 15px rgba(0, 112, 228, 0.25);
}

.btn:active,
.submit-btn:active {
    transform: translateY(1px);
    box-shadow: 0 2px 5px rgba(0, 112, 228, 0.2);
}

.btn:disabled,
.submit-btn:disabled {
    background-color: var(--neutral-medium);
    color: rgba(255, 255, 255, 0.8);
    cursor: not-allowed;
    box-shadow: none;
    transform: none;
}

.btn-secondary {
    background-color: transparent;
    color: var(--primary-blue);
    border: 2px solid var(--primary-blue);
    box-shadow: none;
}

.btn-secondary:hover {
    background-color: rgba(0, 112, 228, 0.05);
    box-shadow: none;
}

.btn [class^="ai-"],
.submit-btn [class^="ai-"] {
    margin-right: 8px;
    font-size: 20px;
}

/* 提交按钮水波纹效果 */
.submit-btn .ripple {
    position: absolute;
    border-radius: 50%;
    background-color: rgba(255, 255, 255, 0.4);
    transform: scale(0);
    animation: ripple 0.6s linear;
    pointer-events: none;
}

@keyframes ripple {
    to {
        transform: scale(2.5);
        opacity: 0;
    }
}

/* 成功消息样式 */
.success-message {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 30px 20px;
    background-color: var(--white);
    border-radius: 12px;
    text-align: center;
    box-shadow: var(--shadow-medium);
    position: relative;
    overflow: hidden;
}

.success-icon {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background-color: var(--accent-green);
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 20px;
    position: relative;
    box-shadow: 0 4px 10px rgba(102, 187, 106, 0.2);
}

.success-icon::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 32px;
    height: 20px;
    border-left: 4px solid white;
    border-bottom: 4px solid white;
    transform: translate(-50%, -60%) rotate(-45deg);
}

.success-message h3 {
    font-size: 24px;
    color: var(--accent-green);
    margin-bottom: 15px;
    font-weight: 600;
}

.success-message p {
    font-size: 16px;
    color: var(--neutral-dark);
    margin-bottom: 25px;
    max-width: 80%;
}

/* 医疗主题加载指示器 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
    -webkit-backdrop-filter: blur(3px);
}

.medical-loader {
    width: 80px;
    height: 80px;
    position: relative;
    margin-bottom: 20px;
}

.ecg-line {
    position: absolute;
    top: 50%;
    left: 0;
    width: 100%;
    height: 2px;
    background: rgba(0, 112, 228, 0.2);
    overflow: hidden;
}

.ecg-line::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 200%;
    height: 100%;
    background: linear-gradient(
        90deg,
        rgba(0, 0, 0, 0) 0%,
        rgba(0, 0, 0, 0) 30%,
        var(--primary-blue) 30%,
        var(--primary-blue) 45%,
        var(--accent-red) 45%,
        var(--accent-red) 50%,
        var(--primary-blue) 50%,
        var(--primary-blue) 55%,
        rgba(0, 0, 0, 0) 55%,
        rgba(0, 0, 0, 0) 100%
    );
    animation: ecg-animation 2s linear infinite;
}

@keyframes ecg-animation {
    0% {
        transform: translateX(-100%);
    }
    100% {
        transform: translateX(0);
    }
}

.medical-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    border: 2px solid var(--primary-blue);
    animation: medical-pulse 2s ease infinite;
}

@keyframes medical-pulse {
    0% {
        transform: scale(0.8);
        opacity: 0.8;
        box-shadow: 0 0 0 0 rgba(0, 112, 228, 0.4);
    }
    70% {
        transform: scale(1);
        opacity: 0;
        box-shadow: 0 0 0 15px rgba(0, 112, 228, 0);
    }
    100% {
        transform: scale(0.8);
        opacity: 0;
    }
}

.medical-cross {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 40%;
    height: 40%;
}

.medical-cross::before,
.medical-cross::after {
    content: '';
    position: absolute;
    background-color: var(--primary-blue);
    border-radius: 3px;
}

.medical-cross::before {
    top: 0;
    left: calc(50% - 2px);
    width: 4px;
    height: 100%;
}

.medical-cross::after {
    top: calc(50% - 2px);
    left: 0;
    width: 100%;
    height: 4px;
}

.loading-text {
    color: var(--primary-dark);
    font-size: 16px;
    font-weight: 500;
    text-align: center;
    animation: pulse-text 1.5s ease infinite;
}

@keyframes pulse-text {
    0%, 100% {
        opacity: 0.7;
    }
    50% {
        opacity: 1;
    }
}

/* 错误提示条 */
.error-banner {
    display: flex;
    align-items: center;
    padding: 15px;
    background-color: rgba(229, 57, 53, 0.1);
    border-left: 4px solid var(--accent-red);
    border-radius: 8px;
    margin: 20px 0;
    animation: slide-down 0.4s ease-out;
    box-shadow: 0 3px 12px rgba(229, 57, 53, 0.15);
}

@keyframes slide-down {
    0% {
        opacity: 0;
        transform: translateY(-20px);
    }
    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

.error-icon {
    color: var(--accent-red);
    font-size: 24px;
    margin-right: 15px;
    flex-shrink: 0;
}

.error-message {
    flex: 1;
    font-size: 15px;
    color: var(--neutral-dark);
}

.error-close {
    color: var(--neutral-dark);
    background: none;
    border: none;
    cursor: pointer;
    padding: 5px;
    margin-left: 10px;
    opacity: 0.6;
    transition: opacity 0.2s ease;
}

.error-close:hover {
    opacity: 1;
}

/* 辅助类 */
.hidden {
    display: none !important;
}

.mt-2 {
    margin-top: 0.5rem;
}

.text-sm {
    font-size: 0.875rem;
}

.text-gray-500 {
    color: #6b7280;
}

/* 淡入效果 */
.fade-in {
    animation: fadeIn 0.4s ease;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 表单验证错误样式 */
.error-input {
    border-color: #f44336 !important;
    background-color: #fff5f5;
}

/* 响应式样式 */
@media (max-width: 480px) {
    .app-container {
        padding: 15px 12px;
    }

    .staff-list {
        grid-template-columns: 1fr;
    }

    .section, .rating-section {
        padding: 18px 16px;
    }

    .emoji {
        font-size: 2.2rem;
    }

    .rating-text {
        font-size: 1rem;
    }

    .form-group input,
    .form-group textarea,
    .form-group select {
        padding: 15px 14px;
        font-size: 1.1rem;
    }

    .rating-option {
        min-height: 100px;
        padding: 15px 5px;
    }

    /* 提高对比度和可点击性 */
    .submit-btn {
        padding: 16px 24px;
        margin-top: 10px;
    }

    /* 错误提示在移动端更容易注意到 */
    .error-banner {
        padding: 16px;
        max-width: 95%;
    }
}

/* 二维码提示区域样式 */
.qr-code-required {
    text-align: center;
    padding: 24px 16px;
    background-color: #f8f9fa;
    border: 2px dashed #dee2e6;
    border-radius: 12px;
    margin-bottom: 24px;
    animation: pulse-border 2s infinite;
}

@keyframes pulse-border {
    0% { border-color: #dee2e6; }
    50% { border-color: #4A90E2; }
    100% { border-color: #dee2e6; }
}

.qr-notice-icon {
    font-size: 48px;
    color: #4A90E2;
    margin-bottom: 16px;
}

.qr-notice-content {
    margin: 16px 0;
    font-size: 16px;
    color: #495057;
}

.qr-scan-illustration {
    margin: 24px auto;
    width: 120px;
    height: 120px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qr-scan-animation {
    position: absolute;
    width: 100%;
    height: 100%;
    border: 2px solid #4A90E2;
    border-radius: 12px;
    animation: scan-animation 3s infinite;
}

@keyframes scan-animation {
    0% {
        transform: scale(0.8);
        opacity: 0.3;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.7;
    }
    100% {
        transform: scale(0.8);
        opacity: 0.3;
    }
}

.qr-notice-help {
    color: #6c757d;
    font-size: 14px;
    margin-top: 16px;
}

/* 禁用状态的样式 */
.section-disabled {
    opacity: 0.7;
    pointer-events: none;
    position: relative;
}

.section-disabled::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 255, 255, 0.5);
    z-index: 1;
}

.submit-btn.disabled {
    background-color: #ced4da !important;
    color: #6c757d !important;
    cursor: not-allowed;
    box-shadow: none !important;
}

.submit-btn.disabled:hover {
    background-color: #ced4da !important;
    transform: none !important;
}

/* 强制隐藏样式 */
.qr-code-required.hidden {
    display: none !important;
    visibility: hidden !important;
    opacity: 0 !important;
    pointer-events: none !important;
    position: absolute !important;
    z-index: -9999 !important;
}

/* 强制显示样式 */
.staff-type-section.force-show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
    pointer-events: auto !important;
}

/* 确保section-disabled样式有足够权重 */
.section.section-disabled {
    opacity: 0.6 !important;
    pointer-events: none !important;
    user-select: none !important;
    filter: grayscale(50%) !important;
}

/* 网络状态指示器 */
.network-status-bar {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 30px;
    background-color: rgba(245, 245, 245, 0.95);
    border-top: 1px solid #ccc;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 15px;
    z-index: 999;
    font-size: 12px;
}

.network-status {
    padding: 3px 8px;
    border-radius: 3px;
    display: inline-block;
    background-color: #dff0d8;
    color: #3c763d;
}

.status-online {
    background-color: #dff0d8;
    color: #3c763d;
}

.status-offline {
    background-color: #f2dede;
    color: #a94442;
}

.offline-count {
    font-size: 12px;
    padding: 3px 8px;
    background-color: #fcf8e3;
    color: #8a6d3b;
    border-radius: 3px;
    display: inline-block;
}

.sync-btn {
    padding: 2px 6px;
    font-size: 12px;
    background-color: #f0f0f0;
    border: 1px solid #ccc;
    border-radius: 3px;
    cursor: pointer;
}

.force-show {
    display: block !important;
    visibility: visible !important;
    opacity: 1 !important;
}

/* 移动端星星评分优化 - 保持美观样式 */
@media (max-width: 768px) {
    .stars {
        gap: 12px !important;
        margin: 20px 0 !important;
    }

    .star {
        width: 40px !important;
        height: 40px !important;
        margin: 0 4px !important;
    }
}