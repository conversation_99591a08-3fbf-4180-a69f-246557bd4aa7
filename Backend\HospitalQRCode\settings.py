"""
Django settings for HospitalQRCode project.

请根据实际情况修改 SECRET_KEY、DEBUG 以及 ALLOWED_HOSTS 等配置。
"""

import os
from pathlib import Path

# 导入环境变量处理模块
from .env import get_env_variable, load_env

# 确保环境变量已加载
load_env()

# Build paths inside the project like this: BASE_DIR / 'subdir'.
BASE_DIR = Path(__file__).resolve().parent.parent

# SECURITY WARNING: keep the secret key used in production secret!
# 从环境变量获取安全密钥
SECRET_KEY = get_env_variable('DJANGO_SECRET_KEY')

# SECURITY WARNING: don't run with debug turned on in production!
DEBUG = get_env_variable('DEBUG', 'False', bool)  # 生产环境设置

# 从环境变量获取允许的主机列表
ALLOWED_HOSTS = get_env_variable('ALLOWED_HOSTS', '127.0.0.1,localhost', list)

# 站点域名，用于生成绝对URL
# 从环境变量获取站点域名
SITE_DOMAIN = get_env_variable('SITE_DOMAIN', 'http://localhost')

# 前端URL配置，用于重定向到前端页面
FRONTEND_URL = get_env_variable('FRONTEND_URL', 'http://localhost')

# Application definition
INSTALLED_APPS = [
    # 'django.contrib.admin',  # 已移除Django内置admin
    'django.contrib.auth',
    'django.contrib.contenttypes',
    'django.contrib.sessions',
    'django.contrib.messages',
    'django.contrib.staticfiles',
    'django_bootstrap5',
    'qrmanager.apps.QrmanagerConfig',  # 注册应用
    'corsheaders',  # 添加corsheaders应用
    'csp',  # 添加CSP应用
]

MIDDLEWARE = [
    'django.middleware.security.SecurityMiddleware',
    'qrcode_based_security.QRCodeSecurityMiddleware',  # 🔒 新增：基于二维码的精准安全中间件
    'whitenoise.middleware.WhiteNoiseMiddleware',  # 添加whitenoise中间件
    'django.contrib.sessions.middleware.SessionMiddleware',
    'corsheaders.middleware.CorsMiddleware',  # 添加CORS中间件（必须在CommonMiddleware之前）
    'django.middleware.common.CommonMiddleware',
    'django.middleware.csrf.CsrfViewMiddleware',  # 重新启用CSRF验证
    'django.contrib.auth.middleware.AuthenticationMiddleware',
    'django.contrib.messages.middleware.MessageMiddleware',
    'django.middleware.clickjacking.XFrameOptionsMiddleware',
    'csp.middleware.CSPMiddleware',  # 添加CSP中间件
    'qrmanager.middleware.security_headers.SecurityHeadersMiddleware',  # 添加安全头部中间件
    'qrmanager.middleware.login_attempt_limit.LoginAttemptLimitMiddleware',  # 添加登录尝试限制中间件
    # 'qrmanager.middleware.rate_limit.RateLimitMiddleware',  # 🔒 暂时禁用旧的速率限制
    # 'qrmanager.middleware.qrcode_rate_limit.QRCodeRateLimitMiddleware',  # 🔒 暂时禁用旧的二维码限制
    'qrmanager.middleware.operation_log.OperationLogMiddleware',  # 添加操作日志中间件
    'qrmanager.middleware.api_security.APISecurityMiddleware',   # API安全中间件
    'qrmanager.middleware.temp_token_security.TempTokenSecurityMiddleware',  # 添加临时令牌安全中间件
    'qrmanager.middleware.session_timeout.SessionTimeoutMiddleware',  # 添加会话超时中间件
]

ROOT_URLCONF = 'HospitalQRCode.urls'

TEMPLATES = [
    {
        'BACKEND': 'django.template.backends.django.DjangoTemplates',
        'DIRS': [os.path.join(BASE_DIR, 'templates')],
        'APP_DIRS': True,
        'OPTIONS': {
            'context_processors': [
                'django.template.context_processors.debug',
                'django.template.context_processors.request',
                'django.contrib.auth.context_processors.auth',
                'django.contrib.messages.context_processors.messages',
            ],
        },
    },
]

WSGI_APPLICATION = 'HospitalQRCode.wsgi.application'

# Database
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# Password validation
AUTH_PASSWORD_VALIDATORS = [
    {
        'NAME': 'django.contrib.auth.password_validation.UserAttributeSimilarityValidator',
        'OPTIONS': {
            'user_attributes': ('username', 'email', 'first_name', 'last_name'),
            'max_similarity': 0.7,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.MinimumLengthValidator',
        'OPTIONS': {
            'min_length': 10,
        }
    },
    {
        'NAME': 'django.contrib.auth.password_validation.CommonPasswordValidator',
    },
    {
        'NAME': 'django.contrib.auth.password_validation.NumericPasswordValidator',
    },
    {
        # 自定义密码验证器，要求密码包含大小写字母、数字和特殊字符
        'NAME': 'qrmanager.validators.PasswordComplexityValidator',
    },
]

# 密码过期设置
PASSWORD_EXPIRY_DAYS = 90  # 密码90天过期
PASSWORD_HISTORY_COUNT = 5  # 记住最近5个密码，防止重复使用

# Internationalization
LANGUAGE_CODE = 'zh-hans'
TIME_ZONE = 'Asia/Shanghai'
USE_I18N = True
USE_TZ = True

# Static files (CSS, JavaScript, Images)
STATIC_URL = 'static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')
# 添加静态文件目录
STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# Media files
MEDIA_URL = 'media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')

# Default primary key field type
DEFAULT_AUTO_FIELD = 'django.db.models.BigAutoField'

# Messages settings
MESSAGE_STORAGE = 'django.contrib.messages.storage.session.SessionStorage'

# Whitenoise settings
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# 登录相关配置
LOGIN_REDIRECT_URL = 'qrmanager:dashboard'  # 登录成功后重定向到仪表板
LOGIN_URL = 'login'  # 登录页面的URL
LOGOUT_REDIRECT_URL = 'qrmanager:login'  # 登出后重定向到登录页面

# 会话设置
SESSION_COOKIE_AGE = get_env_variable('SESSION_COOKIE_AGE', '1200', int)  # 20分钟，用户无操作自动登出
SESSION_EXPIRE_AT_BROWSER_CLOSE = get_env_variable('SESSION_EXPIRE_AT_BROWSER_CLOSE', 'True', bool)

# CORS设置
CORS_ALLOWED_ORIGINS = get_env_variable('CORS_ALLOWED_ORIGINS', 'http://localhost,http://127.0.0.1', list)
CORS_ALLOW_CREDENTIALS = get_env_variable('CORS_ALLOW_CREDENTIALS', 'True', bool)

# 添加REST框架设置
REST_FRAMEWORK = {
    'DEFAULT_AUTHENTICATION_CLASSES': [
        'rest_framework.authentication.SessionAuthentication',
        'qrmanager.authentication.APIKeyAuthentication',
    ],
    'DEFAULT_PERMISSION_CLASSES': [
        'rest_framework.permissions.IsAuthenticated',
    ],
    'DEFAULT_THROTTLE_CLASSES': [
        'rest_framework.throttling.AnonRateThrottle',
        'rest_framework.throttling.UserRateThrottle',
    ],
    'DEFAULT_THROTTLE_RATES': {
        'anon': '10/minute',
        'user': '100/minute',
    },
    'DEFAULT_PAGINATION_CLASS': 'rest_framework.pagination.PageNumberPagination',
    'PAGE_SIZE': 20,
    'DEFAULT_RENDERER_CLASSES': [
        'rest_framework.renderers.JSONRenderer',
        # 在生产环境中移除可浏览的API渲染器，提高安全性
        # 'rest_framework.renderers.BrowsableAPIRenderer',
    ],
    'EXCEPTION_HANDLER': 'qrmanager.utils.custom_exception_handler',
}

# 日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {asctime} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
        'file': {
            'level': 'DEBUG',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'debug.log'),
            'formatter': 'verbose',
        },
        'error_file': {
            'level': 'ERROR',
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'error.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console', 'file'],
            'level': 'INFO',
            'propagate': True,
        },
        'qrmanager': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',  # 生产环境中将日志级别从DEBUG改为INFO
            'propagate': True,
        },
        'qrcode_security': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',  # 二维码安全日志
            'propagate': True,
        },
        'security': {
            'handlers': ['console', 'file', 'error_file'],
            'level': 'INFO',  # 通用安全日志
            'propagate': True,
        },
    },
}

# 添加临时令牌过期时间设置（分钟）
TEMP_TOKEN_EXPIRY_MINUTES = 3

# 缓存配置 - 使用文件缓存，确保在应用重启后仍然保持数据
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.filebased.FileBasedCache',
        'LOCATION': os.path.join(BASE_DIR, 'django_cache'),
        'TIMEOUT': 3600,  # 默认缓存过期时间（秒）
        'OPTIONS': {
            'MAX_ENTRIES': 1000  # 最大缓存条目数
        }
    }
}

# 生产环境中禁用所有来源的CORS，仅允许指定的域名
CORS_ALLOW_ALL_ORIGINS = get_env_variable('CORS_ALLOW_ALL_ORIGINS', 'False', bool)

# 安全设置
SECURE_CONTENT_TYPE_NOSNIFF = get_env_variable('SECURE_CONTENT_TYPE_NOSNIFF', 'True', bool)
SECURE_BROWSER_XSS_FILTER = get_env_variable('SECURE_BROWSER_XSS_FILTER', 'True', bool)
X_FRAME_OPTIONS = get_env_variable('X_FRAME_OPTIONS', 'DENY')

# HTTPS安全设置
SECURE_SSL_REDIRECT = get_env_variable('SECURE_SSL_REDIRECT', 'False', bool)  # 本地开发设置为False，生产环境设置为True
SECURE_HSTS_SECONDS = get_env_variable('SECURE_HSTS_SECONDS', '31536000', int)  # 1年
SECURE_HSTS_INCLUDE_SUBDOMAINS = get_env_variable('SECURE_HSTS_INCLUDE_SUBDOMAINS', 'True', bool)
SECURE_HSTS_PRELOAD = get_env_variable('SECURE_HSTS_PRELOAD', 'True', bool)

# 会话安全设置
SESSION_COOKIE_SECURE = get_env_variable('SESSION_COOKIE_SECURE', 'True', bool)  # 如果使用HTTPS，设置为True
CSRF_COOKIE_SECURE = get_env_variable('CSRF_COOKIE_SECURE', 'True', bool)  # 如果使用HTTPS，设置为True
CSRF_COOKIE_HTTPONLY = True
SESSION_COOKIE_HTTPONLY = True

# CSRF可信来源设置
CSRF_TRUSTED_ORIGINS = [
    'https://zg120pj.cn',
    'https://www.zg120pj.cn',
    'https://zg120pj.cn:8000',
    'https://zg120pj.cn:443',
    'http://zg120pj.cn',
    'http://www.zg120pj.cn',
    'http://127.0.0.1:8001',
    'http://localhost:8001'
]

# CSRF安全配置 - 生产环境优化
CSRF_COOKIE_SECURE = True  # HTTPS环境设置为True
CSRF_COOKIE_SAMESITE = 'Lax'  # 设置为Lax以支持跨站请求
CSRF_USE_SESSIONS = False  # 使用cookie而不是session
CSRF_COOKIE_HTTPONLY = False  # 允许JavaScript访问CSRF cookie（前端需要）
CSRF_COOKIE_AGE = 3600  # CSRF cookie 1小时过期
CSRF_FAILURE_VIEW = 'qrmanager.views.csrf_failure'  # 自定义CSRF失败处理

# 内容安全策略设置 - 临时放宽限制以排查问题
CONTENT_SECURITY_POLICY = {
    'DIRECTIVES': {
        'default-src': ("'self'", "'unsafe-inline'", "'unsafe-eval'", "data:", "blob:", "https:", "http:"),
        'script-src': ("'self'", "'unsafe-inline'", "'unsafe-eval'", "https:", "http:"),  # 允许所有脚本
        'style-src': ("'self'", "'unsafe-inline'", "https:", "http:"),   # 允许所有样式
        'img-src': ("'self'", "data:", "blob:", "https:", "http:"),  # 允许所有图片
        'font-src': ("'self'", "data:", "https:", "http:"),  # 允许所有字体
        'connect-src': ("'self'", "https:", "http:", "ws:", "wss:"),  # 允许所有连接
        'object-src': ("'self'", "data:", "blob:", "https:", "http:"),  # 允许所有对象
        'base-uri': ("'self'",),
        'frame-src': ("'self'", "https:", "http:"),  # 允许所有框架
        'media-src': ("'self'", "data:", "blob:", "https:", "http:"),  # 允许所有媒体
    }
}