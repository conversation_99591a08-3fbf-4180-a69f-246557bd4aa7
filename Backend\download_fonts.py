import os
import requests
from tqdm import tqdm

# 项目根目录
BASE_DIR = r"D:\项目\前后端分离\Backend"

# 定义要下载的文件列表
files_to_download = [
    # Bootstrap Icons 字体文件
    {
        'url': 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/fonts/bootstrap-icons.woff2',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'bootstrap-icons', 'font', 'fonts', 'bootstrap-icons.woff2')
    },
    {
        'url': 'https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.1/font/fonts/bootstrap-icons.woff',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'bootstrap-icons', 'font', 'fonts', 'bootstrap-icons.woff')
    },
    # Font Awesome 字体文件
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-brands-400.woff2',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-brands-400.woff2')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-brands-400.ttf',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-brands-400.ttf')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-regular-400.woff2',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-regular-400.woff2')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-regular-400.ttf',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-regular-400.ttf')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.woff2',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-solid-900.woff2')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-solid-900.ttf',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-solid-900.ttf')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-v4compatibility.woff2',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-v4compatibility.woff2')
    },
    {
        'url': 'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/webfonts/fa-v4compatibility.ttf',
        'path': os.path.join(BASE_DIR, 'static', 'vendor', 'fontawesome', 'webfonts', 'fa-v4compatibility.ttf')
    }
]

def download_file(url, path):
    """下载文件并显示进度条"""
    # 创建目录（如果不存在）
    os.makedirs(os.path.dirname(path), exist_ok=True)
    
    # 下载文件
    response = requests.get(url, stream=True)
    response.raise_for_status()  # 确保请求成功
    
    # 获取文件大小
    total_size = int(response.headers.get('content-length', 0))
    
    # 显示进度条
    with open(path, 'wb') as f, tqdm(
        desc=os.path.basename(path),
        total=total_size,
        unit='B',
        unit_scale=True,
        unit_divisor=1024,
    ) as bar:
        for chunk in response.iter_content(chunk_size=8192):
            if chunk:  # 过滤掉保持连接的新块
                f.write(chunk)
                bar.update(len(chunk))

def main():
    """主函数"""
    print("开始下载字体文件...")
    
    # 下载每个文件
    for file_info in files_to_download:
        url = file_info['url']
        path = file_info['path']
        
        print(f"正在下载 {os.path.basename(path)}...")
        try:
            download_file(url, path)
            print(f"成功下载 {os.path.basename(path)}")
        except Exception as e:
            print(f"下载 {os.path.basename(path)} 失败: {e}")
    
    print("所有文件下载完成！")

if __name__ == "__main__":
    main()