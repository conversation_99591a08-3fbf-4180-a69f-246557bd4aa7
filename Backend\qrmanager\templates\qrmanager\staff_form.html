{% extends "qrmanager/base.html" %}
{% load django_bootstrap5 %}

{% block title %}{% if form.instance.pk %}编辑工作人员{% else %}新增工作人员{% endif %}{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title mb-0">
                        {% if form.instance.pk %}
                        编辑工作人员
                        {% else %}
                        新增工作人员
                        {% endif %}
                    </h3>
                </div>
                <div class="card-body">
                    <form method="post" enctype="multipart/form-data" class="form">
                        {% csrf_token %}
                        
                        <div class="row">
                            <div class="col-md-6">
                                {% bootstrap_field form.work_number %}
                                {% bootstrap_field form.name %}
                                {% bootstrap_field form.staff_type %}
                                {% bootstrap_field form.title %}
                            </div>
                            <div class="col-md-6">
                                {% bootstrap_field form.department %}
                                {% bootstrap_field form.photo %}
                                {% if form.instance.photo %}
                                <div class="mb-3">
                                    <label class="form-label">当前照片</label>
                                    <div>
                                        <img src="{{ form.instance.photo.url }}" alt="{{ form.instance.name }}" 
                                             class="img-thumbnail" style="max-height: 200px;">
                                    </div>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="d-flex justify-content-between mt-3">
                            <a href="{% url 'qrmanager:staff_list' %}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 返回
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-save"></i> 保存
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 图片上传预览
    const photoInput = document.querySelector('input[type="file"]');
    if (photoInput) {
        photoInput.addEventListener('change', function() {
            if (this.files && this.files[0]) {
                const reader = new FileReader();
                reader.onload = function(e) {
                    let preview = document.querySelector('.image-preview');
                    if (!preview) {
                        preview = document.createElement('img');
                        preview.className = 'image-preview';
                        photoInput.parentNode.appendChild(preview);
                    }
                    preview.src = e.target.result;
                }
                reader.readAsDataURL(this.files[0]);
            }
        });
    }
});
</script>
{% endblock %}
{% endblock %} 