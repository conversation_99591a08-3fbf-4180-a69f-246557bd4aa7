from django.contrib.auth.signals import user_logged_in, user_logged_out
from django.dispatch import receiver
from .models import OperationLog

@receiver(user_logged_in)
def log_user_login(sender, request, user, **kwargs):
    OperationLog.objects.create(user=user, action='登录', description='用户登录成功')

@receiver(user_logged_out)
def log_user_logout(sender, request, user, **kwargs):
    OperationLog.objects.create(user=user, action='退出', description='用户登出') 