#!/usr/bin/env python
"""
SQLite数据库备份脚本
定期备份SQLite数据库，并删除旧备份
"""

import os
import datetime
import subprocess
from pathlib import Path
import logging
import shutil
import sys

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(os.path.join(os.path.dirname(os.path.abspath(__file__)), 'backup.log')),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger('db_backup')

def backup_database():
    """
    备份SQLite数据库
    
    创建数据库的备份副本，压缩备份文件，并删除旧备份
    
    返回:
        str: 备份文件路径，如果备份失败则返回None
    """
    try:
        # 获取项目根目录
        BASE_DIR = Path(__file__).resolve().parent.parent
        
        # 数据库文件路径
        db_file = os.path.join(BASE_DIR, 'db.sqlite3')
        
        # 创建备份目录
        backup_dir = os.path.join(BASE_DIR, 'backups')
        os.makedirs(backup_dir, exist_ok=True)
        
        # 生成备份文件名
        timestamp = datetime.datetime.now().strftime('%Y%m%d_%H%M%S')
        backup_file = os.path.join(backup_dir, f'db_backup_{timestamp}.sqlite3')
        
        # 确保数据库文件存在
        if not os.path.exists(db_file):
            logger.error(f"数据库文件不存在: {db_file}")
            return None
        
        # 创建数据库副本
        logger.info(f"开始备份数据库: {db_file} -> {backup_file}")
        
        # 检查是否安装了sqlite3命令行工具
        try:
            # 尝试使用SQLite的vacuum命令创建优化的备份
            subprocess.run(['sqlite3', db_file, f'.backup "{backup_file}"'], check=True)
            logger.info(f"使用sqlite3命令行工具备份成功: {backup_file}")
        except (subprocess.SubprocessError, FileNotFoundError):
            # 如果sqlite3命令行工具不可用，使用文件复制
            logger.warning("sqlite3命令行工具不可用，使用文件复制方式备份")
            # 确保数据库连接已关闭
            try:
                # 尝试创建一个简单的文件锁，确保数据库未被使用
                lock_file = f"{db_file}.lock"
                with open(lock_file, 'w') as f:
                    f.write('backup_lock')
                
                # 复制数据库文件
                shutil.copy2(db_file, backup_file)
                logger.info(f"使用文件复制备份成功: {backup_file}")
                
                # 删除锁文件
                os.remove(lock_file)
            except Exception as e:
                logger.error(f"文件复制备份失败: {str(e)}")
                return None
        
        # 压缩备份文件
        compressed_file = f"{backup_file}.zip"
        try:
            # 尝试使用gzip
            subprocess.run(['gzip', '-f', backup_file], check=True)
            compressed_file = f"{backup_file}.gz"
            logger.info(f"使用gzip压缩备份文件: {compressed_file}")
        except (subprocess.SubprocessError, FileNotFoundError):
            # 如果gzip不可用，使用Python的zipfile模块
            import zipfile
            try:
                with zipfile.ZipFile(compressed_file, 'w', zipfile.ZIP_DEFLATED) as zipf:
                    zipf.write(backup_file, os.path.basename(backup_file))
                # 删除原始备份文件
                os.remove(backup_file)
                logger.info(f"使用zipfile模块压缩备份文件: {compressed_file}")
            except Exception as e:
                logger.error(f"压缩备份文件失败: {str(e)}")
                # 如果压缩失败，保留原始备份文件
                compressed_file = backup_file
        
        # 删除旧备份（保留最近30天的备份）
        retention_days = 30
        cutoff_time = datetime.datetime.now().timestamp() - (retention_days * 24 * 60 * 60)
        
        backup_count = 0
        deleted_count = 0
        
        for file in os.listdir(backup_dir):
            if file.startswith('db_backup_') and (file.endswith('.sqlite3.gz') or file.endswith('.sqlite3.zip') or file.endswith('.sqlite3')):
                backup_count += 1
                file_path = os.path.join(backup_dir, file)
                file_time = os.path.getmtime(file_path)
                
                if file_time < cutoff_time:
                    os.remove(file_path)
                    deleted_count += 1
                    logger.info(f"删除旧备份: {file_path}")
        
        logger.info(f"备份清理完成: 共{backup_count}个备份，删除了{deleted_count}个旧备份")
        logger.info(f"保留了最近{retention_days}天的备份")
        
        return compressed_file
    except Exception as e:
        logger.error(f"备份失败: {str(e)}")
        return None

if __name__ == '__main__':
    logger.info("开始数据库备份过程")
    backup_file = backup_database()
    if backup_file:
        logger.info(f"数据库备份完成: {backup_file}")
    else:
        logger.error("数据库备份失败")
        sys.exit(1)