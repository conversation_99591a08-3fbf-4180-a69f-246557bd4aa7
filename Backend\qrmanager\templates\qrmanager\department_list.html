{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}科室管理{% endblock title %}

{% block extra_css %}
<style>
/* 添加与床位页面相同的渐变背景 */
body {
    background: linear-gradient(135deg, #0078D7, #00ADEF) !important;
    background-attachment: fixed !important;
}

/* 卡片样式优化 */
.card {
    border: none !important;
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    background-color: white !important;
}

.card:hover {
    box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
}

/* 表格样式优化 */
.table {
    background-color: white;
    border-radius: 8px;
    overflow: hidden;
}

/* 统计卡片样式修改 - 保持白色图标和文字 */
.icon-circle {
    background-color: rgba(255, 255, 255, 0.2) !important;
}

.bg-white-25 {
    background-color: rgba(255, 255, 255, 0.25) !important;
}

.display-6 {
    font-size: 2rem;
}

/* 确保统计卡片文字颜色正确 */
.card.bg-primary, .card.bg-success, .card.bg-info, .card.bg-warning {
    color: white !important;
}

.card.bg-primary .display-6, 
.card.bg-success .display-6, 
.card.bg-info .display-6, 
.card.bg-warning .display-6 {
    color: white !important;
}

.text-white-50 {
    color: rgba(255, 255, 255, 0.5) !important;
}

/* 新统计卡片设计 */
.stat-card {
    position: relative;
    height: 120px;
    border-radius: 12px;
    background: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.25, 0.8, 0.25, 1);
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.stat-card-inner {
    position: relative;
    width: 100%;
    height: 100%;
    text-align: center;
    transition: transform 0.4s;
    transform-style: preserve-3d;
}

.stat-card-front {
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    backface-visibility: hidden;
    padding: 20px;
    color: white;
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    font-size: 24px;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-icon {
    transform: scale(1.1) rotate(5deg);
}

.stat-content {
    text-align: left;
    flex-grow: 1;
}

.stat-title {
    font-size: 14px;
    opacity: 0.8;
    margin-bottom: 5px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.stat-value {
    font-size: 28px;
    font-weight: 700;
    transition: all 0.3s ease;
}

.stat-card:hover .stat-value {
    transform: scale(1.05);
}

/* 为每个卡片设置不同的颜色 */
#stat-card-1 {
    background: linear-gradient(135deg, #4e73df, #224abe);
}

#stat-card-1:hover {
    background: linear-gradient(135deg, #3a5fd4, #1a3ea8);
}

#stat-card-2 {
    background: linear-gradient(135deg, #1cc88a, #169a67);
}

#stat-card-2:hover {
    background: linear-gradient(135deg, #19b67c, #13895e);
}

#stat-card-3 {
    background: linear-gradient(135deg, #36b9cc, #258ea0);
}

#stat-card-3:hover {
    background: linear-gradient(135deg, #2fa8ba, #1e7f8f);
}

#stat-card-4 {
    background: linear-gradient(135deg, #f6c23e, #dca71c);
}

#stat-card-4:hover {
    background: linear-gradient(135deg, #f4b620, #c89618);
}

/* 只保留表格和基础样式 */
.table {
    margin-bottom: 0;
    width: 100%;
}

.table th {
    font-weight: 600;
    color: #495057;
    border-bottom: 2px solid #e9ecef;
    background-color: #f8f9fa;
    padding: 0.75rem;
    white-space: nowrap;
    font-size: 0.875rem;
}

.table td {
    padding: 0.75rem;
    vertical-align: middle;
    font-size: 0.875rem;
    border-bottom: 1px solid #e9ecef;
}

.badge {
    font-size: 0.75rem;
    padding: 0.4em 0.8em;
}

.btn-group .btn {
    padding: 0.4rem 0.8rem;
    font-size: 0.8125rem;
}

.text-muted {
    font-size: 0.8125rem;
}

/* 容器宽度优化 */
.container-fluid {
    max-width: 100%;
    padding-left: 1rem;
    padding-right: 1rem;
}

@media (min-width: 1400px) {
    .container-fluid {
        padding-left: 2rem;
        padding-right: 2rem;
    }
}

/* 表格响应式优化 */
.table-responsive {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
    scrollbar-width: thin;
}

.table-responsive::-webkit-scrollbar {
    height: 6px;
}

.table-responsive::-webkit-scrollbar-thumb {
    background-color: rgba(0,0,0,.1);
    border-radius: 3px;
}

.table-responsive::-webkit-scrollbar-track {
    background-color: rgba(0,0,0,.05);
}

/* 动画效果 */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 图标优化 */
.fas {
    font-size: 0.95em;
}

.btn .fas {
    position: relative;
    top: -1px;
}

/* 响应式优化 */
@media (max-width: 1400px) {
    .btn-group .btn {
        padding: 0.5rem;
    }
    
    .btn-group .btn i {
        margin-right: 0 !important;
        font-size: 1em;
    }
    
    .btn-group .btn span {
        display: none;
    }
}

@media (min-width: 1401px) {
    .container-fluid {
        max-width: 1600px;
    }
}

/* 排序图标样式 */
.fa-sort-asc::before {
    content: "\f0de";
}

.fa-sort-desc::before {
    content: "\f0dd";
}

/* 搜索框样式 */
.input-group .form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* 复选框样式 */
.form-check-input {
    cursor: pointer;
}

/* 导入区域样式 */
.import-area {
    border: 2px dashed #ccc;
    border-radius: 10px;
    padding: 30px;
    text-align: center;
    background-color: #f8f9fa;
    transition: all 0.3s ease;
}

.import-area.highlight {
    border-color: #007bff;
    background-color: #e9f2fe;
}

.import-icon {
    font-size: 48px;
    color: #adb5bd;
    margin-bottom: 15px;
}

/* 模态框样式优化 */
.modal-content {
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.modal-header {
    border-bottom: 1px solid #eaeaea;
}

.modal-footer {
    border-top: 1px solid #eaeaea;
}
</style>

<!-- 添加SheetJS库 -->
<script src="https://cdnjs.cloudflare.com/ajax/libs/xlsx/0.18.5/xlsx.full.min.js"></script>

<script>
// 增强Excel样式处理功能
function generateStyledWorksheet(headers, data, instructions) {
    // 在数据的前面添加空行，确保第一行是表头
    var fullData = [headers].concat(data);
    
    // 创建基本工作表
    var ws = XLSX.utils.aoa_to_sheet(fullData);
    
    // 设置列宽
    var wscols = [
        {wch: 12}, // 科室编码列宽
        {wch: 20}, // 科室名称列宽
        {wch: 30}  // 备注列宽
    ];
    
    // 设置表头范围 - 帮助Excel识别为表头
    if (!ws['!ref']) ws['!ref'] = 'A1:C' + (fullData.length);
    
    // 创建样式对象
    if (!ws['!cols']) ws['!cols'] = wscols;
    
    // 添加单元格样式
    // 注意：Excel文件的单元格是从1开始的，而不是从0开始
    
    // 为每个单元格添加样式
    for (var i = 0; i < fullData.length; i++) {
        var rowNum = i + 1; // Excel行号从1开始
        
        for (var j = 0; j < 3; j++) { // 只处理A-C三列
            var cellRef = XLSX.utils.encode_cell({r: i, c: j});
            
            // 初始化单元格样式对象
            if (!ws[cellRef].s) ws[cellRef] = { ...ws[cellRef], s: {} };
            
            // 设置边框样式 - 所有单元格都有边框
            ws[cellRef].s = {
                border: {
                    top: { style: 'thin', color: { rgb: "CCCCCC" } },
                    bottom: { style: 'thin', color: { rgb: "CCCCCC" } },
                    left: { style: 'thin', color: { rgb: "CCCCCC" } },
                    right: { style: 'thin', color: { rgb: "CCCCCC" } }
                }
            };
            
            // 表头行样式 - 第一行
            if (i === 0) {
                ws[cellRef].s.fill = { fgColor: { rgb: "4e73df" } }; // 蓝色背景
                ws[cellRef].s.font = { bold: true, color: { rgb: "FFFFFF" } }; // 白色粗体文字
                ws[cellRef].s.alignment = { horizontal: 'center', vertical: 'center' }; // 居中对齐
            } 
            // 数据行样式 - 交替行颜色
            else {
                // 第一行数据使用蓝色风格
                if (i === 1) {
                    ws[cellRef].s.fill = { fgColor: { rgb: "E6F0FF" } }; // 淡蓝色背景
                    ws[cellRef].s.font = { color: { rgb: "333333" } };
                } 
                // 第二行数据使用绿色风格
                else if (i === 2) {
                    ws[cellRef].s.fill = { fgColor: { rgb: "E6FFF0" } }; // 淡绿色背景
                    ws[cellRef].s.font = { color: { rgb: "333333" } };
                }
                // 空白行样式 - 第三行（用于用户输入）
                else {
                    ws[cellRef].s.fill = { fgColor: { rgb: "FFFFFF" } }; // 白色背景
                }
            }
        }
    }
    
    // 添加E列说明文字的列宽和样式
    if (instructions && instructions.length > 0) {
        wscols.push({wch: 5}); // D列占位
        wscols.push({wch: 40}); // E列导入说明
        
        // 添加说明文字到E列
        for (var i = 0; i < instructions.length; i++) {
            var cellRef = XLSX.utils.encode_cell({r: i, c: 4}); // E列
            XLSX.utils.sheet_add_aoa(ws, [[instructions[i]]], {origin: {r: i, c: 4}});
            
            // 为说明文字添加样式
            if (!ws[cellRef]) ws[cellRef] = {};
            if (!ws[cellRef].s) ws[cellRef].s = {};
            
            // 标题样式
            if (i === 0) {
                ws[cellRef].s = { 
                    font: { bold: true, color: { rgb: "4e73df" }, sz: 12 },
                    fill: { fgColor: { rgb: "F8F9FC" } }
                };
            } 
            // 说明内容样式
            else {
                ws[cellRef].s = { 
                    font: { color: { rgb: "555555" } },
                    fill: { fgColor: { rgb: "F8F9FC" } }
                };
            }
        }
    }
    
    // 更新列宽设置
    ws['!cols'] = wscols;
    
    return ws;
}
</script>
{% endblock extra_css %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-12">
            <!-- 统计卡片 - 全新设计带动画效果 -->
            <div class="row mb-4">
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" id="stat-card-1">
                        <div class="stat-card-inner">
                            <div class="stat-card-front">
                                <div class="stat-icon">
                                    <i class="fas fa-hospital"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-title">科室总数</div>
                                    <div class="stat-value">{{ departments|length }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" id="stat-card-2">
                        <div class="stat-card-inner">
                            <div class="stat-card-front">
                                <div class="stat-icon">
                                    <i class="fas fa-bed"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-title">总床位数</div>
                                    <div class="stat-value">{{ total_beds|default:"0" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" id="stat-card-3">
                        <div class="stat-card-inner">
                            <div class="stat-card-front">
                                <div class="stat-icon">
                                    <i class="fas fa-user-md"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-title">工作人员</div>
                                    <div class="stat-value">{{ total_staff|default:"0" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-3 col-md-6 mb-4">
                    <div class="stat-card" id="stat-card-4">
                        <div class="stat-card-inner">
                            <div class="stat-card-front">
                                <div class="stat-icon">
                                    <i class="fas fa-star"></i>
                                </div>
                                <div class="stat-content">
                                    <div class="stat-title">总评价数</div>
                                    <div class="stat-value">{{ total_evaluations|default:"0" }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 主卡片 -->
            <div class="card shadow-sm rounded-3">
                <div class="card-header bg-white py-3">
                    <div class="row align-items-center">
                        <div class="col-md-6">
                            <h1 class="h3 mb-0 text-gray-800">科室管理</h1>
                            <p class="text-muted mb-0 mt-1">管理医院各科室信息及相关配置</p>
                        </div>
                        <div class="col-md-6">
                            <form method="GET" class="d-flex gap-2">
                                <div class="flex-grow-1">
                                    <div class="input-group">
                                        <input type="text" class="form-control" name="search" 
                                               placeholder="搜索科室编码、名称或备注..." 
                                               value="{{ search_query }}">
                                        <button class="btn btn-outline-secondary" type="submit">
                                            <i class="fas fa-search"></i>
                                        </button>
                                        {% if search_query %}
                                        <a href="{% url 'qrmanager:department_list' %}" 
                                           class="btn btn-outline-secondary">
                                            <i class="fas fa-times"></i>
                                        </a>
                                        {% endif %}
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <div class="card-body p-0">
                    <div class="p-3 border-bottom d-flex justify-content-between align-items-center bg-light">
                        <div class="d-flex gap-2">
                            <button type="button" class="btn btn-outline-danger" onclick="bulkDelete()">
                                <i class="fas fa-trash-alt me-2"></i>批量删除
                            </button>
                            <button type="button" class="btn btn-outline-success" onclick="bulkExport()">
                                <i class="fas fa-file-export me-2"></i>导出选中
                            </button>
                            <button type="button" class="btn btn-outline-primary" id="showImportDepartmentModalBtn">
                                <i class="fas fa-file-import me-2"></i>导入科室
                            </button>
                            <button type="button" class="btn btn-outline-info" onclick="exportDepartments()">
                                <i class="fas fa-file-export me-2"></i>导出全部
                            </button>
                        </div>
                        <div>
                            <a href="{% url 'qrmanager:department_create' %}" class="btn btn-primary">
                                <i class="fas fa-plus-circle me-2"></i>新增科室
                            </a>
                        </div>
                    </div>

                    {% if departments %}
                    <form id="bulkForm" method="POST">
                        {% csrf_token %}
                        <input type="hidden" name="action" id="bulkAction" value="">
                        <div class="table-responsive">
                            <table class="table table-hover mb-0">
                                <thead>
                                    <tr>
                                        <th class="ps-4" style="width: 40px;">
                                            <input type="checkbox" class="form-check-input" id="selectAll">
                                        </th>
                                        <th style="width: 50px;">序号</th>
                                        <th style="width: 110px;">
                                            <a href="?sort=code&order={% if sort_by == 'code' and sort_order == 'asc' %}desc{% else %}asc{% endif %}" 
                                               class="text-dark text-decoration-none">
                                                科室编码
                                                {% if sort_by == 'code' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th style="width: 120px;">
                                            <a href="?sort=name&order={% if sort_by == 'name' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                科室名称
                                                {% if sort_by == 'name' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th class="text-center" style="width: 90px;">
                                            <a href="?sort=staff_count&order={% if sort_by == 'staff_count' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                工作人员
                                                {% if sort_by == 'staff_count' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th class="text-center" style="width: 90px;">
                                            <a href="?sort=bed_count&order={% if sort_by == 'bed_count' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                床位数量
                                                {% if sort_by == 'bed_count' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th class="text-center" style="width: 90px;">
                                            <a href="?sort=qrcode_count&order={% if sort_by == 'qrcode_count' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                二维码数
                                                {% if sort_by == 'qrcode_count' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th style="width: 150px;">
                                            <a href="?sort=created_at&order={% if sort_by == 'created_at' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                创建时间
                                                {% if sort_by == 'created_at' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th style="width: 150px;">
                                            <a href="?sort=updated_at&order={% if sort_by == 'updated_at' and sort_order == 'asc' %}desc{% else %}asc{% endif %}"
                                               class="text-dark text-decoration-none">
                                                更新时间
                                                {% if sort_by == 'updated_at' %}
                                                <i class="fas fa-sort-{{ sort_order }}"></i>
                                                {% endif %}
                                            </a>
                                        </th>
                                        <th class="text-end pe-4" style="width: 300px;">操作</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for department in departments %}
                                    <tr>
                                        <td class="ps-4">
                                            <input type="checkbox" class="form-check-input department-checkbox" 
                                                   name="selected_departments" value="{{ department.id }}">
                                        </td>
                                        <td class="text-muted">{{ forloop.counter }}</td>
                                        <td>
                                            <span class="badge rounded-pill bg-primary-subtle text-primary">
                                                {{ department.code }}
                                            </span>
                                        </td>
                                        <td class="fw-medium">{{ department.name }}</td>
                                        <td class="text-center">
                                            <span class="badge bg-info">
                                                <i class="fas fa-user-md me-1"></i>{{ department.staff_count }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-success">
                                                <i class="fas fa-bed me-1"></i>{{ department.bed_count }}
                                            </span>
                                        </td>
                                        <td class="text-center">
                                            <span class="badge bg-warning">
                                                <i class="fas fa-qrcode me-1"></i>{{ department.qrcode_count }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">
                                                <i class="far fa-clock me-1"></i>
                                                {{ department.created_at|date:"Y-m-d H:i" }}
                                            </span>
                                        </td>
                                        <td>
                                            <span class="text-muted">
                                                <i class="far fa-edit me-1"></i>
                                                {{ department.updated_at|date:"Y-m-d H:i" }}
                                            </span>
                                        </td>
                                        <td class="text-end pe-4">
                                            <div class="btn-group">
                                                <a href="{% url 'qrmanager:bed_list' %}?department={{ department.id }}" 
                                                   class="btn btn-sm btn-outline-primary">
                                                    <i class="fas fa-procedures me-1"></i>床位管理
                                                </a>
                                                <a href="{% url 'qrmanager:qrcode_list' %}?department={{ department.id }}" 
                                                   class="btn btn-sm btn-outline-warning">
                                                    <i class="fas fa-qrcode me-1"></i>二维码管理
                                                </a>
                                                <a href="{% url 'qrmanager:department_edit' department.pk %}" 
                                                   class="btn btn-sm btn-outline-info">
                                                    <i class="fas fa-pencil-alt me-1"></i>编辑
                                                </a>
                                                <button type="button" class="btn btn-sm btn-outline-danger delete-department-btn"
                                                        data-department-id="{{ department.pk }}"
                                                        data-department-name="{{ department.name|escapejs }}"
                                                        data-bed-count="{{ department.beds.count }}">
                                                    <i class="fas fa-trash-alt me-1"></i>删除
                                                </button>
                                            </div>
                                        </td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                    </form>
                    {% else %}
                    <div class="text-center py-5">
                        <img src="{% static 'images/empty.svg' %}" alt="暂无数据" class="mb-3" style="width: 120px;">
                        <h5 class="text-muted mb-3">暂无科室信息</h5>
                        <a href="{% url 'qrmanager:department_create' %}" class="btn btn-primary">
                            <i class="fas fa-plus me-2"></i>添加第一个科室
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 导入科室模态框 -->
<div class="modal" id="importDepartmentModal" tabindex="-1" role="dialog" aria-labelledby="importDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg" role="document">
        <div class="modal-content">
            <div class="modal-header bg-light">
                <h5 class="modal-title" id="importDepartmentModalLabel">
                    <i class="fas fa-file-import me-2 text-primary"></i>导入科室数据
                </h5>
                <button type="button" class="btn-close" onclick="hideModal('importDepartmentModal')" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="alert alert-info mb-4">
                    <div class="d-flex">
                        <div class="me-3">
                            <i class="fas fa-info-circle fa-2x text-info"></i>
                        </div>
                        <div>
                            <h5 class="alert-heading">导入说明</h5>
                            <div class="mb-2">请按照以下规范准备导入数据：</div>
                            <ul class="mb-2">
                                <li><strong>必填字段</strong>：科室编码、科室名称（这两个字段必须填写且不能重复）</li>
                                <li><strong>可选字段</strong>：备注</li>
                                <li><strong>数据格式</strong>：
                                    <ul>
                                        <li>科室编码：字母或数字，最多20个字符，建议使用大写字母和数字组合</li>
                                        <li>科室名称：1-50个字符</li>
                                        <li>备注：最多200个字符</li>
                                    </ul>
                                </li>
                                <li><strong>注意事项</strong>：已存在相同编码的科室将被跳过；导入前请仔细核对数据</li>
                            </ul>
                            <div>您可以下载模板并按照格式填写数据后导入。</div>
                        </div>
                    </div>
                </div>
                
                <div class="card mb-4 shadow-sm">
                    <div class="card-header bg-light">
                        <h6 class="mb-0"><i class="fas fa-table me-2 text-primary"></i>数据格式示例</h6>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-sm table-bordered table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th>科室编码</th>
                                        <th>科室名称</th>
                                        <th>备注</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <tr>
                                        <td>NKBQ</td>
                                        <td>内科病区</td>
                                        <td>内科相关病区</td>
                                    </tr>
                                    <tr>
                                        <td>WKBQ</td>
                                        <td>外科病区</td>
                                        <td>外科相关病区</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                        <div class="d-flex justify-content-center mt-3">
                            <a href="#" id="downloadDepartmentTemplateBtn" class="btn btn-outline-primary">
                                <i class="fas fa-download me-2"></i>下载导入模板
                            </a>
                        </div>
                    </div>
                </div>
                
                <form id="importDepartmentForm" enctype="multipart/form-data" class="mt-4">
                    {% csrf_token %}
                    <div class="import-area" id="departmentDropZone">
                        <div class="import-icon">
                            <i class="fas fa-file-upload"></i>
                        </div>
                        <p class="mb-3">拖拽文件到此处或点击选择文件</p>
                        <input type="file" name="file" id="departmentFileInput" class="d-none" accept=".xlsx,.xls,.csv">
                        <button type="button" class="btn btn-outline-primary" id="browseDepartmentBtn">
                            <i class="fas fa-folder-open me-2"></i>选择文件
                        </button>
                        <div id="departmentFileInfo" class="mt-3 d-none">
                            <p class="text-success mb-1">已选择文件：<span id="departmentFileName"></span></p>
                            <button type="button" class="btn btn-sm btn-outline-danger" id="removeDepartmentFileBtn">
                                <i class="fas fa-times me-1"></i>移除
                            </button>
                        </div>
                    </div>
                </form>
            </div>
            <div class="modal-footer bg-light">
                <button type="button" class="btn btn-secondary" onclick="hideModal('importDepartmentModal')" id="cancelDepartmentImportBtn">取消</button>
                <button type="button" class="btn btn-primary" id="importDepartmentBtn" disabled>
                    <i class="fas fa-upload me-2"></i>开始导入
                </button>
            </div>
        </div>
    </div>
</div>

<!-- 删除科室确认模态框 -->
<div class="modal fade" id="deleteDepartmentModal" tabindex="-1" aria-labelledby="deleteDepartmentModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="deleteDepartmentModalLabel">
                    <i class="fas fa-exclamation-triangle me-2"></i>删除科室确认
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="text-center mb-4">
                    <div class="mb-3">
                        <i class="fas fa-trash-alt text-danger fa-3x"></i>
                    </div>
                    <p class="lead" id="deleteConfirmText">确定要删除科室吗？</p>
                    <div class="alert alert-danger">
                        <p class="mb-1"><strong>警告：此操作将同时删除：</strong></p>
                        <ul class="list-unstyled">
                            <li><i class="fas fa-bed text-danger me-2"></i>关联的床位：<span id="bedCount">0</span></li>
                            <li><i class="fas fa-qrcode text-danger me-2"></i>关联的二维码</li>
                            <li><i class="fas fa-print text-danger me-2"></i>关联的打印模板</li>
                            <li><i class="fas fa-clipboard-list text-danger me-2"></i>关联的评价记录</li>
                        </ul>
                    </div>
                    
                    <div id="staffInfoContainer" class="alert alert-warning mt-3 d-none">
                        <p class="mb-1"><strong><i class="fas fa-exclamation-triangle me-2"></i>该科室有 <span id="staffCount">0</span> 名关联的工作人员</strong></p>
                        <p class="mb-0 text-danger"><strong>科室有关联的工作人员，不允许删除！请先解除工作人员与科室的关联。</strong></p>
                        <div class="mt-2">
                            <button type="button" class="btn btn-sm btn-outline-secondary" data-bs-toggle="collapse" data-bs-target="#staffListCollapse">
                                查看关联工作人员列表
                            </button>
                        </div>
                        <div class="collapse mt-2" id="staffListCollapse">
                            <div class="card card-body bg-light">
                                <ul class="list-group" id="staffList">
                                    <!-- 工作人员列表将通过AJAX加载 -->
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                
                <form id="deleteDepartmentForm" method="post">
                    {% csrf_token %}
                    <input type="hidden" id="departmentId" name="department_id" value="">
                    
                    <!-- 添加错误提示区域 -->
                    <div id="deleteErrorAlert" class="alert alert-danger mb-3 d-none">
                        <i class="fas fa-exclamation-circle me-2"></i>
                        <span id="deleteErrorMessage"></span>
                    </div>
                    
                    <div class="mb-3" id="passwordContainer">
                        <label for="password" class="form-label">请输入您的密码以确认删除</label>
                        <input type="password" class="form-control" id="password" name="password" required>
                        <div class="form-text text-muted">为了安全起见，请输入您的密码以确认此操作</div>
                    </div>
                    
                    <div class="d-flex justify-content-center gap-2 mt-4">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-danger" id="confirmDeleteBtn">
                            <i class="fas fa-trash-alt me-2"></i>确认删除
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<script>
// 显示导入科室模态框
function showImportDepartmentModal() {
    console.log('显示导入科室模态框');
    // 清空文件输入
    $('#departmentFileInput').val('');
    $('#departmentFileInfo').addClass('d-none');
    $('#importDepartmentBtn').prop('disabled', true);
    
    // 显示模态框
    var importDepartmentModal = new bootstrap.Modal(document.getElementById('importDepartmentModal'));
    importDepartmentModal.show();
}

// 将函数赋值给window对象，确保全局可用
window.showImportDepartmentModal = showImportDepartmentModal;

// 隐藏模态框
window.hideModal = function(modalId) {
    var modalElement = document.getElementById(modalId);
    var modal = bootstrap.Modal.getInstance(modalElement);
    if (modal) {
        modal.hide();
    }
}

document.addEventListener('DOMContentLoaded', function() {
    // 添加导入科室按钮点击事件
    $('#showImportDepartmentModalBtn').on('click', function() {
        showImportDepartmentModal();
    });
    
    // 全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            const checkboxes = document.getElementsByClassName('department-checkbox');
            for (let checkbox of checkboxes) {
                checkbox.checked = this.checked;
            }
        });

        // 添加单个复选框的事件监听，当所有复选框都被选中时，全选框也要被选中
        const checkboxes = document.getElementsByClassName('department-checkbox');
        Array.from(checkboxes).forEach(checkbox => {
            checkbox.addEventListener('change', function() {
                const allChecked = Array.from(checkboxes).every(cb => cb.checked);
                selectAllCheckbox.checked = allChecked;
            });
        });
    }

    // 批量删除
    window.bulkDelete = function() {
        const selected = document.querySelectorAll('.department-checkbox:checked');
        if (selected.length === 0) {
            alert('请至少选择一个科室');
            return;
        }
        
        if (confirm(`确定要删除选中的 ${selected.length} 个科室吗？`)) {
            document.getElementById('bulkAction').value = 'delete';
            document.getElementById('bulkForm').submit();
        }
    }

    // 批量导出
    window.bulkExport = function() {
        const selected = document.querySelectorAll('.department-checkbox:checked');
        if (selected.length === 0) {
            alert('请至少选择一个科室');
            return;
        }
        
        document.getElementById('bulkAction').value = 'export';
        document.getElementById('bulkForm').submit();
    }

    // 导入科室相关JS
    $(document).ready(function() {
        // 文件选择功能
        $('#browseDepartmentBtn').click(function() {
            $('#departmentFileInput').click();
        });
        
        // 文件选择变化
        $('#departmentFileInput').change(function() {
            handleDepartmentFileSelect(this.files[0]);
        });
        
        // 移除选中的文件
        $('#removeDepartmentFileBtn').click(function() {
            $('#departmentFileInput').val('');
            $('#departmentFileInfo').addClass('d-none');
            $('#importDepartmentBtn').prop('disabled', true);
        });
        
        // 拖放区域功能
        const departmentDropZone = document.getElementById('departmentDropZone');
        
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            departmentDropZone.addEventListener(eventName, preventDefaults, false);
        });
        
        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }
        
        ['dragenter', 'dragover'].forEach(eventName => {
            departmentDropZone.addEventListener(eventName, highlight, false);
        });
        
        ['dragleave', 'drop'].forEach(eventName => {
            departmentDropZone.addEventListener(eventName, unhighlight, false);
        });
        
        function highlight() {
            departmentDropZone.classList.add('highlight');
        }
        
        function unhighlight() {
            departmentDropZone.classList.remove('highlight');
        }
        
        departmentDropZone.addEventListener('drop', handleDrop, false);
        
        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            if (files.length) {
                handleDepartmentFileSelect(files[0]);
            }
        }
        
        // 处理文件选择
        function handleDepartmentFileSelect(file) {
            if (!file) return;
            
            // 检查文件类型
            const validExtensions = ['.xlsx', '.xls', '.csv'];
            const fileName = file.name;
            const fileExt = fileName.substring(fileName.lastIndexOf('.')).toLowerCase();
            
            if (!validExtensions.includes(fileExt)) {
                alert('请选择Excel(.xlsx/.xls)或CSV文件');
                return;
            }
            
            // 显示文件信息
            $('#departmentFileName').text(fileName);
            $('#departmentFileInfo').removeClass('d-none');
            $('#importDepartmentBtn').prop('disabled', false);
        }
        
        // 导入按钮点击事件
        $('#importDepartmentBtn').click(function() {
            const file = $('#departmentFileInput')[0].files[0];
            if (!file) {
                alert('请选择文件');
                return;
            }

            const formData = new FormData();
            formData.append('file', file);
            formData.append('csrfmiddlewaretoken', $('input[name="csrfmiddlewaretoken"]').val());
            
            // 显示加载提示
            $(this).prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>导入中...');
            $('#cancelDepartmentImportBtn').prop('disabled', true);
            
            // 发送请求
            $.ajax({
                url: "{% url 'qrmanager:department_import' %}",
                type: 'POST',
                data: formData,
                processData: false,
                contentType: false,
                success: function(response) {
                    alert(response.message);
                    window.location.reload();
                },
                error: function(xhr) {
                    let errorMsg = '导入失败';
                    try {
                        const response = JSON.parse(xhr.responseText);
                        if (response.error) {
                            errorMsg = response.error;
                        }
                    } catch (e) {
                        console.error('解析错误信息失败', e);
                    }
                    alert(errorMsg);
                    
                    // 恢复按钮状态
                    $('#importDepartmentBtn').prop('disabled', false).html('<i class="fas fa-upload me-2"></i>开始导入');
                    $('#cancelDepartmentImportBtn').prop('disabled', false);
                }
            });
        });
        
        // 下载模板
        $('#downloadDepartmentTemplateBtn').click(function(e) {
            e.preventDefault();
            try {
                // 使用更简单直接的方式创建工作簿和工作表
                var wb = XLSX.utils.book_new();
                
                // 创建工作表数据 - 标题行和示例数据
                var wsData = [
                    ['科室编码', '科室名称', '备注'],  // 标题行
                    ['NKBQ', '内科病区', '内科相关病区'],  // 示例数据1
                    ['WKBQ', '外科病区', '外科相关病区'],  // 示例数据2
                    ['', '', '']  // 空行供用户填写
                ];
                
                // 创建指导说明
                var instructions = [
                    '导入说明',
                    '1. 科室编码：必填，字母或数字，最多20个字符',
                    '2. 科室名称：必填，1-50个字符',
                    '3. 备注：选填，最多200个字符',
                    '4. 已存在相同编码的科室将被跳过',
                    '5. 导入前请仔细核对数据',
                    '6. 建议科室编码使用大写字母和数字组合'
                ];
                
                // 将说明添加到数据的E列
                for (var i = 0; i < instructions.length && i < wsData.length; i++) {
                    if (i >= wsData.length) {
                        // 如果说明比数据行多，添加新行
                        var newRow = ['', '', ''];
                        newRow[4] = instructions[i]; // E列
                        wsData.push(newRow);
                    } else {
                        // 扩展现有行添加E列
                        if (!wsData[i][4]) {
                            wsData[i][4] = instructions[i];
                        }
                    }
                }
                
                // 如果说明行比数据行更多，继续添加
                for (var i = wsData.length; i < instructions.length; i++) {
                    var newRow = ['', '', ''];
                    newRow[4] = instructions[i];
                    wsData.push(newRow);
                }
                
                // 创建工作表
                var ws = XLSX.utils.aoa_to_sheet(wsData);
                
                // 设置列宽
                ws['!cols'] = [
                    {wch: 12}, // A列 - 科室编码
                    {wch: 20}, // B列 - 科室名称
                    {wch: 30}, // C列 - 备注
                    {wch: 5},  // D列 - 空白分隔
                    {wch: 40}  // E列 - 导入说明
                ];
                
                // 专业样式处理
                // 表头样式 - 蓝色背景，白色文字
                if (!ws.A1.s) ws.A1.s = {};
                if (!ws.B1.s) ws.B1.s = {};
                if (!ws.C1.s) ws.C1.s = {};
                
                var headerStyle = {
                    fill: {
                        fgColor: {rgb: "4e73df"}  // 蓝色背景
                    },
                    font: {
                        color: {rgb: "FFFFFF"},   // 白色文字
                        bold: true,               // 粗体
                        sz: 12                    // 字体大小
                    },
                    alignment: {
                        horizontal: 'center',     // 居中对齐
                        vertical: 'center'
                    },
                    border: {                     // 边框
                        top: {style: 'thin', color: {rgb: "CCCCCC"}},
                        bottom: {style: 'thin', color: {rgb: "CCCCCC"}},
                        left: {style: 'thin', color: {rgb: "CCCCCC"}},
                        right: {style: 'thin', color: {rgb: "CCCCCC"}}
                    }
                };
                
                // 为每个单元格添加样式
                for (var i = 0; i < wsData.length; i++) {
                    for (var j = 0; j < 5; j++) { // 包括A-E五列
                        var cellRef = XLSX.utils.encode_cell({r: i, c: j});
                        if (!ws[cellRef]) ws[cellRef] = {v: "", t: "s"};
                        if (!ws[cellRef].s) ws[cellRef].s = {};
                        
                        // 表头行样式
                        if (i === 0 && j < 3) { // 只给数据列(A-C)的表头设置蓝色
                            ws[cellRef].s = headerStyle;
                        }
                        // 第一行数据 - 浅蓝色背景
                        else if (i === 1 && j < 3) {
                            ws[cellRef].s = {
                                fill: {fgColor: {rgb: "E6F0FF"}}, // 浅蓝色背景
                                font: {color: {rgb: "333333"}},
                                border: {
                                    top: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    bottom: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    left: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    right: {style: 'thin', color: {rgb: "CCCCCC"}}
                                }
                            };
                        }
                        // 第二行数据 - 浅绿色背景
                        else if (i === 2 && j < 3) {
                            ws[cellRef].s = {
                                fill: {fgColor: {rgb: "E6FFF0"}}, // 浅绿色背景
                                font: {color: {rgb: "333333"}},
                                border: {
                                    top: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    bottom: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    left: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    right: {style: 'thin', color: {rgb: "CCCCCC"}}
                                }
                            };
                        }
                        // 所有其他单元格
                        else if (j < 3) {
                            ws[cellRef].s = {
                                border: {
                                    top: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    bottom: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    left: {style: 'thin', color: {rgb: "CCCCCC"}},
                                    right: {style: 'thin', color: {rgb: "CCCCCC"}}
                                }
                            };
                        }
                        // E列说明样式
                        else if (j === 4) {
                            if (i === 0) {
                                // 说明标题 - 蓝色粗体
                                ws[cellRef].s = {
                                    font: {bold: true, color: {rgb: "4e73df"}, sz: 12},
                                    fill: {fgColor: {rgb: "F8F9FC"}}
                                };
                            } else {
                                // 说明内容 - 灰色普通文字
                                ws[cellRef].s = {
                                    font: {color: {rgb: "555555"}},
                                    fill: {fgColor: {rgb: "F8F9FC"}}
                                };
                            }
                        }
                    }
                }
                
                // 添加工作表到工作簿
                XLSX.utils.book_append_sheet(wb, ws, "科室导入模板");
                
                // 添加自动筛选功能
                ws['!autofilter'] = {ref: 'A1:C4'}; // 让Excel自动识别为表格
                
                // 生成并下载文件
                XLSX.writeFile(wb, "科室导入模板.xlsx");
                
                console.log('Excel模板下载已触发');
                
                // 显示下载成功提示
                var successToast = document.createElement('div');
                successToast.style.position = 'fixed';
                successToast.style.bottom = '20px';
                successToast.style.right = '20px';
                successToast.style.backgroundColor = '#1cc88a';
                successToast.style.color = 'white';
                successToast.style.padding = '10px 20px';
                successToast.style.borderRadius = '4px';
                successToast.style.boxShadow = '0 2px 10px rgba(0,0,0,0.1)';
                successToast.style.zIndex = '9999';
                successToast.innerHTML = '<i class="fas fa-check-circle mr-2"></i> 模板下载成功，请在Excel中打开并编辑';
                document.body.appendChild(successToast);
                
                // 3秒后自动移除提示
                setTimeout(function() {
                    document.body.removeChild(successToast);
                }, 3000);
                
            } catch (e) {
                console.error('下载模板失败:', e);
                alert('下载模板失败: ' + e.message);
                
                // 创建备用下载方法 - 使用HTML表格
                var win = window.open('', '_blank');
                win.document.write('<html><head>');
                win.document.write('<title>科室导入模板</title>');
                win.document.write('<style>');
                win.document.write('body { font-family: Arial, sans-serif; margin: 20px; background-color: #f8f9fc; }');
                win.document.write('h3 { color: #4e73df; margin-bottom: 20px; }');
                win.document.write('table { border-collapse: collapse; width: 100%; max-width: 800px; margin: 20px 0; background-color: white; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08); }');
                win.document.write('th { background: linear-gradient(135deg, #4e73df, #224abe); color: white; padding: 12px; text-align: left; font-weight: 600; }');
                win.document.write('td { padding: 10px; border: 1px solid #e3e6f0; }');
                win.document.write('tr:nth-child(odd) { background-color: #f8f9fc; }');
                win.document.write('tr:hover { background-color: #eaecf4; }');
                win.document.write('.instructions { background-color: #f8f9fc; padding: 15px; border-left: 4px solid #4e73df; margin-top: 20px; }');
                win.document.write('.example-row-1 { background-color: #E6F0FF; }');
                win.document.write('.example-row-2 { background-color: #E6FFF0; }');
                win.document.write('.download-btn { display: inline-block; background-color: #4e73df; color: white; padding: 10px 16px; text-decoration: none; border-radius: 4px; margin-top: 20px; cursor: pointer; }');
                win.document.write('.download-btn:hover { background-color: #2e59d9; }');
                win.document.write('.note { margin-top: 30px; padding: 15px; background-color: #fff3cd; border-left: 4px solid #ffc107; }');
                win.document.write('</style>');
                win.document.write('</head><body>');
                win.document.write('<h3>科室导入模板</h3>');
                win.document.write('<p>请按照下表格式准备Excel文件用于导入。表格应包含以下列：</p>');
                
                // 创建彩色示例表格
                win.document.write('<table>');
                win.document.write('<thead>');
                win.document.write('<tr style="background: linear-gradient(135deg, #4e73df, #224abe); color: white;">');
                win.document.write('<th>科室编码</th>');
                win.document.write('<th>科室名称</th>');
                win.document.write('<th>备注</th>');
                win.document.write('</tr>');
                win.document.write('</thead>');
                win.document.write('<tbody>');
                win.document.write('<tr class="example-row-1">');
                win.document.write('<td>NKBQ</td>');
                win.document.write('<td>内科病区</td>');
                win.document.write('<td>内科相关病区</td>');
                win.document.write('</tr>');
                win.document.write('<tr class="example-row-2">');
                win.document.write('<td>WKBQ</td>');
                win.document.write('<td>外科病区</td>');
                win.document.write('<td>外科相关病区</td>');
                win.document.write('</tr>');
                win.document.write('<tr>');
                win.document.write('<td></td>');
                win.document.write('<td></td>');
                win.document.write('<td></td>');
                win.document.write('</tr>');
                win.document.write('</tbody>');
                win.document.write('</table>');
                
                // 添加导入说明
                win.document.write('<div class="instructions">');
                win.document.write('<h4 style="color: #4e73df; margin-top: 0;">导入说明</h4>');
                win.document.write('<ol>');
                win.document.write('<li><strong>科室编码</strong>：必填，字母或数字，最多20个字符</li>');
                win.document.write('<li><strong>科室名称</strong>：必填，1-50个字符</li>');
                win.document.write('<li><strong>备注</strong>：选填，最多200个字符</li>');
                win.document.write('<li>已存在相同编码的科室将被跳过</li>');
                win.document.write('<li>导入前请仔细核对数据</li>');
                win.document.write('<li>建议科室编码使用大写字母和数字组合</li>');
                win.document.write('</ol>');
                win.document.write('</div>');
                
                // 添加下载CSV选项
                win.document.write('<div class="note">');
                win.document.write('<p><strong>提示</strong>：如果Excel下载未正常工作，您可以复制上表内容到Excel中，或使用下面的按钮下载CSV文件。</p>');
                win.document.write('<button class="download-btn" onclick="downloadCSV()">下载CSV模板</button>');
                win.document.write('</div>');
                
                // 添加CSV下载功能
                win.document.write('<script>');
                win.document.write('function downloadCSV() {');
                win.document.write('    var csvContent = "科室编码,科室名称,备注\\nNKBQ,内科病区,内科相关病区\\nWKBQ,外科病区,外科相关病区\\n";');
                win.document.write('    var blob = new Blob(["\ufeff" + csvContent], { type: "text/csv;charset=utf-8;" });');
                win.document.write('    var link = document.createElement("a");');
                win.document.write('    link.href = URL.createObjectURL(blob);');
                win.document.write('    link.download = "科室导入模板.csv";');
                win.document.write('    link.style.display = "none";');
                win.document.write('    document.body.appendChild(link);');
                win.document.write('    link.click();');
                win.document.write('    document.body.removeChild(link);');
                win.document.write('}');
                win.document.write('<\/script>');
                
                win.document.write('</body></html>');
                win.document.title = '科室导入模板';
            }
        });
    });

    // 支持原来的导入科室功能，但不再使用
    window.importDepartments = function() {
        // 使用新的模态框方式
        showImportDepartmentModal();
    }

    // 导出全部科室
    window.exportDepartments = function() {
        window.location.href = "{% url 'qrmanager:department_export' %}";
    }

    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });

    // 初始化删除按钮事件
    $('.delete-department-btn').on('click', function() {
        const departmentId = $(this).data('department-id');
        const departmentName = $(this).data('department-name');
        const bedCount = $(this).data('bed-count');
        
        showDeleteModal(departmentId, departmentName, bedCount);
    });

    // 处理删除表单提交
    $('#deleteDepartmentForm').on('submit', function(e) {
        e.preventDefault();
        
        // 隐藏之前的错误信息
        $('#deleteErrorAlert').addClass('d-none');
        
        // 获取表单数据
        const form = $(this);
        const url = form.attr('action');
        const formData = form.serialize();
        
        // 禁用提交按钮，显示加载状态
        $('#confirmDeleteBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>处理中...');
        
        // 发送AJAX请求
        $.ajax({
            url: url,
            type: 'POST',
            data: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest'
            },
            success: function(response) {
                if (response.status === 'success') {
                    // 关闭模态框
                    bootstrap.Modal.getInstance(document.getElementById('deleteDepartmentModal')).hide();
                    
                    // 显示成功消息
                    showToast('success', response.message);
                    
                    // 刷新页面
                    setTimeout(function() {
                        window.location.reload();
                    }, 1000);
                } else {
                    // 显示错误消息
                    $('#deleteErrorMessage').text(response.message);
                    $('#deleteErrorAlert').removeClass('d-none');
                    
                    // 恢复提交按钮
                    $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-trash-alt me-2"></i>确认删除');
                }
            },
            error: function(xhr, status, error) {
                // 显示错误消息
                $('#deleteErrorMessage').text('请求失败，请稍后重试');
                $('#deleteErrorAlert').removeClass('d-none');
                
                // 恢复提交按钮
                $('#confirmDeleteBtn').prop('disabled', false).html('<i class="fas fa-trash-alt me-2"></i>确认删除');
                
                console.error('删除请求失败:', error);
            }
        });
    });
});

// 获取CSRF Token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

// 显示删除科室确认模态框
function showDeleteModal(departmentId, departmentName, bedCount) {
    console.log('显示删除模态框', departmentId, departmentName, bedCount);
    
    // 设置模态框内容
    $('#deleteConfirmText').text(`确定要删除科室 "${departmentName}" 吗？`);
    $('#departmentId').val(departmentId);
    $('#bedCount').text(bedCount);
    
    // 清空密码字段
    $('#password').val('');
    
    // 设置表单提交地址
    $('#deleteDepartmentForm').attr('action', `/departments/${departmentId}/delete/`);
    
    // 加载关联的工作人员信息
    $.ajax({
        url: `/api/department/${departmentId}/staff/`,
        type: 'GET',
        dataType: 'json',
        success: function(data) {
            console.log('获取到的工作人员数据:', data);
            const staffCount = data.staff.length;
            $('#staffCount').text(staffCount);
            
            if (staffCount > 0) {
                // 显示工作人员信息区域
                $('#staffInfoContainer').removeClass('d-none');
                
                // 填充工作人员列表
                const staffList = $('#staffList');
                staffList.empty();
                
                data.staff.forEach(function(staff) {
                    staffList.append(`<li class="list-group-item">${staff.work_number} - ${staff.name}</li>`);
                });
                
                // 禁用删除按钮和密码输入框
                $('#confirmDeleteBtn').prop('disabled', true);
                $('#passwordContainer').addClass('d-none');
            } else {
                // 隐藏工作人员信息区域
                $('#staffInfoContainer').addClass('d-none');
                
                // 启用删除按钮和密码输入框
                $('#confirmDeleteBtn').prop('disabled', false);
                $('#passwordContainer').removeClass('d-none');
            }
            
            // 显示模态框
            var myModal = new bootstrap.Modal(document.getElementById('deleteDepartmentModal'));
            myModal.show();
        },
        error: function(xhr, status, error) {
            console.error('获取工作人员信息失败:', error);
            $('#staffInfoContainer').addClass('d-none');
            
            // 启用删除按钮和密码输入框
            $('#confirmDeleteBtn').prop('disabled', false);
            $('#passwordContainer').removeClass('d-none');
            
            // 显示模态框
            var myModal = new bootstrap.Modal(document.getElementById('deleteDepartmentModal'));
            myModal.show();
        }
    });
}

// 显示Toast消息
function showToast(type, message) {
    // 创建Toast元素
    const toastId = 'toast-' + Date.now();
    const toastHtml = `
        <div id="${toastId}" class="toast align-items-center text-white bg-${type === 'success' ? 'success' : 'danger'} border-0" role="alert" aria-live="assertive" aria-atomic="true">
            <div class="d-flex">
                <div class="toast-body">
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'exclamation-circle'} me-2"></i>
                    ${message}
                </div>
                <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast" aria-label="Close"></button>
            </div>
        </div>
    `;
    
    // 添加到页面
    if (!document.getElementById('toast-container')) {
        const toastContainer = document.createElement('div');
        toastContainer.id = 'toast-container';
        toastContainer.className = 'toast-container position-fixed bottom-0 end-0 p-3';
        document.body.appendChild(toastContainer);
    }
    
    document.getElementById('toast-container').innerHTML += toastHtml;
    
    // 显示Toast
    const toastElement = document.getElementById(toastId);
    const toast = new bootstrap.Toast(toastElement, {
        autohide: true,
        delay: 3000
    });
    toast.show();
    
    // 自动移除
    toastElement.addEventListener('hidden.bs.toast', function() {
        this.remove();
    });
}
</script>

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化工具提示
    var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'))
    var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
        return new bootstrap.Tooltip(tooltipTriggerEl)
    });
});
</script>
{% endblock extra_js %}
{% endblock content %} 