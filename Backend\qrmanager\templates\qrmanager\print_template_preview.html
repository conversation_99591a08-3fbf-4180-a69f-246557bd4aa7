{% extends "qrmanager/base.html" %}
{% load static %}
{% load qrmanager_tags %}

{% block title %}打印模板预览{% endblock %}

{% block extra_css %}
<style>
/* 打印样式 */
@media print {
    @page {
        size: {{ object.print_width }}mm {{ object.print_height }}mm !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    html, body {
        width: {{ object.print_width }}mm !important;
        height: {{ object.print_height }}mm !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
    }

    /* 隐藏所有预览内容 */
    .preview-area, header, footer, nav {
        display: none !important;
    }

    /* 显示打印区域 */
    .print-area {
        display: block !important;
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: {{ object.print_width }}mm !important;
        height: {{ object.print_height }}mm !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow: visible !important;
        background: none !important;
    }

    /* 背景图片样式 */
    .print-background {
        position: absolute !important;
        top: 0 !important;
        left: 0 !important;
        width: {{ object.print_width }}mm !important;
        height: {{ object.print_height }}mm !important;
        object-fit: contain !important;
    }

    /* 二维码样式 */
    .print-qr {
        position: absolute !important;
        left: {{ object.qr_position_x }}mm !important;
        top: {{ object.qr_position_y }}mm !important;
        width: {{ object.qr_size }}mm !important;
        height: {{ object.qr_size }}mm !important;
        transform: translate(-50%, -50%) !important;
    }
}

/* 预览样式 */
.print-area {
    display: none;
}

.preview-container {
    border: 1px solid #dee2e6;
    border-radius: 8px;
    background-color: #f8f9fa;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    position: relative;
    width: 500px;
    height: 707px;
    margin: 0 auto;
    overflow: hidden;
}

.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0,0,0,.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,.05) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
}

.preview-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    object-fit: contain;
    z-index: 1;
}

.preview-qr {
    position: absolute;
    z-index: 2;
    background-color: white;
    padding: 2px;
    box-shadow: 0 0 5px rgba(0,0,0,0.1);
}

.preview-qr img {
    width: 100%;
    height: 100%;
    display: block;
}

.qr-preview-border {
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    border: 2px dashed #0d6efd;
    border-radius: 4px;
    pointer-events: none;
    background-color: rgba(13, 110, 253, 0.05);
}
</style>
{% endblock %}

{% block content %}
<!-- 打印内容（打印时只显示这部分） -->
<div class="print-area">
    {% if object.background_image %}
    <img src="{{ object.background_image.url }}" class="print-background">
    {% endif %}
    <img src="data:image/png;base64,{{ qr_image }}" class="print-qr">
</div>

<!-- 预览界面（打印时隐藏） -->
<div class="preview-area">
    <div class="container-fluid px-4">
        <div class="row justify-content-center">
            <div class="col-lg-10">
                <div class="card">
                    <div class="card-header bg-white py-3">
                        <div class="d-flex justify-content-between align-items-center">
                            <h1 class="h3 mb-0">打印模板预览</h1>
                            <div class="btn-group">
                                <a href="{% url 'qrmanager:print_template_list' %}" class="btn btn-outline-secondary">
                                    <i class="fas fa-arrow-left me-2"></i>返回模板列表
                                </a>
                                <button onclick="printTemplate()" class="btn btn-primary">
                                    <i class="fas fa-print me-2"></i>打印预览
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <!-- 左侧：模板信息 -->
                            <div class="col-md-4">
                                <div class="info-section">
                                    <h5 class="border-bottom pb-2 mb-3">模板信息</h5>
                                    <table class="table table-borderless">
                                        <tr>
                                            <th class="ps-0">模板名称：</th>
                                            <td>{{ object.name }}</td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0">所属科室：</th>
                                            <td>{{ object.department.name }}</td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0">打印尺寸：</th>
                                            <td>{{ object.print_width }} × {{ object.print_height }}毫米</td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0">二维码尺寸：</th>
                                            <td>{{ object.qr_size }}毫米</td>
                                        </tr>
                                        <tr>
                                            <th class="ps-0">二维码位置：</th>
                                            <td>X:{{ object.qr_position_x }}毫米, Y:{{ object.qr_position_y }}毫米</td>
                                        </tr>
                                    </table>
                                </div>
                            </div>
                            
                            <!-- 右侧：预览区域 -->
                            <div class="col-md-8">
                                <div class="preview-container">
                                    <div class="grid-background"></div>
                                    {% if object.background_image %}
                                    <img src="{{ object.background_image.url }}" alt="背景图片预览" class="preview-background">
                                    {% endif %}
                                    <div class="preview-qr">
                                        <img src="data:image/png;base64,{{ qr_image }}" alt="二维码预览">
                                        <div class="qr-preview-border"></div>
                                    </div>
                                </div>
                                <div class="text-center mt-3">
                                    <small class="text-muted">预览效果（等比缩放）</small><br>
                                    <small class="text-info">网格背景每格代表1厘米</small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script src="{% static 'qrmanager/js/qrcode-unified.js' %}"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 使用统一模块更新预览
    const container = document.querySelector('.preview-container');
    const qrPreview = container.querySelector('.preview-qr');
    
    QRCodeUnified.updatePreview({
        container: container,
        qrElement: qrPreview,
        printWidth: {{ object.print_width }},
        printHeight: {{ object.print_height }},
        qrX: {{ object.qr_position_x }},
        qrY: {{ object.qr_position_y }},
        qrSize: {{ object.qr_size }},
        containerMaxWidth: 500,
        containerMaxHeight: 707
    });
});

// 替换原有的print方法
function printTemplate() {
    // 使用统一模块进行打印
    QRCodeUnified.print({
        width: {{ object.print_width }},
        height: {{ object.print_height }},
        qrX: {{ object.qr_position_x }},
        qrY: {{ object.qr_position_y }},
        qrSize: {{ object.qr_size }},
        backgroundImage: {% if object.background_image %}'{{ object.background_image.url }}'{% else %}null{% endif %},
        qrImage: 'data:image/png;base64,{{ qr_image }}'
    }, function() {
        // 打印完成后的回调
        console.log('打印完成');
    });
}
</script>
{% endblock %} 