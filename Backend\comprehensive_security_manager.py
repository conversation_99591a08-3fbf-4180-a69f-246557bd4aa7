#!/usr/bin/env python
"""
综合安全管理工具 - 基于二维码认证的完整安全解决方案
提供命令行和Web界面来管理二维码安全系统
"""

import os
import sys
import django
import argparse
import json
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.core.cache import cache
from qrcode_based_security import get_qrcode_security_stats, get_ip_security_profile

class ComprehensiveSecurityManager:
    """综合安全管理器"""
    
    def __init__(self):
        self.cache = cache
    
    def get_security_dashboard(self):
        """获取安全仪表板数据"""
        current_time = datetime.now()
        
        dashboard = {
            'timestamp': current_time.isoformat(),
            'system_status': self.get_system_status(),
            'qrcode_security': self.get_qrcode_security_overview(),
            'ip_security': self.get_ip_security_overview(),
            'threat_analysis': self.get_threat_analysis(),
            'recommendations': self.get_security_recommendations()
        }
        
        return dashboard
    
    def get_system_status(self):
        """获取系统状态"""
        # 统计活跃的安全限制
        active_limits = 0
        security_events = 0
        
        # 扫描缓存中的安全相关键（兼容不同缓存后端）
        try:
            if hasattr(self.cache, '_cache') and hasattr(self.cache._cache, 'keys'):
                cache_keys = list(self.cache._cache.keys())
            else:
                # 对于FileBasedCache等，返回空列表（功能降级）
                cache_keys = []
        except:
            cache_keys = []

        for key in cache_keys:
            key_str = str(key)
            if 'qrcode_limit:' in key_str:
                active_limits += 1
            elif 'security_event:' in key_str:
                security_events += 1
        
        return {
            'status': 'active',
            'active_qrcode_limits': active_limits,
            'security_events_count': security_events,
            'cache_health': 'healthy' if active_limits < 1000 else 'warning'
        }
    
    def get_qrcode_security_overview(self):
        """获取二维码安全概览"""
        current_time = int(datetime.now().timestamp())
        
        # 统计最近活跃的二维码
        active_qrcodes = set()
        evaluation_limits = 0
        verification_limits = 0
        
        for key in cache_keys:
            key_str = str(key)
            if 'qrcode_limit:' in key_str:
                parts = key_str.split(':')
                if len(parts) >= 3:
                    uuid = parts[1]
                    operation = parts[2]
                    active_qrcodes.add(uuid)
                    
                    if operation == 'evaluation':
                        evaluation_limits += 1
                    elif operation == 'verification':
                        verification_limits += 1
        
        return {
            'active_qrcodes_count': len(active_qrcodes),
            'evaluation_limits_active': evaluation_limits,
            'verification_limits_active': verification_limits,
            'most_accessed_qrcodes': self.get_most_accessed_qrcodes()
        }
    
    def get_ip_security_overview(self):
        """获取IP安全概览"""
        current_time = int(datetime.now().timestamp())
        
        # 统计活跃IP
        active_ips = set()
        high_risk_ips = []
        
        for key in self.cache._cache.keys():
            key_str = str(key)
            if 'ip_requests:' in key_str or 'ip_qrcodes:' in key_str:
                parts = key_str.split(':')
                if len(parts) >= 2:
                    ip = parts[1]
                    active_ips.add(ip)
                    
                    # 检查风险评分
                    profile = get_ip_security_profile(ip)
                    if profile['risk_score'] > 50:
                        high_risk_ips.append({
                            'ip': ip,
                            'risk_score': profile['risk_score'],
                            'activity': profile['activity']
                        })
        
        return {
            'active_ips_count': len(active_ips),
            'high_risk_ips': high_risk_ips[:10],  # 前10个高风险IP
            'geographic_distribution': self.get_ip_geographic_distribution(list(active_ips))
        }
    
    def get_threat_analysis(self):
        """获取威胁分析"""
        current_time = int(datetime.now().timestamp())
        
        # 分析最近的安全事件
        threat_types = {}
        recent_events = []
        
        for key in self.cache._cache.keys():
            key_str = str(key)
            if 'security_event:' in key_str:
                event_data = self.cache.get(key)
                if event_data and isinstance(event_data, dict):
                    event_type = event_data.get('type', 'unknown')
                    threat_types[event_type] = threat_types.get(event_type, 0) + 1
                    
                    if current_time - event_data.get('timestamp', 0) < 3600:  # 最近1小时
                        recent_events.append(event_data)
        
        # 威胁等级评估
        threat_level = 'low'
        if len(recent_events) > 10:
            threat_level = 'medium'
        if len(recent_events) > 50:
            threat_level = 'high'
        
        return {
            'threat_level': threat_level,
            'threat_types': threat_types,
            'recent_events_count': len(recent_events),
            'top_threats': sorted(threat_types.items(), key=lambda x: x[1], reverse=True)[:5]
        }
    
    def get_security_recommendations(self):
        """获取安全建议"""
        recommendations = []
        
        # 基于当前状态生成建议
        system_status = self.get_system_status()
        threat_analysis = self.get_threat_analysis()
        
        if system_status['active_qrcode_limits'] > 500:
            recommendations.append({
                'priority': 'medium',
                'type': 'performance',
                'message': '活跃的二维码限制较多，建议清理过期限制',
                'action': 'cleanup_expired_limits'
            })
        
        if threat_analysis['threat_level'] == 'high':
            recommendations.append({
                'priority': 'high',
                'type': 'security',
                'message': '检测到高威胁活动，建议加强监控',
                'action': 'increase_monitoring'
            })
        
        if threat_analysis['threat_types'].get('rapid_qrcode_switching', 0) > 5:
            recommendations.append({
                'priority': 'medium',
                'type': 'security',
                'message': '检测到多次快速切换二维码行为，建议调整限制策略',
                'action': 'adjust_switching_limits'
            })
        
        return recommendations
    
    def get_most_accessed_qrcodes(self, limit=10):
        """获取访问最多的二维码"""
        qrcode_stats = {}
        current_time = int(datetime.now().timestamp())
        
        # 统计最近24小时的访问
        for hours_back in range(24):
            hour_timestamp = current_time - (hours_back * 3600)
            
            for key in self.cache._cache.keys():
                key_str = str(key)
                if f'qrcode_stats:' in key_str and str(hour_timestamp // 3600) in key_str:
                    parts = key_str.split(':')
                    if len(parts) >= 2:
                        uuid = parts[1]
                        stats = self.cache.get(key, {'access_count': 0})
                        qrcode_stats[uuid] = qrcode_stats.get(uuid, 0) + stats.get('access_count', 0)
        
        # 排序并返回前N个
        sorted_qrcodes = sorted(qrcode_stats.items(), key=lambda x: x[1], reverse=True)
        return [{'uuid': uuid, 'access_count': count} for uuid, count in sorted_qrcodes[:limit]]
    
    def get_ip_geographic_distribution(self, ips):
        """获取IP地理分布（简化版）"""
        # 这里可以集成IP地理位置服务
        # 目前返回简化的分布信息
        distribution = {
            'domestic': 0,
            'international': 0,
            'unknown': 0
        }
        
        for ip in ips:
            if ip.startswith('192.168.') or ip.startswith('10.') or ip.startswith('172.'):
                distribution['domestic'] += 1
            elif ip.startswith('127.'):
                distribution['domestic'] += 1
            else:
                distribution['unknown'] += 1
        
        return distribution
    
    def analyze_qrcode_security(self, uuid):
        """分析特定二维码的安全状况"""
        stats = get_qrcode_security_stats(uuid)
        
        # 安全评估
        security_score = 100
        issues = []
        
        # 检查访问频率
        if stats['recent_access']['last_hour'] > 50:
            security_score -= 20
            issues.append('访问频率过高')
        
        # 检查IP多样性
        if len(stats['recent_access']['unique_ips']) > 20:
            security_score -= 15
            issues.append('访问IP过于分散')
        
        # 检查速率限制状态
        if stats['rate_limits']['evaluation']['current'] >= stats['rate_limits']['evaluation']['limit']:
            issues.append('评价频率达到限制')
        
        return {
            'uuid': uuid,
            'security_score': max(0, security_score),
            'issues': issues,
            'stats': stats,
            'recommendation': self.get_qrcode_recommendation(security_score, issues)
        }
    
    def get_qrcode_recommendation(self, score, issues):
        """获取二维码安全建议"""
        if score >= 80:
            return '安全状况良好'
        elif score >= 60:
            return '需要关注，建议加强监控'
        else:
            return '存在安全风险，建议立即处理'
    
    def cleanup_expired_data(self, hours=24):
        """清理过期数据"""
        current_time = int(datetime.now().timestamp())
        cutoff_time = current_time - (hours * 3600)
        
        cleaned_count = 0
        
        # 清理过期的访问记录
        for key in list(self.cache._cache.keys()):
            key_str = str(key)
            if any(prefix in key_str for prefix in ['access_record:', 'security_event:', 'qrcode_stats:']):
                # 尝试从键名中提取时间戳
                parts = key_str.split(':')
                if len(parts) >= 3:
                    try:
                        timestamp = int(parts[-1])
                        if timestamp < cutoff_time:
                            self.cache.delete(key)
                            cleaned_count += 1
                    except ValueError:
                        continue
        
        return cleaned_count
    
    def generate_security_report(self, format='text'):
        """生成安全报告"""
        dashboard = self.get_security_dashboard()
        
        if format == 'json':
            return json.dumps(dashboard, indent=2, ensure_ascii=False)
        
        # 文本格式报告
        report = f"""
🔒 医院二维码系统安全报告
生成时间: {dashboard['timestamp']}

📊 系统状态:
- 状态: {dashboard['system_status']['status']}
- 活跃二维码限制: {dashboard['system_status']['active_qrcode_limits']}
- 安全事件数量: {dashboard['system_status']['security_events_count']}

🏥 二维码安全:
- 活跃二维码数量: {dashboard['qrcode_security']['active_qrcodes_count']}
- 评价限制: {dashboard['qrcode_security']['evaluation_limits_active']}
- 验证限制: {dashboard['qrcode_security']['verification_limits_active']}

🌐 IP安全:
- 活跃IP数量: {dashboard['ip_security']['active_ips_count']}
- 高风险IP数量: {len(dashboard['ip_security']['high_risk_ips'])}

⚠️ 威胁分析:
- 威胁等级: {dashboard['threat_analysis']['threat_level']}
- 最近事件: {dashboard['threat_analysis']['recent_events_count']}

📋 安全建议:
"""
        
        for rec in dashboard['recommendations']:
            report += f"- [{rec['priority'].upper()}] {rec['message']}\n"
        
        return report

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='综合安全管理工具')
    parser.add_argument('action', choices=[
        'dashboard', 'report', 'analyze', 'cleanup', 'monitor'
    ], help='要执行的操作')
    parser.add_argument('--uuid', help='要分析的二维码UUID')
    parser.add_argument('--format', choices=['text', 'json'], default='text', help='报告格式')
    parser.add_argument('--hours', type=int, default=24, help='清理数据的时间范围（小时）')
    
    args = parser.parse_args()
    manager = ComprehensiveSecurityManager()
    
    if args.action == 'dashboard':
        dashboard = manager.get_security_dashboard()
        if args.format == 'json':
            print(json.dumps(dashboard, indent=2, ensure_ascii=False))
        else:
            print("🔒 安全仪表板:")
            print(f"系统状态: {dashboard['system_status']['status']}")
            print(f"活跃二维码: {dashboard['qrcode_security']['active_qrcodes_count']}")
            print(f"威胁等级: {dashboard['threat_analysis']['threat_level']}")
    
    elif args.action == 'report':
        report = manager.generate_security_report(args.format)
        print(report)
    
    elif args.action == 'analyze':
        if not args.uuid:
            print("❌ 请指定要分析的二维码UUID --uuid")
            return
        
        analysis = manager.analyze_qrcode_security(args.uuid)
        print(f"🔍 二维码安全分析: {args.uuid}")
        print(f"安全评分: {analysis['security_score']}/100")
        print(f"建议: {analysis['recommendation']}")
        if analysis['issues']:
            print("发现问题:")
            for issue in analysis['issues']:
                print(f"  - {issue}")
    
    elif args.action == 'cleanup':
        cleaned = manager.cleanup_expired_data(args.hours)
        print(f"✅ 已清理 {cleaned} 条过期数据")
    
    elif args.action == 'monitor':
        print("🔍 开始实时监控（按Ctrl+C停止）...")
        try:
            import time
            while True:
                os.system('clear' if os.name == 'posix' else 'cls')
                report = manager.generate_security_report()
                print(report)
                time.sleep(30)  # 每30秒刷新
        except KeyboardInterrupt:
            print("\n监控已停止")

if __name__ == '__main__':
    main()

# 🔧 使用示例:
# python comprehensive_security_manager.py dashboard
# python comprehensive_security_manager.py report --format json
# python comprehensive_security_manager.py analyze --uuid 12345678-1234-1234-1234-123456789abc
# python comprehensive_security_manager.py cleanup --hours 48
# python comprehensive_security_manager.py monitor
