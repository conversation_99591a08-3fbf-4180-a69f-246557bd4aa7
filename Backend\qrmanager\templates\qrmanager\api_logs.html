{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}API调用日志{% endblock %}

{% block extra_css %}
<style>
    .log-table th, .log-table td {
        vertical-align: middle;
    }
    
    .method-label {
        font-weight: 500;
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
        min-width: 50px;
        text-align: center;
        display: inline-block;
    }
    
    .method-GET {
        background-color: rgba(32, 156, 238, 0.2);
        color: #209cee;
    }
    
    .method-POST {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .method-PUT {
        background-color: rgba(255, 193, 7, 0.2);
        color: #ffc107;
    }
    
    .method-DELETE {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    .status-badge {
        font-weight: 500;
        padding: 0.2rem 0.5rem;
        border-radius: 4px;
        font-size: 0.75rem;
    }
    
    .status-success {
        background-color: rgba(40, 167, 69, 0.2);
        color: #28a745;
    }
    
    .status-error {
        background-color: rgba(220, 53, 69, 0.2);
        color: #dc3545;
    }
    
    .filter-form .form-group {
        margin-bottom: 1rem;
    }
    
    .code-block {
        background-color: #f8f9fa;
        padding: 0.5rem;
        border-radius: 4px;
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.85rem;
        white-space: pre-wrap;
        max-height: 100px;
        overflow-y: auto;
    }
    
    .endpoint {
        font-family: SFMono-Regular, Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
        font-size: 0.9rem;
    }
</style>
{% endblock %}

{% block content %}
<div class="container">
    <div class="row mb-4">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item"><a href="{% url 'qrmanager:dashboard' %}">仪表盘</a></li>
                    <li class="breadcrumb-item"><a href="{% url 'qrmanager:api_management' %}">API管理</a></li>
                    <li class="breadcrumb-item active" aria-current="page">API调用日志</li>
                </ol>
            </nav>
            
            <div class="card shadow-sm">
                <div class="card-body">
                    <h2 class="card-title mb-4">
                        <i class="fas fa-history me-2" style="color: #6c757d;"></i>API调用日志
                    </h2>
                    
                    <!-- 筛选表单 -->
                    <div class="card mb-4">
                        <div class="card-header bg-light">
                            <i class="fas fa-filter me-2"></i>筛选条件
                        </div>
                        <div class="card-body">
                            <form method="get" class="filter-form">
                                <div class="row">
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="api_key">API密钥</label>
                                            <select class="form-select" id="api_key" name="api_key">
                                                <option value="">全部</option>
                                                {% for key in api_keys %}
                                                <option value="{{ key.id }}" {% if filters.api_key_id == key.id|stringformat:"i" %}selected{% endif %}>{{ key.name }}</option>
                                                {% endfor %}
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="status">状态</label>
                                            <select class="form-select" id="status" name="status">
                                                <option value="">全部</option>
                                                <option value="success" {% if filters.status == 'success' %}selected{% endif %}>成功</option>
                                                <option value="error" {% if filters.status == 'error' %}selected{% endif %}>错误</option>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="date_from">开始日期</label>
                                            <input type="date" class="form-control" id="date_from" name="date_from" value="{{ filters.date_from }}">
                                        </div>
                                    </div>
                                    <div class="col-md-3">
                                        <div class="form-group">
                                            <label for="date_to">结束日期</label>
                                            <input type="date" class="form-control" id="date_to" name="date_to" value="{{ filters.date_to }}">
                                        </div>
                                    </div>
                                </div>
                                <div class="row mt-2">
                                    <div class="col-md-6">
                                        <div class="form-group">
                                            <label for="endpoint">接口地址</label>
                                            <input type="text" class="form-control" id="endpoint" name="endpoint" placeholder="例如: /api/v1/departments/" value="{{ filters.endpoint }}">
                                        </div>
                                    </div>
                                    <div class="col-md-6 d-flex align-items-end">
                                        <button type="submit" class="btn btn-primary me-2">
                                            <i class="fas fa-search me-1"></i>筛选
                                        </button>
                                        <a href="{% url 'qrmanager:api_logs' %}" class="btn btn-outline-secondary">
                                            <i class="fas fa-undo me-1"></i>重置
                                        </a>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                    
                    <!-- 日志表格 -->
                    <div class="table-responsive">
                        <table class="table table-hover log-table">
                            <thead>
                                <tr>
                                    <th>时间</th>
                                    <th>API密钥</th>
                                    <th>方法</th>
                                    <th>接口地址</th>
                                    <th>状态码</th>
                                    <th>状态</th>
                                    <th>响应时间</th>
                                    <th>IP地址</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for log in logs %}
                                <tr>
                                    <td>{{ log.created_at|date:"Y-m-d H:i:s" }}</td>
                                    <td>
                                        {% if log.api_key %}
                                        {{ log.api_key.name }}
                                        {% else %}
                                        <span class="text-muted">未知</span>
                                        {% endif %}
                                    </td>
                                    <td>
                                        <span class="method-label method-{{ log.method }}">{{ log.method }}</span>
                                    </td>
                                    <td>
                                        <span class="endpoint">{{ log.endpoint }}</span>
                                    </td>
                                    <td>{{ log.status_code }}</td>
                                    <td>
                                        <span class="status-badge status-{{ log.status }}">
                                            {{ log.get_status_display }}
                                        </span>
                                    </td>
                                    <td>{{ log.response_time|floatformat:2 }}ms</td>
                                    <td>{{ log.remote_addr|default:"未知" }}</td>
                                </tr>
                                {% empty %}
                                <tr>
                                    <td colspan="8" class="text-center py-4">暂无日志记录</td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    {% if logs.has_other_pages %}
                    <nav aria-label="Page navigation" class="mt-4">
                        <ul class="pagination justify-content-center">
                            {% if logs.has_previous %}
                            <li class="page-item">
                                <a class="page-link" href="?page=1{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ logs.previous_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="First">
                                    <span aria-hidden="true">&laquo;&laquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Previous">
                                    <span aria-hidden="true">&laquo;</span>
                                </a>
                            </li>
                            {% endif %}
                            
                            {% for i in logs.paginator.page_range %}
                                {% if logs.number == i %}
                                <li class="page-item active"><a class="page-link" href="#">{{ i }}</a></li>
                                {% elif i > logs.number|add:'-3' and i < logs.number|add:'3' %}
                                <li class="page-item"><a class="page-link" href="?page={{ i }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ i }}</a></li>
                                {% endif %}
                            {% endfor %}
                            
                            {% if logs.has_next %}
                            <li class="page-item">
                                <a class="page-link" href="?page={{ logs.next_page_number }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item">
                                <a class="page-link" href="?page={{ logs.paginator.num_pages }}{% for key, value in filters.items %}{% if value %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% else %}
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Next">
                                    <span aria-hidden="true">&raquo;</span>
                                </a>
                            </li>
                            <li class="page-item disabled">
                                <a class="page-link" href="#" aria-label="Last">
                                    <span aria-hidden="true">&raquo;&raquo;</span>
                                </a>
                            </li>
                            {% endif %}
                        </ul>
                    </nav>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %} 