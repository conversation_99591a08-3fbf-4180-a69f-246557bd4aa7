<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接修复测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .status-online {
            background-color: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status-warning {
            background-color: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status-offline {
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 10px;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 连接修复测试页面</h1>
        <p>此页面用于测试修复后的API连接功能</p>
        
        <div class="status" id="networkStatus">
            正在检测网络状态...
        </div>
        
        <div>
            <button onclick="testConnection()">测试API连接</button>
            <button onclick="simulateQRScan()">模拟二维码扫描</button>
            <button onclick="clearLogs()">清除日志</button>
        </div>
        
        <h3>测试日志:</h3>
        <div class="log" id="testLog"></div>
    </div>

    <!-- 加载必要的JS文件 -->
    <script src="js/api.js"></script>
    <script>
        // 初始化测试环境
        window.appData = window.appData || {};
        
        // 获取页面元素
        const networkStatus = document.getElementById('networkStatus');
        const testLog = document.getElementById('testLog');
        
        // 日志函数
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666;">[${timestamp}]</span> <span style="color: ${getLogColor(type)};">${message}</span>`;
            testLog.appendChild(logEntry);
            testLog.scrollTop = testLog.scrollHeight;
            console.log(`[${type.toUpperCase()}] ${message}`);
        }
        
        function getLogColor(type) {
            switch(type) {
                case 'success': return '#28a745';
                case 'warning': return '#ffc107';
                case 'error': return '#dc3545';
                default: return '#007bff';
            }
        }
        
        // 更新网络状态显示
        function updateNetworkStatus(text, className) {
            networkStatus.textContent = text;
            networkStatus.className = `status ${className}`;
        }
        
        // 测试API连接
        async function testConnection() {
            log('开始测试API连接...', 'info');
            updateNetworkStatus('正在测试连接...', 'status-warning');
            
            try {
                if (!window.api || typeof window.api.testConnection !== 'function') {
                    log('错误: API模块未加载', 'error');
                    updateNetworkStatus('API模块未加载', 'status-offline');
                    return;
                }
                
                const isConnected = await window.api.testConnection();
                
                if (isConnected) {
                    log('API连接测试成功', 'success');
                    updateNetworkStatus('系统已连接 ✓', 'status-online');
                } else {
                    log('API连接测试失败', 'warning');
                    updateNetworkStatus('系统连接失败 ⚠', 'status-warning');
                }
            } catch (error) {
                log(`API连接测试出错: ${error.message}`, 'error');
                updateNetworkStatus('系统连接错误 ⚠', 'status-warning');
            }
        }
        
        // 模拟二维码扫描
        async function simulateQRScan() {
            log('模拟二维码扫描...', 'info');
            
            // 模拟验证成功
            window.appData.verificationSuccess = true;
            window.appData.qrParam = 'test_qr_param_123';
            
            log('模拟验证成功，设置 verificationSuccess = true', 'success');
            
            // 再次测试连接
            await testConnection();
        }
        
        // 清除日志
        function clearLogs() {
            testLog.innerHTML = '';
            log('日志已清除', 'info');
        }
        
        // 页面加载完成后自动测试
        window.addEventListener('load', function() {
            log('页面加载完成，开始初始化测试', 'info');
            
            // 检查网络状态
            const isOnline = navigator.onLine;
            log(`浏览器网络状态: ${isOnline ? '在线' : '离线'}`, isOnline ? 'success' : 'warning');
            
            // 自动测试连接
            setTimeout(testConnection, 1000);
        });
        
        // 监听网络状态变化
        window.addEventListener('online', function() {
            log('网络已连接', 'success');
        });
        
        window.addEventListener('offline', function() {
            log('网络已断开', 'warning');
        });
    </script>
</body>
</html>
