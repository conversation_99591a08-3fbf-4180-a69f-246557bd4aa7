{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}{% if object %}编辑{% else %}创建{% endif %}打印模板{% endblock %}

{% block content %}
<div class="container-fluid px-4">
    <div class="row justify-content-center">
        <div class="col-lg-11">
            <div class="card">
                <div class="card-header">
                    <h1 class="h3 mb-0">{% if object %}编辑{% else %}创建{% endif %}打印模板</h1>
                    <p class="text-muted mb-0">为科室 {{ department.name }} 设置二维码打印模板</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        <!-- 表单部分 -->
                        <div class="col-md-6">
                            <form method="post" enctype="multipart/form-data" id="templateForm">
                                {% csrf_token %}
                                <div class="mb-3">
                                    <label for="id_name" class="form-label">模板名称</label>
                                    <input type="text" class="form-control" id="id_name" name="name" 
                                           value="{{ form.name.value|default:'' }}" required>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">打印尺寸设置</label>
                                    <div class="row g-2">
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="print_width" 
                                                       name="print_width" min="1" max="1000" step="1"
                                                       value="{{ form.print_width.value|default:'210' }}"
                                                       onchange="updatePreviewSize()">
                                                <span class="input-group-text">毫米（宽）</span>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="input-group">
                                                <input type="number" class="form-control" id="print_height" 
                                                       name="print_height" min="1" max="1000" step="1"
                                                       value="{{ form.print_height.value|default:'297' }}"
                                                       onchange="updatePreviewSize()">
                                                <span class="input-group-text">毫米（高）</span>
                                            </div>
                                        </div>
                                    </div>
                                    <small class="text-muted">默认为A4纸大小（210 × 297毫米）</small>
                                </div>
                                <div class="mb-3">
                                    <label for="id_background_image" class="form-label">背景图片</label>
                                    <input type="file" class="form-control" id="id_background_image" 
                                           name="background_image" accept="image/*" 
                                           onchange="previewBackground(this)">
                                    <small class="text-muted">支持JPG/PNG格式，建议尺寸与打印尺寸成比例</small>
                                </div>
                                <div class="mb-3">
                                    <label for="id_qr_position_x" class="form-label">二维码X坐标（从左边开始）</label>
                                    <input type="range" class="form-range" id="id_qr_position_x" 
                                           name="qr_position_x" min="0" max="210" 
                                           value="{{ form.qr_position_x.value|default:'105' }}"
                                           oninput="updatePreview()">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">左边缘</small>
                                    <small class="text-muted">当前值：<span id="x_value">105</span>毫米</small>
                                        <small class="text-muted">右边缘</small>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="id_qr_position_y" class="form-label">二维码Y坐标（从上边开始）</label>
                                    <input type="range" class="form-range" id="id_qr_position_y" 
                                           name="qr_position_y" min="0" max="297" 
                                           value="{{ form.qr_position_y.value|default:'148' }}"
                                           oninput="updatePreview()">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">上边缘</small>
                                    <small class="text-muted">当前值：<span id="y_value">148</span>毫米</small>
                                        <small class="text-muted">下边缘</small>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label for="id_qr_size" class="form-label">二维码尺寸</label>
                                    <input type="range" class="form-range" id="id_qr_size" 
                                           name="qr_size" min="15" max="50" 
                                           value="{{ form.qr_size.value|default:'20' }}"
                                           oninput="updatePreview()">
                                    <div class="d-flex justify-content-between">
                                        <small class="text-muted">小</small>
                                    <small class="text-muted">当前值：<span id="size_value">20</span>毫米 
                                            <span class="text-info">(建议20-30毫米)</span>
                                    </small>
                                        <small class="text-muted">大</small>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="id_is_active" 
                                               name="is_active" {% if form.is_active.value %}checked{% endif %}>
                                        <label class="form-check-label" for="id_is_active">启用模板</label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="id_is_public" 
                                               name="is_public" {% if form.is_public.value %}checked{% endif %}>
                                        <label class="form-check-label" for="id_is_public">设为公共模板</label>
                                        <div class="form-text">设置为公共模板后，所有未设置自定义模板的科室将使用此模板</div>
                                    </div>
                                </div>
                                <div class="d-flex justify-content-between">
                                    <a href="{% url 'qrmanager:print_template_list' %}" class="btn btn-secondary">
                                        <i class="fas fa-arrow-left me-2"></i>返回
                                    </a>
                                    <button type="submit" class="btn btn-primary">
                                        <i class="fas fa-save me-2"></i>保存模板
                                    </button>
                                </div>
                            </form>
                        </div>

                        <!-- 预览部分 -->
                        <div class="col-md-6">
                            <div class="preview-wrapper">
                                <h5 class="text-center mb-2">模板预览</h5>
                                <div class="preview-container" style="position: relative; width: 500px; height: 707px; border: 1px solid #ddd; overflow: hidden; margin: 0 auto; background-color: #f8f9fa;">
                                <!-- 网格背景 -->
                                <div class="grid-background"></div>
                                    
                                    <!-- 辅助线 -->
                                    <div class="guide-lines">
                                        <div class="guide-line-x"></div>
                                        <div class="guide-line-y"></div>
                                    </div>
                                    
                                    <!-- 坐标指示器 -->
                                    <div class="coordinate-indicator">
                                        <span class="coordinate-x">X: <span id="preview_x">105</span>mm</span>
                                        <span class="coordinate-y">Y: <span id="preview_y">148</span>mm</span>
                                    </div>
                                    
                                <!-- 背景图片 -->
                                    <div class="background-container" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; display: flex; justify-content: center; align-items: center; z-index: 1;">
                                <img id="background_preview" src="{% if object.background_image %}{{ object.background_image.url }}{% endif %}" 
                                             style="max-width: 100%; max-height: 100%; object-fit: contain;">
                                    </div>
                                    
                                <!-- 二维码预览框 -->
                                <div id="qr_preview" class="qr-preview-box">
                                        <img src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAIQAAACECAYAAABRRIOnAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGwSURBVHhe7dIxAQAADMOg+TfdGYgHH7BwlgBxAYgLQFwA4gIQF4C4AMQFIBaAuADEBSAuAHEBiAtAXADiAhAXgLgAxAUgLgBxAYgLQFwA4gIQF4C4AMQFIBaAuADEBSAuAHEB" 
                                             alt="QR Code Preview"
                                             style="width: 100%; height: 100%; object-fit: contain;">
                                    </div>
                                </div>
                                <div class="text-center mt-2">
                                    <small class="text-muted">提示：拖动滑块调整二维码位置和大小</small>
                                </div>
                                <div class="alert alert-info mt-3">
                                    <small>
                                        <i class="fas fa-info-circle me-1"></i>
                                        预览说明：二维码位置是基于左上角为原点(0,0)的坐标系统。二维码中心点位于所设置的X和Y坐标位置。
                                    </small>
                            </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.preview-wrapper {
    padding: 15px;
    background-color: #fff;
    border-radius: 5px;
    box-shadow: 0 0 10px rgba(0,0,0,0.05);
}

.grid-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
        linear-gradient(rgba(0,0,0,.05) 1px, transparent 1px),
        linear-gradient(90deg, rgba(0,0,0,.05) 1px, transparent 1px);
    background-size: 20px 20px;
    z-index: 0;
}

.qr-preview-box {
    position: absolute;
    background: rgba(255, 255, 255, 0.7);
    border: 2px dashed #0d6efd;
    width: 40px;
    height: 40px;
    z-index: 2;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
}

.guide-lines {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 3;
    pointer-events: none;
}

.guide-line-x, .guide-line-y {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.3);
}

.guide-line-x {
    width: 100%;
    height: 1px;
}

.guide-line-y {
    height: 100%;
    width: 1px;
}

.coordinate-indicator {
    position: absolute;
    bottom: 5px;
    right: 5px;
    background-color: rgba(0, 0, 0, 0.6);
    color: white;
    padding: 3px 6px;
    border-radius: 3px;
    font-size: 12px;
    z-index: 4;
}

.coordinate-indicator span {
    margin: 0 5px;
}

/* 添加响应式支持 */
@media (max-width: 992px) {
    .preview-container {
        width: 100% !important;
        height: auto !important;
        aspect-ratio: 210/297;
    }
}
</style>

<script>
// 页面加载前预加载图片资源
document.addEventListener('DOMContentLoaded', function() {
    // 初始化预览
    updatePreviewSize();
    updatePreview();
    
    // 监听窗口大小变化，调整预览
    window.addEventListener('resize', function() {
        updatePreviewSize();
        updatePreview();
    });
    
    // 如果已有背景图片，预加载
    const backgroundPreview = document.getElementById('background_preview');
    if (backgroundPreview.src && backgroundPreview.src !== window.location.href) {
        const img = new Image();
        img.onload = function() {
            backgroundPreview.style.display = 'block';
        };
        img.src = backgroundPreview.src;
    } else {
        backgroundPreview.style.display = 'none';
    }
    
    // 初始化滑块值显示
    document.getElementById('x_value').textContent = document.getElementById('id_qr_position_x').value;
    document.getElementById('y_value').textContent = document.getElementById('id_qr_position_y').value;
    document.getElementById('size_value').textContent = document.getElementById('id_qr_size').value;
});

// 预览背景图片
function previewBackground(input) {
    const backgroundPreview = document.getElementById('background_preview');
    
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        
        reader.onload = function(e) {
            backgroundPreview.src = e.target.result;
            backgroundPreview.style.display = 'block';
            
            // 加载完成后更新预览
            const img = new Image();
            img.onload = function() {
                updatePreviewSize();
                updatePreview();
            };
            img.src = e.target.result;
        }
        
        reader.readAsDataURL(input.files[0]);
    } else {
        backgroundPreview.style.display = 'none';
    }
}

// 更新预览容器尺寸
function updatePreviewSize() {
    const printWidth = parseInt(document.getElementById('print_width').value) || 210;
    const printHeight = parseInt(document.getElementById('print_height').value) || 297;
    
    // 更新滑块的最大值
    document.getElementById('id_qr_position_x').max = printWidth;
    document.getElementById('id_qr_position_y').max = printHeight;
    
    // 计算等比例缩放的预览尺寸
    const previewWrapper = document.querySelector('.preview-wrapper');
    const containerMaxWidth = previewWrapper.offsetWidth - 30; // 减去内边距
    const containerMaxHeight = window.innerHeight * 0.7; // 最大高度为视窗高度的70%
    
    // 计算缩放比例，保持纸张比例
    const scaleX = containerMaxWidth / printWidth;
    const scaleY = containerMaxHeight / printHeight;
    const scale = Math.min(scaleX, scaleY);
    
    // 计算预览容器的实际尺寸（像素）
    const previewWidth = Math.round(printWidth * scale);
    const previewHeight = Math.round(printHeight * scale);
    
    // 更新预览容器尺寸
    const container = document.querySelector('.preview-container');
    container.style.width = `${previewWidth}px`;
    container.style.height = `${previewHeight}px`;
    
    // 更新网格背景尺寸（每个格子代表1厘米）
    const gridSize = Math.round(scale * 10); // 每10毫米一个格子
    document.querySelector('.grid-background').style.backgroundSize = `${gridSize}px ${gridSize}px`;
    
    // 保存缩放比例到容器的自定义属性中，以便其他函数使用
    container.dataset.scale = scale;
    
    // 重新更新二维码预览位置
    updatePreview();
}

// 更新二维码位置和尺寸
function updatePreview() {
    const qrPositionX = parseInt(document.getElementById('id_qr_position_x').value) || 105;
    const qrPositionY = parseInt(document.getElementById('id_qr_position_y').value) || 148;
    const qrSize = parseInt(document.getElementById('id_qr_size').value) || 20;
    
    // 更新数值显示
    document.getElementById('x_value').textContent = qrPositionX;
    document.getElementById('y_value').textContent = qrPositionY;
    document.getElementById('size_value').textContent = qrSize;
    document.getElementById('preview_x').textContent = qrPositionX;
    document.getElementById('preview_y').textContent = qrPositionY;
    
    // 获取预览容器
    const container = document.querySelector('.preview-container');
    const containerWidth = container.offsetWidth;
    const containerHeight = container.offsetHeight;
    
    // 获取打印尺寸
    const printWidth = parseInt(document.getElementById('print_width').value) || 210;
    const printHeight = parseInt(document.getElementById('print_height').value) || 297;
    
    // 计算比例
    const scale = Math.min(containerWidth / printWidth, containerHeight / printHeight);
    
    // 计算二维码的实际位置和大小（像素）
    const qrSizePixels = Math.round(qrSize * scale);
    const qrLeftPixels = Math.round(qrPositionX * scale);
    const qrTopPixels = Math.round(qrPositionY * scale);
    
    // 更新二维码预览的样式
    const qrPreview = document.getElementById('qr_preview');
    qrPreview.style.width = `${qrSizePixels}px`;
    qrPreview.style.height = `${qrSizePixels}px`;
    
    // 设置左上角位置，并应用transform来居中定位，与床位列表页一致
    qrPreview.style.left = `${qrLeftPixels}px`;
    qrPreview.style.top = `${qrTopPixels}px`;
    qrPreview.style.transform = 'translate(-50%, -50%)';
    
    // 更新辅助线位置 - 现在二维码中心就在 qrLeftPixels 和 qrTopPixels 点上
    const guideLineX = document.querySelector('.guide-line-x');
    const guideLineY = document.querySelector('.guide-line-y');
    
    guideLineX.style.top = `${qrTopPixels}px`;
    guideLineY.style.left = `${qrLeftPixels}px`;
}
</script>
{% endblock %} 