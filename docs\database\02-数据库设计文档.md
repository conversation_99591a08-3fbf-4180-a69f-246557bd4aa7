# 医院服务评价系统 - 数据库设计文档

## 文档信息
- **文档版本**: v1.0
- **创建日期**: 2025-01-27
- **文档类型**: 数据库设计
- **目标读者**: 数据库管理员、后端开发工程师

## 1. 数据库概述

### 1.1 数据库信息
- **数据库类型**: SQLite3
- **版本**: 3.31+
- **字符集**: UTF-8
- **存储引擎**: SQLite默认引擎
- **数据文件**: `db.sqlite3`

### 1.2 设计原则
- **规范化**: 遵循第三范式，减少数据冗余
- **性能优化**: 合理设置索引，优化查询性能
- **数据完整性**: 外键约束保证数据一致性
- **扩展性**: 预留扩展字段，支持业务发展
- **安全性**: 敏感数据加密存储

### 1.3 表结构概览
```
数据库表总数: 15个
├── 业务核心表 (8个)
│   ├── qrmanager_department        # 科室表
│   ├── qrmanager_stafftype         # 工作人员类型表
│   ├── qrmanager_staff             # 工作人员表
│   ├── qrmanager_bed               # 床位表
│   ├── qrmanager_qrcode            # 二维码表
│   ├── qrmanager_evaluation        # 评价表
│   ├── qrmanager_printtemplate     # 打印模板表
│   └── qrmanager_systemconfig      # 系统配置表
├── 系统管理表 (4个)
│   ├── qrmanager_apikey            # API密钥表
│   ├── qrmanager_apilog            # API日志表
│   ├── qrmanager_operationlog      # 操作日志表
│   └── qrmanager_temptoken         # 临时令牌表
├── 安全防护表 (2个)
│   ├── qrmanager_devicefingerprint # 设备指纹表
│   └── qrmanager_dictionary        # 字典表
└── Django系统表 (1个)
    └── django_*                    # Django框架表
```

## 2. 核心业务表设计

### 2.1 科室表 (qrmanager_department)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| code | VARCHAR | 20 | UNIQUE, NOT NULL | - | 科室编码 |
| name | VARCHAR | 100 | NOT NULL | - | 科室名称 |
| remarks | TEXT | - | NULL | - | 备注信息 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

#### 索引设计
```sql
-- 主键索引 (自动创建)
CREATE UNIQUE INDEX pk_department ON qrmanager_department(id);

-- 科室编码唯一索引
CREATE UNIQUE INDEX uk_department_code ON qrmanager_department(code);

-- 科室名称索引 (用于搜索)
CREATE INDEX idx_department_name ON qrmanager_department(name);
```

#### 业务规则
- 科室编码全局唯一，不可重复
- 科室名称不能为空
- 删除科室时需检查是否有关联的床位和工作人员
- 支持软删除机制（通过is_active字段）

#### 示例数据
```sql
INSERT INTO qrmanager_department (code, name, remarks) VALUES
('NK', '内科', '内科科室'),
('WK', '外科', '外科科室'),
('FCK', '妇产科', '妇产科科室'),
('EK', '儿科', '儿科科室');
```

### 2.2 工作人员类型表 (qrmanager_stafftype)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| code | VARCHAR | 20 | UNIQUE, NOT NULL | - | 类型编码 |
| name | VARCHAR | 50 | NOT NULL | - | 类型名称 |
| icon | VARCHAR | 50 | NULL | - | 图标标识 |
| display_order | INTEGER | - | NOT NULL | 0 | 显示顺序 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

#### 索引设计
```sql
-- 类型编码唯一索引
CREATE UNIQUE INDEX uk_stafftype_code ON qrmanager_stafftype(code);

-- 显示顺序索引
CREATE INDEX idx_stafftype_order ON qrmanager_stafftype(display_order);

-- 状态索引
CREATE INDEX idx_stafftype_active ON qrmanager_stafftype(is_active);
```

#### 示例数据
```sql
INSERT INTO qrmanager_stafftype (code, name, icon, display_order) VALUES
('doctor', '医生', 'fa-user-md', 1),
('nurse', '护士', 'fa-user-nurse', 2),
('technician', '技师', 'fa-user-cog', 3),
('admin', '行政', 'fa-user-tie', 4);
```

### 2.3 工作人员表 (qrmanager_staff)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| work_number | VARCHAR | 20 | UNIQUE, NOT NULL | - | 工号 |
| name | VARCHAR | 50 | NOT NULL | - | 姓名 |
| staff_type_id | INTEGER | - | FOREIGN KEY | - | 人员类型ID |
| title | VARCHAR | 50 | NULL | - | 职称 |
| department_id | INTEGER | - | FOREIGN KEY | - | 科室ID |
| photo | VARCHAR | 100 | NULL | - | 照片路径 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否在职 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

#### 外键约束
```sql
-- 科室外键
ALTER TABLE qrmanager_staff 
ADD CONSTRAINT fk_staff_department 
FOREIGN KEY (department_id) REFERENCES qrmanager_department(id);

-- 人员类型外键
ALTER TABLE qrmanager_staff 
ADD CONSTRAINT fk_staff_type 
FOREIGN KEY (staff_type_id) REFERENCES qrmanager_stafftype(id);
```

#### 索引设计
```sql
-- 工号唯一索引
CREATE UNIQUE INDEX uk_staff_work_number ON qrmanager_staff(work_number);

-- 科室索引
CREATE INDEX idx_staff_department ON qrmanager_staff(department_id);

-- 人员类型索引
CREATE INDEX idx_staff_type ON qrmanager_staff(staff_type_id);

-- 姓名索引 (用于搜索)
CREATE INDEX idx_staff_name ON qrmanager_staff(name);

-- 状态索引
CREATE INDEX idx_staff_active ON qrmanager_staff(is_active);
```

### 2.4 床位表 (qrmanager_bed)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| number | VARCHAR | 20 | NOT NULL | - | 床位号 |
| department_id | INTEGER | - | FOREIGN KEY | - | 科室ID |
| area | VARCHAR | 50 | NULL | - | 区域 |
| staff_id | INTEGER | - | FOREIGN KEY | NULL | 负责人ID |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

#### 复合唯一约束
```sql
-- 床位号在科室内唯一
CREATE UNIQUE INDEX uk_bed_number_dept ON qrmanager_bed(number, department_id);
```

#### 外键约束
```sql
-- 科室外键
ALTER TABLE qrmanager_bed 
ADD CONSTRAINT fk_bed_department 
FOREIGN KEY (department_id) REFERENCES qrmanager_department(id);

-- 负责人外键
ALTER TABLE qrmanager_bed 
ADD CONSTRAINT fk_bed_staff 
FOREIGN KEY (staff_id) REFERENCES qrmanager_staff(id);
```

### 2.5 二维码表 (qrmanager_qrcode)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| code | VARCHAR | 36 | UNIQUE, NOT NULL | UUID4 | UUID码 |
| name | VARCHAR | 100 | NULL | - | 二维码名称 |
| description | TEXT | - | NULL | - | 描述信息 |
| bed_id | INTEGER | - | UNIQUE, FOREIGN KEY | - | 床位ID |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |
| updated_at | DATETIME | - | NOT NULL | NOW() | 更新时间 |

#### 外键约束
```sql
-- 床位外键 (一对一关系)
ALTER TABLE qrmanager_qrcode 
ADD CONSTRAINT fk_qrcode_bed 
FOREIGN KEY (bed_id) REFERENCES qrmanager_bed(id);
```

#### 索引设计
```sql
-- UUID码唯一索引
CREATE UNIQUE INDEX uk_qrcode_code ON qrmanager_qrcode(code);

-- 床位唯一索引 (一对一关系)
CREATE UNIQUE INDEX uk_qrcode_bed ON qrmanager_qrcode(bed_id);
```

### 2.6 评价表 (qrmanager_evaluation)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| qr_code_id | INTEGER | - | FOREIGN KEY | - | 二维码ID |
| bed_id | INTEGER | - | FOREIGN KEY | - | 床位ID |
| is_satisfied | BOOLEAN | - | NOT NULL | - | 整体满意度 |
| comment | TEXT | - | NOT NULL | - | 评价内容 |
| hospital_number | VARCHAR | 50 | NULL | - | 住院号 |
| phone_number | VARCHAR | 20 | NULL | - | 联系电话 |

#### 满意工作人员字段 (最多3个)
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| satisfied_staff1_id | INTEGER | - | NULL | - | 满意员工1 ID |
| satisfied_staff1_name | VARCHAR | 50 | NULL | - | 满意员工1 姓名 |
| satisfied_staff1_type | INTEGER | - | NULL | - | 满意员工1 类型ID |
| satisfied_staff1_type_name | VARCHAR | 50 | NULL | - | 满意员工1 类型名称 |
| satisfied_staff2_id | INTEGER | - | NULL | - | 满意员工2 ID |
| satisfied_staff2_name | VARCHAR | 50 | NULL | - | 满意员工2 姓名 |
| satisfied_staff2_type | INTEGER | - | NULL | - | 满意员工2 类型ID |
| satisfied_staff2_type_name | VARCHAR | 50 | NULL | - | 满意员工2 类型名称 |
| satisfied_staff3_id | INTEGER | - | NULL | - | 满意员工3 ID |
| satisfied_staff3_name | VARCHAR | 50 | NULL | - | 满意员工3 姓名 |
| satisfied_staff3_type | INTEGER | - | NULL | - | 满意员工3 类型ID |
| satisfied_staff3_type_name | VARCHAR | 50 | NULL | - | 满意员工3 类型名称 |

#### 不满意工作人员字段 (最多3个)
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| unsatisfied_staff1_id | INTEGER | - | NULL | - | 不满意员工1 ID |
| unsatisfied_staff1_name | VARCHAR | 50 | NULL | - | 不满意员工1 姓名 |
| unsatisfied_staff1_type | INTEGER | - | NULL | - | 不满意员工1 类型ID |
| unsatisfied_staff1_type_name | VARCHAR | 50 | NULL | - | 不满意员工1 类型名称 |
| unsatisfied_staff2_id | INTEGER | - | NULL | - | 不满意员工2 ID |
| unsatisfied_staff2_name | VARCHAR | 50 | NULL | - | 不满意员工2 姓名 |
| unsatisfied_staff2_type | INTEGER | - | NULL | - | 不满意员工2 类型ID |
| unsatisfied_staff2_type_name | VARCHAR | 50 | NULL | - | 不满意员工2 类型名称 |
| unsatisfied_staff3_id | INTEGER | - | NULL | - | 不满意员工3 ID |
| unsatisfied_staff3_name | VARCHAR | 50 | NULL | - | 不满意员工3 姓名 |
| unsatisfied_staff3_type | INTEGER | - | NULL | - | 不满意员工3 类型ID |
| unsatisfied_staff3_type_name | VARCHAR | 50 | NULL | - | 不满意员工3 类型名称 |

#### 系统字段
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| sentiment | VARCHAR | 20 | NOT NULL | 'neutral' | 情感倾向 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |

#### 索引设计
```sql
-- 二维码索引
CREATE INDEX idx_evaluation_qrcode ON qrmanager_evaluation(qr_code_id);

-- 床位索引
CREATE INDEX idx_evaluation_bed ON qrmanager_evaluation(bed_id);

-- 满意度索引
CREATE INDEX idx_evaluation_satisfied ON qrmanager_evaluation(is_satisfied);

-- 情感倾向索引
CREATE INDEX idx_evaluation_sentiment ON qrmanager_evaluation(sentiment);

-- 创建时间索引 (用于时间范围查询)
CREATE INDEX idx_evaluation_created ON qrmanager_evaluation(created_at);

-- 住院号索引 (用于查询患者评价)
CREATE INDEX idx_evaluation_hospital_number ON qrmanager_evaluation(hospital_number);
```

#### 设计说明
- **冗余存储**: 工作人员信息冗余存储，避免历史数据因人员变动而丢失
- **扁平化设计**: 使用扁平化字段存储工作人员评价，简化查询逻辑
- **数量限制**: 最多3个满意和3个不满意工作人员，符合业务需求

## 3. 系统管理表设计

### 3.1 API密钥表 (qrmanager_apikey)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| name | VARCHAR | 100 | NOT NULL | - | 密钥名称 |
| key | VARCHAR | 64 | UNIQUE, NOT NULL | - | 密钥值 |
| permissions | TEXT | - | NOT NULL | '[]' | 权限列表(JSON) |
| ip_whitelist | TEXT | - | NOT NULL | '[]' | IP白名单(JSON) |
| rate_limit | INTEGER | - | NOT NULL | 1000 | 速率限制 |
| is_active | BOOLEAN | - | NOT NULL | TRUE | 是否启用 |
| expires_at | DATETIME | - | NULL | - | 过期时间 |
| last_used_at | DATETIME | - | NULL | - | 最后使用时间 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |

#### 索引设计
```sql
-- 密钥唯一索引
CREATE UNIQUE INDEX uk_apikey_key ON qrmanager_apikey(key);

-- 状态索引
CREATE INDEX idx_apikey_active ON qrmanager_apikey(is_active);

-- 过期时间索引
CREATE INDEX idx_apikey_expires ON qrmanager_apikey(expires_at);
```

### 3.2 临时令牌表 (qrmanager_temptoken)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| token | VARCHAR | 64 | UNIQUE, NOT NULL | - | 令牌值 |
| qr_param | TEXT | - | NOT NULL | - | 原始二维码参数 |
| qr_code_id | INTEGER | - | FOREIGN KEY | - | 二维码ID |
| ip_address | VARCHAR | 45 | NOT NULL | - | 客户端IP |
| expires_at | DATETIME | - | NOT NULL | - | 过期时间 |
| is_used | BOOLEAN | - | NOT NULL | FALSE | 是否已使用 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |

#### 索引设计
```sql
-- 令牌唯一索引
CREATE UNIQUE INDEX uk_temptoken_token ON qrmanager_temptoken(token);

-- 过期时间索引 (用于清理过期令牌)
CREATE INDEX idx_temptoken_expires ON qrmanager_temptoken(expires_at);

-- 使用状态索引
CREATE INDEX idx_temptoken_used ON qrmanager_temptoken(is_used);
```

### 3.3 设备指纹表 (qrmanager_devicefingerprint)

#### 表结构
| 字段名 | 数据类型 | 长度 | 约束 | 默认值 | 说明 |
|--------|----------|------|------|--------|------|
| id | INTEGER | - | PRIMARY KEY | AUTO | 主键ID |
| fingerprint | VARCHAR | 64 | NOT NULL | - | 设备指纹 |
| qr_code_id | INTEGER | - | FOREIGN KEY | - | 二维码ID |
| ip_address | VARCHAR | 45 | NOT NULL | - | IP地址 |
| user_agent | TEXT | - | NOT NULL | - | 用户代理 |
| last_evaluation_at | DATETIME | - | NOT NULL | - | 最后评价时间 |
| evaluation_count | INTEGER | - | NOT NULL | 0 | 评价次数 |
| created_at | DATETIME | - | NOT NULL | NOW() | 创建时间 |

#### 复合唯一约束
```sql
-- 设备指纹和二维码组合唯一
CREATE UNIQUE INDEX uk_fingerprint_qrcode ON qrmanager_devicefingerprint(fingerprint, qr_code_id);
```

#### 索引设计
```sql
-- 设备指纹索引
CREATE INDEX idx_fingerprint ON qrmanager_devicefingerprint(fingerprint);

-- 最后评价时间索引
CREATE INDEX idx_fingerprint_last_eval ON qrmanager_devicefingerprint(last_evaluation_at);
```

## 4. 数据库性能优化

### 4.1 索引优化策略

#### 4.1.1 查询频率分析
```sql
-- 高频查询场景
-- 1. 按科室查询床位和工作人员
-- 2. 按时间范围查询评价
-- 3. 二维码验证查询
-- 4. 工作人员搜索
-- 5. 评价统计查询
```

#### 4.1.2 复合索引设计
```sql
-- 评价查询复合索引 (科室+时间)
CREATE INDEX idx_evaluation_dept_time ON qrmanager_evaluation(bed_id, created_at);

-- 工作人员查询复合索引 (科室+状态)
CREATE INDEX idx_staff_dept_active ON qrmanager_staff(department_id, is_active);

-- 二维码查询复合索引 (状态+床位)
CREATE INDEX idx_qrcode_active_bed ON qrmanager_qrcode(is_active, bed_id);
```

### 4.2 查询优化

#### 4.2.1 常用查询优化
```sql
-- 优化前：全表扫描
SELECT * FROM qrmanager_evaluation WHERE created_at >= '2025-01-01';

-- 优化后：使用索引
SELECT id, is_satisfied, comment, created_at 
FROM qrmanager_evaluation 
WHERE created_at >= '2025-01-01' 
ORDER BY created_at DESC;

-- 科室评价统计查询优化
SELECT 
    d.name as department_name,
    COUNT(e.id) as total_evaluations,
    SUM(CASE WHEN e.is_satisfied = 1 THEN 1 ELSE 0 END) as satisfied_count
FROM qrmanager_department d
LEFT JOIN qrmanager_bed b ON d.id = b.department_id
LEFT JOIN qrmanager_evaluation e ON b.id = e.bed_id
WHERE e.created_at >= '2025-01-01'
GROUP BY d.id, d.name;
```

### 4.3 数据维护

#### 4.3.1 定期清理策略
```sql
-- 清理过期临时令牌 (每天执行)
DELETE FROM qrmanager_temptoken 
WHERE expires_at < datetime('now', '-1 day');

-- 清理旧的API日志 (每月执行)
DELETE FROM qrmanager_apilog 
WHERE created_at < datetime('now', '-3 months');

-- 清理旧的操作日志 (每季度执行)
DELETE FROM qrmanager_operationlog 
WHERE created_at < datetime('now', '-6 months');
```

#### 4.3.2 数据归档策略
```sql
-- 评价数据归档 (年度归档)
CREATE TABLE qrmanager_evaluation_archive AS 
SELECT * FROM qrmanager_evaluation 
WHERE created_at < datetime('now', '-1 year');

-- 删除已归档数据
DELETE FROM qrmanager_evaluation 
WHERE created_at < datetime('now', '-1 year');
```

## 5. 数据安全设计

### 5.1 敏感数据处理

#### 5.1.1 数据脱敏
```sql
-- 电话号码脱敏显示
SELECT 
    id,
    comment,
    CASE 
        WHEN phone_number IS NOT NULL 
        THEN SUBSTR(phone_number, 1, 3) || '****' || SUBSTR(phone_number, -4)
        ELSE NULL 
    END as masked_phone
FROM qrmanager_evaluation;
```

#### 5.1.2 数据加密
- API密钥使用SHA-256哈希存储
- 二维码参数使用Fernet对称加密
- 用户密码使用Django内置加密

### 5.2 访问控制

#### 5.2.1 数据库权限
```sql
-- 应用用户权限 (最小权限原则)
GRANT SELECT, INSERT, UPDATE, DELETE ON qrmanager_* TO app_user;
GRANT SELECT ON django_* TO app_user;

-- 只读用户权限 (报表查询)
GRANT SELECT ON qrmanager_evaluation TO report_user;
GRANT SELECT ON qrmanager_department TO report_user;
GRANT SELECT ON qrmanager_staff TO report_user;
```

## 6. 备份与恢复

### 6.1 备份策略
```bash
# 每日全量备份
sqlite3 db.sqlite3 ".backup backup_$(date +%Y%m%d).sqlite3"

# 每周压缩备份
tar -czf backup_$(date +%Y%m%d).tar.gz backup_*.sqlite3

# 异地备份
rsync -av backup_*.tar.gz remote_server:/backup/hospital_system/
```

### 6.2 恢复策略
```bash
# 从备份恢复
cp backup_20250127.sqlite3 db.sqlite3

# 验证数据完整性
sqlite3 db.sqlite3 "PRAGMA integrity_check;"
```

## 7. 监控与维护

### 7.1 性能监控
```sql
-- 查询执行计划
EXPLAIN QUERY PLAN 
SELECT * FROM qrmanager_evaluation 
WHERE bed_id = 1 AND created_at >= '2025-01-01';

-- 数据库统计信息
SELECT 
    name,
    COUNT(*) as record_count
FROM sqlite_master 
WHERE type = 'table' 
GROUP BY name;
```

### 7.2 维护任务
```sql
-- 重建索引 (定期执行)
REINDEX;

-- 分析表统计信息
ANALYZE;

-- 清理数据库碎片
VACUUM;
```

---

**文档维护**: 数据库管理组  
**审核状态**: 已审核  
**更新频率**: 数据库结构变更时同步更新