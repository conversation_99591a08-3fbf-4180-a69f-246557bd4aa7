# 医院服务评价系统用户手册

## 目录
1. [系统简介](#系统简介)
2. [用户角色](#用户角色)
3. [功能说明](#功能说明)
4. [操作指南](#操作指南)
5. [常见问题](#常见问题)
6. [API管理](#API管理)

## 系统简介
医院服务评价系统是一个用于收集和管理医院服务评价的平台。通过扫描床位二维码，患者可以方便地对医疗服务进行评价，管理人员可以实时查看评价数据和分析报告。

## 用户角色
1. 系统管理员
   - 用户管理
   - 权限管理
   - 系统配置
   - 数据导出

2. 科室管理员
   - 科室信息管理
   - 工作人员管理
   - 床位管理
   - 评价数据查看

3. 普通用户（患者）
   - 扫码评价
   - 查看评价历史

## 功能说明

### 1. 评价功能
- 扫描二维码进入评价页面
- 选择评分（1-5星）
- 填写评价内容
- 上传照片（可选）
- 提交评价

### 2. 科室管理
- 添加/编辑/删除科室
- 管理科室工作人员
- 查看科室评价数据
- 导出科室报表

### 3. 工作人员管理
- 添加/编辑/删除工作人员
- 设置工作人员信息
- 分配科室和职务
- 批量导入工作人员

### 4. 床位管理
- 添加/编辑/删除床位
- 生成床位二维码
- 打印二维码
- 床位状态管理

### 5. 数据分析
- 评价统计
- 情感分析
- 趋势分析
- 导出报表

### 6. 二维码管理（2025年3月更新）
- 查看所有二维码列表
- 按科室筛选二维码
- 按科室批量打印二维码
- 导出二维码数据
- 查看二维码历史记录

## 操作指南

### 管理员操作

#### 登录系统
1. 访问系统登录页面
2. 输入用户名和密码
3. 点击"登录"按钮

#### 添加科室
1. 进入"科室管理"页面
2. 点击"添加科室"按钮
3. 填写科室信息
4. 点击"保存"按钮

#### 添加工作人员
1. 进入"工作人员管理"页面
2. 点击"添加工作人员"按钮
3. 填写工作人员信息
4. 上传照片（可选）
5. 点击"保存"按钮

#### 管理二维码（2025年3月更新）
1. 进入"二维码管理"页面
2. 可以查看所有二维码列表
3. 使用顶部筛选器按科室筛选二维码
4. 选择需要操作的二维码（可多选）
5. 使用工具栏按钮执行批量操作：
   - 导出选中的二维码
   - 导出全部二维码

#### 按科室批量打印二维码（2025年3月更新）
1. 进入"二维码管理"页面
2. 点击顶部的"按科室批量打印"按钮
3. 在弹出的模态框中：
   - 选择要打印的科室
   - 选择打印模板
   - 点击"打印预览"按钮查看效果
   - 确认无误后点击"打印"按钮

### 患者操作

#### 提交评价
1. 扫描床位二维码
2. 进入评价页面
3. 选择评分
4. 填写评价内容
5. 上传照片（可选）
6. 点击"提交"按钮

## 常见问题

### 1. 无法登录系统
- 检查用户名和密码是否正确
- 确认账号是否被禁用
- 联系系统管理员重置密码

### 2. 无法生成二维码
- 检查床位信息是否完整
- 确认是否有生成二维码的权限
- 刷新页面后重试

### 3. 评价提交失败
- 检查网络连接
- 确认是否在24小时内多次评价
- 检查图片大小是否超过限制

### 4. 数据导出失败
- 检查导出时间范围是否正确
- 确认是否有导出权限
- 尝试缩小导出数据范围

### 5. 二维码打印问题（2025年3月更新）
- **问题**: 按科室批量打印时没有反应
  - 确认已选择正确的科室和打印模板
  - 检查浏览器是否允许弹出窗口
  - 尝试使用不同的浏览器

- **问题**: 打印的二维码质量不佳
  - 确认打印机设置正确（建议使用300DPI或更高）
  - 检查打印模板设置
  - 尝试调整二维码大小参数

## 注意事项
1. 定期修改密码以确保账号安全
2. 及时备份重要数据
3. 遵守评价频率限制
4. 保护患者隐私信息

## 联系支持
如遇到问题无法解决，请联系系统管理员或技术支持：
- 电话：[支持电话]
- 邮箱：[支持邮箱]

## 更新记录
- 2025-03-15: 更新二维码管理功能，简化打印流程
- 2024-02-20: 初始版本 

## API管理

### API管理界面使用指南 (2025年3月7日更新)

医院服务评价系统提供了全新的API管理界面，帮助管理员监控和管理系统API。本节将介绍如何使用各项功能。

#### 访问API管理界面

1. 打开浏览器，访问`http://[服务器IP]:8000/api/management/`
2. 输入管理员账号和密码登录
3. 成功登录后，将看到API管理仪表板

#### 仪表板概览

仪表板顶部显示四个主要指标卡：
- **活跃API**：当前系统中活跃的API端点数量
- **API密钥**：已创建的API访问密钥数量
- **今日请求**：当天API调用总数
- **错误率**：API请求错误百分比

每个指标卡都显示相比昨日的变化趋势。

#### 查看API端点

1. 点击"接口端点"标签页
2. 页面显示所有API端点的列表，包括：
   - 端点名称和URL路径
   - 请求方法(GET, POST, PUT, DELETE)
   - 当前状态(活跃/非活跃)
   - 请求数和变化趋势
3. 使用顶部搜索框可按名称或路径搜索端点
4. 使用方法筛选器可查看特定HTTP方法的端点

#### 管理API密钥

1. 点击"API密钥"标签页
2. 查看现有API密钥列表
3. 点击"新建API密钥"按钮创建新密钥
4. 在创建密钥表单中：
   - 输入密钥名称和描述
   - 选择过期时间
   - 设置权限范围
   - 可选择IP地址限制

#### 查看访问日志

1. 点击"访问日志"标签页
2. 查看API调用的详细日志，包括：
   - 时间戳
   - 访问的端点
   - 请求方法
   - 客户端信息
   - IP地址
   - 响应状态
   - 响应时间
3. 使用筛选工具按状态码、时间段和端点筛选日志
4. 错误请求(4xx, 5xx)会以红色高亮显示

#### 实时监控

1. 点击"实时监控"标签页
2. 查看API性能监控图表，显示:
   - 响应时间和请求数的时间序列
   - 不同API端点的请求分布
3. 右侧面板显示:
   - 系统各组件状态(API服务器、数据库等)
   - 热门API端点列表
   - 异常警告信息

#### 系统设置

1. 点击"设置"标签页
2. 配置API行为，包括:
   - 速率限制设置
   - 缓存策略
   - 日志记录级别
   - 安全选项

### 常见问题

**问题**: 为什么某些API端点显示为"低流量"状态?  
**答案**: 当API端点连续30天调用次数低于预设阈值时，系统会将其标记为"低流量"状态。这不影响API功能，只是一个使用情况提示。

**问题**: 如何导出API访问日志?  
**答案**: 在"访问日志"页面，点击右上角的"导出"按钮，选择导出格式(CSV或JSON)即可下载日志文件。 