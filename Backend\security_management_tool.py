#!/usr/bin/env python
"""
安全管理工具 - 用于监控和管理API安全状态
提供命令行界面和Web界面来管理安全设置
"""

import os
import sys
import django
import argparse
from datetime import datetime, timedelta

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.core.cache import cache
from django.core.management.base import BaseCommand
from security_enhancements import get_security_status, manual_blacklist_ip, remove_from_blacklist, get_ip_stats

class SecurityManager:
    """安全管理器"""
    
    def __init__(self):
        self.cache = cache
    
    def get_dashboard_data(self):
        """获取安全仪表板数据"""
        return {
            'timestamp': datetime.now().isoformat(),
            'security_status': get_security_status(),
            'recent_events': self.get_recent_security_events(),
            'top_ips': self.get_top_requesting_ips(),
            'blacklist_status': self.get_blacklist_status(),
            'rate_limit_stats': self.get_rate_limit_stats()
        }
    
    def get_recent_security_events(self, hours=24):
        """获取最近的安全事件"""
        events = []
        current_time = datetime.now()
        
        # 从缓存中获取安全事件
        for i in range(hours * 60):  # 每分钟检查一次
            timestamp = current_time - timedelta(minutes=i)
            minute_key = int(timestamp.timestamp() // 60)
            
            # 检查各种安全事件
            event_keys = [
                f"security:event:{minute_key}",
                f"rate_limit_event:{minute_key}",
                f"blacklist_event:{minute_key}"
            ]
            
            for key in event_keys:
                event_data = self.cache.get(key)
                if event_data:
                    events.append({
                        'timestamp': timestamp.isoformat(),
                        'type': key.split(':')[1],
                        'data': event_data
                    })
        
        return sorted(events, key=lambda x: x['timestamp'], reverse=True)[:50]
    
    def get_top_requesting_ips(self, limit=10):
        """获取请求最多的IP"""
        ip_stats = {}
        current_time = int(datetime.now().timestamp())
        
        # 统计最近1小时的请求
        for minute in range(60):
            minute_timestamp = current_time - (minute * 60)
            
            # 查找该分钟的访问日志
            for key in self.cache._cache.keys():
                if f"access_log:" in str(key) and str(minute_timestamp // 60) in str(key):
                    log_data = self.cache.get(key)
                    if log_data and 'ip' in log_data:
                        ip = log_data['ip']
                        ip_stats[ip] = ip_stats.get(ip, 0) + 1
        
        # 排序并返回前N个
        sorted_ips = sorted(ip_stats.items(), key=lambda x: x[1], reverse=True)
        return [{'ip': ip, 'requests': count, 'stats': get_ip_stats(ip)} 
                for ip, count in sorted_ips[:limit]]
    
    def get_blacklist_status(self):
        """获取黑名单状态"""
        blacklist = self.cache.get('security:ip_blacklist', set())
        return {
            'total_blacklisted': len(blacklist),
            'blacklisted_ips': list(blacklist),
            'last_updated': self.cache.get('security:blacklist_updated', 'Unknown')
        }
    
    def get_rate_limit_stats(self):
        """获取速率限制统计"""
        stats = {
            'verify_qrcode': {'active_limits': 0, 'total_requests': 0},
            'submit_evaluation': {'active_limits': 0, 'total_requests': 0},
            'global': {'active_limits': 0, 'total_requests': 0}
        }
        
        # 统计活跃的速率限制
        for key in self.cache._cache.keys():
            key_str = str(key)
            if 'rate_limit:' in key_str:
                for limit_type in stats.keys():
                    if limit_type in key_str:
                        requests = self.cache.get(key, [])
                        stats[limit_type]['active_limits'] += 1
                        stats[limit_type]['total_requests'] += len(requests)
        
        return stats
    
    def add_ip_to_blacklist(self, ip, duration=3600, reason="手动添加"):
        """添加IP到黑名单"""
        result = manual_blacklist_ip(ip, duration, reason)
        if result:
            # 记录操作
            self.cache.set('security:blacklist_updated', datetime.now().isoformat(), 3600)
            print(f"✅ IP {ip} 已添加到黑名单，时长: {duration}秒")
        return result
    
    def remove_ip_from_blacklist(self, ip):
        """从黑名单移除IP"""
        result = remove_from_blacklist(ip)
        if result:
            self.cache.set('security:blacklist_updated', datetime.now().isoformat(), 3600)
            print(f"✅ IP {ip} 已从黑名单移除")
        else:
            print(f"❌ IP {ip} 不在黑名单中")
        return result
    
    def clear_rate_limits(self, ip=None):
        """清除速率限制"""
        cleared = 0
        pattern = f"rate_limit:{ip}:" if ip else "rate_limit:"
        
        for key in list(self.cache._cache.keys()):
            if pattern in str(key):
                self.cache.delete(key)
                cleared += 1
        
        print(f"✅ 已清除 {cleared} 个速率限制记录")
        return cleared
    
    def generate_security_report(self):
        """生成安全报告"""
        data = self.get_dashboard_data()
        
        report = f"""
🔒 安全状态报告
生成时间: {data['timestamp']}

📊 总体状态:
- 黑名单IP数量: {data['blacklist_status']['total_blacklisted']}
- 活跃速率限制: {sum(s['active_limits'] for s in data['rate_limit_stats'].values())}
- 最近安全事件: {len(data['recent_events'])}

🔝 请求最多的IP:
"""
        
        for ip_data in data['top_ips'][:5]:
            ip = ip_data['ip']
            requests = ip_data['requests']
            is_blacklisted = ip_data['stats']['is_blacklisted']
            status = "🚫 已封禁" if is_blacklisted else "✅ 正常"
            report += f"- {ip}: {requests} 次请求 {status}\n"
        
        report += f"""
📋 速率限制统计:
- 二维码验证: {data['rate_limit_stats']['verify_qrcode']['active_limits']} 个活跃限制
- 评价提交: {data['rate_limit_stats']['submit_evaluation']['active_limits']} 个活跃限制
- 全局限制: {data['rate_limit_stats']['global']['active_limits']} 个活跃限制

🚨 最近安全事件:
"""
        
        for event in data['recent_events'][:5]:
            report += f"- {event['timestamp']}: {event['type']} - {event['data']}\n"
        
        return report

def main():
    """命令行主函数"""
    parser = argparse.ArgumentParser(description='安全管理工具')
    parser.add_argument('action', choices=[
        'status', 'report', 'blacklist', 'unblacklist', 'clear-limits', 'monitor'
    ], help='要执行的操作')
    parser.add_argument('--ip', help='IP地址')
    parser.add_argument('--duration', type=int, default=3600, help='黑名单持续时间（秒）')
    parser.add_argument('--reason', default='手动操作', help='操作原因')
    
    args = parser.parse_args()
    manager = SecurityManager()
    
    if args.action == 'status':
        print("🔒 当前安全状态:")
        status = get_security_status()
        for key, value in status.items():
            print(f"  {key}: {value}")
    
    elif args.action == 'report':
        print(manager.generate_security_report())
    
    elif args.action == 'blacklist':
        if not args.ip:
            print("❌ 请指定IP地址 --ip")
            return
        manager.add_ip_to_blacklist(args.ip, args.duration, args.reason)
    
    elif args.action == 'unblacklist':
        if not args.ip:
            print("❌ 请指定IP地址 --ip")
            return
        manager.remove_ip_from_blacklist(args.ip)
    
    elif args.action == 'clear-limits':
        manager.clear_rate_limits(args.ip)
    
    elif args.action == 'monitor':
        print("🔍 开始实时监控（按Ctrl+C停止）...")
        try:
            import time
            while True:
                os.system('clear' if os.name == 'posix' else 'cls')
                print(manager.generate_security_report())
                time.sleep(30)  # 每30秒刷新
        except KeyboardInterrupt:
            print("\n监控已停止")

if __name__ == '__main__':
    main()

# 🔧 使用示例:
# python security_management_tool.py status
# python security_management_tool.py report
# python security_management_tool.py blacklist --ip ************* --duration 7200 --reason "恶意攻击"
# python security_management_tool.py unblacklist --ip *************
# python security_management_tool.py clear-limits --ip *************
# python security_management_tool.py monitor
