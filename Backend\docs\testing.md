# 测试文档

## 测试环境
- Python 3.8+
- Django 4.2.7
- SQLite3 (测试数据库)

## 测试类型

### 1. 单元测试
- 模型测试
- 视图测试
- 表单测试
- 工具函数测试

### 2. 集成测试
- API 测试
- 数据库集成测试
- 文件上传测试

### 3. 功能测试
- 用户界面测试
- 工作流测试
- 权限测试

### 4. 性能测试
- 负载测试
- 压力测试
- 并发测试

## 测试用例

### 模型测试
```python
from django.test import TestCase
from qrmanager.models import Department, Staff, Bed

class DepartmentModelTest(TestCase):
    def setUp(self):
        Department.objects.create(name="内科")

    def test_department_creation(self):
        dept = Department.objects.get(name="内科")
        self.assertEqual(dept.name, "内科")

class StaffModelTest(TestCase):
    def setUp(self):
        self.dept = Department.objects.create(name="内科")
        Staff.objects.create(
            name="张三",
            work_number="WN001",
            department=self.dept
        )

    def test_staff_creation(self):
        staff = Staff.objects.get(work_number="WN001")
        self.assertEqual(staff.name, "张三")
        self.assertEqual(staff.department.name, "内科")
```

### API 测试
```python
from rest_framework.test import APITestCase
from django.urls import reverse

class EvaluationAPITest(APITestCase):
    def setUp(self):
        # 设置测试数据
        pass

    def test_create_evaluation(self):
        url = reverse('api:evaluation-create')
        data = {
            'rating': 5,
            'comment': '服务很好'
        }
        response = self.client.post(url, data, format='json')
        self.assertEqual(response.status_code, 201)
```

### 二维码打印测试
```python
from django.test import TestCase
from django.urls import reverse
from qrmanager.models import Department, Bed, QRCode, PrintTemplate

class QRCodePrintTest(TestCase):
    def setUp(self):
        # 创建测试数据
        self.department = Department.objects.create(name="测试科室")
        self.bed = Bed.objects.create(number="101", department=self.department)
        self.qrcode = QRCode.objects.create(bed=self.bed)
        self.print_template = PrintTemplate.objects.create(
            name="测试模板",
            print_width=100,
            print_height=100,
            qr_position_x=50,
            qr_position_y=50,
            qr_size=30,
            is_public=True,
            is_active=True
        )
        
    def test_department_print_view(self):
        """测试按科室批量打印视图"""
        url = reverse('qrmanager:print_department_qrcodes', args=[self.department.id])
        response = self.client.get(f"{url}?template_id={self.print_template.id}&copies=1")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
        
    def test_department_print_with_area_filter(self):
        """测试按科室和区域批量打印"""
        url = reverse('qrmanager:print_department_qrcodes', args=[self.department.id])
        response = self.client.get(f"{url}?template_id={self.print_template.id}&copies=1&area=A")
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response['Content-Type'], 'application/pdf')
```

## 运行测试

### 运行所有测试
```bash
python manage.py test
```

### 运行特定应用的测试
```bash
python manage.py test qrmanager
```

### 运行特定测试类
```bash
python manage.py test qrmanager.tests.test_models.DepartmentModelTest
```

### 运行测试并生成覆盖率报告
```bash
coverage run --source='.' manage.py test
coverage report
coverage html  # 生成HTML报告
```

## 测试数据

### 1. 科室测试数据
```python
DEPARTMENT_TEST_DATA = [
    {'name': '内科'},
    {'name': '外科'},
    {'name': '儿科'}
]
```

### 2. 工作人员测试数据
```python
STAFF_TEST_DATA = [
    {
        'name': '张三',
        'work_number': 'WN001',
        'title': '主任医师',
        'department': '内科'
    },
    {
        'name': '李四',
        'work_number': 'WN002',
        'title': '护士长',
        'department': '外科'
    }
]
```

### 3. 评价测试数据
```python
EVALUATION_TEST_DATA = [
    {
        'rating': 5,
        'comment': '医生态度很好',
        'sentiment': 'positive'
    },
    {
        'rating': 2,
        'comment': '等待时间太长',
        'sentiment': 'negative'
    }
]
```

## 测试注意事项

### 1. 数据库测试
- 使用测试数据库
- 每个测试后清理数据
- 使用事务确保数据隔离

### 2. 文件上传测试
- 使用临时文件夹
- 测试后清理文件
- 测试各种文件类型和大小

### 3. API 测试
- 测试认证
- 测试权限
- 测试各种请求方法
- 测试错误处理

### 4. 并发测试
- 测试数据竞争
- 测试死锁情况
- 测试性能瓶颈

## 持续集成

### GitHub Actions 配置
```yaml
name: Django CI

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: '3.8'
    - name: Install Dependencies
      run: |
        python -m pip install --upgrade pip
        pip install -r requirements.txt
    - name: Run Tests
      run: |
        python manage.py test
```

## 测试报告模板

### 测试执行报告
```
测试日期：[日期]
测试版本：[版本号]
测试环境：[环境描述]

1. 测试范围
   - [测试的功能模块]
   - [测试的接口]
   - [测试的场景]

2. 测试结果
   - 总用例数：[数量]
   - 通过数：[数量]
   - 失败数：[数量]
   - 跳过数：[数量]

3. 问题汇总
   - 严重问题：[数量]
   - 普通问题：[数量]
   - 轻微问题：[数量]

4. 详细问题列表
   [问题描述和解决方案]
```

## 更新记录
- 2024-02-20: 初始版本
- 2025-03-15: 更新二维码打印测试用例，移除单个打印和批量打印测试，保留按科室打印测试 