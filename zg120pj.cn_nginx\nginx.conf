worker_processes  1;

events {
    worker_connections  1024;
}

http {
    include       mime.types;
    default_type  application/octet-stream;

    sendfile        on;
    keepalive_timeout  65;
    client_max_body_size 10m;

    # 开启gzip压缩
    gzip  on;
    gzip_min_length 1k;
    gzip_comp_level 4;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # 日志格式定义
    log_format  main  '$remote_addr - $remote_user [$time_local] "$request" '
                      '$status $body_bytes_sent "$http_referer" '
                      '"$http_user_agent" "$http_x_forwarded_for"';

    # 前端服务器配置
    server {
        listen       80;
        server_name  zg120pj.cn;
        
        access_log  logs/access.log  main;
        error_log   logs/error.log;

        # 安全头部
        add_header X-Content-Type-Options nosniff;
        add_header X-Frame-Options SAMEORIGIN;
        add_header X-XSS-Protection "1; mode=block";
        
        # 根目录 - 直接访问前端首页
        location / {
            proxy_pass http://localhost:80;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }

        # API请求 - 代理到后端服务器
        location /api/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 75s;
            proxy_read_timeout 300s;
        }

        # 管理后台 - 代理到后端服务器
        location /admin/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
            proxy_connect_timeout 75s;
            proxy_read_timeout 300s;
        }
        
        # 静态文件 - 从后端代理
        location /static/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            expires 30d;
            add_header Cache-Control "public, max-age=2592000";
        }
        
        # 媒体文件 - 从后端代理
        location /media/ {
            proxy_pass http://127.0.0.1:8000;
            proxy_set_header Host $host;
            expires 7d;
            add_header Cache-Control "public, max-age=604800";
        }

        # 处理错误页面
        error_page  404              /404.html;
        error_page  500 502 503 504  /50x.html;
    }
} 