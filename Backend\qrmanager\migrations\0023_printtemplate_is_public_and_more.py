# Generated by Django 4.2.7 on 2025-02-22 17:54

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0022_alter_printtemplate_print_height_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='printtemplate',
            name='is_public',
            field=models.BooleanField(default=False, help_text='设置为公共模板后，所有未设置自定义模板的科室将使用此模板', verbose_name='是否为公共模板'),
        ),
        migrations.AlterField(
            model_name='printtemplate',
            name='department',
            field=models.OneToOneField(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='print_template', to='qrmanager.department', verbose_name='科室'),
        ),
    ]
