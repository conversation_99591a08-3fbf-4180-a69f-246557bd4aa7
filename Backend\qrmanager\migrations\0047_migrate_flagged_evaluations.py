# Generated manually

from django.db import migrations


def migrate_flagged_evaluations(apps, schema_editor):
    """
    将原来状态为"需关注"的记录迁移到新的 is_flagged 字段
    """
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    
    # 查找所有状态为"需关注"的评价
    flagged_evaluations = Evaluation.objects.filter(process_status='flagged')
    
    # 更新这些评价的状态
    for evaluation in flagged_evaluations:
        evaluation.is_flagged = True
        evaluation.process_status = 'pending'  # 默认设置为"未处理"
        evaluation.save()


def reverse_migrate_flagged_evaluations(apps, schema_editor):
    """
    将 is_flagged=True 的记录迁移回原来的状态
    """
    Evaluation = apps.get_model('qrmanager', 'Evaluation')
    
    # 查找所有 is_flagged=True 的评价
    flagged_evaluations = Evaluation.objects.filter(is_flagged=True)
    
    # 更新这些评价的状态
    for evaluation in flagged_evaluations:
        evaluation.process_status = 'flagged'
        evaluation.is_flagged = False
        evaluation.save()


class Migration(migrations.Migration):

    dependencies = [
        ('qrmanager', '0005_evaluation_is_flagged_and_more'),
    ]

    operations = [
        migrations.RunPython(
            migrate_flagged_evaluations,
            reverse_migrate_flagged_evaluations
        ),
    ]