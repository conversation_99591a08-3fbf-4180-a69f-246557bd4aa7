<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>移动端星星评分测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .test-title {
            text-align: center;
            color: #333;
            margin-bottom: 20px;
        }
        
        .device-info {
            background: #e3f2fd;
            padding: 10px;
            border-radius: 5px;
            margin-bottom: 20px;
            font-size: 14px;
        }
        
        .rating-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        
        .current-rating {
            text-align: center;
            font-size: 18px;
            font-weight: bold;
            color: #0070e4;
            margin: 10px 0;
        }
        
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin-top: 20px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        
        .log-touch { color: #28a745; }
        .log-click { color: #007bff; }
        .log-rating { color: #dc3545; font-weight: bold; }
        
        .clear-btn {
            background: #6c757d;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
        }
    </style>
    
    <!-- 引入原有的样式 -->
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">📱 移动端星星评分测试</h1>
        
        <div class="device-info">
            <strong>设备信息：</strong><br>
            用户代理：<span id="userAgent"></span><br>
            触摸支持：<span id="touchSupport"></span><br>
            屏幕尺寸：<span id="screenSize"></span>
        </div>
        
        <div class="rating-section">
            <h3>医院整体评价测试</h3>
            <p>请尝试点击下面的星星进行评分：</p>
            
            <div class="rating-container">
                <input type="hidden" id="hospitalRating" name="hospitalRating" value="0">
                <div class="stars">
                    <span class="star" data-value="1" title="很不满意">★</span>
                    <span class="star" data-value="2" title="不满意">★</span>
                    <span class="star" data-value="3" title="一般">★</span>
                    <span class="star" data-value="4" title="满意">★</span>
                    <span class="star" data-value="5" title="非常满意">★</span>
                </div>

                <!-- 添加评分标签（评分模块需要） -->
                <div class="rating-labels" style="display: none;">
                    <span class="rating-label" data-value="1">很不满意</span>
                    <span class="rating-label" data-value="2">不满意</span>
                    <span class="rating-label" data-value="3">一般</span>
                    <span class="rating-label" data-value="4">满意</span>
                    <span class="rating-label" data-value="5">非常满意</span>
                </div>
            </div>
            
            <div class="current-rating">
                当前评分：<span id="currentRating">未评分</span>
            </div>
        </div>
        
        <div class="test-log">
            <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px;">
                <strong>操作日志：</strong>
                <button class="clear-btn" onclick="clearLog()">清空日志</button>
            </div>
            <div id="logContainer"></div>
        </div>
    </div>

    <!-- 引入评分模块 -->
    <script src="js/ratingModule.js"></script>
    
    <script>
        // 显示设备信息
        document.getElementById('userAgent').textContent = navigator.userAgent.substring(0, 50) + '...';
        document.getElementById('touchSupport').textContent = 'ontouchstart' in window ? '支持' : '不支持';
        document.getElementById('screenSize').textContent = screen.width + 'x' + screen.height;
        
        // 日志功能
        const logContainer = document.getElementById('logContainer');
        let logCount = 0;
        
        function addLog(message, type = '') {
            logCount++;
            const logEntry = document.createElement('div');
            logEntry.className = 'log-entry ' + (type ? 'log-' + type : '');
            logEntry.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        function clearLog() {
            logContainer.innerHTML = '';
            logCount = 0;
        }
        
        // 监听评分变化
        const ratingInput = document.getElementById('hospitalRating');
        const currentRatingSpan = document.getElementById('currentRating');
        
        ratingInput.addEventListener('change', function() {
            const value = parseInt(this.value) || 0;
            currentRatingSpan.textContent = value === 0 ? '未评分' : value + ' 星';
            addLog(`评分已更新：${value} 星`, 'rating');
        });
        
        // 监听触摸事件（用于日志记录）
        const starsContainer = document.querySelector('.stars');
        
        starsContainer.addEventListener('touchstart', function(e) {
            const star = e.target.closest('.star');
            if (star) {
                const value = star.getAttribute('data-value');
                addLog(`触摸开始：第${value}颗星`, 'touch');
            }
        });
        
        starsContainer.addEventListener('touchend', function(e) {
            addLog('触摸结束', 'touch');
        });
        
        starsContainer.addEventListener('click', function(e) {
            const star = e.target.closest('.star');
            if (star) {
                const value = star.getAttribute('data-value');
                addLog(`点击事件：第${value}颗星`, 'click');
            }
        });
        
        // 初始化日志
        addLog('页面加载完成，等待用户操作...');
        
        // 重写console.log来捕获调试信息
        const originalLog = console.log;
        console.log = function(...args) {
            originalLog.apply(console, args);
            if (args[0] && typeof args[0] === 'string') {
                if (args[0].includes('触摸') || args[0].includes('点击') || args[0].includes('评分') || args[0].includes('拖拽')) {
                    addLog('调试: ' + args.join(' '));
                }
            }
        };
    </script>
</body>
</html>
