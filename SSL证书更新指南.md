# SSL证书更新指南

## 📋 **更新前准备**

### **1. 确认新证书文件**
新证书位置：`C:\前后端分离\zg120pj.cn_nginx\zg120pj.cn_nginx\`

文件清单：
- ✅ `zg120pj.cn_bundle.crt` - 证书文件（包含证书链）
- ✅ `zg120pj.cn.key` - 私钥文件
- ℹ️ `zg120pj.cn_bundle.pem` - PEM格式证书（备用）
- ℹ️ `zg120pj.cn.csr` - 证书签名请求（不需要）

### **2. 当前证书位置**
Nginx SSL目录：`C:\前后端分离\nginx-1.27.5\conf\ssl\`

当前文件：
- `zg120pj.cn.crt` - 当前证书文件
- `zg120pj.cn.key` - 当前私钥文件

## 🚀 **自动更新方法（推荐）**

### **运行更新脚本**
```cmd
# 以管理员身份运行命令提示符
# 切换到项目目录
cd C:\前后端分离

# 运行更新脚本
update_ssl_certificate.bat
```

脚本会自动完成：
1. ✅ 检查新证书文件
2. ✅ 备份旧证书文件
3. ✅ 复制新证书文件
4. ✅ 测试Nginx配置
5. ✅ 重新加载Nginx
6. ✅ 验证证书信息

## 🔧 **手动更新方法**

### **步骤1: 停止Nginx服务**
```cmd
cd C:\前后端分离\nginx-1.27.5
nginx.exe -s stop
```

### **步骤2: 备份旧证书**
```cmd
# 创建备份目录
mkdir C:\前后端分离\nginx-1.27.5\conf\ssl\backup

# 备份旧证书
copy "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt" "C:\前后端分离\nginx-1.27.5\conf\ssl\backup\zg120pj.cn.crt.old"
copy "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.key" "C:\前后端分离\nginx-1.27.5\conf\ssl\backup\zg120pj.cn.key.old"
```

### **步骤3: 复制新证书**
```cmd
# 复制新证书文件
copy "C:\前后端分离\zg120pj.cn_nginx\zg120pj.cn_nginx\zg120pj.cn_bundle.crt" "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt"
copy "C:\前后端分离\zg120pj.cn_nginx\zg120pj.cn_nginx\zg120pj.cn.key" "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.key"
```

### **步骤4: 验证证书文件**
```cmd
# 检查文件是否存在且大小正常
dir "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.*"
```

### **步骤5: 测试Nginx配置**
```cmd
cd C:\前后端分离\nginx-1.27.5
nginx.exe -t
```

如果看到 `syntax is ok` 和 `test is successful`，说明配置正确。

### **步骤6: 启动Nginx**
```cmd
# 启动Nginx
start nginx.exe

# 或者如果已经运行，重新加载配置
nginx.exe -s reload
```

## 🔍 **验证证书更新**

### **1. 浏览器验证**
1. 访问 `https://zg120pj.cn`
2. 点击地址栏的锁图标
3. 查看证书详情，确认：
   - 证书有效期已更新
   - 证书链完整
   - 没有安全警告

### **2. 命令行验证**
```cmd
# 使用OpenSSL检查证书（如果已安装）
openssl x509 -in "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt" -text -noout

# 检查证书有效期
openssl x509 -in "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt" -dates -noout
```

### **3. 在线SSL检测**
使用以下在线工具验证：
- SSL Labs: https://www.ssllabs.com/ssltest/
- SSL Checker: https://www.sslshopper.com/ssl-checker.html

## ⚠️ **故障排除**

### **问题1: Nginx配置测试失败**
```cmd
# 检查错误日志
type C:\前后端分离\nginx-1.27.5\logs\error.log

# 常见原因：
# - 证书文件路径错误
# - 证书文件损坏
# - 私钥与证书不匹配
```

**解决方案**：
```cmd
# 恢复备份文件
copy "C:\前后端分离\nginx-1.27.5\conf\ssl\backup\zg120pj.cn.crt.old" "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt"
copy "C:\前后端分离\nginx-1.27.5\conf\ssl\backup\zg120pj.cn.key.old" "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.key"
```

### **问题2: 浏览器显示证书错误**
可能原因：
- 证书链不完整
- 使用了错误的证书文件

**解决方案**：
```cmd
# 尝试使用PEM格式证书
copy "C:\前后端分离\zg120pj.cn_nginx\zg120pj.cn_nginx\zg120pj.cn_bundle.pem" "C:\前后端分离\nginx-1.27.5\conf\ssl\zg120pj.cn.crt"
```

### **问题3: Nginx无法启动**
```cmd
# 检查端口占用
netstat -ano | findstr :443
netstat -ano | findstr :80

# 检查Nginx进程
tasklist | findstr nginx
```

## 📅 **证书管理建议**

### **1. 设置提醒**
- 在证书到期前30天设置提醒
- 定期检查证书有效期

### **2. 自动化更新**
考虑使用以下工具：
- Let's Encrypt + Certbot（免费证书）
- ACME客户端自动续期

### **3. 监控证书状态**
- 使用SSL监控服务
- 设置证书到期邮件提醒

## 📞 **紧急联系**

如果更新过程中遇到问题导致网站无法访问：

1. **立即恢复备份**：
   ```cmd
   copy "C:\前后端分离\nginx-1.27.5\conf\ssl\backup\*.old" "C:\前后端分离\nginx-1.27.5\conf\ssl\"
   nginx.exe -s reload
   ```

2. **检查服务状态**：
   ```cmd
   nginx.exe -t
   tasklist | findstr nginx
   ```

3. **查看错误日志**：
   ```cmd
   type C:\前后端分离\nginx-1.27.5\logs\error.log
   ```

## ✅ **更新完成检查清单**

- [ ] 新证书文件已复制到正确位置
- [ ] Nginx配置测试通过
- [ ] Nginx服务正常运行
- [ ] 网站可以正常访问（https://zg120pj.cn）
- [ ] 浏览器显示安全锁图标
- [ ] 证书有效期正确显示
- [ ] 手机端访问正常
- [ ] 二维码扫描功能正常

更新完成后，建议立即测试网站的所有功能，确保SSL证书更新没有影响正常业务。
