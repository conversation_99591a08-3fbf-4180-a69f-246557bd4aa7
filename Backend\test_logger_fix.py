# -*- coding: utf-8 -*-
"""
测试 LoggerHelper 修复
"""

import os
import django

# 设置Django环境
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'HospitalQRCode.settings')
django.setup()

from django.contrib.auth.models import User
from qrmanager.utils import LoggerHelper

def test_logger_with_none_instance():
    """测试 LoggerHelper 处理 None 实例的情况"""
    print("=== 测试 LoggerHelper 修复 ===")
    
    try:
        # 获取一个用户（如果没有用户则创建一个）
        user = User.objects.first()
        if not user:
            user = User.objects.create_user(
                username='test_user',
                email='<EMAIL>',
                password='testpass123'
            )
            print("创建了测试用户")
        
        # 测试 instance=None 的情况
        print("测试 instance=None 的情况...")
        LoggerHelper.log_model_operation(
            user=user,
            instance=None,
            operation_type='bulk_import',
            description='测试批量导入操作',
            extra_data={
                'success_count': 5,
                'error_count': 0,
                'test': True
            }
        )
        print("✅ LoggerHelper.log_model_operation(instance=None) 测试通过")
        
        # 测试正常实例的情况
        print("测试正常实例的情况...")
        LoggerHelper.log_model_operation(
            user=user,
            instance=user,
            operation_type='test',
            description='测试正常实例操作'
        )
        print("✅ LoggerHelper.log_model_operation(instance=user) 测试通过")
        
        print("🎉 所有测试通过！LoggerHelper 修复成功")
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_logger_with_none_instance()
