{% extends 'qrmanager/base.html' %}
{% load static %}

{% block title %}安全管理 - 医院二维码管理系统{% endblock %}

{% block extra_css %}
<style>
.management-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    padding: 20px;
    margin-bottom: 20px;
}

.action-btn {
    margin: 5px;
    padding: 8px 16px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9em;
}

.btn-danger { background: #dc3545; color: white; }
.btn-success { background: #28a745; color: white; }
.btn-warning { background: #ffc107; color: #212529; }
.btn-info { background: #17a2b8; color: white; }
.btn-primary { background: #007bff; color: white; }

.action-btn:hover {
    opacity: 0.8;
    transform: translateY(-1px);
}

.ip-item, .limit-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 15px;
    margin: 8px 0;
    background: #f8f9fa;
    border-radius: 6px;
    border-left: 4px solid #007bff;
}

.banned-ip { border-left-color: #dc3545; }
.active-limit { border-left-color: #ffc107; }

.config-form {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 15px;
    margin-top: 15px;
}

.form-group {
    display: flex;
    flex-direction: column;
}

.form-group label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #333;
}

.form-group input, .form-group select {
    padding: 8px 12px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 0.9em;
}

.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: white;
    margin: 15% auto;
    padding: 20px;
    border-radius: 8px;
    width: 80%;
    max-width: 500px;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover { color: black; }

.operation-log {
    max-height: 300px;
    overflow-y: auto;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 6px;
}

.log-item {
    padding: 8px 0;
    border-bottom: 1px solid #eee;
    font-size: 0.9em;
}

.log-time {
    color: #666;
    font-size: 0.8em;
}

.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 8px;
}

.status-active { background: #28a745; }
.status-banned { background: #dc3545; }
.status-limited { background: #ffc107; }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2>🛠️ 安全管理中心</h2>
                <div>
                    <button class="action-btn btn-warning" onclick="clearAllLimits()">清除所有限制</button>
                    <button class="action-btn btn-info" onclick="showConfigModal()">安全配置</button>
                </div>
            </div>
        </div>
    </div>

    <!-- 黑名单IP管理 -->
    <div class="row">
        <div class="col-md-6">
            <div class="management-card">
                <h4>🚫 黑名单IP管理</h4>
                <div class="mb-3">
                    <button class="action-btn btn-danger" onclick="showBanModal()">封禁新IP</button>
                </div>
                
                <div id="blacklist-container">
                    {% if blacklisted_ips %}
                        {% for ip_info in blacklisted_ips %}
                        <div class="ip-item banned-ip" id="ip-{{ ip_info.ip|slugify }}">
                            <div>
                                <strong>{{ ip_info.ip }}</strong><br>
                                <small class="text-muted">
                                    封禁时间: {{ ip_info.banned_at }}<br>
                                    原因: {{ ip_info.reason }}<br>
                                    过期时间: {{ ip_info.expires_at }}
                                </small>
                            </div>
                            <div>
                                <button class="action-btn btn-success" onclick="unbanIP('{{ ip_info.ip }}')">解封</button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无被封禁的IP</p>
                    {% endif %}
                </div>
            </div>
        </div>

        <!-- 活跃限制管理 -->
        <div class="col-md-6">
            <div class="management-card">
                <h4>⏱️ 活跃限制管理</h4>
                
                <div id="limits-container">
                    {% if active_limits %}
                        {% for limit in active_limits %}
                        <div class="limit-item active-limit" id="limit-{{ limit.uuid|slugify }}-{{ limit.operation }}">
                            <div>
                                <strong>{{ limit.type }}</strong><br>
                                <small class="text-muted">
                                    目标: {{ limit.target }}<br>
                                    当前: {{ limit.current_count }}/{{ limit.limit }}
                                </small>
                            </div>
                            <div>
                                <button class="action-btn btn-warning" onclick="clearLimit('{{ limit.uuid }}', '{{ limit.operation }}')">清除</button>
                            </div>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无活跃的速率限制</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- 操作日志 -->
    <div class="row">
        <div class="col-12">
            <div class="management-card">
                <h4>📋 最近操作日志</h4>
                <div class="operation-log">
                    {% if recent_operations %}
                        {% for operation in recent_operations %}
                        <div class="log-item">
                            <span class="status-indicator status-active"></span>
                            <strong>{{ operation.type }}</strong> - {{ operation.operator }}
                            <span class="float-right log-time">{{ operation.timestamp }}</span>
                            <br>
                            <small class="text-muted">目标: {{ operation.target }}</small>
                        </div>
                        {% endfor %}
                    {% else %}
                        <p class="text-muted">暂无操作记录</p>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 封禁IP模态框 -->
<div id="banModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeBanModal()">&times;</span>
        <h3>封禁IP地址</h3>
        <form id="banForm">
            <div class="form-group">
                <label for="banIP">IP地址:</label>
                <input type="text" id="banIP" name="ip" placeholder="*************" required>
            </div>
            <div class="form-group">
                <label for="banDuration">封禁时长(秒):</label>
                <select id="banDuration" name="duration">
                    <option value="3600">1小时</option>
                    <option value="86400">24小时</option>
                    <option value="604800">7天</option>
                    <option value="0">永久</option>
                </select>
            </div>
            <div class="form-group">
                <label for="banReason">封禁原因:</label>
                <input type="text" id="banReason" name="reason" placeholder="恶意攻击" required>
            </div>
            <div style="margin-top: 20px;">
                <button type="submit" class="action-btn btn-danger">确认封禁</button>
                <button type="button" class="action-btn btn-secondary" onclick="closeBanModal()">取消</button>
            </div>
        </form>
    </div>
</div>

<!-- 安全配置模态框 -->
<div id="configModal" class="modal">
    <div class="modal-content">
        <span class="close" onclick="closeConfigModal()">&times;</span>
        <h3>安全配置</h3>
        <form id="configForm">
            <div class="config-form">
                <div class="form-group">
                    <label for="evalLimit">二维码评价限制(次/分钟):</label>
                    <input type="number" id="evalLimit" name="qrcode_evaluation_limit" value="{{ security_config.qrcode_evaluation_limit }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="verifyLimit">二维码验证限制(次/分钟):</label>
                    <input type="number" id="verifyLimit" name="qrcode_verification_limit" value="{{ security_config.qrcode_verification_limit }}" min="1" max="100">
                </div>
                <div class="form-group">
                    <label for="ipLimit">IP请求限制(次/分钟):</label>
                    <input type="number" id="ipLimit" name="ip_requests_per_minute" value="{{ security_config.ip_requests_per_minute }}" min="1" max="1000">
                </div>
                <div class="form-group">
                    <label for="banDurationConfig">默认封禁时长(秒):</label>
                    <input type="number" id="banDurationConfig" name="ban_duration" value="{{ security_config.ban_duration }}" min="60">
                </div>
                <div class="form-group">
                    <label for="anomalyDetection">异常检测:</label>
                    <select id="anomalyDetection" name="anomaly_detection_enabled">
                        <option value="true" {% if security_config.anomaly_detection_enabled %}selected{% endif %}>启用</option>
                        <option value="false" {% if not security_config.anomaly_detection_enabled %}selected{% endif %}>禁用</option>
                    </select>
                </div>
                <div class="form-group">
                    <label for="autoBan">自动封禁:</label>
                    <select id="autoBan" name="auto_ban_enabled">
                        <option value="true" {% if security_config.auto_ban_enabled %}selected{% endif %}>启用</option>
                        <option value="false" {% if not security_config.auto_ban_enabled %}selected{% endif %}>禁用</option>
                    </select>
                </div>
            </div>
            <div style="margin-top: 20px;">
                <button type="submit" class="action-btn btn-primary">保存配置</button>
                <button type="button" class="action-btn btn-secondary" onclick="closeConfigModal()">取消</button>
            </div>
        </form>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
// 封禁IP
function showBanModal() {
    document.getElementById('banModal').style.display = 'block';
}

function closeBanModal() {
    document.getElementById('banModal').style.display = 'none';
}

document.getElementById('banForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{% url "qrmanager:ban_ip" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.error);
        }
    })
    .catch(error => {
        alert('请求失败: ' + error);
    });
    
    closeBanModal();
});

// 解封IP
function unbanIP(ip) {
    if (confirm('确定要解封IP ' + ip + ' 吗？')) {
        fetch('{% url "qrmanager:unban_ip" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({ip: ip})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('操作失败: ' + data.error);
            }
        });
    }
}

// 清除限制
function clearLimit(uuid, operation) {
    if (confirm('确定要清除该限制吗？')) {
        fetch('{% url "qrmanager:clear_qrcode_limits" %}', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': '{{ csrf_token }}'
            },
            body: JSON.stringify({uuid: uuid, operation: operation})
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert(data.message);
                location.reload();
            } else {
                alert('操作失败: ' + data.error);
            }
        });
    }
}

// 清除所有限制
function clearAllLimits() {
    if (confirm('确定要清除所有速率限制吗？这将重置所有二维码的访问限制。')) {
        window.location.href = '{% url "qrmanager:clear_all_limits" %}';
    }
}

// 安全配置
function showConfigModal() {
    document.getElementById('configModal').style.display = 'block';
}

function closeConfigModal() {
    document.getElementById('configModal').style.display = 'none';
}

document.getElementById('configForm').addEventListener('submit', function(e) {
    e.preventDefault();
    
    const formData = new FormData(this);
    const data = Object.fromEntries(formData);
    
    fetch('{% url "qrmanager:update_security_config" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify(data)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            alert(data.message);
            location.reload();
        } else {
            alert('操作失败: ' + data.error);
        }
    });
    
    closeConfigModal();
});

// 点击模态框外部关闭
window.onclick = function(event) {
    const banModal = document.getElementById('banModal');
    const configModal = document.getElementById('configModal');
    
    if (event.target == banModal) {
        banModal.style.display = 'none';
    }
    if (event.target == configModal) {
        configModal.style.display = 'none';
    }
}
</script>
{% endblock %}
