/**
 * API服务模块 - 第二部分
 * 继续apiService.js的实现
 */

(function() {
    // 获取已定义的apiService对象
    const apiService = window.apiService || {};
    
    // 扩展API服务对象 - 第二部分
    Object.assign(apiService, {
        /**
         * 提交评价
         * @param {object} evaluationData - 评价数据
         * @param {boolean} [noRedirect=false] - 是否禁止自动跳转
         * @returns {Promise<object>} - 提交结果
         */
        submitEvaluation: async function(evaluationData, noRedirect = false) {
            if (!evaluationData) {
                throw new Error('未提供评价数据');
            }
            
            // 检查是否已经提交过评价
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                console.log('用户已经提交过评价，不执行提交操作');
                return { status: 'success', message: '您已经提交过评价' };
            }
            
            try {
                // 验证qrParam参数
                if (!evaluationData.qr_param) {
                    // 尝试从appData获取
                    if (window.appData && window.appData.qrParam) {
                        evaluationData.qr_param = window.appData.qrParam;
                    } else if (window.appData && window.appData.qrCode) {
                        evaluationData.qr_param = window.appData.qrCode;
                    } else {
                        throw new Error('无法获取二维码参数，请重新扫描二维码');
                    }
                }
                
                // 构建请求选项
                const options = {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(evaluationData)
                };
                
                // 发送请求
                const data = await window.sendRequest(
                    window.API_CONFIG.ENDPOINTS.SUBMIT_EVALUATION, 
                    options
                );
                
                // 保存响应数据
                window.appData.lastEvaluationResponse = data;
                
                // 验证响应数据
                if (data.status === 'success' || data.code === 0 || data.code === 200) {
                    // 记录评价状态到会话存储
                    sessionStorage.setItem('evaluationSubmitted', 'true');
                    sessionStorage.setItem('evaluationSubmittedTime', new Date().toISOString());
                    sessionStorage.setItem('qrParam', evaluationData.qr_param || '');
                    
                    // 如果需要跳转且没有禁止
                    if (!noRedirect) {
                        // 如果有重定向URL，跳转到该URL
                        if (data.data && data.data.redirect_url) {
                            window.location.href = data.data.redirect_url;
                        } else {
                            window.location.href = '/thank-you.html';
                        }
                    }
                    
                    return data;
                } else {
                    throw new Error(data.message || data.msg || '评价提交失败');
                }
            } catch (error) {
                // 处理错误
                const standardError = window.handleApiError(error, '提交评价');
                throw standardError;
            }
        },
        
        /**
         * 测试与API服务器的连接
         * @returns {Promise<boolean>} - 连接是否成功
         */
        testConnection: async function() {
            // 检查是否已经提交过评价
            if (sessionStorage.getItem('evaluationSubmitted') === 'true') {
                return true;
            }
            
            try {
                // 构建请求选项
                const options = {
                    method: 'OPTIONS',
                    headers: {
                        'Accept': 'application/json'
                    }
                };
                
                // 发送请求（不显示加载指示器）
                await window.sendRequest(
                    window.API_CONFIG.ENDPOINTS.VERIFY_QRCODE, 
                    options, 
                    false
                );
                
                // 设置连接状态
                window.appData = window.appData || {};
                window.appData.apiConnected = true;
                
                return true;
            } catch (error) {
                // 设置连接状态
                window.appData = window.appData || {};
                window.appData.apiConnected = false;
                
                // 静默处理错误，不显示错误消息
                console.warn('API连接测试失败:', error);
                
                return false;
            }
        },
        
        /**
         * 更新应用数据
         * @param {object} data - API返回的数据
         */
        updateAppData: function(data) {
            window.appData = window.appData || {};
            
            if (data) {
                // 保存加密字符串
                if (data.encrypted_string) {
                    window.appData.encryptedString = data.encrypted_string;
                } else if (data.encrypt_string) {
                    window.appData.encryptedString = data.encrypt_string;
                } else if (data.auth_string) {
                    window.appData.encryptedString = data.auth_string;
                } else if (data.token) {
                    window.appData.encryptedString = data.token;
                } else if (data.temp_token) {
                    window.appData.tempToken = data.temp_token;
                }
                
                // 保存科室信息
                if (data.department) {
                    window.appData.department = data.department;
                }
                
                // 保存床位信息
                if (data.bed) {
                    window.appData.bed = data.bed;
                }
                
                // 保存工作人员类型
                if (data.staff_types && Array.isArray(data.staff_types)) {
                    window.appData.staffTypes = data.staff_types;
                }
                
                // 保存工作人员列表
                if (data.staff && Array.isArray(data.staff)) {
                    window.appData.staffList = data.staff;
                }
                
                // 设置验证成功标志
                window.appData.verificationSuccess = true;
            }
        }
    });
    
    // 导出API服务
    window.apiService = apiService;
})();
