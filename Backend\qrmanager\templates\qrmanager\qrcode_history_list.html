{% extends "qrmanager/base.html" %}
{% load static %}

{% block title %}二维码历史记录{% endblock %}

{% block content %}
<div class="container-fluid p-4">
    <!-- 页面标题和导航 -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <div>
            <h1 class="h3 mb-0 fw-bold text-primary">二维码历史记录</h1>
        </div>
        <div>
            <a href="{% url 'qrmanager:qrcode_list' %}" class="btn btn-outline-primary">
                <i class="fas fa-arrow-left me-1"></i>返回二维码管理
            </a>
        </div>
    </div>

    <!-- 筛选表单 -->
    <div class="card mb-4">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">筛选条件</h5>
        </div>
        <div class="card-body">
            <form method="get" class="row g-3">
                <div class="col-md-3">
                    <label for="department" class="form-label">科室</label>
                    <select class="form-select" id="department" name="department" onchange="this.form.submit()">
                        <option value="">全部科室</option>
                        {% for dept in departments %}
                        <option value="{{ dept.id }}" {% if department_id == dept.id|stringformat:"s" %}selected{% endif %}>{{ dept }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="bed" class="form-label">床位</label>
                    <select class="form-select" id="bed" name="bed">
                        <option value="">全部床位</option>
                        {% for bed in beds %}
                        <option value="{{ bed.id }}" {% if bed_id == bed.id|stringformat:"s" %}selected{% endif %}>{{ bed.number }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="is_security_issue" class="form-label">安全问题</label>
                    <select class="form-select" id="is_security_issue" name="is_security_issue">
                        <option value="">全部</option>
                        <option value="true" {% if is_security_issue == 'true' %}selected{% endif %}>是</option>
                        <option value="false" {% if is_security_issue == 'false' %}selected{% endif %}>否</option>
                    </select>
                </div>
                <div class="col-md-3">
                    <label for="date_from" class="form-label">开始日期</label>
                    <input type="date" class="form-control" id="date_from" name="date_from" value="{{ date_from }}">
                </div>
                <div class="col-md-3">
                    <label for="date_to" class="form-label">结束日期</label>
                    <input type="date" class="form-control" id="date_to" name="date_to" value="{{ date_to }}">
                </div>
                <div class="col-12">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-filter me-1"></i>筛选
                    </button>
                    <a href="{% url 'qrmanager:qrcode_history' %}" class="btn btn-outline-secondary">
                        <i class="fas fa-redo me-1"></i>重置
                    </a>
                </div>
            </form>
        </div>
    </div>

    <!-- 历史记录列表 -->
    <div class="card">
        <div class="card-header bg-white">
            <h5 class="card-title mb-0">二维码历史记录</h5>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>床位</th>
                            <th>科室</th>
                            <th>旧二维码</th>
                            <th>新二维码</th>
                            <th>更换原因</th>
                            <th>安全问题</th>
                            <th>操作人</th>
                            <th>更换时间</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for history in histories %}
                        <tr>
                            <td>{{ history.bed.number }}</td>
                            <td>{{ history.bed.department.name }}</td>
                            <td><code>{{ history.old_code }}</code></td>
                            <td><code>{{ history.new_code }}</code></td>
                            <td>{{ history.reason|default:"无" }}</td>
                            <td>
                                {% if history.is_security_issue %}
                                <span class="badge bg-danger">是</span>
                                {% else %}
                                <span class="badge bg-secondary">否</span>
                                {% endif %}
                            </td>
                            <td>{{ history.created_by.username|default:"系统" }}</td>
                            <td>{{ history.created_at|date:"Y-m-d H:i" }}</td>
                        </tr>
                        {% empty %}
                        <tr>
                            <td colspan="8" class="text-center">暂无历史记录</td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            {% if is_paginated %}
            <nav aria-label="Page navigation">
                <ul class="pagination justify-content-center">
                    {% if page_obj.has_previous %}
                    <li class="page-item">
                        <a class="page-link" href="?page=1{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="First">
                            <span aria-hidden="true">&laquo;&laquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.previous_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Previous">
                            <span aria-hidden="true">&laquo;</span>
                        </a>
                    </li>
                    {% endif %}
                    
                    {% for num in page_obj.paginator.page_range %}
                    {% if page_obj.number == num %}
                    <li class="page-item active"><a class="page-link" href="#">{{ num }}</a></li>
                    {% elif num > page_obj.number|add:'-3' and num < page_obj.number|add:'3' %}
                    <li class="page-item"><a class="page-link" href="?page={{ num }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}">{{ num }}</a></li>
                    {% endif %}
                    {% endfor %}
                    
                    {% if page_obj.has_next %}
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.next_page_number }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Next">
                            <span aria-hidden="true">&raquo;</span>
                        </a>
                    </li>
                    <li class="page-item">
                        <a class="page-link" href="?page={{ page_obj.paginator.num_pages }}{% for key, value in request.GET.items %}{% if key != 'page' %}&{{ key }}={{ value }}{% endif %}{% endfor %}" aria-label="Last">
                            <span aria-hidden="true">&raquo;&raquo;</span>
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </nav>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    $(document).ready(function() {
        // 当科室选择变化时提交表单
        $('#department').change(function() {
            $(this).closest('form').submit();
        });

        // 当其他筛选条件变化时，延迟提交表单
        $('#bed, #is_security_issue, #date_from, #date_to').change(function() {
            setTimeout(function() {
                $('form').submit();
            }, 300);
        });
    });
</script>
{% endblock %} 