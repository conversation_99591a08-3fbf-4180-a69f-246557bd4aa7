{% extends 'qrmanager/base.html' %}
{% load static %}

{% block title %}API文档{% endblock %}

{% block extra_css %}
<style>
    .api-section {
        margin-bottom: 2rem;
        border-bottom: 1px solid #eee;
        padding-bottom: 1.5rem;
    }
    .api-endpoint {
        background-color: #f8f9fa;
        border-radius: 6px;
        padding: 1.5rem;
        margin-bottom: 1.5rem;
        border-left: 4px solid #0d6efd;
    }
    .api-endpoint.get {
        border-left-color: #0d6efd;
    }
    .api-endpoint.post {
        border-left-color: #198754;
    }
    .api-endpoint.put {
        border-left-color: #ffc107;
    }
    .api-endpoint.delete {
        border-left-color: #dc3545;
    }
    .method {
        display: inline-block;
        padding: 0.25rem 0.5rem;
        border-radius: 4px;
        color: white;
        font-weight: bold;
        min-width: 60px;
        text-align: center;
        margin-right: 0.5rem;
    }
    .method.get {
        background-color: #0d6efd;
    }
    .method.post {
        background-color: #198754;
    }
    .method.put {
        background-color: #ffc107;
        color: #212529;
    }
    .method.delete {
        background-color: #dc3545;
    }
    .endpoint-url {
        font-family: monospace;
        font-size: 1rem;
        font-weight: 500;
    }
    .code-block {
        background-color: #f8f9fa;
        border-radius: 4px;
        padding: 1rem;
        font-family: monospace;
        overflow-x: auto;
    }
    .nav-pills .nav-link.active {
        background-color: #0d6efd;
    }
    .sticky-top {
        top: 20px;
    }
    .api-category {
        scroll-margin-top: 80px;
    }
    .access-badge {
        display: inline-block;
        margin-left: 10px;
        padding: 2px 8px;
        border-radius: 4px;
        font-size: 12px;
        font-weight: bold;
    }
    .access-badge.frontend {
        background-color: #20c997;
        color: white;
    }
    .access-badge.admin {
        background-color: #6f42c1;
        color: white;
    }
    .access-badge.public {
        background-color: #fd7e14;
        color: white;
    }
    .access-badge.third-party {
        background-color: #0dcaf0;
        color: #212529;
    }
</style>
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12 mb-4">
            <div class="card shadow-sm">
                <div class="card-body">
                    <h1 class="card-title">
                        <i class="fas fa-book me-2"></i>API文档
                    </h1>
                    <p class="lead">本文档提供了医院服务评价系统所有API的详细信息和使用示例。共计 {{ api_counts.total }} 个API接口，包括 {{ api_counts.public_api }} 个公开API。</p>
                    <div class="alert alert-warning">
                        <i class="fas fa-shield-alt me-2"></i>
                        <strong>安全提示：</strong> 本文档包含不同访问级别的API。请注意查看每个API的访问标识，确保按照正确的方式进行调用。
                    </div>
                    <div class="mt-3">
                        <span class="access-badge frontend">前端使用</span> 仅供系统前端界面调用
                        <span class="access-badge admin">管理员</span> 仅供管理员使用
                        <span class="access-badge public">公开</span> 可公开访问，有安全限制
                        <span class="access-badge third-party">第三方</span> 供第三方系统集成，需API密钥
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- 左侧导航 -->
        <div class="col-md-3">
            <div class="card shadow-sm sticky-top">
                <div class="card-body">
                    <h5 class="card-title mb-3">目录</h5>
                    <div class="nav flex-column nav-pills" id="api-tabs" role="tablist">
                        <a class="nav-link active" href="#overview">概述</a>
                        <a class="nav-link" href="#authentication">认证与安全</a>
                        <a class="nav-link" href="#frontend-api">前端API <span class="badge bg-secondary">{{ api_counts.internal_api }}</span></a>
                        <a class="nav-link" href="#admin-api">管理API <span class="badge bg-secondary">{{ api_counts.admin_api }}</span></a>
                        <a class="nav-link" href="#public-api">公开API <span class="badge bg-secondary">{{ api_counts.public_api }}</span></a>
                        <a class="nav-link" href="#rest-api">集成API <span class="badge bg-secondary">{{ api_counts.rest_api }}</span></a>
                        <a class="nav-link" href="#error-codes">错误代码</a>
                        <a class="nav-link" href="#examples">使用示例</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 右侧内容 -->
        <div class="col-md-9">
            <div class="card shadow-sm">
                <div class="card-body">
                    <!-- 概述 -->
                    <section id="overview" class="api-section">
                        <h2>概述</h2>
                        <p>医院服务评价系统API提供了对科室、床位、人员、二维码和评价等核心功能的编程访问能力。根据访问权限和使用场景，API分为以下几类：</p>
                        <ul>
                            <li><strong>前端API</strong> <span class="access-badge frontend">前端使用</span>：仅供系统前端界面调用，需要用户登录</li>
                            <li><strong>管理API</strong> <span class="access-badge admin">管理员</span>：需要管理员登录，用于系统管理功能</li>
                            <li><strong>公开API</strong> <span class="access-badge public">公开</span>：无需认证，但有安全验证，用于公开访问的功能（如评价提交）</li>
                            <li><strong>集成API</strong> <span class="access-badge third-party">第三方</span>：需要API密钥认证，提供标准RESTful接口，用于第三方系统集成</li>
                        </ul>

                        <h4 class="mt-4">API数量统计</h4>
                        <p>系统当前包含以下API端点：</p>
                        <ul>
                            <li><strong>管理API</strong>：{{ api_counts.admin_api }}个 - 用于系统管理界面的数据交互</li>
                            <li><strong>内部API</strong>：{{ api_counts.internal_api }}个 - 用于系统内部组件间通信</li>
                            <li><strong>RESTful API</strong>：{{ api_counts.rest_api }}个 - 用于第三方系统集成</li>
                            <li><strong>公开API</strong>：{{ api_counts.public_api }}个 - 允许无认证访问的公共API</li>
                            <li><strong>总计</strong>：{{ api_counts.total }}个API端点</li>
                        </ul>

                        <h4 class="mt-4">统一响应格式</h4>
                        <p>所有API响应都遵循以下JSON格式：</p>
                        <div class="code-block">
                            <pre>{
  "status": "success", // 或 "error"
  "data": {},          // 成功时返回的数据对象
  "message": "",       // 操作结果的文本描述
  "errors": {}         // 出错时返回的详细错误信息
}</pre>
                        </div>

                        <h4 class="mt-4">错误处理</h4>
                        <p>API错误遵循以下HTTP状态码约定：</p>
                        <ul>
                            <li><strong>400 Bad Request</strong> - 请求参数无效或格式错误</li>
                            <li><strong>401 Unauthorized</strong> - 未提供认证信息或认证失败</li>
                            <li><strong>403 Forbidden</strong> - 无权访问请求的资源</li>
                            <li><strong>404 Not Found</strong> - 请求的资源不存在</li>
                            <li><strong>429 Too Many Requests</strong> - 请求频率超出限制</li>
                            <li><strong>500 Internal Server Error</strong> - 服务器内部错误</li>
                        </ul>
                        <p>详细的错误信息会在响应的<code>errors</code>字段中提供。</p>

                        <h4 class="mt-4">API版本</h4>
                        <p>当前API版本为 <strong>v1</strong>。我们采用以下版本控制策略：</p>
                        <ul>
                            <li>主版本号更新（如v1到v2）：可能包含不兼容变更，需要调整客户端代码</li>
                            <li>次版本号更新（如v1.1到v1.2）：添加新功能，保持向后兼容</li>
                            <li>修订版本更新：小型修复，保持完全兼容</li>
                        </ul>
                        <p>所有API变更将提前30天通知，以便开发者做好升级准备。</p>
                    </section>

                    <!-- 认证与安全 -->
                    <section id="authentication" class="api-section">
                        <h2>认证与安全</h2>

                        <h4 class="mt-4">前端和管理API认证 <span class="access-badge frontend">前端使用</span> <span class="access-badge admin">管理员</span></h4>
                        <p>前端和管理API使用Django的会话认证，需要先登录系统。所有请求需要包含CSRF Token：</p>
                        <div class="code-block">
                            <code>X-CSRFToken: YOUR_CSRF_TOKEN</code>
                        </div>

                        <h4 class="mt-4">API密钥认证 <span class="access-badge third-party">第三方</span></h4>
                        <p>集成API（RESTful API）请求需要在HTTP头部包含以下认证信息:</p>
                        <div class="code-block">
                            <code>Authorization: Bearer YOUR_API_KEY</code>
                        </div>

                        <h4 class="mt-4">安全限制</h4>
                        <p>为防止滥用，API请求有以下限制：</p>
                        <ul>
                            <li><strong>匿名用户</strong>：每分钟20次请求，每小时300次，每天1000次</li>
                            <li><strong>认证用户</strong>：每分钟60次请求，每小时600次，每天2000次</li>
                            <li><strong>API密钥限制</strong>：每个API密钥可单独配置以下限制：
                                <ul>
                                    <li>每分钟请求限制（默认10次）</li>
                                    <li>每小时请求限制（默认100次）</li>
                                    <li>每日请求限制（默认1000次）</li>
                                    <li>IP地址白名单，只允许特定IP地址访问</li>
                                    <li>资源访问权限（科室、床位、员工等）</li>
                                    <li>操作权限（读取、写入、删除）</li>
                                </ul>
                            </li>
                            <li><strong>敏感操作保护</strong>：数据删除、密码修改等敏感操作需要额外的权限验证</li>
                            <li><strong>并发限制</strong>：每个API密钥同时最多允许5个并发连接</li>
                        </ul>
                        <p>超过限制将返回<code>429 Too Many Requests</code>状态码和<code>{"status": "error", "message": "请求过于频繁，请稍后再试"}</code>响应。</p>

                        <h4 class="mt-4">CSRF保护</h4>
                        <p>所有非GET请求都受到CSRF保护，必须包含有效的CSRF令牌。前端JavaScript请求应该从页面中获取CSRF令牌：</p>
                        <div class="code-block">
                            <code>const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;</code>
                        </div>

                        <h4 class="mt-4">跨域限制</h4>
                        <p>为了提高安全性，API采用了严格的跨域限制：</p>
                        <ul>
                            <li>前端和管理API：只允许同源请求</li>
                            <li>公开API：允许特定来源的跨域请求</li>
                            <li>集成API：可配置允许的域名列表</li>
                        </ul>

                        <h4 class="mt-4">防护常见攻击</h4>
                        <p>系统实现了以下安全措施来防护常见的API攻击：</p>
                        <ul>
                            <li><strong>SQL注入防护</strong>：系统使用Django ORM和参数化查询，所有数据库操作都经过严格过滤</li>
                            <li><strong>XSS防护</strong>：所有用户输入在存储前和显示前都经过严格过滤，前端使用安全的sanitizeHTML函数处理动态内容</li>
                            <li><strong>CSRF保护</strong>：所有非GET请求都要求有效的CSRF令牌验证，系统启用了Django的CsrfViewMiddleware</li>
                            <li><strong>API请求伪造防护</strong>：实现了请求签名验证和API密钥验证，确保请求来自合法来源</li>
                            <li><strong>请求注入防护</strong>：使用内容类型验证和请求体大小限制，防止恶意请求</li>
                            <li><strong>中间件保护</strong>：通过APISecurityMiddleware实现多层次安全检查，包括：
                              <ul>
                                <li>来源IP验证：API密钥可以限制特定IP地址访问</li>
                                <li>请求速率限制：防止暴力攻击和DoS攻击</li>
                                <li>API密钥过期检查：确保不使用已过期的凭证</li>
                                <li>权限细粒度控制：每个API密钥都有明确的资源访问权限</li>
                              </ul>
                            </li>
                            <li><strong>日志记录与审计</strong>：所有API访问都详细记录，包括请求IP、时间、内容和响应状态，便于安全审计</li>
                        </ul>

                        <h4 class="mt-4">数据格式与编码</h4>
                        <p>所有API请求和响应都使用以下数据格式：</p>
                        <ul>
                            <li>请求体：JSON格式，UTF-8编码，<code>Content-Type: application/json</code></li>
                            <li>响应体：JSON格式，UTF-8编码</li>
                            <li>日期时间：ISO 8601格式（<code>YYYY-MM-DDTHH:MM:SSZ</code>）</li>
                            <li>布尔值：使用<code>true</code>或<code>false</code></li>
                        </ul>

                        <h4 class="mt-4">并发请求限制</h4>
                        <p>除了每分钟请求次数限制外，系统还对并发请求数有以下限制：</p>
                        <ul>
                            <li>匿名用户：最多5个并发请求</li>
                            <li>认证用户：最多15个并发请求</li>
                            <li>API密钥用户：可配置（默认20个并发请求）</li>
                        </ul>
                        <p>超过并发限制的请求将返回503状态码（服务暂时不可用）。</p>

                        <!-- 添加数据加密与隐私保护部分 -->
                        <section id="data-security" class="api-section">
                            <h2>数据加密与隐私保护</h2>
                            <p>医院服务评价系统高度重视数据安全和患者隐私保护，实施了以下安全措施：</p>

                            <h4 class="mt-4">敏感数据加密</h4>
                            <ul>
                                <li><strong>UUID加密</strong>：二维码中的UUID在传输和处理过程中使用安全算法加密</li>
                                <li><strong>床位信息加密</strong>：床位与患者关联信息使用数据库级加密存储</li>
                                <li><strong>评价内容保护</strong>：评价内容在存储前进行脱敏处理，移除可能的个人标识信息</li>
                                <li><strong>API密钥保护</strong>：API密钥使用不可逆加密存储，即使数据库泄露也无法还原</li>
                            </ul>

                            <h4 class="mt-4">数据传输安全</h4>
                            <ul>
                                <li><strong>HTTPS加密</strong>：生产环境中所有API通信强制使用HTTPS加密传输</li>
                                <li><strong>参数签名</strong>：关键API请求参数使用HMAC算法进行签名验证，防止篡改</li>
                                <li><strong>敏感信息处理</strong>：响应中的敏感信息（如完整的UUID）会部分隐藏</li>
                            </ul>

                            <h4 class="mt-4">隐私数据处理规范</h4>
                            <ul>
                                <li><strong>最小数据原则</strong>：API只返回完成当前任务所需的最少数据</li>
                                <li><strong>数据分级</strong>：系统对数据进行安全分级，不同级别采用不同的保护措施</li>
                                <li><strong>数据生命周期</strong>：对临时数据设置严格的过期和清理机制</li>
                                <li><strong>日志脱敏</strong>：系统日志中自动脱敏个人敏感信息</li>
                            </ul>

                            <div class="alert alert-warning mt-3">
                                <strong>开发者注意事项：</strong>
                                <p>作为API调用方，您有责任：</p>
                                <ul>
                                    <li>保护好您的API密钥，定期轮换</li>
                                    <li>不在客户端存储敏感数据</li>
                                    <li>实施适当的权限控制，防止未授权访问</li>
                                    <li>遵守医疗数据相关法规和隐私保护要求</li>
                                </ul>
                            </div>
                        </section>
                    </section>

                    <!-- 前端API -->
                    <section id="frontend-api" class="api-section api-category">
                        <h2>前端API <span class="access-badge frontend">前端使用</span></h2>
                        <p>前端API专门用于系统界面与后端交互，需要用户登录。<strong>这些API不应被外部系统直接调用。</strong></p>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/departments/</span>
                            </div>
                            <p>获取科室列表（前端展示用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/beds/</span>
                            </div>
                            <p>获取床位列表（前端展示用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/staff/</span>
                            </div>
                            <p>获取工作人员列表（前端展示用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/qrcodes/</span>
                            </div>
                            <p>获取二维码列表（前端展示用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/department/{pk}/staff/</span>
                            </div>
                            <p>获取科室的工作人员列表（前端下拉选项用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/evaluations/</span>
                            </div>
                            <p>获取评价列表（前端展示用）</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/print_templates/{template_id}/</span>
                            </div>
                            <p>获取打印模板详情</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/departments/{pk}/</span>
                            </div>
                            <p>获取科室详情</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/admin/api/beds/import/</span>
                            </div>
                            <p>导入床位数据</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin/api/beds/template/</span>
                            </div>
                            <p>获取床位导入模板</p>
                        </div>
                    </section>

                    <!-- 管理API -->
                    <section id="admin-api" class="api-section api-category">
                        <h2>管理API <span class="access-badge admin">管理员</span></h2>
                        <p>管理API需要管理员权限，用于系统管理功能。<strong>这些API仅供系统管理页面使用，不应被外部系统调用。</strong></p>

                        <div class="alert alert-danger">
                            <i class="fas fa-user-shield me-2"></i>
                            <strong>管理员权限警告：</strong> 这些API具有较高权限，可以进行敏感操作。系统会记录所有管理API调用，包括操作人、时间、IP地址等信息，以便审计和追溯。
                        </div>

                        <h4 class="mt-4">科室管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/departments/</span>
                            </div>
                            <p>获取科室列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>search</code>：搜索关键词（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/departments/create/</span>
                            </div>
                            <p>创建新科室</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：科室名称</li>
                                <li><code>code</code>：科室代码</li>
                                <li><code>description</code>：描述（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/departments/{pk}/edit/</span>
                            </div>
                            <p>编辑科室信息</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：科室名称</li>
                                <li><code>code</code>：科室代码</li>
                                <li><code>description</code>：描述（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/departments/{pk}/delete/</span>
                            </div>
                            <p>删除科室</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/departments/import/</span>
                            </div>
                            <p>导入科室数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>file</code>：Excel文件（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/departments/export/</span>
                            </div>
                            <p>导出科室数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、csv（可选，默认excel）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">床位管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/beds/</span>
                            </div>
                            <p>获取床位列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>search</code>：搜索关键词（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/beds/create/</span>
                            </div>
                            <p>创建新床位</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>number</code>：床位号</li>
                                <li><code>department</code>：科室ID</li>
                                <li><code>area</code>：区域（可选）</li>
                                <li><code>staff</code>：负责人ID（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/beds/{pk}/edit/</span>
                            </div>
                            <p>编辑床位信息</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>number</code>：床位号</li>
                                <li><code>department</code>：科室ID</li>
                                <li><code>area</code>：区域（可选）</li>
                                <li><code>staff</code>：负责人ID（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/beds/{pk}/delete/</span>
                            </div>
                            <p>删除床位</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/beds/import/</span>
                            </div>
                            <p>导入床位数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>file</code>：Excel文件（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/beds/export/</span>
                            </div>
                            <p>导出床位数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、csv（可选，默认excel）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/beds/qr_preview/</span>
                            </div>
                            <p>预览床位二维码</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>bed_id</code>：床位ID（必填）</li>
                                <li><code>template_id</code>：模板ID（可选）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">二维码管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/qrcodes/</span>
                            </div>
                            <p>获取二维码列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>search</code>：搜索关键词（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/qrcodes/create/</span>
                            </div>
                            <p>创建新二维码</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>bed</code>：床位ID</li>
                                <li><code>code</code>：二维码内容（可选，自动生成）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/qrcodes/{pk}/update/</span>
                            </div>
                            <p>更新二维码信息</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>bed</code>：床位ID</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/qrcodes/{pk}/delete/</span>
                            </div>
                            <p>删除二维码</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/qrcodes/{pk}/regenerate/</span>
                            </div>
                            <p>重新生成二维码</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/qrcodes/{pk}/preview/</span>
                            </div>
                            <p>预览二维码</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>template_id</code>：模板ID（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/qrcodes/{qrcode_id}/image/</span>
                            </div>
                            <p>获取二维码图片</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>size</code>：图片大小（可选，默认200）</li>
                                <li><code>format</code>：图片格式（可选，默认png）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/qrcodes/history/</span>
                            </div>
                            <p>获取二维码历史记录</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/qrcodes/import/</span>
                            </div>
                            <p>导入二维码数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>file</code>：Excel文件（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/qrcodes/export/</span>
                            </div>
                            <p>导出二维码数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、csv（可选，默认excel）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/qrcodes/print/department/{department_id}/</span>
                            </div>
                            <p>打印指定科室的二维码</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>template_id</code>：模板ID</li>
                                <li><code>copies</code>：打印份数（可选）</li>
                                <li><code>area</code>：区域筛选（可选）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">评价管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/evaluations/</span>
                            </div>
                            <p>获取评价列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>is_satisfied</code>：是否满意（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/evaluations/process/{evaluation_id}/</span>
                            </div>
                            <p>处理评价</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>status</code>：处理状态（必填）</li>
                                <li><code>note</code>：处理备注（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/evaluations/export/</span>
                            </div>
                            <p>导出评价数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、csv（可选，默认excel）</li>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>is_satisfied</code>：是否满意（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/evaluations/bulk_delete/</span>
                            </div>
                            <p>批量删除评价</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>ids</code>：评价ID列表（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/sentiment/</span>
                            </div>
                            <p>情感分析</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/sentiment/export/</span>
                            </div>
                            <p>导出情感分析报告</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、pdf（可选，默认excel）</li>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">工作人员管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/staff/</span>
                            </div>
                            <p>获取工作人员列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>staff_type</code>：人员类型（可选）</li>
                                <li><code>search</code>：搜索关键词（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/staff/create/</span>
                            </div>
                            <p>创建工作人员</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>work_number</code>：工号（必填）</li>
                                <li><code>name</code>：姓名（必填）</li>
                                <li><code>staff_type</code>：人员类型ID（必填）</li>
                                <li><code>title</code>：职称ID（可选）</li>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>photo</code>：照片（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/staff/{pk}/edit/</span>
                            </div>
                            <p>编辑工作人员</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>work_number</code>：工号（可选）</li>
                                <li><code>name</code>：姓名（可选）</li>
                                <li><code>staff_type</code>：人员类型ID（可选）</li>
                                <li><code>title</code>：职称ID（可选）</li>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>photo</code>：照片（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/staff/{pk}/delete/</span>
                            </div>
                            <p>删除工作人员</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/staff/export/</span>
                            </div>
                            <p>导出工作人员数据</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>format</code>：导出格式，支持excel、csv（可选，默认excel）</li>
                                <li><code>department</code>：科室ID（可选）</li>
                                <li><code>staff_type</code>：人员类型（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/staff/bulk_import/</span>
                            </div>
                            <p>批量导入工作人员</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>file</code>：Excel文件（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/staff/template/</span>
                            </div>
                            <p>下载工作人员导入模板</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/staff/bulk_delete/</span>
                            </div>
                            <p>批量删除工作人员</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>ids</code>：工作人员ID列表（必填）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">用户管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/admin_permissions/</span>
                            </div>
                            <p>获取管理员权限列表</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/admin/create/</span>
                            </div>
                            <p>创建管理员账号</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>username</code>：用户名（必填）</li>
                                <li><code>password</code>：密码（必填）</li>
                                <li><code>email</code>：邮箱（可选）</li>
                                <li><code>is_staff</code>：是否为工作人员（可选）</li>
                                <li><code>is_superuser</code>：是否为超级用户（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/admin_account_update/{pk}/</span>
                            </div>
                            <p>更新管理员账号</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>username</code>：用户名（可选）</li>
                                <li><code>email</code>：邮箱（可选）</li>
                                <li><code>is_staff</code>：是否为工作人员（可选）</li>
                                <li><code>is_superuser</code>：是否为超级用户（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/admin/reset_password/{pk}/</span>
                            </div>
                            <p>重置用户密码</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>password</code>：新密码（必填）</li>
                                <li><code>confirm_password</code>：确认密码（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/admin/delete_user/{pk}/</span>
                            </div>
                            <p>删除用户</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/account_settings/</span>
                            </div>
                            <p>获取账号设置</p>
                        </div>

                        <h4 class="mt-4">日志管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/operation_logs/</span>
                            </div>
                            <p>获取操作日志列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>user</code>：用户ID（可选）</li>
                                <li><code>action</code>：操作类型（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/operation_logs/export/excel/</span>
                            </div>
                            <p>导出操作日志（Excel格式）</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>user</code>：用户ID（可选）</li>
                                <li><code>action</code>：操作类型（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/operation_logs/export/pdf/</span>
                            </div>
                            <p>导出操作日志（PDF格式）</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>user</code>：用户ID（可选）</li>
                                <li><code>action</code>：操作类型（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/logging-config/</span>
                            </div>
                            <p>获取日志配置</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/logging-config/clear-old-logs/</span>
                            </div>
                            <p>清理旧日志</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>days</code>：保留天数（必填）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">打印模板管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/print-templates/</span>
                            </div>
                            <p>获取打印模板列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/print-templates/create/</span>
                            </div>
                            <p>创建打印模板</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：模板名称（必填）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>background_image</code>：背景图片（可选）</li>
                                <li><code>qr_position_x</code>：二维码X坐标（可选）</li>
                                <li><code>qr_position_y</code>：二维码Y坐标（可选）</li>
                                <li><code>qr_size</code>：二维码大小（可选）</li>
                                <li><code>text_position_x</code>：文本X坐标（可选）</li>
                                <li><code>text_position_y</code>：文本Y坐标（可选）</li>
                                <li><code>text_font_size</code>：文本字体大小（可选）</li>
                                <li><code>text_color</code>：文本颜色（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/departments/{department_id}/print-template/create/</span>
                            </div>
                            <p>为科室创建打印模板</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：模板名称（必填）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>background_image</code>：背景图片（可选）</li>
                                <li><code>qr_position_x</code>：二维码X坐标（可选）</li>
                                <li><code>qr_position_y</code>：二维码Y坐标（可选）</li>
                                <li><code>qr_size</code>：二维码大小（可选）</li>
                                <li><code>text_position_x</code>：文本X坐标（可选）</li>
                                <li><code>text_position_y</code>：文本Y坐标（可选）</li>
                                <li><code>text_font_size</code>：文本字体大小（可选）</li>
                                <li><code>text_color</code>：文本颜色（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/print-templates/{pk}/update/</span>
                            </div>
                            <p>更新打印模板</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：模板名称（可选）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>background_image</code>：背景图片（可选）</li>
                                <li><code>qr_position_x</code>：二维码X坐标（可选）</li>
                                <li><code>qr_position_y</code>：二维码Y坐标（可选）</li>
                                <li><code>qr_size</code>：二维码大小（可选）</li>
                                <li><code>text_position_x</code>：文本X坐标（可选）</li>
                                <li><code>text_position_y</code>：文本Y坐标（可选）</li>
                                <li><code>text_font_size</code>：文本字体大小（可选）</li>
                                <li><code>text_color</code>：文本颜色（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/print-templates/{pk}/preview/</span>
                            </div>
                            <p>预览打印模板</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>bed_id</code>：床位ID（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/print-templates/{pk}/delete/</span>
                            </div>
                            <p>删除打印模板</p>
                        </div>

                        <h4 class="mt-4">字典管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/dictionaries/</span>
                            </div>
                            <p>获取字典列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/dictionaries/create/</span>
                            </div>
                            <p>创建字典</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：字典名称（必填）</li>
                                <li><code>code</code>：字典代码（必填）</li>
                                <li><code>description</code>：描述（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/dictionaries/{pk}/update/</span>
                            </div>
                            <p>更新字典</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：字典名称（可选）</li>
                                <li><code>code</code>：字典代码（可选）</li>
                                <li><code>description</code>：描述（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/dictionaries/{pk}/delete/</span>
                            </div>
                            <p>删除字典</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/dictionaries/{dictionary_id}/items/</span>
                            </div>
                            <p>获取字典项列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/dictionaries/{dictionary_id}/items/create/</span>
                            </div>
                            <p>创建字典项</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>label</code>：标签（必填）</li>
                                <li><code>value</code>：值（必填）</li>
                                <li><code>sort_order</code>：排序（可选）</li>
                                <li><code>is_default</code>：是否默认（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/dictionaries/items/{pk}/update/</span>
                            </div>
                            <p>更新字典项</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>label</code>：标签（可选）</li>
                                <li><code>value</code>：值（可选）</li>
                                <li><code>sort_order</code>：排序（可选）</li>
                                <li><code>is_default</code>：是否默认（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/dictionaries/items/{pk}/delete/</span>
                            </div>
                            <p>删除字典项</p>
                        </div>

                        <h4 class="mt-4">系统配置</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/system-configs/</span>
                            </div>
                            <p>获取系统配置列表</p>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/system-configs/{pk}/update/</span>
                            </div>
                            <p>更新系统配置</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>value</code>：配置值（必填）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/system-configs/initialize/</span>
                            </div>
                            <p>初始化系统配置</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/update_url_settings/</span>
                            </div>
                            <p>更新URL设置</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>base_url</code>：基础URL（必填）</li>
                            </ul>
                        </div>

                        <h4 class="mt-4">API管理</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/management/</span>
                            </div>
                            <p>获取API管理信息</p>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/api/keys/create/</span>
                            </div>
                            <p>创建API密钥</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：密钥名称（必填）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>expires_at</code>：过期时间（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/api/keys/{pk}/toggle/</span>
                            </div>
                            <p>切换API密钥状态</p>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/api/keys/{pk}/delete/</span>
                            </div>
                            <p>删除API密钥</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/logs/</span>
                            </div>
                            <p>获取API日志</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api-keys/</span>
                            </div>
                            <p>获取API密钥列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/api-keys/create/</span>
                            </div>
                            <p>创建API密钥</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：密钥名称（必填）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>expires_at</code>：过期时间（可选）</li>
                                <li><code>allowed_ips</code>：允许的IP地址（可选）</li>
                                <li><code>allowed_domains</code>：允许的域名（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint put">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method put">PUT</span>
                                <span class="endpoint-url">/api-keys/{pk}/update/</span>
                            </div>
                            <p>更新API密钥</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>name</code>：密钥名称（可选）</li>
                                <li><code>description</code>：描述（可选）</li>
                                <li><code>expires_at</code>：过期时间（可选）</li>
                                <li><code>allowed_ips</code>：允许的IP地址（可选）</li>
                                <li><code>allowed_domains</code>：允许的域名（可选）</li>
                                <li><code>is_active</code>：是否激活（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint delete">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method delete">DELETE</span>
                                <span class="endpoint-url">/api-keys/{pk}/delete/</span>
                            </div>
                            <p>删除API密钥</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api-logs/</span>
                            </div>
                            <p>获取API日志列表</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>page</code>：页码（可选）</li>
                                <li><code>api_key</code>：API密钥ID（可选）</li>
                                <li><code>status_code</code>：状态码（可选）</li>
                                <li><code>date_from</code>：开始日期（可选）</li>
                                <li><code>date_to</code>：结束日期（可选）</li>
                            </ul>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api-logs/{pk}/</span>
                            </div>
                            <p>获取API日志详情</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/docs/</span>
                            </div>
                            <p>获取API文档</p>
                        </div>
                    </section>

                    <!-- 公开API -->
                    <section id="public-api" class="api-section api-category">
                        <h2>公开API <span class="access-badge public">公开</span></h2>
                        <p>公开API无需认证，但有安全验证，用于公开访问的功能。这些API有严格的访问限制和安全措施。</p>

                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>安全警告：</strong> 虽然这些API可以公开访问，但系统会对请求进行严格验证，包括请求频率、来源IP、防护参数篡改等措施。任何异常请求将被记录并可能导致临时封禁。
                        </div>



                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/api/v1/public/submit-evaluation/</span>
                            </div>
                            <p>提交评价（公开API，使用加密参数认证）</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>qr_param</code>：加密参数（必填，与verify API使用相同参数）</li>
                                <li><code>comment</code>：评价内容（必填，文本）</li>
                                <li><code>staff_evaluations</code>：工作人员评价列表（必填，数组），每项包含：
                                    <ul>
                                        <li><code>staff_id</code>: 工作人员ID</li>
                                        <li><code>is_satisfied</code>: 是否满意（布尔值）</li>
                                    </ul>
                                </li>
                                <li><code>hospital_number</code>：住院号（选填，文本）</li>
                                <li><code>phone_number</code>：联系电话（选填，文本）</li>
                            </ul>

                            <div class="alert alert-warning mt-3">
                                <strong>评价限制：</strong>
                                <ul>
                                    <li>满意的工作人员最多可以选择3个</li>
                                    <li>不满意的工作人员最多可以选择3个</li>
                                    <li>超出限制将返回错误响应</li>
                                    <li>至少需要有一个有效的工作人员ID</li>
                                </ul>
                            </div>

                            <div class="alert alert-info mt-3">
                                <strong>安全说明：</strong>
                                <ul>
                                    <li>此接口采用特殊安全措施防止滥用：
                                        <ul>
                                            <li>加密参数验证，确保请求合法且未被篡改</li>
                                            <li>UUID有效性验证，确保二维码存在且未过期</li>
                                            <li>内容过滤，移除所有潜在危险HTML标签和脚本</li>
                                            <li>IP限流，相同IP短时间内提交次数限制，防止刷评价</li>
                                            <li>评价内容敏感词过滤</li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>

                            <p><strong>示例请求：</strong></p>
                            <pre>{
  "qr_param": "0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5ADM4UzYwMWM...",
  "comment": "感谢各位医护人员的耐心服务",
  "hospital_number": "HN202503231234",
  "phone_number": "13800138000",
  "staff_evaluations": [
    {"staff_id": 1, "is_satisfied": true},
    {"staff_id": 2, "is_satisfied": false},
    {"staff_id": 3, "is_satisfied": true}
  ]
}</pre>

                            <p><strong>示例响应：</strong></p>
                            <pre>{
  "status": "success",
  "message": "评价提交成功",
  "data": {
    "evaluation_id": 123,
    "successful_count": 3,
    "failed_count": 0,
    "failed_staff_ids": [],
    "staff_evaluations": [
      {"id": 1, "name": "张医生", "is_satisfied": true, "type": "医生"},
      {"id": 2, "name": "王护士", "is_satisfied": false, "type": "护士"},
      {"id": 3, "name": "李医生", "is_satisfied": true, "type": "医生"}
    ]
  }
}</pre>

                            <p><strong>错误响应：</strong></p>
                            <pre>{
  "status": "error",
  "message": "满意评价超出限制，最多选择3个",
  "detail": "请减少满意评价的数量"
}</pre>
                        </div>

                        <div class="api-endpoint post">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method post">POST</span>
                                <span class="endpoint-url">/api/v1/public/qrcode/verify/</span>
                            </div>
                            <p>验证二维码并返回科室及工作人员信息 (推荐使用)</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>qr_param</code> 或 <code>encrypted_param</code>：二维码加密参数（必填，在请求体JSON中提供）</li>
                            </ul>

                            <div class="alert alert-info mt-3">
                                <strong>接口说明：</strong>
                                <ul>
                                    <li>此接口是验证二维码的推荐方式，使用POST请求，参数在请求体中提供，更安全</li>
                                    <li>返回临时令牌（<code>temp_token</code>）用于后续提交评价</li>
                                    <li>同时返回二维码对应的床位、科室和工作人员信息</li>
                                    <li>接口会进行严格的参数验证，包括格式检查、长度检查和字符集检查</li>
                                </ul>
                            </div>

                            <p><strong>示例请求：</strong></p>
                            <pre>{
  "qr_param": "0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5ADM4UzYwMWM..."
}</pre>

                            <p><strong>示例响应：</strong></p>
                            <pre>{
  "status": "success",
  "data": {
    "temp_token": "8e7a1b3c-5d9f-4e2a-8b0c-6f7d4e3c2b1a",
    "expires_at": "2025-03-18T19:42:15Z",
    "qrcode": {},
    "bed": {
      "id": 123,
      "number": "101",
      "area": "A区"
    },
    "department": {
      "id": 45,
      "name": "内科",
      "code": "NK"
    },
    "staff_types": [
      {"id": 1, "name": "医生", "code": "doctor"},
      {"id": 2, "name": "护士", "code": "nurse"}
    ],
    "staff": [
      {"id": 789, "name": "张三", "title": "主任医师", "staff_type": 1},
      {"id": 790, "name": "李四", "title": "护士长", "staff_type": 2}
    ]
  }
}</pre>

                            <p><strong>错误响应：</strong></p>
                            <pre>{
  "status": "error",
  "message": "二维码参数无效"
}</pre>
                        </div>


                    </section>

                    <!-- 集成API (RESTful) -->
                    <section id="rest-api" class="api-section api-category">
                        <h2>集成API <span class="access-badge third-party">第三方</span></h2>
                        <p>集成API需要API令牌认证，提供标准RESTful接口，适用于第三方系统集成。<strong>使用这些API需要先申请API密钥。</strong></p>

                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>
                            要访问集成API，第三方系统需要提前申请API密钥，并配置IP白名单。详情请联系系统管理员。
                        </div>

                        <div class="alert alert-warning mt-2">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>密钥安全说明：</strong> API密钥具有较高权限，应妥善保管，不得泄露。密钥不应直接嵌入客户端应用程序或前端代码中。建议通过后端服务器转发请求来调用API。
                        </div>

                        <h4 class="mt-4">科室API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/departments/</span>
                            </div>
                            <p>获取所有科室</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/departments/{pk}/</span>
                            </div>
                            <p>获取单个科室详情</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/staff-types/</span>
                            </div>
                            <p>获取所有工作人员类型</p>
                            <p><strong>描述：</strong></p>
                            <p>此接口用于获取系统中所有可用的工作人员类型。返回工作人员类型的ID、名称和图标（如果有）。</p>
                            <p><strong>示例响应：</strong></p>
                            <pre>[
  {
    "id": "doctor",
    "name": "医生",
    "icon": "fa-user-md"
  },
  {
    "id": "nurse",
    "name": "护士",
    "icon": "fa-user-nurse"
  },
  {
    "id": "technician",
    "name": "技师",
    "icon": "fa-microscope"
  }
]</pre>
                        </div>

                        <h4 class="mt-4">床位API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/beds/</span>
                            </div>
                            <p>获取所有床位</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/beds/{pk}/</span>
                            </div>
                            <p>获取单个床位详情</p>
                        </div>

                        <h4 class="mt-4">工作人员API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/staff/</span>
                            </div>
                            <p>获取所有工作人员</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/staff/{pk}/</span>
                            </div>
                            <p>获取单个工作人员详情</p>
                        </div>

                        <h4 class="mt-4">二维码API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/qrcodes/</span>
                            </div>
                            <p>获取所有二维码</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/qrcodes/{pk}/</span>
                            </div>
                            <p>获取单个二维码详情</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/secure/qrcode/{qr_code}/</span>
                            </div>
                            <p>获取安全的二维码信息（需要API令牌认证）</p>
                            <p><strong>参数：</strong></p>
                            <ul>
                                <li><code>qr_code</code>：二维码ID（路径参数）</li>
                            </ul>
                            <p><strong>描述：</strong></p>
                            <p>此接口用于获取二维码的详细信息，包括关联的床位、科室和工作人员信息。需要API令牌认证。</p>
                            <p><strong>示例响应：</strong></p>
                            <pre>{
  "success": true,
  "qrcode": {
    "id": 123,
    "code": "1d11b138-d604-4d04-9ba6-b19141305797",
    "created_at": "2023-01-01T12:00:00Z"
  },
  "bed": {
    "id": 456,
    "number": "101",
    "area": "A区"
  },
  "department": {
    "id": 789,
    "name": "内科",
    "code": "NK"
  },
  "staff": {
    "id": 101,
    "name": "张医生",
    "work_number": "ZYS001",
    "staff_type": {
      "id": "doctor",
      "name": "医生"
    },
    "title": "主任医师",
    "photo_url": "/media/staff_photos/zys001.jpg"
  }
}</pre>
                            <p><strong>错误响应：</strong></p>
                            <pre>{
  "success": false,
  "error": "无法获取二维码信息",
  "message": "二维码不存在或已失效"
}</pre>
                        </div>

                        <h4 class="mt-4">评价API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/evaluations/</span>
                            </div>
                            <p>获取所有评价</p>
                        </div>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/evaluations/{pk}/</span>
                            </div>
                            <p>获取单个评价详情</p>
                        </div>

                        <h4 class="mt-4">打印模板API</h4>

                        <div class="api-endpoint get">
                            <div class="d-flex align-items-center mb-2">
                                <span class="method get">GET</span>
                                <span class="endpoint-url">/api/v1/print-templates/{pk}/</span>
                            </div>
                            <p>获取打印模板详情</p>
                        </div>
                    </section>

                    <!-- 错误代码 -->
                    <section id="error-codes" class="api-section">
                        <h2>错误代码</h2>
                        <p>API返回的错误代码及说明：</p>

                        <table class="table table-bordered">
                            <thead>
                                <tr>
                                    <th>状态码</th>
                                    <th>错误代码</th>
                                    <th>说明</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>400</td>
                                    <td>BAD_REQUEST</td>
                                    <td>请求参数错误</td>
                                </tr>
                                <tr>
                                    <td>401</td>
                                    <td>UNAUTHORIZED</td>
                                    <td>未登录或认证失败</td>
                                </tr>
                                <tr>
                                    <td>403</td>
                                    <td>FORBIDDEN</td>
                                    <td>权限不足</td>
                                </tr>
                                <tr>
                                    <td>404</td>
                                    <td>NOT_FOUND</td>
                                    <td>资源不存在</td>
                                </tr>
                                <tr>
                                    <td>422</td>
                                    <td>VALIDATION_ERROR</td>
                                    <td>数据验证失败</td>
                                </tr>
                                <tr>
                                    <td>429</td>
                                    <td>TOO_MANY_REQUESTS</td>
                                    <td>请求频率过高</td>
                                </tr>
                                <tr>
                                    <td>500</td>
                                    <td>INTERNAL_ERROR</td>
                                    <td>服务器内部错误</td>
                                </tr>
                                <tr>
                                    <td>503</td>
                                    <td>SERVICE_UNAVAILABLE</td>
                                    <td>服务暂时不可用（如并发请求过多）</td>
                                </tr>
                            </tbody>
                        </table>

                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle me-2"></i>
                            <strong>错误响应格式：</strong> 所有错误响应都遵循统一的JSON格式：
                            <div class="code-block mt-2">
                                <pre><code>{
    "error": {
        "code": "ERROR_CODE",
        "message": "错误描述信息",
        "details": {
            // 可选的错误详情
        }
    }
}</code></pre>
                            </div>
                        </div>
                    </section>

                    <!-- 使用示例 -->
                    <section id="examples" class="api-section">
                        <h2>使用示例</h2>

                        <h4 class="mt-4">前端和管理API调用示例 <span class="access-badge frontend">前端使用</span> <span class="access-badge admin">管理员</span></h4>
                        <div class="code-block">
                            <pre><code>// 前端JavaScript示例（需先登录）
// 获取CSRF Token
const csrfToken = document.querySelector('[name=csrfmiddlewaretoken]').value;

// 获取科室列表
fetch("/admin/api/departments/", {
    headers: {
        "X-CSRFToken": csrfToken,
        "Content-Type": "application/json"
    }
})
.then(response => response.json())
.then(data => {
    console.log(data);
});</code></pre>
                        </div>

                        <h4 class="mt-4">集成API调用示例 <span class="access-badge third-party">第三方</span></h4>
                        <div class="code-block">
                            <pre><code>import requests

# API密钥认证示例
api_key = "your_api_key"
headers = {
    "Authorization": f"Bearer {api_key}",
    "X-Request-ID": "unique-request-id",  # 推荐添加唯一请求ID便于跟踪
    "Content-Type": "application/json"
}

# 获取所有科室
response = requests.get(
    "http://example.com/api/v1/departments/",
    headers=headers
)

if response.status_code == 200:
    departments = response.json()
    print(departments)
else:
    print(f"Error: {response.status_code}")
    print(response.json())</code></pre>
                        </div>

                        <h4 class="mt-4">公开API调用示例 <span class="access-badge public">公开</span></h4>
                        <div class="code-block">
                            <pre><code>// JavaScript示例 - 提交评价
fetch("/api/v1/public/submit-evaluation/", {
    method: "POST",
    headers: {
        "Content-Type": "application/json"
    },
    body: JSON.stringify({
        qr_param: "0nI2cjMhJTNykjY0QmYwM2MwUTZmV2M2QjN5gzY4AzMyUmYxgTO1EjM5...",
        comment: "感谢医护人员的服务",
        hospital_number: "HN202503231234",
        phone_number: "13800138000",
        staff_evaluations: [
            {staff_id: 1, is_satisfied: true},
            {staff_id: 2, is_satisfied: false}
        ]
    })
})
.then(response => response.json())
.then(data => {
    console.log(data);
});</code></pre>
                        </div>

                        <h4 class="mt-4">安全最佳实践</h4>
                        <div class="alert alert-info">
                            <i class="fas fa-shield-alt me-2"></i>
                            <strong>API调用安全建议：</strong>
                            <ul class="mb-0 mt-2">
                                <li>定期轮换API密钥，建议每90天更换一次</li>
                                <li>使用最小权限原则，只申请实际需要的API权限</li>
                                <li>设置IP白名单，限制API密钥的使用范围</li>
                                <li>所有API调用使用HTTPS，禁止HTTP请求</li>
                                <li>存储API响应数据时，移除敏感信息</li>
                                <li>实现请求重试逻辑，但使用指数退避策略避免触发限流</li>
                                <li>为每个请求设置超时时间，通常建议5-10秒</li>
                                <li>记录所有API调用日志，但不记录敏感数据</li>
                            </ul>
                        </div>
                    </section>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
    // 激活导航栏
    document.addEventListener('DOMContentLoaded', function() {
        // 滚动到锚点位置时激活对应的导航项
        const sections = document.querySelectorAll('.api-section');
        const navLinks = document.querySelectorAll('#api-tabs a');

        function updateActiveLink() {
            let found = false;

            sections.forEach(section => {
                const sectionTop = section.getBoundingClientRect().top;
                const sectionId = section.getAttribute('id');

                if (sectionTop < 100 && !found) {
                    navLinks.forEach(link => {
                        link.classList.remove('active');
                        if (link.getAttribute('href') === '#' + sectionId) {
                            link.classList.add('active');
                            found = true;
                        }
                    });
                }
            });

            if (!found) {
                navLinks[0].classList.add('active');
            }
        }

        window.addEventListener('scroll', updateActiveLink);
        updateActiveLink();

        // 点击导航项滚动到对应位置
        navLinks.forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href').substring(1);
                const targetSection = document.getElementById(targetId);

                if (targetSection) {
                    window.scrollTo({
                        top: targetSection.offsetTop - 80,
                        behavior: 'smooth'
                    });
                }
            });
        });
    });
</script>
{% endblock %}